package com.baidu.translate;

import cn.hutool.crypto.SecureUtil;

import java.util.HashMap;
import java.util.Map;

public class TransApi {
    private static final String TRANS_API_HOST = "https://fanyi-api.baidu.com/api/trans/vip/translate";

    private String appid;
    private String securityKey;

    public TransApi(String appid, String securityKey) {
        this.appid = appid;
        this.securityKey = securityKey;
    }

    public String getTransResult(String query, String from, String to) {
        Map<String, String> params = buildParams(query, from, to);
        return HttpGet.get(TRANS_API_HOST, params);
    }

    private Map<String, String> buildParams(String query, String from, String to) {
        Map<String, String> params = new HashMap<String, String>();
        params.put("q", query);
        params.put("from", from);
        params.put("to", to);

        params.put("appid", appid);

        // 随机数
        String salt = String.valueOf(System.currentTimeMillis());
        params.put("salt", salt);

        // 签名
        String src = appid + query + salt + securityKey; // 加密前的原文
//        params.put("sign", MD5.md5(src));
        params.put("sign",  SecureUtil.md5(src));

        return params;
    }


    public static void main(String[] args) {
        //朱总的appid和密钥  不能用
        //    TransApi api = new TransApi("20241212002226187", "NmqryDkBORnoD0cXbJ9l");
        //8-阿毛的appid和密钥  不能用了，需要缴费
        //       TransApi api = new TransApi("20241211002225837", "I9Xa2UuW0r8gQfgGsMm5");
//        密钥：aDoxzeWO3nmOZkHYSKvG
//        APP ID：20241211002225571   陈总
        //       TransApi api = new TransApi("20241211002225571", "aDoxzeWO3nmOZkHYSKvG");
        //琼兵  APP ID：20241211002225758    密钥：zacYAY_JtcOvPVXwJue1
        TransApi api = new TransApi("20241211002225758", "zacYAY_JtcOvPVXwJue1");

     //frying   APP ID：20240603002069064           密钥：FzWSVOGwDJ6i6uPceHQl
        //阿斌
//        TransApi api = new TransApi("20240923002158398", "z0Tj11UPmBv74fQNr6C8");

        System.out.println(api.getTransResult("Ale czy dojdzie do świąt", "auto", "zh"));
	}

}
