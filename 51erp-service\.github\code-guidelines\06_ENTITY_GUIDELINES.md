# Entity 层规范

## 1. 概述

Entity 层是对象关系映射(ORM)的基础，代表了数据库表结构在应用程序中的映射。良好的 Entity 设计能提高代码可维护性和数据库交互的稳定性。

### 1.1 核心职责

- 映射数据库表结构
- 提供属性访问方法
- 支持ORM框架的各种特性（如关系映射、懒加载等）
- 不包含业务逻辑

## 2. 基本规范

### 2.1 位置与命名

- **包路径:** `com.xinghuo.[模块名].entity`
- **类命名:** 以 `Entity` 结尾，例如：`UserEntity`, `OzonChatMessageEntity`
- **表映射:** 使用 `@TableName` 明确指定表名，不依赖默认映射

### 2.2 基础注解

| 注解 | 提供方 | 作用 | 必要性 |
|------|--------|------|--------|
| `@Data` | Lombok | 自动生成getter/setter等方法 | 必须 |
| `@TableName` | MyBatis-Plus | 指定数据库表名 | 必须 |
| `@TableField` | MyBatis-Plus | 指定字段映射 | 必须 |
| `@TableLogic` | MyBatis-Plus | 标识逻辑删除字段 | 必须(有逻辑删除时) |
| `@EqualsAndHashCode(callSuper = true)` | Lombok | 继承时包含父类字段比较 | 必须(有继承时) |

## 3. 类继承体系

项目使用 `BaseEntityV2` 作为实体基类，提供多种变体以适应不同需求：

**正确的导入路径**：
```java
import com.xinghuo.common.base.entity.BaseEntityV2;
```

**继承体系结构**：
```
BaseEntityV2 (基础接口)
  └── IBaseEntityV2 (包含ID)
      └── TBaseEntityV2 (包含ID + 租户ID)
          └── CBaseEntityV2 (包含创建信息)
              ├── CUBaseEntityV2 (包含创建+更新信息) ← 推荐默认使用
              └── CUDBaseEntityV2 (包含创建+更新+删除信息)
```

### 3.1 选择合适的基类

- **一般实体:** 使用 `CUBaseEntityV2`（包含ID、租户ID、创建和更新信息）
- **需要删除信息:** 使用 `CUDBaseEntityV2`
- **简单实体:** 可使用 `IBaseEntityV2` 或 `TBaseEntityV2`

### 3.2 基类字段说明

基类自动提供以下字段，无需在实体中重复定义：

| 基类 | 提供字段 |
|------|----------|
| IBaseEntityV2 | `id` (主键) |
| TBaseEntityV2 | `tenantId` (租户ID) |
| CBaseEntityV2 | `createdAt`, `createdBy` |
| CUBaseEntityV2 | 以上 + `lastUpdatedAt`, `lastUpdatedBy` |
| CUDBaseEntityV2 | 以上 + `deleteMark`, `deletedAt`, `deletedBy` |

### 3.3 其他基类选择

项目中还提供了其他基类，适用于特定场景：

**AbstractBaseEntity 系列**：
```java
import com.xinghuo.common.base.entity.AbstractBaseEntity;

// 使用示例
public class MessageEntity extends AbstractBaseEntity.AbstractTBaseEntity<String> {
    // 实体字段...
}
```

**BaseExtendEntity 系列**：
```java
import com.xinghuo.common.base.entity.BaseExtendEntity;

// 使用示例
public class GroupEntity extends BaseExtendEntity.BaseExtendSortEntity<String> {
    // 实体字段...
}
```

**BaseEntity (传统基类)**：
```java
import com.xinghuo.common.base.entity.BaseEntity;

// 使用示例
public class SystemEntity extends BaseEntity<String> {
    // 实体字段...
}
```

**选择建议**：
- **新项目/新模块**: 优先使用 `BaseEntityV2` 系列
- **现有模块**: 保持与现有代码一致的基类选择
- **特殊需求**: 根据具体字段需求选择合适的基类

## 4. 字段设计规范

### 4.1 命名规范

- **Java字段:** 使用驼峰命名法 (如 `messageId`)
- **数据库列:** 使用下划线分隔 (如 `message_id`)
- **总是通过 `@TableField` 明确映射关系**

### 4.2 类型选择指南

| 数据用途 | Java类型 | 数据库类型 |
|---------|---------|-----------|
| ID/编码 | String, Long | varchar, bigint |
| 布尔值 | Boolean, Integer | tinyint(1), int |
| 日期时间 | Date, LocalDateTime | datetime, timestamp |
| 金额 | BigDecimal | decimal |
| 普通数值 | Integer, Long | int, bigint |
| 文本 | String | varchar, text |
| JSON数据 | String | varchar, text, json |
| 枚举值 | Integer, String | int, varchar |

### 4.3 注释规范

- 每个字段必须有注释，说明其用途
- 使用 `/** 注释内容 */` 格式
- 注明字段约束条件、可选值或特殊处理要求

## 5. 注解使用详解

### 5.1 表映射注解

```java
@TableName("表名")
```

- 始终显式指定表名，不使用默认映射规则
- 表名使用小写和下划线命名法

### 5.2 字段映射注解

```java
// 基本映射
@TableField("列名")

// 字段填充策略
@TableField(value = "列名", fill = FieldFill.INSERT)  // 仅插入时填充
@TableField(value = "列名", fill = FieldFill.INSERT_UPDATE)  // 插入和更新时填充

// 忽略策略
@TableField(exist = false)  // 非数据库字段
```

### 5.3 逻辑删除

```java
@TableField("is_deleted")
@TableLogic
private Boolean isDeleted;
```

- 推荐使用 Boolean 或 Integer 类型
- 始终使用 `@TableLogic` 标记

## 6. 完整示例

```java
/**
 * Ozon聊天消息实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ozon_chat_message")
public class OzonChatMessageEntity extends BaseEntityV2.CUBaseEntityV2<String> {

    /**
     * 店铺ID
     */
    @TableField("store_id")
    private String storeId;

    /**
     * 聊天ID
     */
    @TableField("chat_id")
    private String chatId;

    /**
     * Ozon消息ID
     */
    @TableField("message_id")
    private Long messageId;

    /**
     * 消息发送者类型（buyer/seller/system）
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 消息内容
     */
    @TableField("content")
    private String content;

    /**
     * 文件ID
     */
    @TableField("file_id")
    private Long fileId;

    /**
     * 文件名称
     */
    @TableField("file_name")
    private String fileName;

    /**
     * 文件URL
     */
    @TableField("file_url")
    private String fileUrl;

    /**
     * 文件类型
     */
    @TableField("mime_type")
    private String mimeType;

    /**
     * 消息发送时间
     */
    @TableField("message_time")
    private Date messageTime;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    @TableLogic
    private Boolean isDeleted;
}
```

### 6.2 简洁示例

```java
package com.xinghuo.product.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.BaseEntityV2;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商品收藏实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("product_favorite")
public class ProductFavoriteEntity extends BaseEntityV2.CUBaseEntityV2<String> {

    /**
     * 用户ID
     */
    @TableField("user_id")
    private String userId;

    /**
     * 商品ID
     */
    @TableField("product_id")
    private String productId;

    /**
     * 收藏分组ID
     */
    @TableField("group_id")
    private String groupId;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    @TableLogic
    private Boolean isDeleted;
}
```

## 7. 最佳实践

### 7.1 核心原则

1. 实体类保持**纯数据结构**，不包含业务逻辑
2. 继承适当的基类获取通用字段
3. 显式指定**所有**数据库映射，不依赖默认规则
4. 使用字段自动填充减少重复代码
5. 设计合理的**字段类型**和**长度**

### 7.2 常见问题与解决方案

| 问题 | 解决方案 |
|------|----------|
| 字段类型不匹配 | 严格按照数据库类型选择对应Java类型 |
| 忘记添加表名注解 | 使用代码模板确保必要注解 |
| equals/hashCode错误 | 继承时使用`@EqualsAndHashCode(callSuper = true)` |
| 需要临时字段 | 使用`@TableField(exist = false)` |
| 复杂数据存储 | 使用JSON格式存储，配合TypeHandler |

### 7.3 高级技巧

1. **类型处理器**：对于特殊类型（如JSON、枚举）使用自定义TypeHandler

   ```java
   @TableField(value = "status", typeHandler = EnumTypeHandler.class)
   private OrderStatus status;
   ```

2. **字段加密**：敏感字段考虑使用注解加密或脱敏处理

3. **复杂关联**：避免直接在Entity中使用关联实体，使用ID引用并在Service层处理关系

4. **条件构造**：善用EntityWrapper或LambdaQueryWrapper构造动态查询条件

## 8. 规范检查清单

开发Entity时，请检查以下项目：

- [ ] 正确命名并放置在合适的包中
- [ ] 继承了适当的基类
- [ ] 所有字段都有明确的注解和注释
- [ ] 使用了`@EqualsAndHashCode(callSuper = true)`（继承时）
- [ ] 所有字段类型与数据库兼容
- [ ] 包含适当的逻辑删除字段
- [ ] 没有包含业务逻辑
- [ ] 代码格式规范一致
