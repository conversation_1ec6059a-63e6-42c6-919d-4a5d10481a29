package com.xinghuo.allegro.aiservice.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class ServiceTaskModel {

    /**
     * 主键，自增
     */
    @Schema(description = "主键，自增")
    private Long id;

    @Schema(description = "任务类型 1-标题 2-描述")
    private Integer taskType;

    @Schema(description = "原始文本")
    private String originalText;

    @Schema(description = "产品id")
    private String productId;
}