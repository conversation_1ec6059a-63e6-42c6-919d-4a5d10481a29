package com.xinghuo.allegro.collect.controller;


import com.xinghuo.allegro.collect.entity.UpsEntity;
import com.xinghuo.allegro.collect.service.UpsService;
import com.xinghuo.common.annotation.NoDataSourceBind;
import com.xinghuo.common.base.ActionResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 检查事件处理
 *
 * <AUTHOR>
 * @date 2023-12-13
 */
@Slf4j
@RestController
@Tag(name = "UPS", description = "ups")
@RequestMapping("/api/allegro/collect/ups")
public class UpsController {

    @Autowired
    private UpsService upsService;


    @Operation(summary = "获取后台待处理数据清单")
    @GetMapping("/waitGets")
    @NoDataSourceBind
    public ActionResult<UpsEntity> waitGets() {
        UpsEntity entity  = upsService.waitGets();
        return ActionResult.success(entity);
    }

    /**
     * 接收并处理前端提交的商品信息解析请求。
     * 该方法主要负责解析前端上传的JSON数据，提取其中的商家和产品信息，
     * 并更新相应的数据库记录。
     */
    @Operation(summary = "前端提交详情页的json数据")
    @PutMapping("/putTaskJson")
    @NoDataSourceBind
    public ActionResult<String> putLinkJson(@RequestBody UpsEntity taskForm) {
        log.debug("前端提交详情页的json数据 taskId:{}", taskForm.getId());
        //满足条件的数据，设置已采集。
        UpsEntity entity = upsService.getById(taskForm.getId());
        if (entity != null) {
            upsService.updateById(taskForm);

        }
        else {
            log.warn("任务数据在系统中不存在已存在，忽略。ID:" + taskForm.getId());
        }
        return ActionResult.success("成功保存！" + taskForm.getId());
    }
}
