package com.xinghuo.allegro.collect.model.collect;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xinghuo.common.base.model.Pagination;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 采集任务分页查询参数
 * <AUTHOR>
 */
@Data
public class CollectTaskPagination extends Pagination {
    
    /**
     * 查询key
     */
    private String[] selectKey;
    
    /**
     * json
     */
    private String json;
    
    /**
     * 数据类型 0-当前页，1-全部数据
     */
    private String dataType;
    
    /**
     * 高级查询
     */
    private String superQueryJson;
    
    /**
     * 功能id
     */
    private String moduleId;
    
    /**
     * 菜单id
     */
    private String menuId;

    @Schema(description = "关键词搜索")
    private String keyword;

    @Schema(description = "任务类型")
    private String taskType;

    @Schema(description = "采集状态")
    private Integer status;

    @Schema(description = "采集客户端")
    private String clientId;

    @Schema(description = "平台")
    private String platform;


    private String sellerId;

    @Schema(description = "时间类型： 创建时间(f_created_at)，请求时间(request_time)，完成时间(finish_time)")
    private String dateType;


    @Schema(description = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @Schema(description = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;
}
