package com.xinghuo.allegro.data.model.sale;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class SaleSkuModel {


    @Schema(description = "销售日期")
    private String saleDate;
    @Schema(description = "当天销售数量")
    private Integer todayTotalSales;
    @Schema(description = "当天销售商品")
    private Integer totalOfferCount;
    @Schema(description = "当天新售商品")
    private Integer totalNewOfferCount;
    @Schema(description = "当天新售数量")
    private Integer todayNewOfferSales;
    @Schema(description = "匹配SKU数")
    private Integer skuAmount;
    @Schema(description = "待审核SKU数")
    private Integer skuUncheckAmount;
    @Schema(description = "可推送SKU数")
    private Integer skuOkAmount;
}
