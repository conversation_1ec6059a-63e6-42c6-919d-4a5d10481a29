package com.xinghuo.allegro.collect.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xinghuo.allegro.collect.dao.CollectTaskAnalysisMapper;
import com.xinghuo.allegro.collect.entity.CollectTaskEntity;
import com.xinghuo.allegro.collect.model.analysis.*;
import com.xinghuo.allegro.collect.service.CollectTaskAnalysisService;
import com.xinghuo.allegro.collect.service.CollectTaskService;
import com.xinghuo.allegro.util.AllegroConstant;
import com.xinghuo.common.util.core.StrXhUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 采集任务分析服务实现类
 * <AUTHOR>
 */
@Slf4j
@Service
public class CollectTaskAnalysisServiceImpl implements CollectTaskAnalysisService {

    @Autowired
    private CollectTaskService collectTaskService;

    @Autowired
    private CollectTaskAnalysisMapper analysisMapper;

    @Override
    public Long getPendingTaskCount() {
        try {
            QueryWrapper<CollectTaskEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(CollectTaskEntity::getStatus, AllegroConstant.REQUEST_STATUS_INIT);
            return collectTaskService.count(queryWrapper);
        } catch (Exception e) {
            log.error("获取待采集任务总数失败", e);
            return 0L;
        }
    }

    @Override
    public Long getInProgressTaskCount() {
        try {
            QueryWrapper<CollectTaskEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(CollectTaskEntity::getStatus, AllegroConstant.REQUEST_STATUS_PROCESSING);
            return collectTaskService.count(queryWrapper);
        } catch (Exception e) {
            log.error("获取采集中任务数失败", e);
            return 0L;
        }
    }

    @Override
    public Long getCompletedTodayCount(String date) {
        try {
            String targetDate = StrXhUtil.isNotEmpty(date) ? date : LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

            QueryWrapper<CollectTaskEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda()
                    .eq(CollectTaskEntity::getStatus, AllegroConstant.REQUEST_STATUS_FINISH)
                    .apply("DATE(finish_time) = {0}", targetDate);

            return collectTaskService.count(queryWrapper);
        } catch (Exception e) {
            log.error("获取今日已完成任务数失败", e);
            return 0L;
        }
    }

    @Override
    public Long getCollectedTodayOffers(String date) {
        try {
            String targetDate = StrXhUtil.isNotEmpty(date) ? date : LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

            QueryWrapper<CollectTaskEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda()
                    .eq(CollectTaskEntity::getStatus, AllegroConstant.REQUEST_STATUS_FINISH)
                    .apply("DATE(finish_time) = {0}", targetDate);

            List<CollectTaskEntity> tasks = collectTaskService.list(queryWrapper);
            return tasks.stream()
                    .mapToLong(task -> task.getTotalNum() != null ? task.getTotalNum() : 0)
                    .sum();
        } catch (Exception e) {
            log.error("获取今日采集总量失败", e);
            return 0L;
        }
    }

    @Override
    public Long getSalesTodayOffers(String date) {
        try {
            String targetDate = StrXhUtil.isNotEmpty(date) ? date : LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

            QueryWrapper<CollectTaskEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda()
                    .eq(CollectTaskEntity::getStatus, AllegroConstant.REQUEST_STATUS_FINISH)
                    .apply("DATE(finish_time) = {0}", targetDate);

            List<CollectTaskEntity> tasks = collectTaskService.list(queryWrapper);
            return tasks.stream()
                    .mapToLong(task -> task.getTotalSalesNum() != null ? task.getTotalSalesNum() : 0)
                    .sum();
        } catch (Exception e) {
            log.error("获取今日有销售额采集量失败", e);
            return 0L;
        }
    }

    @Override
    public Long getActiveClientsToday(String date) {
        try {
            String targetDate = StrXhUtil.isNotEmpty(date) ? date : LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

            QueryWrapper<CollectTaskEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda()
                    .eq(CollectTaskEntity::getStatus, AllegroConstant.REQUEST_STATUS_FINISH)
                    .apply("DATE(finish_time) = {0}", targetDate)
                    .isNotNull(CollectTaskEntity::getClientId);

            List<CollectTaskEntity> tasks = collectTaskService.list(queryWrapper);
            return tasks.stream()
                    .map(CollectTaskEntity::getClientId)
                    .distinct()
                    .count();
        } catch (Exception e) {
            log.error("获取今日活跃客户端数失败", e);
            return 0L;
        }
    }

    @Override
    public List<InProgressTaskModel> getInProgressTaskList(String clientId, String taskType, Integer page, Integer pageSize) {
        try {
            QueryWrapper<CollectTaskEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(CollectTaskEntity::getStatus, AllegroConstant.REQUEST_STATUS_PROCESSING);

            if (StrXhUtil.isNotEmpty(clientId)) {
                queryWrapper.lambda().eq(CollectTaskEntity::getClientId, clientId);
            }

            if (StrXhUtil.isNotEmpty(taskType)) {
                queryWrapper.lambda().eq(CollectTaskEntity::getTaskType, taskType);
            }

            queryWrapper.orderByDesc("request_time");

            Page<CollectTaskEntity> pageParam = new Page<>(page != null ? page : 1, pageSize != null ? pageSize : 10);
            Page<CollectTaskEntity> result = collectTaskService.page(pageParam, queryWrapper);

            return result.getRecords().stream().map(this::convertToInProgressTaskModel).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取进行中任务列表失败", e);
            return new ArrayList<>();
        }
    }

    private InProgressTaskModel convertToInProgressTaskModel(CollectTaskEntity entity) {
        InProgressTaskModel model = new InProgressTaskModel();
        model.setTaskId(entity.getId());
        model.setTaskType(entity.getTaskType());
        model.setTaskTypeDesc(getTaskTypeDesc(entity.getTaskType()));
        model.setCategoryPath(entity.getPath());
        model.setClientId(entity.getClientId());
        model.setCollectedCount(entity.getSumCount());
        model.setSalesCount(entity.getTotalSalesNum());
        model.setStartTime(entity.getRequestTime());
        model.setCollectLink(entity.getLink());
        model.setPriority(entity.getPriority());

        // 计算进度（如果有总数的话）
        if (entity.getTotalNum() != null && entity.getTotalNum() > 0 && entity.getSumCount() != null) {
            model.setProgress((double) entity.getSumCount() / entity.getTotalNum() * 100);
        }

        return model;
    }

    private String getTaskTypeDesc(String taskType) {
        if (taskType == null) return "未知";
        switch (taskType) {
            case "C": return "类目采集";
            case "S": return "卖家采集";
            case "P": return "产品采集";
            case "CNEW": return "类目新上采集";
            default: return taskType;
        }
    }

    @Override
    public List<TaskStatusSummaryModel> getTaskSummaryByStatusPriority(String taskType) {
        try {
            return analysisMapper.getTaskSummaryByStatusPriority(taskType);
        } catch (Exception e) {
            log.error("获取任务积压与优先级分布失败", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<TaskStatusSummaryModel> getTaskSummaryByTypeStatus(String date) {
        try {
            return analysisMapper.getTaskSummaryByTypeStatus(date);
        } catch (Exception e) {
            log.error("获取任务类型状态统计失败", e);
            return new ArrayList<>();
        }
    }



    @Override
    public List<DailyCompletionModel> getDailyCompletionDetails(String date, String groupBy,
                                                               String filterTaskType, String filterClientId) {
        try {
            String targetDate = StrXhUtil.isNotEmpty(date) ? date : LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

            if (StrXhUtil.isEmpty(groupBy)) {
                // 默认返回最细粒度数据
                return analysisMapper.getDailyCompletionDetails(targetDate, filterTaskType, filterClientId);
            }

            switch (groupBy.toLowerCase()) {
                case "hour":
                    return analysisMapper.getDailyCompletionByHour(targetDate, filterTaskType, filterClientId);
                case "tasktype":
                    return analysisMapper.getDailyCompletionByTaskType(targetDate, filterTaskType, filterClientId);
                case "client":
                    return analysisMapper.getDailyCompletionByClient(targetDate, filterTaskType, filterClientId);
                default:
                    return analysisMapper.getDailyCompletionDetails(targetDate, filterTaskType, filterClientId);
            }
        } catch (Exception e) {
            log.error("获取当日完成任务详细分析失败", e);
            return new ArrayList<>();
        }
    }
}
