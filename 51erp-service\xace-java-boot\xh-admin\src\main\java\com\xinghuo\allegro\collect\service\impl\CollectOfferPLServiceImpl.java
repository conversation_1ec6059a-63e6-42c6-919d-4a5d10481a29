package com.xinghuo.allegro.collect.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xinghuo.allegro.collect.dao.CollectOfferPLMapper;
import com.xinghuo.allegro.collect.entity.CollectOfferPLEntity;
import com.xinghuo.allegro.collect.service.CollectOfferPLService;
import com.xinghuo.common.base.service.BaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 报价录入，必须对数据进行侵权词管理
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class CollectOfferPLServiceImpl extends BaseServiceImpl<CollectOfferPLMapper, CollectOfferPLEntity> implements CollectOfferPLService {

    @Override
   public CollectOfferPLEntity  getByOfferId(String offerId){
        QueryWrapper<CollectOfferPLEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("offer_id", offerId);
        return getOne(queryWrapper);

    }

}
