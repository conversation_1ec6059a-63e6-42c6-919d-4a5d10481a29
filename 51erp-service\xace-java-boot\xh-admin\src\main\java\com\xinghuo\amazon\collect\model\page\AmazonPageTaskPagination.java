package com.xinghuo.amazon.collect.model.page;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xinghuo.common.base.model.Pagination;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Amazon页面任务分页查询参数
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
public class AmazonPageTaskPagination extends Pagination {
    
    @Schema(description = "查询key")
    private String[] selectKey;
    
    @Schema(description = "json")
    private String json;
    
    @Schema(description = "数据类型 0-当前页，1-全部数据")
    private String dataType;
    
    @Schema(description = "高级查询")
    private String superQueryJson;
    
    @Schema(description = "功能id")
    private String moduleId;
    
    @Schema(description = "菜单id")
    private String menuId;

    @Schema(description = "ASIN")
    private String entryAsin;

    @Schema(description = "URL")
    private String url;

    @Schema(description = "任务状态")
    private Integer status;

    @Schema(description = "重试次数")
    private Integer retryCount;

    @Schema(description = "列表页标题")
    private String listPageTitle;

    @Schema(description = "列表页价格")
    private BigDecimal listPagePrice;

    @Schema(description = "列表页原价")
    private BigDecimal listOrginPrice;

    @Schema(description = "列表页购买数量")
    private Integer listBoughtNum;

    @Schema(description = "列表页评分")
    private BigDecimal listPageRating;

    @Schema(description = "列表页评论数")
    private Integer listPageReviewCount;

    @Schema(description = "列表页主图URL")
    private String listPageMainImageUrl;

    @Schema(description = "列表页Prime信息")
    private String listPagePrimeInfo;

    @Schema(description = "是否赞助商品")
    private Boolean isSponsored;

    @Schema(description = "Amazon分类ID")
    private Long amazonCategoryId;

    @Schema(description = "列表任务ID")
    private Integer listTaskId;

    @Schema(description = "错误信息")
    private String errorMessage;

    @Schema(description = "创建时间-开始")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @Schema(description = "创建时间-结束")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;
}
