package com.xinghuo.allegro.collect.service;

import com.xinghuo.allegro.collect.entity.CollectOfferEntity;
import com.xinghuo.allegro.collect.entity.CollectOfferJsonEntity;
import com.xinghuo.allegro.collect.model.collect.WySyncForm;
import com.xinghuo.allegro.collect.model.collectOffer.CollectForm;
import com.xinghuo.allegro.collect.model.collectOffer.CollectOfferPagination;
import com.xinghuo.allegro.collect.model.collectOffer.CollectSettingModel;
import com.xinghuo.allegro.collect.model.collectOffer.SumModel;
import com.xinghuo.allegro.manage.entity.AllegroStoreEntity;
import com.xinghuo.common.base.service.BaseService;

import java.util.List;
import java.util.Set;

public interface CollectOfferService extends BaseService<CollectOfferEntity> {

    List<CollectOfferEntity> getList(CollectOfferPagination pagination);

    List<SumModel> getSumList(String beginTime, String endTime);
    int updateDealStatusClear();

    List<CollectOfferEntity> dealDataList();

    void syncWyData(WySyncForm form);

    List<CollectOfferEntity> noDetailDeal2List();

    void saveBatch(String type,List<CollectOfferEntity> list);

    boolean checkNewSellerIdExists(String sellerId);

    List<CollectOfferEntity> waitGets();
    CollectOfferEntity waitGet();

    void dealData(CollectOfferJsonEntity offerParseTmpEntity, CollectOfferEntity collectOfferEntity) ;

    List<CollectOfferEntity> noDetailDealList();

    CollectOfferEntity getByOfferId(String offerId);


    List<CollectOfferEntity> getListBySkuId(Integer skuId);
    List<CollectOfferEntity> getListBySkuIdSet(Set<Integer> skuId);

    List<CollectOfferEntity> blockedSellerOfferList(String tenantId);

    CollectOfferEntity  getByOfferNameAndImageUrl(String offerName,String imageUrl,String excludeId);

    void dealDataMember(CollectForm offerForm, CollectSettingModel settingModel ,String tenantId );
    CollectOfferEntity refactor(CollectForm offerForm);

    void dealDataOld(CollectOfferJsonEntity offerJsonEntity, CollectOfferEntity collectOfferEntity);

    void contractData(String sellerId);

    void contractData2(AllegroStoreEntity storeEntity, String offerId, String ship_rating_id, WySyncForm form);


    Long todayDelete();


    Long todayCollect();


    Long yesterdayCollect();


    void dealDataFh(CollectOfferJsonEntity offerJsonEntity, CollectOfferEntity collectOfferEntity);

    void updateBuyersQuantity( Integer skuId);

    /**
     * 根据卖家ID统计产品数量
     *
     * @param sellerId 卖家ID
     * @return 产品数量
     */
    long countBySellerId(String sellerId);


    /**
     * 根据卖家ID获取产品列表（分页）
     *
     * @param sellerId 卖家ID
     * @param page 页码
     * @param limit 每页条数
     * @return 产品列表
     */
    List<CollectOfferEntity> getProductsBySellerId(String sellerId, Integer page, Integer limit);
}
