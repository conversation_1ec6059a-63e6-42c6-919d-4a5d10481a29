package com.xinghuo.allegro.collect.model.collectOffer;

import com.fasterxml.jackson.databind.JsonNode;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class CollectForm {
    @Schema(description = "采集的链接")
    @NotBlank(message = "采集链接不能为空")
    private String offerLink ;

    @Schema(description = "采集的json数据")
    @NotNull(message = "采集的json数据不能为空")
    private JsonNode offerJson;
}
