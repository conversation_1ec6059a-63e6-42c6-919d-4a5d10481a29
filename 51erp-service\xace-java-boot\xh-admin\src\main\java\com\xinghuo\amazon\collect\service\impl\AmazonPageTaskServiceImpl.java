package com.xinghuo.amazon.collect.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xinghuo.amazon.collect.dao.AmazonPageTaskMapper;
import com.xinghuo.amazon.collect.entity.AmazonPageTaskEntity;
import com.xinghuo.amazon.collect.model.page.AmazonPageTaskPagination;
import com.xinghuo.amazon.collect.service.AmazonPageTaskService;
import com.xinghuo.amazon.util.AmazonConstant;
import com.xinghuo.common.base.service.BaseServiceImpl;
import com.xinghuo.common.util.core.StrXhUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * Amazon页面任务Service实现类
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Slf4j
@Service
public class AmazonPageTaskServiceImpl extends BaseServiceImpl<AmazonPageTaskMapper, AmazonPageTaskEntity> implements AmazonPageTaskService {

    @Override
    public List<AmazonPageTaskEntity> getList(AmazonPageTaskPagination pagination) {
        QueryWrapper<AmazonPageTaskEntity> queryWrapper = new QueryWrapper<>();
        
        // 添加查询条件
        if (StrXhUtil.isNotBlank(pagination.getEntryAsin())) {
            queryWrapper.lambda().like(AmazonPageTaskEntity::getEntryAsin, pagination.getEntryAsin());
        }
        
        if (pagination.getStatus() != null) {
            queryWrapper.lambda().eq(AmazonPageTaskEntity::getStatus, pagination.getStatus());
        }
        
        if (pagination.getListTaskId() != null) {
            queryWrapper.lambda().eq(AmazonPageTaskEntity::getListTaskId, pagination.getListTaskId());
        }
        
        if (pagination.getAmazonCategoryId() != null) {
            queryWrapper.lambda().eq(AmazonPageTaskEntity::getAmazonCategoryId, pagination.getAmazonCategoryId());
        }
        
        // 排序
        queryWrapper.lambda().orderByDesc(AmazonPageTaskEntity::getCreatedAt);
        
        // 分页查询
        if (pagination.getCurrentPage() != null && pagination.getPageSize() != null) {
            IPage<AmazonPageTaskEntity> page = new Page<>(pagination.getCurrentPage(), pagination.getPageSize());
            IPage<AmazonPageTaskEntity> result = this.page(page, queryWrapper);
            return result.getRecords();
        } else {
            return this.list(queryWrapper);
        }
    }

    @Override
    public AmazonPageTaskEntity waitGets(String clientId, String taskType, String platform) {
        // 创建查询条件，查询待处理的任务
        QueryWrapper<AmazonPageTaskEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(AmazonPageTaskEntity::getStatus, AmazonConstant.REQUEST_STATUS_INIT)
                .orderByDesc(AmazonPageTaskEntity::getCreatedAt)
                .last("limit 1");

        // 根据查询条件获取任务
        AmazonPageTaskEntity entity = this.getOne(queryWrapper);

        // 如果找到任务，更新状态为处理中
        if (entity != null) {
            entity.setStatus(AmazonConstant.REQUEST_STATUS_PROCESSING);
            entity.setUpdatedAt(new Date());
            this.updateById(entity);
            log.info("获取到待处理的Amazon页面任务: {}", entity.getId());
        } else {
            log.info("没有找到待处理的Amazon页面任务");
        }
        
        return entity;
    }

    @Override
    public int updateCollectTask(String taskId) {
        if (StrXhUtil.isBlank(taskId)) {
            return 0;
        }
        
        AmazonPageTaskEntity entity = this.getById(taskId);
        if (entity != null) {
            entity.setStatus(AmazonConstant.REQUEST_STATUS_FINISH);
            entity.setUpdatedAt(new Date());
            return this.updateById(entity) ? 1 : 0;
        }
        
        return 0;
    }

    @Override
    public boolean existTask(String asin) {
        if (StrXhUtil.isBlank(asin)) {
            return false;
        }
        
        QueryWrapper<AmazonPageTaskEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(AmazonPageTaskEntity::getEntryAsin, asin)
                .eq(AmazonPageTaskEntity::getStatus, AmazonConstant.REQUEST_STATUS_INIT);
        
        return this.count(queryWrapper) > 0;
    }

    @Override
    public long getPendingTaskCount(String taskType) {
        QueryWrapper<AmazonPageTaskEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(AmazonPageTaskEntity::getStatus, AmazonConstant.REQUEST_STATUS_INIT);
        
        return this.count(queryWrapper);
    }

    @Override
    public AmazonPageTaskEntity getByAsin(String asin) {
        if (StrXhUtil.isBlank(asin)) {
            return null;
        }
        
        QueryWrapper<AmazonPageTaskEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(AmazonPageTaskEntity::getEntryAsin, asin);
        
        return this.getOne(queryWrapper);
    }

    @Override
    public void saveBatch(String taskType, List<AmazonPageTaskEntity> entityList) {
        if (entityList == null || entityList.isEmpty()) {
            return;
        }
        
        // 设置创建时间和状态
        Date now = new Date();
        for (AmazonPageTaskEntity entity : entityList) {
            if (entity.getCreatedAt() == null) {
                entity.setCreatedAt(now);
            }
            if (entity.getUpdatedAt() == null) {
                entity.setUpdatedAt(now);
            }
            if (entity.getStatus() == null) {
                entity.setStatus(AmazonConstant.REQUEST_STATUS_INIT);
            }
        }
        
        super.saveBatch(entityList);
        log.info("批量保存Amazon页面任务: {} 条", entityList.size());
    }

    @Override
    public int updateTaskCompleted(Integer taskId, String detailData) {
        if (taskId == null) {
            return 0;
        }
        
        AmazonPageTaskEntity entity = this.getById(taskId);
        if (entity != null) {
            entity.setStatus(AmazonConstant.REQUEST_STATUS_FINISH);
            entity.setUpdatedAt(new Date());
            return this.updateById(entity) ? 1 : 0;
        }
        
        return 0;
    }

    @Override
    public int updateTaskFailed(Integer taskId, String errorMessage) {
        if (taskId == null) {
            return 0;
        }
        
        AmazonPageTaskEntity entity = this.getById(taskId);
        if (entity != null) {
            entity.setStatus(AmazonConstant.REQUEST_STATUS_FAILED);
            entity.setErrorMessage(errorMessage);
            entity.setUpdatedAt(new Date());
            return this.updateById(entity) ? 1 : 0;
        }
        
        return 0;
    }

    @Override
    public List<AmazonPageTaskEntity> getByListTaskId(Integer listTaskId) {
        if (listTaskId == null) {
            return null;
        }
        
        QueryWrapper<AmazonPageTaskEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(AmazonPageTaskEntity::getListTaskId, listTaskId)
                .orderByDesc(AmazonPageTaskEntity::getCreatedAt);
        
        return this.list(queryWrapper);
    }
}
