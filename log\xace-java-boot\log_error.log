[2025-06-15 17:24:16.043] [ERROR] [main] [c.baomidou.mybatisplus.core.MybatisConfiguration] [?] [?] - mapper[com.xinghuo.allegro.push.dao.PushOfferMapper.pushList1] is ignored, because it exists, maybe from xml file
[2025-06-15 17:24:25.252] [ERROR] [main] [org.springframework.boot.SpringApplication] [?] [?] - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'permissionAdminAspect': Unsatisfied dependency expressed through field 'organizeRelationService': Error creating bean with name 'organizeRelationServiceImpl': Unsatisfied dependency expressed through field 'roleService': Error creating bean with name 'roleServiceImpl': Unsatisfied dependency expressed through field 'userService': Error creating bean with name 'userServiceImpl': Unsatisfied dependency expressed through field 'userRelationService': Error creating bean with name 'userRelationServiceImpl': Unsatisfied dependency expressed through field 'synThirdInfoService': Error creating bean with name 'synThirdInfoServiceImpl': Lookup method resolution failed
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:787)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:767)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:508)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1419)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:326)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:962)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:624)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:334)
	at com.xinghuo.admin.XhAdminApplication.main(XhAdminApplication.java:41)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'organizeRelationServiceImpl': Unsatisfied dependency expressed through field 'roleService': Error creating bean with name 'roleServiceImpl': Unsatisfied dependency expressed through field 'userService': Error creating bean with name 'userServiceImpl': Unsatisfied dependency expressed through field 'userRelationService': Error creating bean with name 'userRelationServiceImpl': Unsatisfied dependency expressed through field 'synThirdInfoService': Error creating bean with name 'synThirdInfoServiceImpl': Lookup method resolution failed
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:787)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:767)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:508)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1419)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:326)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:784)
	... 18 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'roleServiceImpl': Unsatisfied dependency expressed through field 'userService': Error creating bean with name 'userServiceImpl': Unsatisfied dependency expressed through field 'userRelationService': Error creating bean with name 'userRelationServiceImpl': Unsatisfied dependency expressed through field 'synThirdInfoService': Error creating bean with name 'synThirdInfoServiceImpl': Lookup method resolution failed
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:787)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:767)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:508)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1419)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:326)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:784)
	... 32 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'userServiceImpl': Unsatisfied dependency expressed through field 'userRelationService': Error creating bean with name 'userRelationServiceImpl': Unsatisfied dependency expressed through field 'synThirdInfoService': Error creating bean with name 'synThirdInfoServiceImpl': Lookup method resolution failed
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:787)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:767)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:508)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1419)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:326)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:784)
	... 46 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'userRelationServiceImpl': Unsatisfied dependency expressed through field 'synThirdInfoService': Error creating bean with name 'synThirdInfoServiceImpl': Lookup method resolution failed
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:787)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:767)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:145)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:508)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1419)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:599)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:326)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:784)
	... 60 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'synThirdInfoServiceImpl': Lookup method resolution failed
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.checkLookupMethods(AutowiredAnnotationBeanPostProcessor.java:497)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.determineCandidateConstructors(AutowiredAnnotationBeanPostProcessor.java:367)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.determineConstructorsFromBeanPostProcessors(AbstractAutowireCapableBeanFactory.java:1294)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1189)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:326)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:784)
	... 74 common frames omitted
Caused by: java.lang.IllegalStateException: Failed to introspect Class [com.xinghuo.message.service.impl.SynThirdInfoServiceImpl] from ClassLoader [jdk.internal.loader.ClassLoaders$AppClassLoader@36baf30c]
	at org.springframework.util.ReflectionUtils.getDeclaredMethods(ReflectionUtils.java:483)
	at org.springframework.util.ReflectionUtils.doWithLocalMethods(ReflectionUtils.java:320)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.checkLookupMethods(AutowiredAnnotationBeanPostProcessor.java:475)
	... 87 common frames omitted
Caused by: java.lang.NoClassDefFoundError: com/xinghuo/message/util/SynThirdTotal
	at java.base/java.lang.Class.getDeclaredMethods0(Native Method)
	at java.base/java.lang.Class.privateGetDeclaredMethods(Class.java:3502)
	at java.base/java.lang.Class.getDeclaredMethods(Class.java:2601)
	at org.springframework.util.ReflectionUtils.getDeclaredMethods(ReflectionUtils.java:465)
	... 89 common frames omitted
Caused by: java.lang.ClassNotFoundException: com.xinghuo.message.util.SynThirdTotal
	at java.base/jdk.internal.loader.BuiltinClassLoader.loadClass(BuiltinClassLoader.java:641)
	at java.base/jdk.internal.loader.ClassLoaders$AppClassLoader.loadClass(ClassLoaders.java:188)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:521)
	... 93 common frames omitted
[2025-06-15 17:48:27.343] [ERROR] [main] [c.baomidou.mybatisplus.core.MybatisConfiguration] [?] [?] - mapper[com.xinghuo.allegro.push.dao.PushOfferMapper.pushList1] is ignored, because it exists, maybe from xml file
[2025-06-15 17:49:18.814] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 17:49:51.815] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 17:50:24.823] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 17:50:57.835] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 17:51:30.854] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 17:52:03.877] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 17:52:36.895] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 17:53:09.908] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 17:53:42.915] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 17:54:15.924] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 17:54:48.941] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 17:55:21.947] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 17:55:54.968] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 17:56:27.986] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 17:57:00.987] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 17:57:34.003] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 17:58:07.020] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 17:58:40.038] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 17:59:13.059] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 17:59:46.077] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:00:19.091] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:00:52.115] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:01:25.142] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:01:58.146] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:02:31.165] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:03:04.184] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:03:37.205] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:04:10.222] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:04:43.244] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:05:16.263] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:05:49.278] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:06:22.304] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:06:55.317] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:07:28.319] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:07:39.600] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:07:50.529] [ERROR] [main] [c.baomidou.mybatisplus.core.MybatisConfiguration] [?] [?] - mapper[com.xinghuo.allegro.push.dao.PushOfferMapper.pushList1] is ignored, because it exists, maybe from xml file
[2025-06-15 18:08:42.990] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:09:16.000] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:09:49.021] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:10:22.029] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:10:55.052] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:11:28.074] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:11:46.646] [ERROR] [http-nio-32000-exec-3] [com.xinghuo.exception.exception.ResultException] [?] [?] - 系统异常:No static resource api/amazon/listTask/waitGets.
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource api/amazon/listTask/waitGets.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at com.github.xiaoymin.knife4j.extend.filter.basic.JakartaServletSecurityBasicAuthFilter.doFilter(JakartaServletSecurityBasicAuthFilter.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at com.xinghuo.common.config.SecurityConfiguration$ClearThreadContextFilter.doFilter(SecurityConfiguration.java:121)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1623)
[2025-06-15 18:12:01.090] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:12:34.107] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:12:59.426] [ERROR] [http-nio-32000-exec-3] [com.xinghuo.exception.exception.ResultException] [?] [?] - 系统异常:No static resource favicon.ico.
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at com.github.xiaoymin.knife4j.extend.filter.basic.JakartaServletSecurityBasicAuthFilter.doFilter(JakartaServletSecurityBasicAuthFilter.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at com.xinghuo.common.config.SecurityConfiguration$ClearThreadContextFilter.doFilter(SecurityConfiguration.java:121)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1623)
[2025-06-15 18:13:07.133] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:13:40.147] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:13:59.436] [ERROR] [http-nio-32000-exec-3] [com.xinghuo.exception.exception.ResultException] [?] [?] - 系统异常:No static resource favicon.ico.
org.springframework.web.servlet.resource.NoResourceFoundException: No static resource favicon.ico.
	at org.springframework.web.servlet.resource.ResourceHttpRequestHandler.handleRequest(ResourceHttpRequestHandler.java:585)
	at org.springframework.web.servlet.mvc.HttpRequestHandlerAdapter.handle(HttpRequestHandlerAdapter.java:52)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:564)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at com.github.xiaoymin.knife4j.extend.filter.basic.JakartaServletSecurityBasicAuthFilter.doFilter(JakartaServletSecurityBasicAuthFilter.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at com.xinghuo.common.config.SecurityConfiguration$ClearThreadContextFilter.doFilter(SecurityConfiguration.java:121)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1623)
[2025-06-15 18:14:13.160] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:14:46.178] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:15:19.201] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:15:52.215] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:16:25.233] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:16:58.247] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:17:31.256] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:18:04.268] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:18:37.278] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:19:10.282] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:19:43.295] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:20:16.309] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:20:49.326] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:21:22.331] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:21:55.347] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:22:28.354] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:23:01.366] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:23:34.378] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:24:07.398] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:24:40.410] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:24:46.061] [ERROR] [http-nio-32000-exec-6] [com.xinghuo.exception.exception.ResultException] [?] [?] - 系统异常:Request method 'POST' is not supported
org.springframework.web.HttpRequestMethodNotSupportedException: Request method 'POST' is not supported
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.handleNoMatch(RequestMappingInfoHandlerMapping.java:265)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lookupHandlerMethod(AbstractHandlerMethodMapping.java:441)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.getHandlerInternal(AbstractHandlerMethodMapping.java:382)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:126)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:68)
	at org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(AbstractHandlerMapping.java:507)
	at org.springframework.web.servlet.DispatcherServlet.getHandler(DispatcherServlet.java:1283)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1065)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at com.github.xiaoymin.knife4j.extend.filter.basic.JakartaServletSecurityBasicAuthFilter.doFilter(JakartaServletSecurityBasicAuthFilter.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at com.xinghuo.common.config.SecurityConfiguration$ClearThreadContextFilter.doFilter(SecurityConfiguration.java:121)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1623)
[2025-06-15 18:24:59.709] [ERROR] [http-nio-32000-exec-8] [com.xinghuo.exception.exception.ResultException] [?] [?] - 系统异常:Request method 'POST' is not supported
org.springframework.web.HttpRequestMethodNotSupportedException: Request method 'POST' is not supported
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.handleNoMatch(RequestMappingInfoHandlerMapping.java:265)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lookupHandlerMethod(AbstractHandlerMethodMapping.java:441)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.getHandlerInternal(AbstractHandlerMethodMapping.java:382)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:126)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:68)
	at org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(AbstractHandlerMapping.java:507)
	at org.springframework.web.servlet.DispatcherServlet.getHandler(DispatcherServlet.java:1283)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1065)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at com.github.xiaoymin.knife4j.extend.filter.basic.JakartaServletSecurityBasicAuthFilter.doFilter(JakartaServletSecurityBasicAuthFilter.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at com.xinghuo.common.config.SecurityConfiguration$ClearThreadContextFilter.doFilter(SecurityConfiguration.java:121)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1623)
[2025-06-15 18:25:13.426] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:25:20.222] [ERROR] [http-nio-32000-exec-2] [com.xinghuo.exception.exception.ResultException] [?] [?] - 系统异常:Request method 'POST' is not supported
org.springframework.web.HttpRequestMethodNotSupportedException: Request method 'POST' is not supported
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.handleNoMatch(RequestMappingInfoHandlerMapping.java:265)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lookupHandlerMethod(AbstractHandlerMethodMapping.java:441)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.getHandlerInternal(AbstractHandlerMethodMapping.java:382)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:126)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:68)
	at org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(AbstractHandlerMapping.java:507)
	at org.springframework.web.servlet.DispatcherServlet.getHandler(DispatcherServlet.java:1283)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1065)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at com.github.xiaoymin.knife4j.extend.filter.basic.JakartaServletSecurityBasicAuthFilter.doFilter(JakartaServletSecurityBasicAuthFilter.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at com.xinghuo.common.config.SecurityConfiguration$ClearThreadContextFilter.doFilter(SecurityConfiguration.java:121)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1623)
[2025-06-15 18:25:40.201] [ERROR] [http-nio-32000-exec-5] [com.xinghuo.exception.exception.ResultException] [?] [?] - 系统异常:Request method 'POST' is not supported
org.springframework.web.HttpRequestMethodNotSupportedException: Request method 'POST' is not supported
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.handleNoMatch(RequestMappingInfoHandlerMapping.java:265)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.lookupHandlerMethod(AbstractHandlerMethodMapping.java:441)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodMapping.getHandlerInternal(AbstractHandlerMethodMapping.java:382)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:126)
	at org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping.getHandlerInternal(RequestMappingInfoHandlerMapping.java:68)
	at org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(AbstractHandlerMapping.java:507)
	at org.springframework.web.servlet.DispatcherServlet.getHandler(DispatcherServlet.java:1283)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1065)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at com.github.xiaoymin.knife4j.extend.filter.basic.JakartaServletSecurityBasicAuthFilter.doFilter(JakartaServletSecurityBasicAuthFilter.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at com.xinghuo.common.config.SecurityConfiguration$ClearThreadContextFilter.doFilter(SecurityConfiguration.java:121)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1623)
[2025-06-15 18:25:46.427] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:26:19.428] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:26:52.430] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:27:25.432] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:27:58.434] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:28:31.436] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:29:04.436] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:29:37.439] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:30:10.441] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:30:43.442] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:31:16.444] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:31:49.445] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:32:22.457] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:32:55.464] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:33:28.475] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:34:01.492] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:34:34.511] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:35:07.527] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:35:40.536] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:36:13.559] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:36:46.576] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:37:19.589] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:37:52.606] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:38:25.616] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:38:58.635] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:39:31.656] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:40:04.683] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:40:37.696] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:40:57.333] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:41:08.569] [ERROR] [main] [c.baomidou.mybatisplus.core.MybatisConfiguration] [?] [?] - mapper[com.xinghuo.allegro.push.dao.PushOfferMapper.pushList1] is ignored, because it exists, maybe from xml file
[2025-06-15 18:42:00.745] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:42:33.762] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:45:04.008] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:49:35.462] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:50:08.471] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:50:41.475] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:51:14.476] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:51:47.477] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:52:20.481] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:52:53.499] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:53:26.518] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:53:59.529] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:54:32.558] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:55:05.575] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:55:38.593] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
[2025-06-15 18:56:11.608] [ERROR] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.util.XxlJobRemotingUtil] [?] [?] - 调度服务器连接失败：Connect timed out
