package com.xinghuo.allegro.collect.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("zz_ups")
public class UpsEntity  {

    @TableId(value = "f_id")
    private String id;

    private String trackNumber;

    private String sendDate;

    private String sendCity;

    private String dstCity;

    private String status;

    private Integer dealStatus;
}
