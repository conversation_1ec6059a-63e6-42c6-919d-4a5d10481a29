<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinghuo.allegro.data.dao.AllegroSellerMapper">

    <update id="updateSellerOfferCount" parameterType="map">
        UPDATE zz_allegro_seller
        SET offer_all_num = (SELECT COUNT(*) FROM zz_collect_offer WHERE seller_id = #{sellerId}),
            total_sales_num = (SELECT COUNT(*) FROM zz_collect_offer WHERE seller_id = #{sellerId} AND buyers_quantity > 0),
            total_num = #{totalNum}
        WHERE seller_id = #{sellerId}
    </update>

    <select id="checkBlockSeller">
        {call checkBlockSeller(#{tenantId})}
    </select>
</mapper>