package com.xinghuo.amazon.collect.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xinghuo.amazon.collect.dao.AmazonListTaskMapper;
import com.xinghuo.amazon.collect.entity.AmazonListTaskEntity;
import com.xinghuo.amazon.collect.model.list.AmazonListTaskPagination;
import com.xinghuo.amazon.collect.service.AmazonListTaskService;
import com.xinghuo.amazon.util.AmazonConstant;
import com.xinghuo.common.base.service.BaseServiceImpl;
import com.xinghuo.common.util.core.StrXhUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * Amazon列表任务Service实现类
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Slf4j
@Service
public class AmazonListTaskServiceImpl extends BaseServiceImpl<AmazonListTaskMapper, AmazonListTaskEntity> implements AmazonListTaskService {

    @Override
    public List<AmazonListTaskEntity> getList(AmazonListTaskPagination pagination) {
        QueryWrapper<AmazonListTaskEntity> queryWrapper = new QueryWrapper<>();
        
        // 添加查询条件
        if (StrXhUtil.isNotBlank(pagination.getUrl())) {
            queryWrapper.lambda().like(AmazonListTaskEntity::getUrl, pagination.getUrl());
        }
        
        if (pagination.getStatus() != null) {
            queryWrapper.lambda().eq(AmazonListTaskEntity::getStatus, pagination.getStatus());
        }
        
        if (pagination.getSourceCategoryId() != null) {
            queryWrapper.lambda().eq(AmazonListTaskEntity::getSourceCategoryId, pagination.getSourceCategoryId());
        }
        
        // 排序
        queryWrapper.lambda().orderByDesc(AmazonListTaskEntity::getCreatedAt);
        
        // 分页查询
        if (pagination.getCurrentPage() != null && pagination.getPageSize() != null) {
            IPage<AmazonListTaskEntity> page = new Page<>(pagination.getCurrentPage(), pagination.getPageSize());
            IPage<AmazonListTaskEntity> result = this.page(page, queryWrapper);
            return result.getRecords();
        } else {
            return this.list(queryWrapper);
        }
    }

    @Override
    public AmazonListTaskEntity waitGets(String clientId, String taskType, String platform) {
        // 创建查询条件，查询待处理的任务
        QueryWrapper<AmazonListTaskEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(AmazonListTaskEntity::getStatus, AmazonConstant.REQUEST_STATUS_INIT)
                .orderByDesc(AmazonListTaskEntity::getCreatedAt)
                .last("limit 1");

        // 根据查询条件获取任务
        AmazonListTaskEntity entity = this.getOne(queryWrapper);

        // 如果找到任务，更新状态为处理中
        if (entity != null) {
            entity.setStatus(AmazonConstant.REQUEST_STATUS_PROCESSING);
            entity.setUpdatedAt(new Date());
            this.updateById(entity);
            log.info("获取到待处理的Amazon列表任务: {}", entity.getId());
        } else {
            log.info("没有找到待处理的Amazon列表任务");
        }
        
        return entity;
    }

    @Override
    public int updateCollectTask(String taskId) {
        if (StrXhUtil.isBlank(taskId)) {
            return 0;
        }
        
        AmazonListTaskEntity entity = this.getById(taskId);
        if (entity != null) {
            entity.setStatus(AmazonConstant.REQUEST_STATUS_FINISH);
            entity.setUpdatedAt(new Date());
            return this.updateById(entity) ? 1 : 0;
        }
        
        return 0;
    }

    @Override
    public boolean existTask(String categoryId) {
        if (StrXhUtil.isBlank(categoryId)) {
            return false;
        }
        
        QueryWrapper<AmazonListTaskEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(AmazonListTaskEntity::getSourceCategoryId, categoryId)
                .eq(AmazonListTaskEntity::getStatus, AmazonConstant.REQUEST_STATUS_INIT);
        
        return this.count(queryWrapper) > 0;
    }

    @Override
    public long getPendingTaskCount(String taskType) {
        QueryWrapper<AmazonListTaskEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(AmazonListTaskEntity::getStatus, AmazonConstant.REQUEST_STATUS_INIT);
        
        return this.count(queryWrapper);
    }

    @Override
    public int updateTaskCompleted(Integer taskId, Integer crawledPages, Integer totalProducts) {
        if (taskId == null) {
            return 0;
        }
        
        AmazonListTaskEntity entity = this.getById(taskId);
        if (entity != null) {
            entity.setStatus(AmazonConstant.REQUEST_STATUS_FINISH);
            entity.setCrawledPages(crawledPages);
            entity.setUpdatedAt(new Date());
            return this.updateById(entity) ? 1 : 0;
        }
        
        return 0;
    }

    @Override
    public int updateTaskFailed(Integer taskId, String errorMessage) {
        if (taskId == null) {
            return 0;
        }
        
        AmazonListTaskEntity entity = this.getById(taskId);
        if (entity != null) {
            entity.setStatus(AmazonConstant.REQUEST_STATUS_FAILED);
            entity.setErrorMessage(errorMessage);
            entity.setUpdatedAt(new Date());
            return this.updateById(entity) ? 1 : 0;
        }
        
        return 0;
    }
}
