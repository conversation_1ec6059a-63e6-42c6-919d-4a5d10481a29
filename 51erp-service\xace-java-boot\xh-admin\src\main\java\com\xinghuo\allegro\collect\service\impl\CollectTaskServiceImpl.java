package com.xinghuo.allegro.collect.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xinghuo.allegro.collect.dao.CollectTaskMapper;
import com.xinghuo.allegro.collect.entity.CollectTaskEntity;
import com.xinghuo.allegro.collect.model.collect.CollectTaskPagination;
import com.xinghuo.allegro.collect.service.CollectTaskService;
import com.xinghuo.allegro.util.AllegroConstant;
import com.xinghuo.common.base.service.BaseServiceImpl;
import com.xinghuo.common.util.core.StrXhUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 报价录入，必须对数据进行侵权词管理
 * <AUTHOR>
 */
@Slf4j
@Service
public class CollectTaskServiceImpl extends BaseServiceImpl<CollectTaskMapper, CollectTaskEntity> implements CollectTaskService {

    @Override
    public List<CollectTaskEntity> getList(CollectTaskPagination pagination) {
        QueryWrapper<CollectTaskEntity> queryWrapper = new QueryWrapper<>();

        // 关键词搜索
        if (StrXhUtil.isNotEmpty(pagination.getKeyword())) {
            queryWrapper.and(wrapper -> {
                wrapper.or().like("link", pagination.getKeyword())
                       .or().like("client_id", pagination.getKeyword())
                       .or().like("category_id", pagination.getKeyword());
            });
        }

        // 任务类型
        if (StrXhUtil.isNotEmpty(pagination.getTaskType())) {
            queryWrapper.lambda().eq(CollectTaskEntity::getTaskType, pagination.getTaskType());
        }

        // 采集状态
        if (pagination.getStatus() != null) {
            queryWrapper.lambda().eq(CollectTaskEntity::getStatus, pagination.getStatus());
        }

        // 采集客户端
        if (StrXhUtil.isNotEmpty(pagination.getClientId())) {
            queryWrapper.lambda().eq(CollectTaskEntity::getClientId, pagination.getClientId());
        }

        // 平台
        if (StrXhUtil.isNotEmpty(pagination.getPlatform())) {
            queryWrapper.lambda().eq(CollectTaskEntity::getPlatform, pagination.getPlatform());
        }

        // 时间范围查询
        if (StrXhUtil.isNotEmpty(pagination.getDateType()) && pagination.getStartTime() != null) {
            queryWrapper.ge(pagination.getDateType(), pagination.getStartTime());
        }
        if (StrXhUtil.isNotEmpty(pagination.getDateType()) && pagination.getEndTime() != null) {
            queryWrapper.le(pagination.getDateType(), pagination.getEndTime());
        }

        // 排序
        queryWrapper.orderByDesc("f_created_at");



        Page<CollectTaskEntity> page = new Page<>(pagination.getCurrentPage(), pagination.getPageSize());
        IPage<CollectTaskEntity> userIpage = this.page(page, queryWrapper);
        return pagination.setDataList(userIpage.getRecords(), userIpage.getTotal());

    }

    /**

     */
    @Override
    public CollectTaskEntity waitGets(String clientId,String taskType,String platform) {
        // 创建查询条件，指定查询状态的采集优惠信息，并按照购买数量降序排列，限制查询一条数据。
        QueryWrapper<CollectTaskEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
//                .select(CollectTaskEntity::getId, CollectTaskEntity::getLink)
                .eq(CollectTaskEntity::getStatus, AllegroConstant.REQUEST_STATUS_INIT);
        queryWrapper.lambda().eq(CollectTaskEntity::getPlatform, platform.toUpperCase());
        // 根据clientId的前缀来设置taskType的条件
        if (clientId.startsWith("S-")) {
            queryWrapper.lambda().eq(CollectTaskEntity::getTaskType, "S");
        } else if (clientId.startsWith("C-")) {
            queryWrapper.lambda().eq(CollectTaskEntity::getTaskType, "C");
        }else if (clientId.startsWith("P-")) {
            queryWrapper.lambda().eq(CollectTaskEntity::getTaskType, "P");
        } else if (clientId.startsWith("CNEW-")) {
            queryWrapper.lambda().eq(CollectTaskEntity::getTaskType, "CNEW");
        } else {
            // 如果clientId不以特定前缀开头，使用传入的taskType参数
            if (StrXhUtil.isNotEmpty(taskType)) {
                queryWrapper.lambda().eq(CollectTaskEntity::getTaskType, taskType);
            }
        }
        queryWrapper.lambda().orderByDesc(CollectTaskEntity::getPriority)
                .last("limit 1");

//        queryWrapper.lambda().last("limit 1");

        // 根据查询条件获取采集优惠信息列表
        CollectTaskEntity entity = this.getOne(queryWrapper);

        // 遍历列表中的每个采集优惠信息，更新其请求状态和请求次数，并保存更新。
        if(entity!=null) {
            entity.setStatus(AllegroConstant.REQUEST_STATUS_PROCESSING);
            entity.setRequestTime(new Date());
            entity.setClientId(clientId);
            this.updateById(entity);
        }
        else{
            if(platform.equals("ALLEGRO")){
                log.info("目标类型采集任务已经OK,接收随机的任务");
                entity = getNextAvailableTask(clientId);
            }else{
                return null;
            }

        }
        return entity;
    }


    private
    CollectTaskEntity getNextAvailableTask(String clientId) {
        // 创建查询条件，查询所有未处理或已完成的任务
        QueryWrapper<CollectTaskEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(CollectTaskEntity::getStatus, AllegroConstant.REQUEST_STATUS_INIT)
                .orderByDesc(CollectTaskEntity::getPriority)
                .last("limit 1");

        // 获取下一个可用的任务
        CollectTaskEntity nextTask = this.getOne(queryWrapper);

        if (nextTask != null) {
            // 如果找到任务，更新任务状态为正在处理，并设置clientId
            nextTask.setStatus(AllegroConstant.REQUEST_STATUS_PROCESSING);
            nextTask.setRequestTime(new Date());
            nextTask.setClientId(clientId);
            this.updateById(nextTask);
        }

        return nextTask;
    }


    @Override
   public int  updateCollectTask(String taskId){
        return this.baseMapper.updateCollectTask(taskId);
   }

   @Override
   public long getPendingTaskCount(String taskType){
        QueryWrapper<CollectTaskEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(CollectTaskEntity::getStatus, AllegroConstant.REQUEST_STATUS_INIT).eq(CollectTaskEntity::getTaskType, taskType);
        return this.count(queryWrapper);
    }



    public boolean existTask(String productId){
        QueryWrapper<CollectTaskEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(CollectTaskEntity::getCategoryId, productId).eq(CollectTaskEntity::getStatus, AllegroConstant.REQUEST_STATUS_INIT);
        return this.count(queryWrapper) > 0;
    }

}
