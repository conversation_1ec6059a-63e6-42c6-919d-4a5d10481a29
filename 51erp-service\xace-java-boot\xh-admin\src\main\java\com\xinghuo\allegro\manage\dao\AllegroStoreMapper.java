package com.xinghuo.allegro.manage.dao;

import com.xinghuo.allegro.manage.entity.AllegroStoreEntity;
import com.xinghuo.common.base.dao.XHBaseMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;

/**
 * 店铺token mapper
 *
 * <AUTHOR>
 * @date 2024-06-02
 */
public interface AllegroStoreMapper extends XHBaseMapper<AllegroStoreEntity> {

    void syncStoreOfferAndOrder();

    void checkVioOfferToBatch();

    void genTodayStoreInfo();

    void updateOrderCny(@Param("sellerId") String sellerId);
    void updateOrderItemCny(@Param("sellerId") String sellerId);
    void updateOrderItemCnyTenant(@Param("sellerId") String sellerId);
    void updateStoreOrders(@Param("sellerId") String sellerId);
    void updateStoreTodayOrders(@Param("sellerId") String sellerId);

    //20241112 删除店铺数据
    @Delete("DELETE FROM zz_allegro_offer WHERE seller_id = #{sellerId}")
    void deleteAllegroOffer(@Param("sellerId") String sellerId);

    @Delete("DELETE FROM zz_push_new_offer WHERE seller_id = #{sellerId}")
    void deleteAllegroPushOffer(@Param("sellerId") String sellerId);
    @Delete("DELETE FROM zz_allegro_store WHERE seller_id = #{sellerId}")
    void deleteAllegroStore(@Param("sellerId") String sellerId);

    @Delete("DELETE FROM zz_allegro_store_config WHERE seller_id = #{sellerId}")
    void deleteAllegroStoreConfig(@Param("sellerId") String sellerId);

    // 删除订单相关信息
    @Delete("DELETE FROM zz_allegro_order_buyer WHERE order_id IN (SELECT order_id FROM zz_allegro_order WHERE seller_id = #{sellerId})")
    void deleteAllegroOrderBuyer(@Param("sellerId") String sellerId);

    @Delete("DELETE FROM zz_allegro_order WHERE seller_id = #{sellerId}")
    void deleteAllegroOrder(@Param("sellerId") String sellerId);

    @Delete("DELETE FROM zz_allegro_order_item WHERE seller_id = #{sellerId}")
    void deleteAllegroOrderItem(@Param("sellerId") String sellerId);

    // 删除消息相关信息
    @Delete("DELETE FROM zz_msg_message WHERE thread_id IN (SELECT f_id FROM zz_msg_dispute WHERE seller_id = #{sellerId})")
    void deleteMsgMessage(@Param("sellerId") String sellerId);

    @Delete("DELETE FROM zz_msg_thread WHERE seller_id = #{sellerId}")
    void deleteMsgThread(@Param("sellerId") String sellerId);

    @Delete("DELETE FROM zz_msg_rating WHERE seller_id = #{sellerId}")
    void deleteMsgRating(@Param("sellerId") String sellerId);

    // 删除争议相关信息
    @Delete("DELETE FROM zz_msg_dispute_message WHERE dispute_id IN (SELECT f_id FROM zz_msg_dispute WHERE seller_id = #{sellerId})")
    void deleteMsgDisputeMessage(@Param("sellerId") String sellerId);

    @Delete("DELETE FROM zz_msg_dispute WHERE seller_id = #{sellerId}")
    void deleteMsgDispute(@Param("sellerId") String sellerId);

}

