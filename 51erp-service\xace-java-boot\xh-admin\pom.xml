<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.xinghuo.51erp</groupId>
        <artifactId>51erp-boot</artifactId>
        <version>1.0.0</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <groupId>com.xinghuo.51erp</groupId>
    <artifactId>xh-admin</artifactId>
    <packaging>jar</packaging>
    <version>2.1.0</version>
    <!--打包WAR包删除注释-->
    <!--<packaging>war</packaging>-->

    <properties>
        <!--依赖输出目录-->
        <output.dependence.file.path>lib/</output.dependence.file.path>
        <!--jar输出目录-->
        <output.jar.file.path>/</output.jar.file.path>
        <!--配置文件输出目录-->
        <output.resource.file.path>config/</output.resource.file.path>
    </properties>



    <dependencies>
<!--
        <dependency>
            <groupId>org.bytedeco</groupId>
            <artifactId>javacv-platform</artifactId>
            <version>1.5.9</version>
        </dependency>-->

        <dependency>
            <groupId>com.alibaba.ocean</groupId>
            <artifactId>ocean-rawsdk</artifactId>
            <version>1.0</version>
        </dependency>

        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>1.15.1</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.xinghuo.xace</groupId>
            <artifactId>xh-file</artifactId>
            <version>${xace.version}</version>
        </dependency>

        <dependency>
            <groupId>com.xinghuo.xace</groupId>
            <artifactId>xh-ext-demo</artifactId>
            <version>${xace.version}</version>
        </dependency>

        <dependency>
            <groupId>com.xinghuo.xace</groupId>
            <artifactId>xh-system</artifactId>
            <version>${xace.version}</version>
        </dependency>
        <!---->
        <!-- -->
        <dependency>
            <groupId>com.xinghuo.xace</groupId>
            <artifactId>xh-ext-scheduletask</artifactId>
            <version>${xace.version}</version>
        </dependency>

        <dependency>
            <groupId>com.xinghuo.xace</groupId>
            <artifactId>xh-visualdev-base</artifactId>
            <version>${xace.version}</version>
        </dependency>
        <dependency>
            <groupId>com.xinghuo.xace</groupId>
            <artifactId>xh-visualdev-generater</artifactId>
            <version>${xace.version}</version>
        </dependency>
        <dependency>
            <groupId>com.xinghuo.xace</groupId>
            <artifactId>xh-visualdev-portal</artifactId>
            <version>${xace.version}</version>
        </dependency>
        <!--
        <dependency>
            <groupId>com.xinghuo</groupId>
            <artifactId>xh-visualdata</artifactId>
            <version>${project.version}</version>
        </dependency>
        -->
        <dependency>
            <groupId>com.xinghuo.xace</groupId>
            <artifactId>xh-workflow-engine</artifactId>
            <version>${xace.version}</version>
        </dependency>
        <dependency>
            <groupId>com.xinghuo.xace</groupId>
            <artifactId>xh-oauth-controller</artifactId>
            <version>${xace.version}</version>
        </dependency>
        <dependency>
            <groupId>com.xinghuo.xace</groupId>
            <artifactId>xh-ext-app</artifactId>
            <version>${xace.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zt</groupId>
            <artifactId>zt-openapi-sdk</artifactId>
            <version>1.0.0</version>
        </dependency>
        <!--
                <dependency>
                    <groupId>com.xinghuo.xace</groupId>
                    <artifactId>xh-ext-magicapi</artifactId>
                    <version>${xace.version}</version>
                </dependency>
        -->


        <!--引入Knife4j的官方start包,该指南选择Spring Boot版本<3.0,开发者需要注意-->
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-openapi3-spring-boot-starter</artifactId>
            <version>${knife4j.version}</version>
        </dependency>
        <dependency>
            <groupId>org.sejda.imageio</groupId>
            <artifactId>webp-imageio</artifactId>
            <version>0.1.6</version>
        </dependency>

        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
            <version>2.0.24</version>
        </dependency>

        <dependency>
            <groupId>com.qcloud</groupId>
            <artifactId>cos_api</artifactId>
            <version>5.6.137</version>
        </dependency>


    </dependencies>

    <build>
        <finalName>xh-admin-${project.version}</finalName>

        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.*</include>
                </includes>
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
                <filtering>false</filtering>
            </resource>
        </resources>

        <plugins>
            <!-- 打JAR包，不包含依赖文件；显式剔除配置文件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <!--${env.LEARN_HOME}为项目配置的环境变量，下同-->
                    <outputDirectory>${project.build.directory}/${output.jar.file.path}</outputDirectory>
                    <!-- 将配置文件排除在jar包 -->
                    <excludes>
                        <exclude>*.yml</exclude>
                    </excludes>
                    <archive>
                        <!-- 生成的jar中，包含pom.xml和pom.properties这两个文件 -->
                        <addMavenDescriptor>true</addMavenDescriptor>
                        <!-- 生成MANIFEST.MF的设置 -->
                        <manifest>
                            <!--这个属性特别关键，如果没有这个属性，有时候我们引用的包maven库 下面可能会有多个包，并且只有一个是正确的，
                            其余的可能是带时间戳的，此时会在classpath下面把那个带时间戳的给添加上去，然后我们 在依赖打包的时候，
                            打的是正确的，所以两头会对不上，报错。 -->
                            <useUniqueVersions>false</useUniqueVersions>
                            <!-- 为依赖包添加路径, 这些路径会写在MANIFEST文件的Class-Path下 -->
                            <addClasspath>true</addClasspath>
                            <!-- MANIFEST.MF 中 Class-Path 各个依赖加入前缀 -->
                            <!--这个jar所依赖的jar包添加classPath的时候的前缀，需要 下面maven-dependency-plugin插件补充-->
                            <!--一定要找对目录，否则jar找不到依赖lib，前边加../是因为jar在bin下，而bin与lib是平级目录-->
                            <classpathPrefix>./${output.dependence.file.path}</classpathPrefix>
                            <!--指定jar启动入口类 -->
                            <mainClass>com.xinghuo.admin.XhAdminApplication</mainClass>
                        </manifest>
                        <manifestEntries>
                            <!-- 假如这个项目可能要引入一些外部资源，但是你打包的时候并不想把 这些资源文件打进包里面，这个时候你必须在
                            这边额外指定一些这些资源文件的路径,假如你的pom文件里面配置了 <scope>system</scope>,就是你依赖是你本地的
                            资源，这个时候使用这个插件，classPath里面是不会添加，所以你得手动把这个依赖添加进这个地方 -->
                            <!--MANIFEST.MF 中 Class-Path 加入自定义路径，多个路径用空格隔开 -->
                            <!--此处resources文件夹的内容，需要maven-resources-plugin插件补充上-->
                            <Class-Path>./${output.resource.file.path}</Class-Path>
                        </manifestEntries>
                    </archive>
                </configuration>
            </plugin>

            <!-- 复制依赖的jar包到指定的文件夹里 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-dependencies</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <!-- 拷贝项目依赖包到指定目录下 -->
                            <outputDirectory>${project.build.directory}/${output.dependence.file.path}</outputDirectory>
                            <!-- 是否排除间接依赖，间接依赖也要拷贝 -->
                            <excludeTransitive>false</excludeTransitive>
                            <!-- 是否带上版本号 -->
                            <stripVersion>false</stripVersion>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <!-- 用于复制指定的文件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.0.2</version>
            </plugin>

            <!-- exec插件用于运行Java程序 -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>exec-maven-plugin</artifactId>
                <version>3.1.0</version>
                <configuration>
                    <mainClass>com.xinghuo.amazon.test.AmazonHtmlParseTest</mainClass>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
