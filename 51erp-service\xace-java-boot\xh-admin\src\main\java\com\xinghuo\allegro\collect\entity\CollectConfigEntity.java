package com.xinghuo.allegro.collect.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.BaseEntityV2;
import lombok.Data;

@Data
@TableName("zz_collect_config")
public class CollectConfigEntity  extends BaseEntityV2.CUBaseEntityV2 {

    @TableField("config")
    private String config;

    @TableField("config_type")
    private String configType;

}
