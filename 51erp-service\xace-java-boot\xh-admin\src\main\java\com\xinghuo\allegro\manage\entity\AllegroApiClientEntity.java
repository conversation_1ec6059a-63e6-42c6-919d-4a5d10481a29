package com.xinghuo.allegro.manage.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("zz_allegro_api_client")
public class AllegroApiClientEntity {

    @TableId
    private String id;

    private String name;

    private String clientId;

    private String clientSecert;

    private String redirectUrl;

    private String allegroUrl;

    private String tokenUrl;

    private Boolean isDefault;

    private int status;

    private Date createTime;

    @TableField("f_tenantid")
    private String tenantId;
}
