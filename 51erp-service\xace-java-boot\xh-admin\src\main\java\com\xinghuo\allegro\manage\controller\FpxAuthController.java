package com.xinghuo.allegro.manage.controller;


import com.xinghuo.allegro.manage.model.fourpx.AccessTokenResponse;
import com.xinghuo.allegro.order.entity.ExpressAuthorizeEntity;
import com.xinghuo.allegro.order.entity.ExpressEntity;
import com.xinghuo.allegro.order.model.express.ExpressConstant;
import com.xinghuo.allegro.order.service.ExpressService;
import com.xinghuo.common.annotation.NoDataSourceBind;
import com.xinghuo.common.util.core.DateXhUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.niceapi.fpx.Fpx4Client;
import com.xinghuo.permission.entity.UserEntity;
import com.xinghuo.permission.service.UserService;
import com.xinghuo.tenant.entity.TenantEntity;
import com.xinghuo.tenant.service.TenantService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

@Slf4j
@Controller
@RequestMapping("/api/allegro/auth/fourpx")
@Tag(name = "递四方授权", description = "递四方授权")
public class FpxAuthController {


    @Autowired
    private UserService userService;
    @Resource
    private TenantService tenantService;

    private static final String FPX_CLIENT_ID = "ebfb1720-2d3c-4db4-a693-413d8dd92321";
    private static final String FPX_CLIENT_SECRET =    "9579c5ef-3142-4b12-8041-1e5ea2b4c515";

//    private static final String FOURPX_REDIRECT_URI = "https://51erp.store//api/allegro/auth/fourpx/callback";
    private static final String FPX_REDIRECT_URI = "http://127.0.0.1:32000/api/allegro/auth/fourpx/callback";
    @Autowired
    private ExpressService expressService;


    @Operation(summary = "递四方授权")
    @GetMapping("/authorize")
    @Parameters({
            @Parameter(name = "name", description = "名称", required = true),
            @Parameter(name = "id", description = "现有ID", required = true)
    })
    @NoDataSourceBind
    public ResponseEntity<String> authorize(@RequestParam String name, @RequestParam String id, @RequestParam String userId) {
        try {
            if (StrXhUtil.isBlank(name)) {
                throw new IllegalArgumentException("name 不能是空的。");
            }

            UserEntity userEntity = userService.getInfo(userId);

            //根据userId 获取到用户的租户信息
            if (userEntity == null) {
                throw new IllegalArgumentException("用户不存在");
            }
            if (StrXhUtil.isNotBlank(userEntity.getTenantId())) {
                TenantEntity tenantEntity = tenantService.getInfoByEnCode(userEntity.getTenantId());
                if (tenantEntity == null) {
                    throw new IllegalArgumentException("租户不存在");
                }
            }

            // 生成state参数，用于防止CSRF攻击和确认回调时的请求来源
            String state = String.format("state_for_%s_%d_%s",  URLEncoder.encode(name, StandardCharsets.UTF_8), id, userId);
            if (state.length() > 512) {
                state = state.substring(0, 512);
            }
            String encodedRedirectUri = URLEncoder.encode(FPX_REDIRECT_URI+"/"+state, StandardCharsets.UTF_8);
            // 构建并返回重定向URL，引导用户前往Allegro授权页面
            String authorizationUrl = String.format("https://open.4px.com/authorize/get?client_id=%s&response_type=code&redirect_uri=%s", FPX_CLIENT_ID, encodedRedirectUri);
            return ResponseEntity.status(HttpStatus.FOUND).header("Location", authorizationUrl).build();
        } catch (IllegalArgumentException e) {
            String errorMessage = "<html><body><h1>发生错误</h1><p>" + e.getMessage() + "</p></body></html>";
            return ResponseEntity.badRequest().body(errorMessage);
        } catch (Exception e) {
            String errorMessage = "<html><body><h1>发生错误</h1><p>未知错误：" + e.getMessage() + "</p></body></html>";
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorMessage);
        }
    }



    @GetMapping("/callback/{state}")
    @ResponseBody
    @NoDataSourceBind
    public String callback(@PathVariable("state") String state, @RequestParam String code) {
        String name = state.split("_")[2];
        String id = (state.split("_")[3]);
        String userId = state.split("_")[4];
        try {

            Fpx4Client fpx4Client = new Fpx4Client();
            AccessTokenResponse accessTokenResponse = fpx4Client.accessToken( code, FPX_REDIRECT_URI);
            System.out.println("code:" + code);
            System.out.println(accessTokenResponse);
            //处理保存操作
            //保存到数据库
            if (StrXhUtil.equalsIgnoreCase(id, "0")) {
                //表示新增
                ExpressEntity expressEntity = expressService.getByExpressKey(ExpressConstant.ALLEGRO_EXPRESS_4PX_EXPRESS);
                if (expressEntity == null) {
                    return name + "，递四方未配置";
                }
                ExpressAuthorizeEntity entity = new ExpressAuthorizeEntity();
                entity.setName(name);
                entity.setKey1(accessTokenResponse.getAccessToken());
                entity.setKey2(accessTokenResponse.getRefreshToken());
                entity.setExpireTime(DateXhUtil.offsetSecond(DateXhUtil.date(), accessTokenResponse.getExpiresIn()));
                entity.setExpressKey(ExpressConstant.ALLEGRO_EXPRESS_4PX_EXPRESS);
                entity.setExpressId(expressEntity.getId());

            }

                return name + "，递四方授权成功";

//            return ResponseEntity.ok("Access Token: " + accessTokenResponse.getAccessToken());
            } catch(Exception e){
                log.error(name + "递四方授权失败：" + e);
                return name + "，递四方授权失败" + e.getMessage();
            }
        }

}
