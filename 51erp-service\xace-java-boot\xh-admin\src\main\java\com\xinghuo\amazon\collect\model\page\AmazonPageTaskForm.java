package com.xinghuo.amazon.collect.model.page;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * Amazon页面任务表单
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
public class AmazonPageTaskForm {

    @Schema(description = "主键ID")
    private Integer id;

    @Schema(description = "ASIN")
    private String entryAsin;

    @Schema(description = "URL")
    private String url;

    @Schema(description = "任务状态")
    private Integer status;

    @Schema(description = "重试次数")
    private Integer retryCount;

    @Schema(description = "列表页标题")
    private String listPageTitle;

    @Schema(description = "列表页价格")
    private BigDecimal listPagePrice;

    @Schema(description = "列表页原价")
    private BigDecimal listOrginPrice;

    @Schema(description = "列表页购买数量")
    private Integer listBoughtNum;

    @Schema(description = "列表页评分")
    private BigDecimal listPageRating;

    @Schema(description = "列表页评论数")
    private Integer listPageReviewCount;

    @Schema(description = "列表页主图URL")
    private String listPageMainImageUrl;

    @Schema(description = "列表页Prime信息")
    private String listPagePrimeInfo;

    @Schema(description = "是否赞助商品")
    private Boolean isSponsored;

    @Schema(description = "Amazon分类ID")
    private Long amazonCategoryId;

    @Schema(description = "列表任务ID")
    private Integer listTaskId;

    @Schema(description = "错误信息")
    private String errorMessage;

    @Schema(description = "JSON数据")
    private String jsonData;

    @Schema(description = "详情数据")
    private String detailData;
}
