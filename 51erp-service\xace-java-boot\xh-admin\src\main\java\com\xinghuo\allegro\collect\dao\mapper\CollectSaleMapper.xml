<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinghuo.allegro.data.dao.AllegroSaleMapper">
    <resultMap id="saleSkuModel" type="com.xinghuo.allegro.data.model.sale.SaleSkuModel">
        <id column="sale_date" property="saleDate" />
        <result column="today_total_sales" property="todayTotalSales" />
        <result column="total_offer_count" property="totalOfferCount" />
        <result column="total_new_offer_count" property="totalNewOfferCount" />
        <result column="today_new_offer_sales" property="todayNewOfferSales" />
        <result column="sku_amount" property="skuAmount" />
        <result column="sku_uncheck_amount" property="skuUncheckAmount" />
        <result column="sku_ok_amount" property="skuOkAmount" />
    </resultMap>
    <select id="getSkuList" resultType="com.xinghuo.allegro.data.model.sale.SaleSkuModel">
        SELECT
            s.sale_date,
            SUM(s.sales_num) AS today_total_sales,
            COUNT(DISTINCT s.offer_id) AS total_offer_count,
            COUNT(DISTINCT CASE WHEN s.old_sales IS NULL THEN s.offer_id END) AS total_new_offer_count,
            SUM(CASE WHEN s.old_sales IS NULL THEN s.sales_num END) AS today_new_offer_sales,
            COUNT(DISTINCT   s.sku_id ) AS sku_amount,
            COUNT(DISTINCT CASE WHEN p.is_audit = 0   THEN s.sku_id END) AS sku_uncheck_amount,

            COUNT(DISTINCT CASE WHEN p.is_audit = 1 AND p.status = 1 THEN s.sku_id END) AS sku_ok_amount
        FROM
            zz_collect_sale s
                LEFT JOIN
            zz_erp_product p ON s.sku_id = p.sku_id
        WHERE
            s.sale_date > DATE_SUB(NOW(), INTERVAL 30 DAY)
        GROUP BY
            s.sale_date
        ORDER BY
            s.sale_date DESC
    </select>
</mapper>