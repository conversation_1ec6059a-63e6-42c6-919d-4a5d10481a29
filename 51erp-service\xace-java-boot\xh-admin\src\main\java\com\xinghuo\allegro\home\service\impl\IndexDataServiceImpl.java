package com.xinghuo.allegro.home.service.impl;

import com.xinghuo.base.service.ExtendedBaseServiceImpl;
import com.xinghuo.allegro.home.dao.IndexDataMapper;
import com.xinghuo.allegro.home.entity.IndexDataEntity;
import com.xinghuo.allegro.home.service.IndexDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


/**
 */
@Slf4j
@Service
public class IndexDataServiceImpl extends ExtendedBaseServiceImpl<IndexDataMapper, IndexDataEntity> implements IndexDataService {

    @Override
    public String getValue(String key){
        IndexDataEntity entity = this.getById(key);
        if (entity != null)
            return entity.getIndexValue();
        return "数据不存在";
    }
}
