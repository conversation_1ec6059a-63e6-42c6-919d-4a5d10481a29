package com.xinghuo.allegro.data.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Allegro 商品数据实体类
 */
@TableName("zz_allegro_data_offer")
@Data
public class AllegroDataOfferEntity {

    /**
     * 报价ID (Offer ID) - 主键
     */
    @TableId("offer_id")
    private String offerId;

    /**
     * 商品变体源ID (Variant ID) - 用于区分不同的商品变体
     */
    @TableField("variant_id")
    private String variantId;

    /**
     * 商品链接
     */
    @TableField("offer_link")
    private String offerLink;

    /**
     * 商品名称
     */
    @TableField("offer_name")
    private String offerName;

    /**
     * 商品首图URL
     */
    @TableField("image_url")
    private String imageUrl;

    /**
     * 商品价格
     */
    @TableField("price")
    private BigDecimal price;

    /**
     * 运费价格
     */
    @TableField("ship_fee")
    private BigDecimal shipFee;

    /**
     * 商品总价 (价格 + 运费，或特定计算的总价)
     */
    @TableField("total_price")
    private BigDecimal totalPrice;

    /**
     * 类目ID
     */
    @TableField("category_id")
    private String categoryId;

    /**
     * 商品分类名称
     */
    @TableField("category_name")
    private String categoryName;

    /**
     * 类目路径名称 (波兰名称)
     */
    @TableField("category_path")
    private String categoryPath;

    /**
     * 任务ID (关联的任务标识)
     */
    @TableField("task_id")
    private String taskId;

    /**
     * 卖家ID
     */
    @TableField("seller_id")
    private String sellerId;

    /**
     * 商品已售数量
     */
    @TableField("buyers_quantity")
    private Integer buyersQuantity;

    /**
     * SKU_ID (商品子规格ID，用于区分变体)
     */
    @TableField("sku_id")
    private Integer skuId;

    /**
     * 列表页采集人/客户端标识
     */
    @TableField("list_client_id")
    private String listClientId;

    /**
     * 采集优先级，数字越高，级别越高
     */
    @TableField("priority")
    private Integer priority;

    /**
     * 侵权状态: 1-表示侵权，0-表示不侵权
     */
    @TableField("vio_status")
    private Boolean vioStatus;

    /**
     * 详情页采集人/客户端ID
     */
    @TableField("detail_client_id")
    private String detailClientId;

    /**
     * 备注信息
     */
    @TableField("note")
    private String note;

    /**
     * 数据请求状态: -2 表示404不处理, -1 表示侵权词, 0-未请求, 1-请求中, 2-已完成
     */
    @TableField("request_status")
    private Integer requestStatus;

    /**
     * 数据请求时间
     */
    @TableField("request_time")
    private Date requestTime;

    /**
     * 数据请求完成时间
     */
    @TableField("req_finish_time")
    private Date reqFinishTime;

    /**
     * 后台采集的次数
     */
    @TableField("req_num")
    private Integer reqNum; // smallint maps to Short or Integer

    /**
     * 处理状态 (具体含义需根据业务定义)
     */
    @TableField("deal_status")
    private Integer dealStatus; // Was int, changed to Integer as schema implies nullable (False for PK, False for Nullable)

    /**
     * 图片地址列表 (通常是JSON字符串或逗号分隔的URL)
     */
    @TableField("images")
    private String images;

    /**
     * 商品参数 (通常是JSON字符串，描述商品规格属性)
     */
    @TableField("parameters")
    private String parameters;

    /**
     * 商品描述字段 (HTML或纯文本)
     */
    @TableField("description")
    private String description;

    /**
     * 原产品ID (来源于详情页，可能是Allegro的产品ID或卖家自定义的产品ID)
     */
    @TableField("product_id")
    private String productId;

    /**
     * 新产品ID (来源于API接口，可能是Allegro规范化后的产品ID)
     */
    @TableField("new_product_id")
    private String newProductId;

    /**
     * 发货国家 (原始字段)
     */
    @TableField("country")
    private String country;

    /**
     * 发货国家 (通过新方式获取，可能更准确或标准化的国家信息)
     */
    @TableField("dispatch_country")
    private String dispatchCountry;

    /**
     * 预计送达时间描述 (例如 "3-5 days", "do 24 godzin")
     */
    @TableField("delivery_time")
    private String deliveryTime;

    /**
     * 送达时间相关的数量/单位个数 (例如，如果delivery_time是 "X days", this might be X)
     */
    @TableField("delivery_count")
    private Short deliveryCount; // smallint maps to Short or Integer

    /**
     * 详情处理状态 (布尔型，具体含义需根据业务定义)
     */
    @TableField("detail_deal_status")
    private Boolean detailDealStatus; // tinyint(1) often maps to Boolean

    /**
     * 解析状态 (标识数据解析的阶段或结果)
     */
    @TableField("parse_status")
    private Integer parseStatus; // smallint maps to Short or Integer

    /**
     * 记录创建时间
     */
    @TableField("f_created_at")
    private Date createdAt;

    /**
     * 记录最后更新时间
     */
    @TableField("f_last_updated_at")
    private Date lastUpdatedAt;


}