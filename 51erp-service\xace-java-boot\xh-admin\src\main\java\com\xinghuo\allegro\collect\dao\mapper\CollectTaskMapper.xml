<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinghuo.allegro.collect.dao.CollectTaskMapper">

    <update id="updateCollectTask" parameterType="java.lang.String">
        UPDATE zz_collect_task
        SET total_num = (SELECT COUNT(*) FROM zz_collect_offer WHERE task_id = #{taskId}),
            total_sales_num = (SELECT COUNT(*) FROM zz_collect_offer WHERE task_id = #{taskId} AND buyers_quantity > 0)
        WHERE id = #{taskId}
    </update>
</mapper>