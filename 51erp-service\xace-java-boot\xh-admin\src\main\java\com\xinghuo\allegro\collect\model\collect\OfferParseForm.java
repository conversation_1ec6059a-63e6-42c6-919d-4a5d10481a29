package com.xinghuo.allegro.collect.model.collect;

import com.fasterxml.jackson.databind.JsonNode;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 前端提交的Json
 */
@Data
public class OfferParseForm {
    @Schema(description = "采集Offer的id")
    private String cpId;
    @Schema(description = "Offer的json")
    private JsonNode offerJson;
    @Schema(description = "跟卖json")
    private JsonNode customJson;
    @Schema(description = "客户端id")
    private String clientId;
}
