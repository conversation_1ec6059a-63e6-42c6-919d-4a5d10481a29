package com.xinghuo.allegro.data.controller;

import com.xinghuo.allegro.collect.model.seller.CollectSellerForm;
import com.xinghuo.allegro.collect.model.seller.CollectSellerModel;
import com.xinghuo.allegro.data.entity.AllegroSellerEntity;
import com.xinghuo.allegro.data.model.seller.AllegroSellerPagination;
import com.xinghuo.allegro.data.service.AllegroSellerService;
import com.xinghuo.allegro.push.service.ErpProductVioService;
import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.constant.MsgCode;
import com.xinghuo.common.database.util.TenantHolder;
import com.xinghuo.common.exception.DataException;
import com.xinghuo.common.util.UserProvider;
import com.xinghuo.common.util.core.BeanCopierUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 产品采集Controller，负责处理产品采集相关的请求。
 * 接受客户端采集的数据上传。
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@Tag(name = "产品采集-卖家管理", description = "产品采集-卖家管理")
@RequestMapping("/api/allegro/push/seller")
public class AllegroSellerController {

    @Resource
    private AllegroSellerService allegroSellerService;
    @Resource
    private ErpProductVioService erpProductVioService;

    @Resource
    private UserProvider userProvider;



    /**
     * 列表
     */
    @Operation(summary = "卖家列表")
    @PostMapping("/getList")
    public ActionResult<PageListVO<CollectSellerModel>> list(@RequestBody AllegroSellerPagination pagination) {
        List<AllegroSellerEntity> list = allegroSellerService.getList(pagination);
        List<CollectSellerModel> listVO = BeanCopierUtils.copyList(list, CollectSellerModel.class);
        PaginationVO page = BeanCopierUtils.copy(pagination, PaginationVO.class);
        return ActionResult.page(listVO, page);
    }


    /**
     * 获取详情(编辑页)--转换数据
     */
    @Operation(summary = "卖家详情")
    @GetMapping("/{id}")
    @Parameters({
            @Parameter(name = "id", description = "主键", required = true)
    })
    public ActionResult<CollectSellerModel> info(@PathVariable("id") String id) {
        AllegroSellerEntity entity = allegroSellerService.getInfo(id);
        CollectSellerModel infoVo = BeanCopierUtils.copy(entity, CollectSellerModel.class);
        return ActionResult.success(infoVo);
    }




    /**
     * 编辑
     */
    @PutMapping("/{id}")
    @Parameters({
            @Parameter(name = "id", description = "主键", required = true),
            @Parameter(name = "form", description = "卖家数据", required = true),
    })
    @Operation(summary = "卖家数据-更新")
    public ActionResult update(@PathVariable("id") String id, @RequestBody @Valid CollectSellerForm form) throws DataException {
        AllegroSellerEntity entity = allegroSellerService.getInfo(id);
        log.info("卖家数据-更新:{}", TenantHolder.getDatasourceId());
        log.info("卖家数据-更新:{}", TenantHolder.getDatasourceName());
        if (entity != null) {
            String oldSellType = entity.getSellType();
            String newSellType = form.getSellType();

            entity.setNote(form.getNote());
            entity.setGroupName(form.getGroupName());
            entity.setSellType(form.getSellType());

            allegroSellerService.update(id, entity);

            if(!newSellType.equals(oldSellType)){
               allegroSellerService.checkBlockSeller(userProvider.get().getTenantId() , entity.getSellerId(),("BLOCKED").equals(newSellType));
            }
            return ActionResult.success(MsgCode.SU004.get());
        }
        return ActionResult.fail(MsgCode.FA002.get());
    }



    @DeleteMapping("/{id}")
    @Parameters({
            @Parameter(name = "id", description = "主键", required = true)
    })
    @Operation(summary = "卖家单个删除")
    public ActionResult delete(@PathVariable("id") String id) throws DataException {
        AllegroSellerEntity entity = allegroSellerService.getInfo(id);
        if (entity != null) {
            allegroSellerService.removeById(id);
            if(("BLOCKED").equals(entity.getSellType())){
                allegroSellerService.checkBlockSeller(userProvider.get().getTenantId() , entity.getSellerId(),false);
            }
            return ActionResult.success(MsgCode.SU003.get());
        }
        return ActionResult.success(MsgCode.FA002.get());
    }


    @PostMapping("/blocked")
    @Operation(summary = "重构黑名单数据")
    @Transactional
    public ActionResult blocked(String tenantId) throws DataException {
        log.info("开始重构黑名单数据......");
        allegroSellerService.checkBlockSeller(tenantId);
        return ActionResult.success(MsgCode.SU000.get());
    }

}
