# DAO/Mapper 层规范

## 基本结构

DAO/Mapper层负责定义数据库访问操作，在项目中主要基于MyBatis-Plus框架实现。

### 位置与命名

* **包路径:** `com.xinghuo.[模块名].dao` 和 `com.xinghuo.[模块名].dao.mapper`
* **命名规范:** 以 `Mapper` 结尾，如 `UserMapper`，`ProductCategoryMapper`

### 基础设置

* 继承自 `XHBaseMapper<T>` 接口，获取基本CRUD操作
* 使用 `@Mapper` 注解或在配置类中统一扫描
* 对应的XML文件应放在 `resources/mapper` 目录下

## 完整示例

```java

import com.xinghuo.common.base.dao.XHBaseMapper;

/**
 * 产品分类Mapper接口
 *
 * @author： frying52
 * date： 2023-05-15
 */
@Mapper
public interface ProductCategoryMapper extends XHBaseMapper<ProductCategoryEntity> {
    
    /**
     * 根据父分类ID查询子分类列表
     *
     * @param parentId 父分类ID
     * @return 分类列表
     */
    List<ProductCategoryEntity> selectByParentId(@Param("parentId") Long parentId);
    
    /**
     * 查询分类树结构
     *
     * @return 分类树列表
     */
    List<ProductCategoryTreeVO> selectCategoryTree();
    
    /**
     * 批量更新排序值
     *
     * @param sortList 排序列表
     * @return 影响行数
     */
    int batchUpdateSort(List<ProductCategorySortDTO> sortList);
}
```

## XML映射文件示例

```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinghuo.admin.dao.ProductCategoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xinghuo.admin.entity.ProductCategoryEntity">
        <id column="ID" property="id" />
        <result column="NAME" property="name" />
        <result column="PARENT_ID" property="parentId" />
        <result column="SORT" property="sort" />
        <result column="STATUS" property="status" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
    </resultMap>
    
    <!-- 树结构结果映射 -->
    <resultMap id="TreeResultMap" type="com.xinghuo.admin.model.vo.ProductCategoryTreeVO">
        <id column="ID" property="id" />
        <result column="NAME" property="name" />
        <result column="PARENT_ID" property="parentId" />
        <result column="SORT" property="sort" />
        <result column="STATUS" property="status" />
        <collection property="children" ofType="com.xinghuo.admin.model.vo.ProductCategoryTreeVO" 
                    column="ID" select="selectChildrenById"/>
    </resultMap>
    
    <!-- 自定义查询方法 -->
    <select id="selectByParentId" resultMap="BaseResultMap">
        SELECT * FROM PRODUCT_CATEGORY WHERE PARENT_ID = #{parentId} ORDER BY SORT ASC
    </select>
    
    <!-- 查询分类树根节点 -->
    <select id="selectCategoryTree" resultMap="TreeResultMap">
        SELECT * FROM PRODUCT_CATEGORY WHERE PARENT_ID = 0 ORDER BY SORT ASC
    </select>
    
    <!-- 查询子节点 -->
    <select id="selectChildrenById" resultMap="TreeResultMap">
        SELECT * FROM PRODUCT_CATEGORY WHERE PARENT_ID = #{id} ORDER BY SORT ASC
    </select>
    
    <!-- 批量更新排序值 -->
    <update id="batchUpdateSort" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            UPDATE PRODUCT_CATEGORY SET SORT = #{item.sort} WHERE ID = #{item.id}
        </foreach>
    </update>
</mapper>
```

## 查询构造器使用示例

```java
// 使用LambdaQueryWrapper构造查询条件
LambdaQueryWrapper<ProductCategoryEntity> wrapper = new LambdaQueryWrapper<>();
wrapper.like(StrUtil.isNotBlank(query.getName()), ProductCategoryEntity::getName, query.getName());
wrapper.eq(query.getParentId() != null, ProductCategoryEntity::getParentId, query.getParentId());
wrapper.eq(query.getStatus() != null, ProductCategoryEntity::getStatus, query.getStatus());
wrapper.orderByAsc(ProductCategoryEntity::getSort);

// 执行查询
List<ProductCategoryEntity> list = productCategoryMapper.selectList(wrapper);
```

## 设计原则

1. **接口设计**
   * 方法名应明确表达SQL操作意图，如select*, insert*, update*, delete*
   * 参数应明确，使用 `@Param` 注解标识XML中使用的参数名
   * 返回类型应明确，尽量不使用 `Map` 类型

2. **SQL编写**
   * 复杂SQL应写在XML文件中，而非注解中
   * 注意SQL注入问题，不要拼接SQL
   * 大量数据操作应考虑分页
   * 避免使用 `*` 查询所有字段，明确指定需要的字段

3. **性能考虑**
   * 合理设置索引
   * 避免子查询和多表连接，必要时拆分成多次简单查询
   * 大数据量操作考虑批处理
   * 合理使用缓存减少数据库压力

## 最佳实践

1. 优先使用MyBatis-Plus提供的内置方法，如 `selectById`, `insert`, `updateById`, `deleteById`
2. 自定义复杂查询方法应放在XML文件中维护
3. 避免在Mapper接口使用默认方法实现业务逻辑
4. 复杂条件查询使用 `QueryWrapper` 或 `LambdaQueryWrapper` 构建条件
5. 分页查询使用MyBatis-Plus的 `Page` 对象
6. 对于需要多表联查的复杂查询，可以定义VO对象映射结果
7. 批量操作注意性能，可以使用 `saveBatch`, `updateBatchById` 等批处理方法
