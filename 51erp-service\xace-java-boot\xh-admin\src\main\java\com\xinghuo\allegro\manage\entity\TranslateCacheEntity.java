package com.xinghuo.allegro.manage.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.BaseEntityV2;
import lombok.Data;

import java.util.Date;

@Data
@TableName("zz_translate_cache")
public class TranslateCacheEntity extends BaseEntityV2.CBaseEntityV2 {

    @TableField("source_text")
    private String sourceText;

    @TableField("source_lang")
    private String sourceLang;

    @TableField("target_text")
    private String targetText;

    @TableField("target_lang")
    private String targetLang;

    @TableField("source_hash")
    private String sourceHash;

    @TableField("provider")
    private String provider;

    @TableField("last_used_at")
    private Date lastUsedAt;

    @TableField("hit_count")
    private Integer hitCount;
}
