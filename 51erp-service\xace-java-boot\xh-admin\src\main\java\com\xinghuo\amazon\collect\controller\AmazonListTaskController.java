package com.xinghuo.amazon.collect.controller;

import cn.hutool.core.codec.Base64;
import com.xinghuo.amazon.collect.entity.AmazonListTaskEntity;
import com.xinghuo.amazon.collect.entity.AmazonPageTaskEntity;
import com.xinghuo.amazon.collect.model.list.AmazonListTaskContentForm;
import com.xinghuo.amazon.collect.model.list.AmazonListTaskForm;
import com.xinghuo.amazon.collect.model.list.AmazonListTaskModel;
import com.xinghuo.amazon.collect.model.list.AmazonListTaskPagination;
import com.xinghuo.amazon.collect.service.AmazonDataParseService;
import com.xinghuo.amazon.collect.service.AmazonHtmlParseService;
import com.xinghuo.amazon.collect.service.AmazonListTaskService;
import com.xinghuo.amazon.collect.service.AmazonPageTaskService;
import com.xinghuo.amazon.util.AmazonConstant;
import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.exception.DataException;
import com.xinghuo.common.util.UserProvider;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.common.util.core.StrXhUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * Amazon列表任务Controller，负责处理Amazon列表采集相关的请求。
 * 接受客户端采集的数据上传。
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Slf4j
@RestController
@Tag(name = "Amazon列表采集任务", description = "Amazon列表采集任务")
@RequestMapping("/api/amazon/list/task")
public class AmazonListTaskController {

    @Resource
    private AmazonListTaskService amazonListTaskService;

    @Resource
    private AmazonPageTaskService amazonPageTaskService;

    @Resource
    private AmazonDataParseService amazonDataParseService;

    @Resource
    private AmazonHtmlParseService amazonHtmlParseService;

    @Resource
    private UserProvider userProvider;

    /**
     * 获取Amazon列表任务列表
     *
     * @param pagination 分页参数
     * @return 任务列表
     */
    @Operation(summary = "获取Amazon列表任务列表")
    @GetMapping
    public ActionResult<PageListVO<AmazonListTaskModel>> getList(AmazonListTaskPagination pagination) {
        List<AmazonListTaskEntity> list = amazonListTaskService.getList(pagination);
        List<AmazonListTaskModel> listVO = BeanCopierUtils.copyList(list, AmazonListTaskModel.class);
        PaginationVO page = BeanCopierUtils.copy(pagination, PaginationVO.class);
        return ActionResult.page(listVO, page);
    }

    /**
     * 获取Amazon列表任务详情
     *
     * @param id 任务ID
     * @return 任务详情
     */
    @Operation(summary = "获取Amazon列表任务详情")
    @GetMapping("/{id}")
    public ActionResult<AmazonListTaskModel> getInfo(@PathVariable("id") String id) {
        AmazonListTaskEntity entity = amazonListTaskService.getById(id);
        if (entity == null) {
            throw new DataException("数据不存在");
        }
        AmazonListTaskModel model = BeanCopierUtils.copy(entity, AmazonListTaskModel.class);
        return ActionResult.success(model);
    }

    /**
     * 创建Amazon列表任务
     *
     * @param taskForm 任务表单
     * @return 创建结果
     */
    @Operation(summary = "创建Amazon列表任务")
    @PostMapping
    public ActionResult<String> create(@RequestBody AmazonListTaskForm taskForm) {
        AmazonListTaskEntity entity = BeanCopierUtils.copy(taskForm, AmazonListTaskEntity.class);
        entity.setStatus(AmazonConstant.REQUEST_STATUS_INIT);
        entity.setCreatedAt(new Date());
        entity.setUpdatedAt(new Date());
        
        boolean result = amazonListTaskService.save(entity);
        if (result) {
            return ActionResult.success("创建成功");
        } else {
            return ActionResult.fail("创建失败");
        }
    }

    /**
     * 更新Amazon列表任务
     *
     * @param id 任务ID
     * @param taskForm 任务表单
     * @return 更新结果
     */
    @Operation(summary = "更新Amazon列表任务")
    @PutMapping("/{id}")
    public ActionResult<String> update(@PathVariable("id") String id, @RequestBody AmazonListTaskForm taskForm) {
        AmazonListTaskEntity entity = amazonListTaskService.getById(id);
        if (entity == null) {
            throw new DataException("数据不存在");
        }

        entity = BeanCopierUtils.copy(taskForm, AmazonListTaskEntity.class);
        entity.setUpdatedAt(new Date());
        
        boolean result = amazonListTaskService.updateById(entity);
        if (result) {
            return ActionResult.success("更新成功");
        } else {
            return ActionResult.fail("更新失败");
        }
    }

    /**
     * 删除Amazon列表任务
     *
     * @param id 任务ID
     * @return 删除结果
     */
    @Operation(summary = "删除Amazon列表任务")
    @DeleteMapping("/{id}")
    public ActionResult<String> delete(@PathVariable("id") String id) {
        AmazonListTaskEntity entity = amazonListTaskService.getById(id);
        if (entity == null) {
            throw new DataException("数据不存在");
        }
        
        boolean result = amazonListTaskService.removeById(id);
        if (result) {
            return ActionResult.success("删除成功");
        } else {
            return ActionResult.fail("删除失败");
        }
    }

    /**
     * 获取待处理的Amazon列表任务
     *
     * @return 表示操作结果的ActionResult对象，包含待处理的任务
     */
    @Operation(summary = "获取后台待处理数据清单")
    @GetMapping("/waitGets")
    public ActionResult<AmazonListTaskModel> waitGets(String clientId, String taskType) {
        AmazonListTaskEntity entity = amazonListTaskService.waitGets(clientId, taskType, AmazonConstant.PLATFORM_AMAZON);
        AmazonListTaskModel model = BeanCopierUtils.copy(entity, AmazonListTaskModel.class);
        return ActionResult.success(model);
    }

    /**
     * 接收并处理前端提交的Amazon列表页JSON数据
     * 该方法主要负责解析前端上传的JSON数据，提取其中的商品信息，
     * 并更新相应的数据库记录。
     *
     * @param taskForm 包含待解析的JSON数据和相关ID的信息表单。
     * @return 成功保存的提示信息。
     */
    @Operation(summary = "前端提交列表页的json数据")
    @PutMapping("/putTaskJson")
    public ActionResult<String> putLinkJson(@RequestBody AmazonListTaskForm taskForm) {
        log.debug("前端提交列表页的json数据 taskId:{}", taskForm.getId());

        // 获取任务实体
        AmazonListTaskEntity entity = amazonListTaskService.getById(taskForm.getId());
        if (entity != null) {
            // 解析JSON数据，创建页面任务
            if (taskForm.getJsonData() != null) {
                try {
                    List<AmazonPageTaskEntity> pageTaskList = amazonDataParseService.parseListPageData(
                            taskForm.getJsonData(), taskForm.getId());

                    if (!pageTaskList.isEmpty()) {
                        // 批量保存页面任务
                        amazonPageTaskService.saveBatch(AmazonConstant.TASK_TYPE_PAGE, pageTaskList);
                        log.info("成功创建{}个Amazon页面任务", pageTaskList.size());
                    }
                } catch (Exception e) {
                    log.error("解析Amazon列表页JSON数据失败", e);
                }
            }

            // 更新列表任务状态
            entity.setStatus(AmazonConstant.REQUEST_STATUS_FINISH);
            entity.setCrawledPages(taskForm.getCrawledPages());
            entity.setUpdatedAt(new Date());

            amazonListTaskService.updateById(entity);

            log.info("Amazon列表任务完成: taskId={}, crawledPages={}", taskForm.getId(), taskForm.getCrawledPages());
        } else {
            log.warn("Amazon列表任务数据在系统中不存在，忽略。ID:" + taskForm.getId());
        }

        return ActionResult.success("成功保存！" + taskForm.getId());
    }



    @Operation(summary = "前端AMAZON列表提交的页面")
    @PutMapping("/submitForm")
    public ActionResult<String> submitForm(@RequestBody AmazonListTaskContentForm taskForm) {
        log.debug("前端提交列表页的HTML数据 taskId:{}", taskForm.getId());

        // 获取任务实体
        AmazonListTaskEntity entity = amazonListTaskService.getById(taskForm.getId());
        if (entity == null) {
            throw new DataException("数据不存在");
        }

        // 解码HTML内容
        String htmlContent = Base64.decodeStr(taskForm.getHtmlContent());
        if (StrXhUtil.isBlank(htmlContent)) {
            log.warn("HTML内容为空，taskId: {}", taskForm.getId());
            return ActionResult.fail("HTML内容不能为空");
        }

        try {
            // 解析HTML内容，提取商品信息并创建页面任务
            List<AmazonPageTaskEntity> pageTaskList = amazonHtmlParseService.parseListPageHtml(
                    htmlContent, taskForm.getId(), taskForm.getUrl());

            if (!pageTaskList.isEmpty()) {
                // 批量保存页面任务
                amazonPageTaskService.saveBatch(AmazonConstant.TASK_TYPE_PAGE, pageTaskList);
                log.info("成功从HTML解析并创建{}个Amazon页面任务", pageTaskList.size());

                // 更新列表任务状态和页数信息
                entity.setStatus(AmazonConstant.REQUEST_STATUS_FINISH);
                if (taskForm.getCurrentPage() != null) {
                    entity.setCrawledPages(taskForm.getCurrentPage());
                }
                if (taskForm.getTotalPages() != null) {
                    entity.setMaxPagesToCrawl(taskForm.getTotalPages());
                }
                entity.setUpdatedAt(new Date());

                amazonListTaskService.updateById(entity);

                log.info("Amazon列表任务HTML解析完成: taskId={}, 解析商品数={}, 当前页={}, 总页数={}",
                        taskForm.getId(), pageTaskList.size(), taskForm.getCurrentPage(), taskForm.getTotalPages());

                return ActionResult.success("成功解析HTML并保存" + pageTaskList.size() + "个商品信息！");
            } else {
                log.warn("HTML解析未找到任何商品信息，taskId: {}", taskForm.getId());
                return ActionResult.fail("未从HTML中解析到商品信息，请检查页面内容");
            }

        } catch (Exception e) {
            log.error("解析Amazon列表页HTML失败，taskId: {}", taskForm.getId(), e);

            // 更新任务状态为失败
            entity.setStatus(AmazonConstant.REQUEST_STATUS_FAILED);
            entity.setErrorMessage("HTML解析失败: " + e.getMessage());
            entity.setUpdatedAt(new Date());
            amazonListTaskService.updateById(entity);

            return ActionResult.fail("HTML解析失败: " + e.getMessage());
        }
    }

    /**
     * 获取待处理任务数量
     *
     * @param taskType 任务类型
     * @return 待处理任务数量
     */
    @Operation(summary = "获取待处理任务数量")
    @GetMapping("/pendingCount")
    public ActionResult<Long> getPendingCount(@RequestParam(required = false) String taskType) {
        long count = amazonListTaskService.getPendingTaskCount(taskType);
        return ActionResult.success(count);
    }
}
