package com.xinghuo.allegro.collect.controller;

import com.xinghuo.allegro.collect.model.analysis.*;
import com.xinghuo.allegro.collect.service.CollectTaskAnalysisService;
import com.xinghuo.common.base.ActionResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Allegro采集任务分析控制器
 * <AUTHOR>
 */
@Slf4j
@Tag(name = "Allegro采集任务分析", description = "AllegroTaskAnalysisController")
@RestController
@RequestMapping("/api/allegro/task/analysis")
public class AllegroTaskAnalysisController {

    @Autowired
    private CollectTaskAnalysisService analysisService;

    /**
     * 获取待采集任务总数
     */
    @Operation(summary = "获取待采集任务总数")
    @GetMapping("/kpi/pendingCount")
    public ActionResult<Long> getPendingCount() {
        try {
            Long count = analysisService.getPendingTaskCount();
            return ActionResult.success(count);
        } catch (Exception e) {
            log.error("获取待采集任务总数失败", e);
            return ActionResult.fail("获取数据失败");
        }
    }

    /**
     * 获取采集中任务数
     */
    @Operation(summary = "获取采集中任务数")
    @GetMapping("/kpi/inProgressCount")
    public ActionResult<Long> getInProgressCount() {
        try {
            Long count = analysisService.getInProgressTaskCount();
            return ActionResult.success(count);
        } catch (Exception e) {
            log.error("获取采集中任务数失败", e);
            return ActionResult.fail("获取数据失败");
        }
    }

    /**
     * 获取今日已完成任务数
     */
    @Operation(summary = "获取今日已完成任务数")
    @GetMapping("/kpi/completedTodayCount")
    public ActionResult<Long> getCompletedTodayCount(
            @Parameter(description = "查询日期(YYYY-MM-DD)，可选，默认当天")
            @RequestParam(required = false) String date) {
        try {
            Long count = analysisService.getCompletedTodayCount(date);
            return ActionResult.success(count);
        } catch (Exception e) {
            log.error("获取今日已完成任务数失败", e);
            return ActionResult.fail("获取数据失败");
        }
    }

    /**
     * 获取今日采集总量（Offer数）
     */
    @Operation(summary = "获取今日采集总量（Offer数）")
    @GetMapping("/kpi/collectedTodayOffers")
    public ActionResult<Long> getCollectedTodayOffers(
            @Parameter(description = "查询日期(YYYY-MM-DD)，可选，默认当天")
            @RequestParam(required = false) String date) {
        try {
            Long count = analysisService.getCollectedTodayOffers(date);
            return ActionResult.success(count);
        } catch (Exception e) {
            log.error("获取今日采集总量失败", e);
            return ActionResult.fail("获取数据失败");
        }
    }

    /**
     * 获取今日有销售额采集量
     */
    @Operation(summary = "获取今日有销售额采集量")
    @GetMapping("/kpi/salesTodayOffers")
    public ActionResult<Long> getSalesTodayOffers(
            @Parameter(description = "查询日期(YYYY-MM-DD)，可选，默认当天")
            @RequestParam(required = false) String date) {
        try {
            Long count = analysisService.getSalesTodayOffers(date);
            return ActionResult.success(count);
        } catch (Exception e) {
            log.error("获取今日有销售额采集量失败", e);
            return ActionResult.fail("获取数据失败");
        }
    }

    /**
     * 获取今日活跃客户端数
     */
    @Operation(summary = "获取今日活跃客户端数")
    @GetMapping("/kpi/activeClientsToday")
    public ActionResult<Long> getActiveClientsToday(
            @Parameter(description = "查询日期(YYYY-MM-DD)，可选，默认当天")
            @RequestParam(required = false) String date) {
        try {
            Long count = analysisService.getActiveClientsToday(date);
            return ActionResult.success(count);
        } catch (Exception e) {
            log.error("获取今日活跃客户端数失败", e);
            return ActionResult.fail("获取数据失败");
        }
    }

    /**
     * 获取进行中任务列表
     */
    @Operation(summary = "获取进行中任务列表")
    @GetMapping("/inProgressList")
    public ActionResult<List<InProgressTaskModel>> getInProgressList(
            @Parameter(description = "采集客户端ID") @RequestParam(required = false) String clientId,
            @Parameter(description = "任务类型") @RequestParam(required = false) String taskType,
            @Parameter(description = "页码，默认1") @RequestParam(required = false, defaultValue = "1") Integer page,
            @Parameter(description = "每页数量，默认10") @RequestParam(required = false, defaultValue = "10") Integer pageSize) {
        try {
            List<InProgressTaskModel> list = analysisService.getInProgressTaskList(clientId, taskType, page, pageSize);
            return ActionResult.success(list);
        } catch (Exception e) {
            log.error("获取进行中任务列表失败", e);
            return ActionResult.fail("获取数据失败");
        }
    }

    /**
     * 获取任务积压与优先级分布
     */
    @Operation(summary = "获取任务积压与优先级分布")
    @GetMapping("/summaryByStatusPriority")
    public ActionResult<List<TaskStatusSummaryModel>> getSummaryByStatusPriority(
            @Parameter(description = "任务类型") @RequestParam(required = false) String taskType) {
        try {
            List<TaskStatusSummaryModel> list = analysisService.getTaskSummaryByStatusPriority(taskType);
            return ActionResult.success(list);
        } catch (Exception e) {
            log.error("获取任务积压与优先级分布失败", e);
            return ActionResult.fail("获取数据失败");
        }
    }

    /**
     * 获取任务类型状态统计
     */
    @Operation(summary = "获取任务类型状态统计")
    @GetMapping("/summaryByTypeStatus")
    public ActionResult<List<TaskStatusSummaryModel>> getSummaryByTypeStatus(
            @Parameter(description = "查询日期(YYYY-MM-DD)") @RequestParam(required = false) String date) {
        try {
            List<TaskStatusSummaryModel> list = analysisService.getTaskSummaryByTypeStatus(date);
            return ActionResult.success(list);
        } catch (Exception e) {
            log.error("获取任务类型状态统计失败", e);
            return ActionResult.fail("获取数据失败");
        }
    }



    /**
     * 获取当日完成任务详细分析
     */
    @Operation(summary = "获取当日完成任务详细分析")
    @GetMapping("/dailyCompletionDetails")
    public ActionResult<List<DailyCompletionModel>> getDailyCompletionDetails(
            @Parameter(description = "分析日期(YYYY-MM-DD)，默认当天") @RequestParam(required = false) String date,
            @Parameter(description = "分组维度(hour/taskType/client)") @RequestParam(required = false) String groupBy,
            @Parameter(description = "任务类型筛选") @RequestParam(required = false) String filterTaskType,
            @Parameter(description = "客户端ID筛选") @RequestParam(required = false) String filterClientId) {
        try {
            List<DailyCompletionModel> list = analysisService.getDailyCompletionDetails(date, groupBy, filterTaskType, filterClientId);
            return ActionResult.success(list);
        } catch (Exception e) {
            log.error("获取当日完成任务详细分析失败", e);
            return ActionResult.fail("获取数据失败");
        }
    }
}
