package com.xinghuo.allegro.collect.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.AbstractBaseEntity;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@TableName("zz_collect_offer")
@Data
public class CollectOfferEntity extends AbstractBaseEntity.AbstractCUBaseEntity<String> {

    @TableField("offer_id")
    private String offerId;

    @TableField("sku_id")
    private Integer skuId;

    @TableField("offer_link")
    private String offerLink;

    @TableField("offer_name")
    private String offerName;

    @TableField("image_url")
    private String imageUrl;

    @TableField("price")
    private BigDecimal price;

    @TableField("ship_fee")
    private BigDecimal shipFee;

    @TableField("total_price")
    private BigDecimal totalPrice;

    @TableField("buyers_quantity")
    private Integer buyersQuantity;
    @TableField("list_client_id")
    private String listClientId;
    @TableField("note")
    private String note;
    @TableField("detail_client_id")
    private String detailClientId;

    //采集优先级
    @TableField("priority")
    private Integer priority;
    @TableField("request_status")
    private Integer requestStatus;
    @TableField("request_time")
    private Date requestTime;
    @TableField("req_finish_time")
    private Date reqFinishTime;
    @TableField("category_id")
    private String categoryId;
    @TableField("category_path")
    private String categoryPath;
    @TableField("task_id")
    private String taskId;
    @TableField("seller_id")
    private String sellerId;
    @TableField("product_id")
    private String productId;
    @TableField("new_product_id")
    private String newProductId;
    @TableField("dispatch_country")
    private String dispatchCountry;
    @TableField("delivery_time")
    private String deliveryTime;
    @TableField("delivery_count")
    private Integer deliveryCount;
    @TableField("deal_status")
    private int dealStatus;
    @TableField("country")
    private String country;
    @TableField("parse_status")
    private Integer parseStatus;
    @TableField("detail_deal_status")
    private Boolean detailDealStatus;
    @TableField("delete_reason_type")
    private String deleteReasonType;

}
