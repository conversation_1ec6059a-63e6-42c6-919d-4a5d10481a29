package com.xinghuo.amazon.collect.model.list;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xinghuo.common.base.model.Pagination;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * Amazon列表任务分页查询参数
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
public class AmazonListTaskPagination extends Pagination {
    
    @Schema(description = "查询key")
    private String[] selectKey;
    
    @Schema(description = "json")
    private String json;
    
    @Schema(description = "数据类型 0-当前页，1-全部数据")
    private String dataType;
    
    @Schema(description = "高级查询")
    private String superQueryJson;
    
    @Schema(description = "功能id")
    private String moduleId;
    
    @Schema(description = "菜单id")
    private String menuId;

    @Schema(description = "URL")
    private String url;

    @Schema(description = "源分类ID")
    private Long sourceCategoryId;

    @Schema(description = "任务状态")
    private Integer status;

    @Schema(description = "最大爬取页数")
    private Integer maxPagesToCrawl;

    @Schema(description = "已爬取页数")
    private Integer crawledPages;

    @Schema(description = "错误信息")
    private String errorMessage;

    @Schema(description = "源分类路径")
    private String sourceCategoryPath;

    @Schema(description = "Temu分类ID")
    private Integer temuCategoryId;

    @Schema(description = "Temu分类路径")
    private String temuCategoryPath;

    @Schema(description = "创建时间-开始")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @Schema(description = "创建时间-结束")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;
}
