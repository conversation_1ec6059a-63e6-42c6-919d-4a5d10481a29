package com.xinghuo.allegro.manage.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("zz_allegro_bill_type")
public class AllegroBillTypeEntity {

    @TableId(value = "id",type = IdType.AUTO)
    private Integer id;

    /**  账单类型   */
    @TableField("type_id")
    private String typeId;

    /**  账单描述   */
    @TableField("type_name")
    private String typeName;

    /**  账单中文描述   */
    @TableField("type_name_cn")
    private String typeNameCn;
}
