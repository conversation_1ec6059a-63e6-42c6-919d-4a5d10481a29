<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinghuo.allegro.collect.dao.CollectTaskAnalysisMapper">

    <!-- 获取任务积压与优先级分布统计 -->
    <select id="getTaskSummaryByStatusPriority" resultType="com.xinghuo.allegro.collect.model.analysis.TaskStatusSummaryModel">
        SELECT
            CASE
                WHEN t.status = 0 THEN 'pending'
                WHEN t.status = 1 THEN 'processing'
                WHEN t.status = 2 THEN 'completed'
                ELSE 'other'
            END as statusCategory,
            CASE
                WHEN t.status = 0 THEN '未采集'
                WHEN t.status = 1 THEN '采集中'
                WHEN t.status = 2 THEN '已采集'
                ELSE '其他'
            END as statusDesc,
            COALESCE(t.priority, 1) as priority,
            COUNT(*) as taskCount,
            COALESCE(SUM(t.sum_count), 0) as totalCollectedCount,
            COALESCE(SUM(t.total_sales_num), 0) as totalSalesCount,
            t.task_type as taskType,
            CASE
                WHEN t.task_type = 'C' THEN '类目采集'
                WHEN t.task_type = 'S' THEN '卖家采集'
                WHEN t.task_type = 'P' THEN '产品采集'
                WHEN t.task_type = 'CNEW' THEN '类目新上采集'
                ELSE t.task_type
            END as taskTypeDesc
        FROM zz_collect_task t
        <where>

        <if test="taskType != null and taskType != ''">
            AND t.task_type = #{taskType}
        </if>
        </where>
        GROUP BY t.status, t.priority, t.task_type
        ORDER BY t.status, t.priority DESC, t.task_type
    </select>

    <!-- 获取任务类型状态统计 -->
    <select id="getTaskSummaryByTypeStatus" resultType="com.xinghuo.allegro.collect.model.analysis.TaskStatusSummaryModel">
        SELECT
            t.task_type as taskType,
            CASE
                WHEN t.task_type = 'C' THEN '类目采集'
                WHEN t.task_type = 'S' THEN '卖家采集'
                WHEN t.task_type = 'P' THEN '产品采集'
                WHEN t.task_type = 'CNEW' THEN '类目新上采集'
                ELSE t.task_type
            END as taskTypeDesc,
            CASE
                WHEN t.status = 0 THEN 'pending'
                WHEN t.status = 1 THEN 'processing'
                WHEN t.status = 2 THEN 'completed'
                ELSE 'other'
            END as statusCategory,
            CASE
                WHEN t.status = 0 THEN '未采集'
                WHEN t.status = 1 THEN '采集中'
                WHEN t.status = 2 THEN '已采集'
                ELSE '其他'
            END as statusDesc,
            COUNT(*) as taskCount,
            COALESCE(SUM(t.sum_count), 0) as totalCollectedCount,
            COALESCE(SUM(t.total_sales_num), 0) as totalSalesCount
        FROM zz_collect_task t
        <where>

        <if test="date != null and date != ''">
            AND DATE(t.f_created_at) = #{date}
        </if>
        </where>
        GROUP BY t.task_type, t.status
        ORDER BY t.task_type, t.status
    </select>



    <!-- 获取当日完成任务详细分析 - 按小时分组 -->
    <select id="getDailyCompletionByHour" resultType="com.xinghuo.allegro.collect.model.analysis.DailyCompletionModel">
        SELECT
            hour_val as hour,
            CONCAT(hour_val, ':00-', hour_val+1, ':00') as timeSlot,
            completedTaskCount,
            salesTaskCount,
            'hour' as groupBy
        FROM (
            SELECT
                HOUR(t.finish_time) as hour_val,
                COUNT(*) as completedTaskCount,
                SUM(CASE WHEN t.total_sales_num > 0 THEN 1 ELSE 0 END) as salesTaskCount
            FROM zz_collect_task t
            WHERE t.status = 2
            AND DATE(t.finish_time) = #{date}
            <if test="filterTaskType != null and filterTaskType != ''">
                AND t.task_type = #{filterTaskType}
            </if>
            <if test="filterClientId != null and filterClientId != ''">
                AND t.client_id LIKE CONCAT('%', #{filterClientId}, '%')
            </if>
            GROUP BY HOUR(t.finish_time)
        ) sub
        ORDER BY hour_val
    </select>

    <!-- 获取当日完成任务详细分析 - 按任务类型分组 -->
    <select id="getDailyCompletionByTaskType" resultType="com.xinghuo.allegro.collect.model.analysis.DailyCompletionModel">
        SELECT
            t.task_type as taskType,
            CASE
                WHEN t.task_type = 'C' THEN '类目采集'
                WHEN t.task_type = 'S' THEN '卖家采集'
                WHEN t.task_type = 'P' THEN '产品采集'
                WHEN t.task_type = 'CNEW' THEN '类目新上采集'
                ELSE t.task_type
            END as taskTypeDesc,
            COUNT(*) as completedTaskCount,
            SUM(CASE WHEN t.total_sales_num > 0 THEN 1 ELSE 0 END) as salesTaskCount,
            'taskType' as groupBy
        FROM zz_collect_task t
        WHERE t.status = 2
        AND DATE(t.finish_time) = #{date}
        <if test="filterTaskType != null and filterTaskType != ''">
            AND t.task_type = #{filterTaskType}
        </if>
        <if test="filterClientId != null and filterClientId != ''">
            AND t.client_id LIKE CONCAT('%', #{filterClientId}, '%')
        </if>
        GROUP BY t.task_type
        ORDER BY t.task_type
    </select>

    <!-- 获取当日完成任务详细分析 - 按客户端分组 -->
    <select id="getDailyCompletionByClient" resultType="com.xinghuo.allegro.collect.model.analysis.DailyCompletionModel">
        SELECT
            t.client_id as clientId,
            COUNT(*) as completedTaskCount,
            SUM(CASE WHEN t.total_sales_num > 0 THEN 1 ELSE 0 END) as salesTaskCount,
            'client' as groupBy
        FROM zz_collect_task t
        WHERE t.status = 2
        AND DATE(t.finish_time) = #{date}
        AND t.client_id IS NOT NULL
        <if test="filterTaskType != null and filterTaskType != ''">
            AND t.task_type = #{filterTaskType}
        </if>
        <if test="filterClientId != null and filterClientId != ''">
            AND t.client_id LIKE CONCAT('%', #{filterClientId}, '%')
        </if>
        GROUP BY t.client_id
        ORDER BY completedTaskCount DESC
    </select>

    <!-- 获取当日完成任务详细分析 - 最细粒度 -->
    <select id="getDailyCompletionDetails" resultType="com.xinghuo.allegro.collect.model.analysis.DailyCompletionModel">
        SELECT
            hour_val as hour,
            CONCAT(hour_val, ':00-', hour_val+1, ':00') as timeSlot,
            task_type as taskType,
            CASE
                WHEN task_type = 'C' THEN '类目采集'
                WHEN task_type = 'S' THEN '卖家采集'
                WHEN task_type = 'P' THEN '产品采集'
                WHEN task_type = 'CNEW' THEN '类目新上采集'
                ELSE task_type
            END as taskTypeDesc,
            client_id as clientId,
            completedTaskCount,
            salesTaskCount,
            'details' as groupBy
        FROM (
            SELECT
                HOUR(t.finish_time) as hour_val,
                t.task_type as task_type,
                t.client_id as client_id,
                COUNT(*) as completedTaskCount,
                SUM(CASE WHEN t.total_sales_num > 0 THEN 1 ELSE 0 END) as salesTaskCount
            FROM zz_collect_task t
            WHERE t.status = 2
            AND DATE(t.finish_time) = #{date}
            <if test="filterTaskType != null and filterTaskType != ''">
                AND t.task_type = #{filterTaskType}
            </if>
            <if test="filterClientId != null and filterClientId != ''">
                AND t.client_id LIKE CONCAT('%', #{filterClientId}, '%')
            </if>
            GROUP BY HOUR(t.finish_time), t.task_type, t.client_id
        ) sub
        ORDER BY hour_val, task_type, client_id
    </select>

</mapper>
