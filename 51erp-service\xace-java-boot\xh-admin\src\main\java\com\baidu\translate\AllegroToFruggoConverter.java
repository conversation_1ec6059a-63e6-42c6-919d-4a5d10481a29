package com.baidu.translate;

import com.baidu.translate.util.Utf8mb4FilterRegex;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.common.util.json.JsonXhUtil;
import lombok.Data;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.sql.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * allegro 转换 fruugo 的数据
 */
public class AllegroToFruggoConverter {

    private static final String JDBC_URL = "*********************************************";
    //    private static final String JDBC_URL = "***********************************************";
    private static final String DB_USER = "root";
    private static final String DB_PASSWORD = "xh884813@@@XH";
    private static final String TEMPLATE_PATH = "d:/fruugo/数据demo-马帮数据样式.xlsx";
    private static final String OUTPUT_DIR = "d:/fruugo/export/";
    private static final int RECORDS_PER_FILE = 5000;
    private static final int TOTAL_RECORDS = 80000;
    private static final String Manufacturer="";

    public static void main(String[] args) {
        try {
            // Load JDBC driver
            Class.forName("com.mysql.cj.jdbc.Driver");
            // Fetch products from database
            List<FruggoProduct> products = fetchProductsFromDatabase();
            // Export products in batches

            exportProductsInBatches(products);
            System.out.println("Export completed successfully!");


        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    private static List<FruggoProduct> fetchProductsFromDatabase() throws SQLException {
        List<FruggoProduct> products = new ArrayList<>();

        try (Connection conn = DriverManager.getConnection(JDBC_URL, DB_USER, DB_PASSWORD);
             Statement stmt = conn.createStatement()) {

            // Modify this query based on your actual database schema
            String sql = "SELECT * FROM zz_allegro_new_product_en where name is not null and  fruugo_category is not null AND deal_status=1 and  is_instore=0 LIMIT  " + TOTAL_RECORDS;
            ResultSet rs = stmt.executeQuery(sql);

            while (rs.next()) {
                String mainJson = rs.getString("main_json");
                try {
                    AllegroProduct allegroProduct = JsonXhUtil.toBean(mainJson, AllegroProduct.class);
                    if(allegroProduct==null){
                        continue;
                    }
                    FruggoProduct fruggoProduct = convert(allegroProduct);
                    if(fruggoProduct==null){
                        continue;
                    }
                    fruggoProduct.setCategory(rs.getString("fruugo_category"));
                    fruggoProduct.setOriginalPrice(rs.getBigDecimal("price").multiply(new BigDecimal(0.8)).toString());
                    fruggoProduct.setDiscountPrice(rs.getBigDecimal("price").multiply(new BigDecimal(0.4)).toString());
                    fruggoProduct.setCeMark(rs.getString("ce"));
                    fruggoProduct.setSafetyWarnings(rs.getString("safe"));
                    products.add(fruggoProduct);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }

        return products;
    }

    private static void exportProductsInBatches(List<FruggoProduct> products) {
        int totalFiles = (products.size() + RECORDS_PER_FILE - 1) / RECORDS_PER_FILE;

        for (int i = 0; i < totalFiles; i++) {
            int fromIndex = i * RECORDS_PER_FILE;
            int toIndex = Math.min(fromIndex + RECORDS_PER_FILE, products.size());

            List<FruggoProduct> batch = products.subList(fromIndex, toIndex);

            // Generate filename with timestamp
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String filename = OUTPUT_DIR + "fruugo_export_" + timestamp + "_batch" + (i + 1) + ".xlsx";

            exportToNewExcel(batch, TEMPLATE_PATH, filename);

            System.out.println("Exported batch " + (i + 1) + " of " + totalFiles + " to " + filename);
        }
    }

    public static FruggoProduct convert(AllegroProduct allegroProduct) {
        FruggoProduct fruggoProduct = new FruggoProduct();

        // Set Category
//        fruggoProduct.setCategory(allegroProduct.getCategory().getPath().stream().map(Category::getName).collect(Collectors.joining(" > ")));
        fruggoProduct.setCategory("TEST");


        // Set Manufacturer
//        if(allegroProduct.getParameters()!=null) {
//            allegroProduct.getParameters().stream()
//                    .filter(param -> param.getName().equalsIgnoreCase("Manufacturer code"))
//                    .findFirst()
//                    .ifPresent(param -> fruggoProduct.setManufacturer(param.getValues().get(0)));
//        }else{
            fruggoProduct.setManufacturer(Manufacturer);
//        }

        // Set Brand
//        allegroProduct.getParameters().stream()
//                .filter(param -> param.getName().equalsIgnoreCase("Brand"))
//                .findFirst()
//                .ifPresent(param -> fruggoProduct.setBrand(param.getValuesLabels().get(0)));

        // 生成 SPU（假设使用 GTIN 码的前 10 位）
        String gtin = allegroProduct.getParameters().stream()
                .filter(param -> param.getName().equalsIgnoreCase("EAN (GTIN)"))
                .findFirst()
                .map(param -> param.getValuesLabels().get(0))
                .orElse("0000000000000");

        String spu = gtin.substring(0, Math.min(gtin.length(), 10));
        fruggoProduct.setSpu(spu);

        // 生成 SKU（SPU + 变体编号）
        fruggoProduct.setSku(spu + "-001");
        fruggoProduct.setEanValue(gtin);

        // Set Weight
        if(allegroProduct.getParameters()!=null) {
            allegroProduct.getParameters().stream()
                    .filter(param -> param.getName().equalsIgnoreCase("Weight (with packaging)"))
                    .findFirst()
                    .ifPresent(param -> fruggoProduct.setWeight((int) (Double.parseDouble(param.getValues().get(0)) * 1000)));
        }
        // Set Title
        fruggoProduct.setTitle(formatTitle(allegroProduct.getName()));

        // Set Description (concatenate all text contents)
        System.out.println("====================================" );

        System.out.println(JsonXhUtil.toJSONString(allegroProduct) );

        System.out.println(JsonXhUtil.toJSONString(allegroProduct.getDescription()) );

        if(allegroProduct.getDescription()==null){
             return null;
        }

        String description = allegroProduct.getDescription().getSections().stream()
                .flatMap(section -> section.getItems().stream())
                .filter(item -> item.getType().equalsIgnoreCase("TEXT"))
                .map(Item::getContent)
                .collect(Collectors.joining(" "));
        // Clean HTML tags from description
        description =  cleanHtmlContent(description);
        fruggoProduct.setDescription(description);

        // Set Images (limit to 5)
        List<String> imageUrls = allegroProduct.getImages().stream()
                .map(Image::getUrl)
                .limit(5)
                .collect(Collectors.toList());
        fruggoProduct.setImages(imageUrls);


        // 提取颜色和尺寸信息
        if (allegroProduct.getParameters() != null) {
            Parameter colorParam = allegroProduct.getParameters().stream()
                    .filter(param -> param.getName().toLowerCase().contains("color") ||
                            param.getName().toLowerCase().contains("colour"))
                    .findFirst()
                    .orElse(null);

            Parameter sizeParam = allegroProduct.getParameters().stream()
                    .filter(param -> param.getName().toLowerCase().contains("size"))
                    .findFirst()
                    .orElse(null);

            if (colorParam != null) {
                String colorValue = colorParam.getValuesLabels() != null && !colorParam.getValuesLabels().isEmpty() ?
                        colorParam.getValuesLabels().get(0) : "";
                fruggoProduct.setColor(colorValue);
            }

            if (sizeParam != null) {
                String sizeValue = sizeParam.getValuesLabels() != null && !sizeParam.getValuesLabels().isEmpty() ?
                        sizeParam.getValuesLabels().get(0) : "";
                // 处理尺寸值格式，例如将 "1000,00 cm" 转换为 "1000cm"
                if (StrXhUtil.isNotBlank(sizeValue)) {
                    // 移除千分位逗号并将小数点后为零的部分去掉
                    sizeValue = sizeValue.replaceAll("(,\\d+)(?:,00| cm,00| mm,00)", "$1")
                            .replaceAll(",00\\s*([a-zA-Z]+)", "$1")
                            .replaceAll(",00$", "")
                            // 替换欧洲格式的小数点(逗号)为点号
                            .replaceAll("(\\d+),(\\d+)", "$1.$2")
                            // 删除单位前的空格
                            .replaceAll("\\s+([a-zA-Z]+)", "$1");
                }
                fruggoProduct.setSize(sizeValue);
            }
        }

        return fruggoProduct;
    }



  public static void exportToNewExcel(List<FruggoProduct> products, String templatePath, String newFilePath) {
        try (FileInputStream fis = new FileInputStream(templatePath);
             Workbook workbook = new XSSFWorkbook(fis)) {

            // 获取第一个工作表
            Sheet sheet = workbook.getSheetAt(0);
            int lastRowNum = sheet.getLastRowNum();
            int newRowNum = lastRowNum + 1;
            // Create cell style for wrapped text
            CellStyle wrappedStyle = workbook.createCellStyle();
            wrappedStyle.setWrapText(true);  // Enable text wrapping

            for (FruggoProduct product : products) {
                Row row = sheet.createRow(newRowNum++);

                // 按照FruggoProduct中的字段顺序填充单元格
                row.createCell(2).setCellValue(product.getStoreName()); // 店铺名称
                row.createCell(3).setCellValue(product.getCategory()); // 产品分类
                row.createCell(4).setCellValue(product.getSpu()); // SPU
                row.createCell(5).setCellValue(product.getManufacturer()); // 制造商
                row.createCell(6).setCellValue(product.getBrand()); // 品牌
                row.createCell(7).setCellValue(product.getSku()); // SKU
                row.createCell(8).setCellValue(product.getGtin()); // GTIN类型
                row.createCell(9).setCellValue(product.getEanValue()); // GTIN号码
                row.createCell(10).setCellValue(product.getStockType()); // 库存类型
                row.createCell(11).setCellValue(product.getTimeType()); // 时间类型
                row.createCell(12).setCellValue(product.getTime()); // 时间
                row.createCell(13).setCellValue(product.getStockStatus()); // 库存情况
                row.createCell(14).setCellValue(product.getCurrency()); // 币种
                row.createCell(15).setCellValue(product.getTaxRate()); // 税率
                row.createCell(16).setCellValue(product.getOriginalPrice()); // 含税原价
                row.createCell(17).setCellValue(product.getDiscountPrice()); // 含税折扣价
                row.createCell(18).setCellValue(product.getDiscountStartTime()); // 折扣开始时间
                row.createCell(19).setCellValue(product.getDiscountEndTime()); // 折扣结束时间
                row.createCell(20).setCellValue(product.getWeight()); // 重量(g)
                row.createCell(21).setCellValue(product.getVolume()); // 体积
                row.createCell(22).setCellValue(product.getTitle()); // 标题


                // Create cell for description with special formatting
                Cell descriptionCell = row.createCell(23);
                descriptionCell.setCellValue(product.getDescription()); // 描述
//                descriptionCell.setCellStyle(wrappedStyle);  // Apply wrap text style

                // 设置图片（最多 5 张）
                List<String> images = product.getImages();
                for (int i = 0; i < 5; i++) {
                    if (i < images.size()) {
                        row.createCell(24 + i).setCellValue(images.get(i)); // 图片1~5
                    } else {
                        row.createCell(24 + i).setCellValue(""); // 空白填充
                    }
                }

                // 其他额外字段
                row.createCell(29).setCellValue(product.getSafetyWarnings()); // Safety Warnings
                row.createCell(30).setCellValue(product.getCeMark()); // CE Mark
                row.createCell(31).setCellValue(product.getIngredients()); // Ingredients
                row.createCell(32).setCellValue(product.getSerialNumber()); // Serial Number
                row.createCell(33).setCellValue(product.getManufacturerPartNumber()); // Manufacturer Part Number
                row.createCell(34).setCellValue(product.getModelNumber()); // Model Number
                row.createCell(35).setCellValue(product.getBatchNumber()); // Batch Number
                row.createCell(36).setCellValue(product.getSafetyAttestation()); // Safety Attestation


                // 从 AM 列开始添加颜色和尺寸信息 (AM 列索引为 38)
                int variantColIndex = 38;

                // 根据是否有颜色和尺寸，设置不同格式的变体信息
                if ( StrXhUtil.isNotBlank(product.getColor()) && StrXhUtil.isNotBlank(product.getSize())) {
                    // 两者都有
                    row.createCell(variantColIndex++).setCellValue("Colour");
                    row.createCell(variantColIndex++).setCellValue(product.getColor());
                    row.createCell(variantColIndex++).setCellValue("Size");
                    row.createCell(variantColIndex++).setCellValue(product.getSize());
                } else if ( StrXhUtil.isNotBlank(product.getColor())) {
                    // 只有颜色
                    row.createCell(variantColIndex++).setCellValue("Colour");
                    row.createCell(variantColIndex++).setCellValue(product.getColor());
                } else if (StrXhUtil.isNotBlank(product.getSize())) {
                    // 只有尺寸
                    row.createCell(variantColIndex++).setCellValue("Size");
                    row.createCell(variantColIndex++).setCellValue(product.getSize());
                }

            }

            // 保存为新文件
            try (FileOutputStream fos = new FileOutputStream(newFilePath)) {
                workbook.write(fos);
            }

        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private static String cleanHtmlContent(String htmlContent) {
        if (htmlContent == null || htmlContent.trim().isEmpty()) {
            return "";
        }

        // Replace paragraph tags with newline characters
        String content = htmlContent.replaceAll("<[pP][^>]*>", "")
                .replaceAll("</[pP]>", "\n")
                .replaceAll("<[bB][rR][^>]*>", "\n");

        // Remove all other HTML tags
        content = content.replaceAll("<[^>]*>", "");

        // Replace multiple spaces with a single space
//        content = content.replaceAll("\\s+", " ");

        // Replace multiple newlines with double newline
        content = content.replaceAll("\n+", "\n\n");

        // 过滤表情标签
        content = Utf8mb4FilterRegex.filterSymbols(content);

        // Trim leading/trailing whitespace
        return content.trim();
    }

    /**
     * 修改标题，使其首字母大写，其他字母小写
     *
     * @param title 原始标题
     * @return 修改后的标题
     */
    public static String formatTitle(String title) {
        if (title == null || title.isEmpty()) {
            return title;
        }
        String[] words = title.split(" ");
        StringBuilder formattedTitle = new StringBuilder();
        for (String word : words) {
            if (word.length() > 1) {
                formattedTitle.append(Character.toUpperCase(word.charAt(0)))
                        .append(word.substring(1).toLowerCase());
            } else {
                formattedTitle.append(word.toUpperCase());
            }
            formattedTitle.append(" ");
        }
        return formattedTitle.toString().trim();
    }


    @Data
    public static class FruggoProduct {
        private String storeName = "示例店铺"; // C列 店铺名称
        private String category; // D列 产品分类
        private String spu; // E列 SPU
        private String manufacturer; // F列 制造商
        private String brand="Unbranded"; // G列 品牌
        private String sku; // H列 SKU
        private String gtin="EAN"; // I列 GTIN（类型/号码）
        private String eanValue; // J列 GTIN（类型/号码）
        private String stockType="INSTOCK"; // K列 库存类型
        private String timeType="准备时间（天）"; // L列 时间类型
        private String time="7"; // M列 时间
        private String stockStatus="200"; // N列 库存情况
        private String currency="GBP"; // O列 币种
        private String taxRate="0.0%"; // P列 税率
        private String originalPrice; // Q列 含税原价
        private String discountPrice; // R列 含税折扣价
        private String discountStartTime="2025/02/18"; // S列 折扣开始时间
        private String discountEndTime="2045/02/18"; // T列 折扣结束时间
        private int weight=100; // U列 重量（g）
        private String volume="1"; // V列 体积（cm³）
        private String title; // W列 标题
        private String description; // X列 描述信息
        private List<String> images; // Y列- AC列 图片1-5
        private String safetyWarnings=""; // AD列 Safety Warnings
        private String ceMark=""; // AE列 CE Mark
        private String ingredients=""; // AF列 Ingredients
        private String serialNumber=""; // AG列 Serial Number
        private String manufacturerPartNumber=""; // AH列 Manufacturer Part Number
        private String modelNumber=""; // AI列 Model Number
        private String batchNumber=""; // AJ列 Batch Number
        private String safetyAttestation=""; // AK列 Safety Attestation
        private String color=""; // AL列 颜色
        private String size=""; // AM列 尺寸

    }

    @Data
    public static class AllegroProduct {
        private String id;
        private String name;
        private Category category;
        private List<Parameter> parameters;
        private List<Image> images;
        private Description description;

    }

    @Data
    public static class Category {
        private String id;
        private String name;
        private List<Category> path;
    }

    @Data
    public static class Parameter {
        private String id;
        private String name;
        private List<String> values;
        private List<String> valuesLabels;
    }

    @Data
    public static class Image {
        private String url;

    }

    @Data
    public static class Description {
        private List<Section> sections;

    }

    @Data
    public static class Section {
        private List<Item> items;

    }

    @Data
    public static class Item {
        private String type;
        private String content;

    }
}
