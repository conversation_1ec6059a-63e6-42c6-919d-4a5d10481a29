package com.xinghuo.allegro.data.service;

import com.xinghuo.allegro.data.entity.AllegroDataTaskEntity;
import com.xinghuo.allegro.collect.model.collect.CollectTaskPagination;
import com.xinghuo.common.base.service.BaseService;

import java.util.List;

/**
 * Allegro 英文采集任务服务接口
 * 
 * <AUTHOR>
 * @date 2024-11-05
 */
public interface AllegroDataTaskService extends BaseService<AllegroDataTaskEntity> {

    /**
     * 分页查询采集任务列表
     * @param pagination 分页查询参数
     * @return 采集任务列表
     */
    List<AllegroDataTaskEntity> getList(CollectTaskPagination pagination);

    /**
     * 获取待处理的任务
     * @param clientId 客户端ID
     * @param taskType 任务类型
     * @param platform 平台
     * @return 待处理的任务
     */
    AllegroDataTaskEntity waitGets(String clientId, String taskType, String platform);

    /**
     * 更新采集任务状态
     * @param taskId 任务ID
     * @return 更新结果
     */
    int updateCollectTask(String taskId);

    /**
     * 检查是否存在指定产品的任务
     * @param productId 产品ID
     * @return 是否存在
     */
    boolean existTask(String productId);

    /**
     * 获取待处理任务数量
     * @param taskType 任务类型
     * @return 待处理任务数量
     */
    long getPendingTaskCount(String taskType);
}
