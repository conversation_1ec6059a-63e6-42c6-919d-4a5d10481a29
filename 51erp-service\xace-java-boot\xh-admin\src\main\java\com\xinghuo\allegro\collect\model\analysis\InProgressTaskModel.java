package com.xinghuo.allegro.collect.model.analysis;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 进行中任务模型
 * <AUTHOR>
 */
@Data
public class InProgressTaskModel {

    @Schema(description = "任务ID")
    private String taskId;

    @Schema(description = "任务类型")
    private String taskType;

    @Schema(description = "任务类型描述")
    private String taskTypeDesc;

    @Schema(description = "类目路径")
    private String categoryPath;

    @Schema(description = "采集客户端ID")
    private String clientId;

    @Schema(description = "已采集数量")
    private Integer collectedCount;

    @Schema(description = "已采集有销售额数量")
    private Integer salesCount;

    @Schema(description = "开始采集时间")
    private Date startTime;

    @Schema(description = "采集链接")
    private String collectLink;

    @Schema(description = "优先级")
    private Integer priority;

    @Schema(description = "采集进度（百分比）")
    private Double progress;
}
