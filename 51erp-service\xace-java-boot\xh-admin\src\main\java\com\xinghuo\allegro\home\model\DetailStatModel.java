package com.xinghuo.allegro.home.model;

import lombok.Builder;
import lombok.Data;

/**
 * 详情后台采集
 */
@Data
@Builder
public class DetailStatModel {

   /**
     * 今日后台采集条数
     */
    private Long todayCollect;
    /**
     * 今日删除条数
     */
    private Long todayDelete;
    /**
     * 昨天后台采集
     */
    private Long yesterdayCollect;
    /**
     * 待处理有销售  WAIT_SALE_SUM
     */
    private String  waitSaleSum;
    /**
     *  累计待采集  ALL_WAIT_COLLECT_SUM
     */
    private String  allWaitCollectSum;
    /**
     * 累计已采集数
     */
    private String  allCollectSum;


}
