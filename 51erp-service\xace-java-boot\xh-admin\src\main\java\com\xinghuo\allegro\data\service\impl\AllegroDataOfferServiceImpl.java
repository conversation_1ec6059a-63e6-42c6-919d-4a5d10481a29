package com.xinghuo.allegro.data.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xinghuo.allegro.data.dao.AllegroDataOfferMapper;
import com.xinghuo.allegro.data.entity.AllegroDataOfferEntity;
import com.xinghuo.allegro.data.service.AllegroDataOfferService;
import com.xinghuo.common.base.service.BaseServiceImpl;
import com.xinghuo.common.util.core.StrXhUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Allegro 英文采集商品服务实现
 *
 * <AUTHOR>
 * @date 2024-11-05
 */
@Slf4j
@Service
public class AllegroDataOfferServiceImpl extends BaseServiceImpl<AllegroDataOfferMapper, AllegroDataOfferEntity> implements AllegroDataOfferService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBatch(String taskType, List<AllegroDataOfferEntity> entityList) {
        if (entityList == null || entityList.isEmpty()) {
            return;
        }

        Date now = new Date();
        List<AllegroDataOfferEntity> newEntityList = new ArrayList<>();

        for (AllegroDataOfferEntity entity : entityList) {
            // 检查是否已存在相同的 offerId
            if (StrXhUtil.isNotBlank(entity.getOfferId())) {
                AllegroDataOfferEntity existingEntity = this.getById(entity.getOfferId());
                if (existingEntity != null) {
                    log.debug("英文采集数据已存在，跳过保存，offerId: {}", entity.getOfferId());
                    continue;
                }
            }

            // 设置基础字段
            if (entity.getCreatedAt() == null) {
                entity.setCreatedAt(now);
            }
            entity.setLastUpdatedAt(now);

            // 设置默认状态
            if (entity.getRequestStatus() == null) {
                entity.setRequestStatus(0); // 未请求
            }
            if (entity.getDealStatus() == null) {
                entity.setDealStatus(0); // 未处理
            }
            if (entity.getPriority() == null) {
                entity.setPriority(1); // 默认优先级
            }

            newEntityList.add(entity);
        }

        // 批量保存新数据
        if (!newEntityList.isEmpty()) {
            super.saveBatch(newEntityList); // 调用父类的 saveBatch 方法
            log.info("批量保存英文采集商品数据完成，任务类型: {}, 原始数量: {}, 实际保存数量: {}",
                    taskType, entityList.size(), newEntityList.size());
        } else {
            log.info("所有英文采集商品数据已存在，跳过保存，任务类型: {}, 数量: {}", taskType, entityList.size());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBatch(List<AllegroDataOfferEntity> entityList) {
        this.saveBatch("DEFAULT", entityList);
    }

    @Override
    public List<AllegroDataOfferEntity> waitGets() {
        LambdaQueryWrapper<AllegroDataOfferEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AllegroDataOfferEntity::getRequestStatus, 0) // 未请求状态
                .orderByDesc(AllegroDataOfferEntity::getPriority) // 按优先级排序
                .orderByAsc(AllegroDataOfferEntity::getCreatedAt) // 按创建时间排序
                .last("LIMIT 100"); // 限制返回数量

        List<AllegroDataOfferEntity> result = this.list(queryWrapper);

        // 更新状态为请求中
        if (!result.isEmpty()) {
            Date now = new Date();
            for (AllegroDataOfferEntity entity : result) {
                entity.setRequestStatus(1); // 请求中
                entity.setRequestTime(now);
                entity.setLastUpdatedAt(now);
            }
            this.updateBatchById(result);

            log.info("获取待处理英文采集商品数据，数量: {}", result.size());
        }

        return result;
    }

    @Override
    public boolean checkNewSellerIdExists(String sellerId) {
        if (StrXhUtil.isBlank(sellerId)) {
            return false;
        }

        LambdaQueryWrapper<AllegroDataOfferEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AllegroDataOfferEntity::getSellerId, sellerId);

        long count = this.count(queryWrapper);
        return count > 0;
    }

    @Override
    public AllegroDataOfferEntity getByOfferNameAndImageUrl(String offerName, String imageUrl, String excludeId) {
        if (StrXhUtil.isBlank(offerName) || StrXhUtil.isBlank(imageUrl)) {
            return null;
        }

        LambdaQueryWrapper<AllegroDataOfferEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AllegroDataOfferEntity::getOfferName, offerName)
                .eq(AllegroDataOfferEntity::getImageUrl, imageUrl);

        if (StrXhUtil.isNotBlank(excludeId)) {
            queryWrapper.ne(AllegroDataOfferEntity::getOfferId, excludeId);
        }

        queryWrapper.last("LIMIT 1");

        return this.getOne(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void dealData(AllegroDataOfferEntity offerEntity) {
        if (offerEntity == null) {
            return;
        }

        try {
            // 更新处理状态
            offerEntity.setDealStatus(1); // 已处理
            offerEntity.setRequestStatus(2); // 已完成
            offerEntity.setReqFinishTime(new Date());
            offerEntity.setLastUpdatedAt(new Date());

            // 增加请求次数
            Integer reqNum = offerEntity.getReqNum();
            if (reqNum == null) {
                reqNum = 0;
            }
            offerEntity.setReqNum(reqNum + 1);

            this.updateById(offerEntity);

            log.info("处理英文采集商品数据完成，offerId: {}", offerEntity.getOfferId());

        } catch (Exception e) {
            log.error("处理英文采集商品数据失败，offerId: {}", offerEntity.getOfferId(), e);
            throw e;
        }
    }
}
