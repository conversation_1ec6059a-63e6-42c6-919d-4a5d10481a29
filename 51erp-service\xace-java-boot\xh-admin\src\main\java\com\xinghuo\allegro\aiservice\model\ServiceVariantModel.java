package com.xinghuo.allegro.aiservice.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class ServiceVariantModel {

    /** 主键，自增 */
    @Schema(description = "")
    private Long id;

    /** 标题ID，关联到 zz_service_title 表 */
    @Schema(description = "标题ID")
    private Long taskId;


    /** 所属平台（如 QWEN, XUNFEI） */
    @Schema(description = "所属平台（如 QWEN, XUNFEI）")
    private String platform;

    private Integer taskType;

    private List<String> variantTextList;

}