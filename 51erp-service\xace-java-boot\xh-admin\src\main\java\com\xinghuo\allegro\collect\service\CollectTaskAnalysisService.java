package com.xinghuo.allegro.collect.service;

import com.xinghuo.allegro.collect.model.analysis.*;

import java.util.List;

/**
 * 采集任务分析服务接口
 * <AUTHOR>
 */
public interface CollectTaskAnalysisService {

    /**
     * 获取待采集任务总数
     * @return 待采集任务数量
     */
    Long getPendingTaskCount();

    /**
     * 获取采集中任务数
     * @return 采集中任务数量
     */
    Long getInProgressTaskCount();

    /**
     * 获取今日已完成任务数
     * @param date 查询日期（YYYY-MM-DD），可选，默认当天
     * @return 今日完成任务数量
     */
    Long getCompletedTodayCount(String date);

    /**
     * 获取今日采集总量（Offer数）
     * @param date 查询日期（YYYY-MM-DD），可选，默认当天
     * @return 今日采集Offer总数量
     */
    Long getCollectedTodayOffers(String date);

    /**
     * 获取今日有销售额采集量
     * @param date 查询日期（YYYY-MM-DD），可选，默认当天
     * @return 今日有销售额Offer数量
     */
    Long getSalesTodayOffers(String date);

    /**
     * 获取今日活跃客户端数
     * @param date 查询日期（YYYY-MM-DD），可选，默认当天
     * @return 今日活跃客户端数量
     */
    Long getActiveClientsToday(String date);

    /**
     * 获取进行中任务列表
     * @param clientId 采集客户端ID，可选
     * @param taskType 任务类型，可选
     * @param page 页码，默认1
     * @param pageSize 每页数量，默认10
     * @return 进行中任务列表
     */
    List<InProgressTaskModel> getInProgressTaskList(String clientId, String taskType, Integer page, Integer pageSize);

    /**
     * 获取任务积压与优先级分布
     * @param taskType 任务类型，可选
     * @return 任务状态统计列表
     */
    List<TaskStatusSummaryModel> getTaskSummaryByStatusPriority(String taskType);

    /**
     * 获取任务类型状态统计
     * @param date 查询日期，可选
     * @return 任务类型状态统计列表
     */
    List<TaskStatusSummaryModel> getTaskSummaryByTypeStatus(String date);



    /**
     * 获取当日完成任务详细分析
     * @param date 分析日期（YYYY-MM-DD），可选，默认当天
     * @param groupBy 分组维度（hour/taskType/client），可选
     * @param filterTaskType 任务类型筛选，可选
     * @param filterClientId 客户端ID筛选，可选
     * @return 当日完成任务分析列表
     */
    List<DailyCompletionModel> getDailyCompletionDetails(String date, String groupBy,
                                                         String filterTaskType, String filterClientId);
}
