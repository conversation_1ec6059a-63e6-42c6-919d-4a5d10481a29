package com.xinghuo.allegro.collect.service;

import com.alicp.jetcache.anno.CacheInvalidate;
import com.xinghuo.allegro.collect.entity.CollectConfigEntity;
import com.xinghuo.allegro.collect.model.config.ExpressConfigModel;
import com.xinghuo.allegro.order.model.express.ExpressFeeSettingModel;
import com.xinghuo.common.base.service.BaseService;

public interface CollectConfigService extends BaseService<CollectConfigEntity> {

    String CACHE_NAME =  "COLLECT_CONFIG";

//    @Cached(name = CACHE_NAME,  expire = 360)
    CollectConfigEntity getInfo(String configType, String tenantId);

    @CacheInvalidate(name = CACHE_NAME,allEntries = true)
    boolean save(CollectConfigEntity entity);


    @CacheInvalidate(name = CACHE_NAME,allEntries = true)
    boolean update(CollectConfigEntity entity);


    ExpressFeeSettingModel getExpressFeeSetting(String tenantId);

    /**
     * 获取IOSS编号
     */
    String getIoss();

    /**
     * 获取提现费率
     */




    ExpressConfigModel getExpressConfig();

}
