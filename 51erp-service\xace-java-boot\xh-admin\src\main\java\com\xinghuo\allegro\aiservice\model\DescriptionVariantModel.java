package com.xinghuo.allegro.aiservice.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class DescriptionVariantModel {

    /** 主键，自增 */
    @Schema(description = "")
    private Long id;

    /** 标题ID，关联到 zz_service_title 表 */
    @Schema(description = "关联ID")
    private Long descriptionId;

    /** 变体标题 */
    @Schema(description = "变体标题")
    private String variantDescription;

    /** 标题长度 */
    @Schema(description = "标题长度")
    private Integer length;

    /** 所属平台（如 QWEN, XUNFEI） */
    @Schema(description = "所属平台（如 QWEN, XUNFEI）")
    private String platform;
}