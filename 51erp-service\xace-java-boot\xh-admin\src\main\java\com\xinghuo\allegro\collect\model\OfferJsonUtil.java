package com.xinghuo.allegro.collect.model;

import cn.hutool.core.io.FileUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.xinghuo.allegro.collect.entity.CollectOfferEntity;
import com.xinghuo.allegro.collect.model.collect.OfferParseForm;
import com.xinghuo.allegro.push.entity.ShelfTemplateEntity;
import com.xinghuo.allegro.util.AllegroConstant;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.common.util.json.JsonXhUtil;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;


@Slf4j
public class OfferJsonUtil {

    public static String getSellerId(JsonNode offerJson) {
        if (offerJson == null) {
            log.warn("详情解析卖家数据，offerJson is null");
            return null;
        }
        if (offerJson.has("offer") && offerJson.get("offer").has("seller")) {
            return offerJson.get("offer").get("seller").get("id").asText();
        }
        return null;
    }

    public static boolean isPL(JsonNode offerJson) {
        if (offerJson == null) {
            log.warn("详情解析卖家数据，offerJson is null");
            return false;
        }
        String country = "";
        if (offerJson.get("offer").has("shipping") && offerJson.get("offer").get("shipping").has("delivery") && offerJson.get("offer").get("shipping").get("delivery").has("from")
                && offerJson.get("offer").get("shipping").get("delivery").get("from").has("country")) {
            country = offerJson.path("offer").path("shipping").path("delivery").path("from").path("country").asText();
            log.debug("country:{}",country);
            if(country.equals("Polska")){
                return true;
            }
        }
        return false;
    }


    public static String checkProductIdExists(JsonNode offerJson) {
        if (offerJson == null) {
            log.warn("详情解析卖家数据，offerJson is null");
            return null;
        }
        String country = "";
        if (offerJson.get("offer").has("product") && offerJson.get("offer").get("product").has("id") ) {
            String productId = offerJson.path("offer").path("product").path("id").asText();
            if(StrXhUtil.isNotBlank(productId)){
                return productId;
            }

        }
        return null;
    }


    /**select  offer_json ,
     JSON_UNQUOTE(JSON_EXTRACT(p.offer_json, '$.offer.shipping.delivery.cheapestMethod.price.amount'))
     from zz_collect_offer_parse_tmp p
     where  JSON_UNQUOTE(JSON_EXTRACT(p.offer_json, '$.offer.location.city'))='FUZHOU'
     AND JSON_UNQUOTE(JSON_EXTRACT(p.offer_json, '$.offer.shipping.delivery.cheapestMethod.price.amount'))='5.00'
     limit 100
     * 福州不发货的数据
     * @param offerJson
     * @return
     */
    public static boolean isFZ(JsonNode offerJson) {
        if (offerJson == null) {
            log.warn("详情解析卖家数据，offerJson is null");
            return false;
        }
        BigDecimal price = BigDecimal.ZERO;
        if (offerJson.get("offer").has("shipping") && offerJson.get("offer").get("shipping").has("delivery") && offerJson.get("offer").get("shipping").get("delivery").has("cheapestMethod")
                && offerJson.get("offer").get("shipping").get("delivery").get("cheapestMethod").has("price")) {
            price = BigDecimal.valueOf(offerJson.path("offer").path("shipping").path("delivery").path("cheapestMethod").path("price").path("amount").asDouble());
        }
        String city = "";
        if(offerJson.get("offer").has("location") && offerJson.get("offer").get("location").has("city")){
            city = offerJson.path("offer").path("location").path("city").asText();
        }

        if((StrXhUtil.equalsIgnoreCase(city,"fuzhou") && price.compareTo(BigDecimal.valueOf(5))==0)){
            log.info("fuzhou:{}",price);
            return true;
        }

        return false;
    }


    public static String contructImagerUrl(String imageUrl) {
        int count = 0;
        int index = 0;

        // 循环查找第六个“/”的位置
        for (int i = 0; i < imageUrl.length(); i++) {
            if (imageUrl.charAt(i) == '/') {
                count++;
                if (count == 6) {
                    index = i;
                    break;
                }
            }
        }
        if(index == 0)
            index = imageUrl.length();

        // 截取从开始到第六个“/”的子字符串
        String extractedUrl = imageUrl.substring(0, index).replace("/s180/", "/original/");
        return extractedUrl;
    }

    public static String trunTitle75(String str) {
        int maxLength=75;
        if (str == null || maxLength <= 0 || str.getBytes().length <= maxLength) {
            return str;
        }
        str = str.substring(0, Math.min(str.length(), maxLength));
        while (str.getBytes().length > maxLength) {
            int lastSpaceIndex = str.lastIndexOf(' ');
            if (lastSpaceIndex == -1) {
                break;
            }
            str = str.substring(0, lastSpaceIndex);
        }
        return str;
    }

    public static String minOfferJson(String str) {
        try {
            JsonNode jsonNode = JsonXhUtil.parseObject(str);
            List<String> fieldsToKeep = Arrays.asList("category", "assortmentCategoryPath", "delivery", "descriptions", "id", "images", "location", "name",
                    "parameters", "parametersGroups", "popularity", "product", "sellingMode", "shipping","seller");
            // 创建一个新的ObjectNode
            ObjectNode newRootNode = JsonXhUtil.createObjectNode();
            ObjectNode newNode = JsonXhUtil.createObjectNode();

            // 遍历原始JsonNode并仅保留指定的节点
            jsonNode.get("offer").fields().forEachRemaining(entry -> {
                if (fieldsToKeep.contains(entry.getKey())) {
                    newNode.set(entry.getKey(), entry.getValue());
                }
            });
//            ObjectNode sellerNode = JsonXhUtil.createObjectNode();
//            sellerNode.put("id", jsonNode.get("offer").get("seller").get("id").asText());
//            //保存sellerId
//            newNode.set("seller", sellerNode);
            newRootNode.set("offer", newNode);
            return newRootNode.toString();
        }catch(Exception e){
            log.error("minOfferJson error",e);
        }
        return str;
    }

    public static String refactorTitle(String title, ShelfTemplateEntity templateEntity) {
        String result = title;

        // 检查模板是否有效
        if (templateEntity != null && StrXhUtil.isNotBlank(templateEntity.getTitleContent())) {
            boolean isPrefix = templateEntity.getTitlePosition() != null && templateEntity.getTitlePosition() == 0;
            String templateContent = templateEntity.getTitleContent();
            String candidate = isPrefix ? (templateContent + " " + title) : (title + " " + templateContent);

            // 仅在拼接后长度不超过75时使用拼接结果
            if (candidate.length() <= 75) {
                result = candidate;
            }
        }

        // 确保结果不超过70字符
        if (result.length() > 75) {
            result = result.substring(0, 75);
        }

        return result;
    }

    public static String refactorDescription(String str, ShelfTemplateEntity templateEntity) {
        // Parse the root JSON object
        JsonNode rootNode = JsonXhUtil.parseObject(str);
        ArrayNode sectionsArray = (ArrayNode) rootNode.path("sections");

        // Clean up existing sections
        for (JsonNode sectionNode : sectionsArray) {
            // Process items array in each section
            for (JsonNode itemNode : sectionNode.path("items")) {
                if (itemNode.isObject()) {
                    ObjectNode objectNode = (ObjectNode) itemNode;
                    objectNode.retain("type", "url", "content");
                    // 处理 content 字段，转义 HTML 实体
                    // 处理 content 字段
                    if (objectNode.has("content")) {
                        String originalContent = objectNode.get("content").asText();
                        // 只替换独立的 `&`，避免 `&nbsp;` 被重复替换
                        String escapedContent = originalContent.replaceAll("&(?!nbsp;)", "&nbsp;");
                        objectNode.put("content", escapedContent);
                    }
                }
            }
        }

        // Add new description content if provided
        if (templateEntity != null &&  StrXhUtil.isNotBlank(templateEntity.getDescriptionContent())) {
            ObjectNode newSection = JsonXhUtil.createObjectNode();
            ArrayNode newItemsArray = JsonXhUtil.createArrayNode();
            ObjectNode itemNode = JsonXhUtil.createObjectNode();

            itemNode.put("type", "TEXT");
            itemNode.put("content", "<p>"+ templateEntity.getDescriptionContent().trim()+"</p>");
            newItemsArray.add(itemNode);
            newSection.set("items", newItemsArray);

            // Add new section based on position
            if (templateEntity.getDescriptionPosition() != null &&
                    templateEntity.getDescriptionPosition() == 0) {
                // Add new section to the beginning
                sectionsArray.insert(0, newSection);
            } else {
                // Add new section to the end
                sectionsArray.add(newSection);
            }
        }

        return JsonXhUtil.toJSONString(rootNode);
    }



    /**
     * 补充collectOffer 实体
     * @param offerParseForm
     * @param collectOfferEntity
     */
    public static void constructOfferEntity(OfferParseForm offerParseForm, CollectOfferEntity collectOfferEntity){
        JsonNode offerJsonNode = offerParseForm.getOfferJson().get("offer");

        String sellerId = getSellerId(offerParseForm.getOfferJson());
        collectOfferEntity.setSellerId(sellerId);
        JsonNode productJsonNode = offerJsonNode.get("product");
        String productId = productJsonNode.path("id").asText(null);
        String categoryId = offerJsonNode.path("category").path("id").asText(null);
        BigDecimal price = BigDecimal.ZERO;
        BigDecimal shipFee = BigDecimal.ZERO;
        int buyersQuantity = 0;


        if (offerJsonNode.path("sellingMode").path("buyNow").path("price").path("sale").has("amount")) {
            price = new BigDecimal(offerJsonNode.path("sellingMode").path("buyNow").path("price").path("sale").path("amount").asText());
        }

        if (offerJsonNode.path("shipping").path("delivery").path("cheapestMethod").path("price").has("amount")) {
            shipFee = new BigDecimal(offerJsonNode.path("shipping").path("delivery").path("cheapestMethod").path("price").path("amount").asText());
        }

        if (offerJsonNode.has("popularity") && offerJsonNode.path("popularity").has("buyersQuantity")) {
            buyersQuantity = offerJsonNode.path("popularity").path("buyersQuantity").asInt();
        }

        String country = "";
        if (offerJsonNode.get("shipping").has("delivery") && offerJsonNode.get("shipping").get("delivery").has("from")) {
            country = offerJsonNode.path("shipping").path("delivery").path("from").path("country").asText();
        }

        //处理imageUrl
        //20240907 判断首页图片
        if (StrXhUtil.isBlank(collectOfferEntity.getImageUrl())) {
            if (offerParseForm.getOfferJson().get("offer").has("images")) {
                ArrayNode imagesNode = (ArrayNode) offerParseForm.getOfferJson().get("offer").get("images");
                if (!imagesNode.isEmpty()) {
                    String imageUrl = imagesNode.get(0).path("url").asText();
                    if (StrXhUtil.isNotBlank(imageUrl)) {
                        collectOfferEntity.setImageUrl(OfferJsonUtil.contructImagerUrl(imageUrl));
                    }
                }
            }
        }

        // 20240629 处理新的发货方式处理
        JsonNode develiveryJsonNode = offerJsonNode.get("delivery");
        ArrayNode summaryArrayNode = (ArrayNode) develiveryJsonNode.get("summary");
        //从summary 获取
        for (JsonNode item : summaryArrayNode) {
            JsonNode analyticTagNode = item.get("analyticTag");
            if (analyticTagNode != null && "CountryOfDispatch".equals(analyticTagNode.asText())) {
                JsonNode valueNode = item.get("value");
                if (valueNode != null) {
                    JsonNode textNode = valueNode.get("text");
                    if (textNode != null) {
                        String countryOfDispatch = textNode.asText();
                        collectOfferEntity.setDispatchCountry(countryOfDispatch);
                        System.out.println("Country of Dispatch: " + countryOfDispatch);
                    }
                }
            }
            if (analyticTagNode != null && "DeliveryTime".equals(analyticTagNode.asText())) {
                JsonNode valueNode = item.get("name");
                if (valueNode != null) {
                    JsonNode textNode = valueNode.get("text");
                    if (textNode != null) {
                        String deliveryTime = textNode.asText();
                        collectOfferEntity.setDeliveryTime(deliveryTime);
                        System.out.println("Country of Dispatch: " + deliveryTime);
                    }
                }
            }
        }

        ArrayNode groupNode = (ArrayNode) offerJsonNode.get("shipping").get("delivery").get("groups");
        int deliveryCount = 0;
        for (JsonNode group : groupNode) {
            if ("PL".equalsIgnoreCase(group.get("countryCode").asText())) {
                ArrayNode deliveryMethodsNode = (ArrayNode) group.get("deliveryMethods");
                deliveryCount += deliveryMethodsNode.size();

            }
        }
        collectOfferEntity.setDeliveryCount(deliveryCount);
        collectOfferEntity.setLastModifyTime(new Date());
        BigDecimal totalPrice = price.add(shipFee);
        collectOfferEntity.setOfferName(offerJsonNode.path("name").asText());
        collectOfferEntity.setCountry(country);
        collectOfferEntity.setProductId(productId);
       collectOfferEntity.setNewProductId(productId);
        collectOfferEntity.setCategoryId(categoryId);
        collectOfferEntity.setPrice(price);
        collectOfferEntity.setShipFee(shipFee);
        collectOfferEntity.setTotalPrice(totalPrice);
        collectOfferEntity.setBuyersQuantity(buyersQuantity);
        collectOfferEntity.setDetailDealStatus(Boolean.TRUE);
        collectOfferEntity.setRequestStatus(AllegroConstant.REQUEST_STATUS_FINISH);
        collectOfferEntity.setReqFinishTime(new Date());
        collectOfferEntity.setDetailClientId(offerParseForm.getClientId());
        collectOfferEntity.setParseStatus(AllegroConstant.PARSE_STATUS_VALID);
    }




    public  static void main(String[] args) {
//        System.out.println(contructImagerUrl("https://a.allegroimg.com/original/11e9ec/efcb74554994a42f3c68573adac0/LAKIER-SAMOCHODOWY-USUWAJACY-ZARYSOWANIA-NAPRAWA-POLEROWANIE-TKANINY"));
//        System.out.println(contructImagerUrl("https://a.allegroimg.com/original/11e9ec/efcb74554994a42f3c68573adac0/"));
//        System.out.println(contructImagerUrl("https://a.allegroimg.com/original/11e9ec/efcb74554994a42f3c68573adac0"));
//        System.out.println(contructImagerUrl("https://a.allegroimg.com/original/11e9ec"));

//        String str = FileUtil.readString("g:\\1.json", "UTF-8");
//        System.out.println(minOfferJson(str));
//        System.out.println(trunTitle75("40 SZTUK UROCZYCH OŁÓWKÓW DO PISANIA OŁÓWKI DREWNIANE OŁÓWKI DO SZKICOWANIA HALLOWEEN PATT"));
//        System.out.println(trunTitle75("40 SZTUK UROCZYCH OŁÓWKÓW DO PISANIA OŁÓWKI DREWNIANE OŁÓWKI"));

//        String str = FileUtil.readString("g:\\fuzhou.json", "UTF-8");
//        String str2 = FileUtil.readString("g:\\NOfuzhou.json", "UTF-8");
//
//        JsonNode jsonNode = JsonXhUtil.parseObject(str);
//        JsonNode jsonNode2 = JsonXhUtil.parseObject(str2);
//        System.out.println(isFZ(jsonNode));
//        System.out.println(isFZ(jsonNode2));

        String str = FileUtil.readString("g:\\1.json", "UTF-8");
        JsonNode jsonNode = JsonXhUtil.parseObject(str);

        String str3 = refactorDescription(jsonNode.get("offer").get("descriptions").get("standardized").toString(),null);

        System.out.println(str3);

        ShelfTemplateEntity templateEntity = new ShelfTemplateEntity();
        templateEntity.setDescriptionContent("test");
        templateEntity.setDescriptionPosition(1);
        String str2 = refactorDescription(jsonNode.get("offer").get("descriptions").get("standardized").toString(),templateEntity);
        System.out.println(str2);


    }
}
