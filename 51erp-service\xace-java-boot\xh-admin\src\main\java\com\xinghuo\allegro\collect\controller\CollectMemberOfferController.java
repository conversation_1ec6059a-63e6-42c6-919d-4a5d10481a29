package com.xinghuo.allegro.collect.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.xinghuo.allegro.collect.entity.CollectConfigEntity;
import com.xinghuo.allegro.collect.entity.CollectOfferEntity;
import com.xinghuo.allegro.data.entity.AllegroSellerEntity;
import com.xinghuo.allegro.collect.model.OfferJsonUtil;
import com.xinghuo.allegro.collect.model.collectOffer.*;
import com.xinghuo.allegro.collect.service.CollectConfigService;
import com.xinghuo.allegro.collect.service.CollectOfferJsonService;
import com.xinghuo.allegro.collect.service.CollectOfferService;
import com.xinghuo.allegro.data.service.AllegroSellerService;
import com.xinghuo.allegro.push.entity.ErpProductEntity;
import com.xinghuo.allegro.push.service.CollectVioWordService;
import com.xinghuo.allegro.push.service.ErpProductService;
import com.xinghuo.allegro.util.UrlExtractor;
import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.constant.MsgCode;
import com.xinghuo.common.exception.DataException;
import com.xinghuo.common.util.UserProvider;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.common.util.json.JsonXhUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;
import java.util.regex.Pattern;

import static com.xinghuo.allegro.util.AllegroConstant.SELLER_TYPE_BLOCKED;

/**
 * 产品采集Controller，负责处理产品采集相关的请求。
 * 接受客户端采集的数据上传。
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@Tag(name = "会员采集箱", description = "会员采集箱")
@RequestMapping("/api/allegro/collect/offer")
public class CollectMemberOfferController {

    @Resource
    private CollectOfferService collectOfferService;

    @Resource
    private CollectOfferJsonService collectOfferJsonService;

    @Resource
    private AllegroSellerService allegroSellerService;

    @Resource
    private ErpProductService erpProductService;

    @Resource
    private CollectConfigService collectConfigService;

    @Resource
    private UserProvider userProvider;

    @Resource
    private CollectVioWordService collectVioWordService;

    @Operation(summary = "判断数据是否重复")
    @PostMapping("/verifyDuplicate")
    public ActionResult verifyDuplicate(@RequestBody DuplicateForm form) {
        // 从链接中提取offerId
        String offerId = UrlExtractor.extractOfferId(form.getSourceUrl());
        // 检查offerId是否为数字格式，不是则忽略该Offer
        if (offerId != null && Pattern.matches("\\d+", offerId)) {
            //正常采集
            CollectOfferEntity collectOfferEntity = collectOfferService.getByOfferId(offerId);
            if(collectOfferEntity != null){
                return ActionResult.fail("重复数据，不采集！");
            }
        }
        return ActionResult.success("可采集");
    }


    @Operation(summary = "收集上传的数据")
    @PostMapping("/getSourceUrlEntity")
    public ActionResult getSourceUrlEntity(@RequestBody DuplicateForm form) {
        // 从链接中提取offerId
        log.info("getSourceUrlEntity:开始解析链接：{}", form.getSourceUrl());

        return ActionResult.success("可采集");
    }



    /**
     * 数据校验：检查数据库中是否存在该offerId的数据。
     * 状态更新：若存在，则更新其状态为“已采集”，并记录完成时间和客户端ID。
     * 波兰产品处理：检测是否为波兰本地销售产品，若是则删除该条目。
     * 黑名单卖家检查：检查卖家是否在黑名单中，若是则标记为黑名单并删除。
     * 图片URL处理：若首页图片为空，则尝试从JSON数据中提取并更新。
     * 临时表存储：将处理后的数据存入临时表，并同步卖家信息。
     * 销量检查：若销量为0且存在相同数据，则删除重复数据。
     * 异步处理：保存临时数据后，异步处理数据。
     * @param offerForm
     * @return
     */
    @Operation(summary = "会员采集提交详情页的json数据")
    @PostMapping("/saveOffer")
    @Transactional
    public ActionResult<String> saveOffer(@RequestBody @Valid CollectForm offerForm) {
        String tenantId = userProvider.get().getTenantId();
        CollectConfigEntity configEntity = collectConfigService.getInfo("COLLECT",tenantId);
        if(configEntity == null){
            return ActionResult.fail("请先配置采集参数！" );
        }
        String json = configEntity.getConfig();


            CollectSettingModel settingModel = JsonXhUtil.toBean(json, CollectSettingModel.class);
            if(settingModel == null){
                return ActionResult.fail("采集参数解析失败！请联系管理员！" );
            }
            CollectOfferEntity collectOfferEntity = collectOfferService.refactor(offerForm);
            //对采集数据进行校验
            if(collectOfferEntity!=null){

                if(settingModel.getPriceMode().isEnabled()){
                    //价格校验
                    if(settingModel.getPriceMode().getMin()!=null && collectOfferEntity.getTotalPrice().compareTo(settingModel.getPriceMode().getMin())<0){
                        return ActionResult.fail("采集总价小于采集设置最低价格："+ settingModel.getPriceMode().getMin()+"PLN，不采集。" );
                    }
                    if(settingModel.getPriceMode().getMax()!=null && collectOfferEntity.getTotalPrice().compareTo(settingModel.getPriceMode().getMax())>0){
                        return ActionResult.fail("采集总价大于采集设置最高价格："+settingModel.getPriceMode().getMax() +"PLN，不采集。" );
                    }
                }

                if(settingModel.getCompanyMode().isEnabled()){
                    //国家校验
                    List<String> countryList = settingModel.getCompanyMode().getCountries();
                    if(countryList!=null && !countryList.isEmpty()){
                        boolean isInclude = countryList.contains(collectOfferEntity.getCountry());
                        if(!isInclude){
                            return ActionResult.fail("采集卖家国家："+collectOfferEntity.getCountry()+" 不在采集设置国家列表内，不采集。" );
                        }
                    }
               }
                if(settingModel.getGoodRegionMode().isEnabled()){
                    //地区校验
                    List<String> countryList = settingModel.getGoodRegionMode().getCountries();
                    if(countryList!=null && !countryList.isEmpty()){
                        boolean isInclude = countryList.contains(collectOfferEntity.getDispatchCountry());
                        if(!isInclude){
                            return ActionResult.fail("商品发货地："+collectOfferEntity.getDispatchCountry()+"不在采集设置国家列表内，不采集。" );
                        }
                    }
                }

                if(settingModel.getZeroSaleMode().isVioEnabled()){
                    if(collectOfferEntity.getBuyersQuantity()==0){
                        Set<String> vioWords = collectVioWordService.getValidList(tenantId);
                        // 检查OfferName中是否包含侵权词,
                        boolean containsVioWord = vioWords.stream()
                                .anyMatch(vioWord -> collectOfferEntity.getOfferName().toLowerCase().contains(vioWord.toLowerCase()));
                        // 根据检查结果设置侵权状态
                        if (containsVioWord  ) {

                            List<String> matchingVioWords = vioWords.stream().filter(vioWord -> collectOfferEntity.getOfferName().toLowerCase().contains(vioWord.toLowerCase())).toList();


                            return ActionResult.fail("销量为0，标题包含侵权词:"+  String.join(", ", matchingVioWords) +"，不采集。" );
                        }
                    }
                }
                if(settingModel.getZeroSaleMode().isSameBlockedEnabled()){
                    //销量校验
                    if(collectOfferEntity.getBuyersQuantity()==0){
                        //获取相同产品数据
                        List<CollectOfferEntity> list = collectOfferService.getListBySkuId(collectOfferEntity.getSkuId());
                        if(list!=null && !list.isEmpty()){
                            for(CollectOfferEntity tmpOfferEntity:list){
                                if(tmpOfferEntity.getBuyersQuantity()>0){
                                    return ActionResult.fail("销量为0，存在同样标题和首图的数据，疑似重复数据,不采集。" );
                                }
                            }
                        }
                    }
                }
                if(settingModel.getDuplicateMode().isEnabled()){
                    //重复数据校验
                    CollectOfferEntity tmpOfferEntity = collectOfferService.getByOfferId(collectOfferEntity.getOfferId());
                    if(tmpOfferEntity!=null){
                        return ActionResult.fail("重复offerId数据，不采集" );
                    }
                }
                if(settingModel.getBlackSellerMode().isEnabled()){
                    //黑名单卖家校验
                    String sellerId = OfferJsonUtil.getSellerId(offerForm.getOfferJson());

                    if (StrXhUtil.isNotBlank(sellerId)) {
                        AllegroSellerEntity sellerEntity = allegroSellerService.getSimpleInfoBySellerId(sellerId);
                        if (sellerEntity != null && sellerEntity.getSellType().equals(SELLER_TYPE_BLOCKED)) {
                            return ActionResult.fail("黑名单卖家:"+sellerEntity.getLogin()+" 的数据，不采集。" );

                        }
                    }
                }
                if(settingModel.getNonDeliveryMode().isEnabled()){
                    //不发货地校验
                    JsonNode offerJson = JsonXhUtil.parseObject(offerForm.getOfferJson()).get("offer");
                    String city = "";
                    if(offerJson.has("location") && offerJson.get("location").has("city")){
                        city = offerJson.path("location").path("city").asText();
                    }
                    List<CollectSettingModel.NonDeliverySeller> nonDeliverySellers = settingModel.getNonDeliveryMode().getNonDeliverySellers();
                    if(nonDeliverySellers!=null && !nonDeliverySellers.isEmpty()){
                        for(CollectSettingModel.NonDeliverySeller nonDeliverySeller:nonDeliverySellers){
                            if(nonDeliverySeller.getCity().equals(city)){
                                if(nonDeliverySeller.getShipee().compareTo(collectOfferEntity.getShipFee())==0){
                                    return ActionResult.fail("不发货地："+city+"，运费："+collectOfferEntity.getShipFee()+"PLN，不采集。" );
                                }
                            }
                        }
                    }
                }
            }


            //更新卖家数据数据
            if (offerForm.getOfferJson() != null && offerForm.getOfferJson().has("offer") && offerForm.getOfferJson().get("offer").has("seller")) {
                allegroSellerService.syncSellerData(offerForm.getOfferJson().get("offer"));
            }
            try {
                collectOfferService.dealDataMember(offerForm,settingModel,tenantId);
            } catch (Exception e) {
                log.error("保存采集数据失败", e);
                return ActionResult.fail("保存采集数据失败" );
            }
            return ActionResult.success("成功保存数据！" );
    }


    @Operation(summary = "采集Offer列表查询")
    @PostMapping("/getList")
    public ActionResult<PageListVO<CollectOfferModel>> list(@RequestBody CollectOfferPagination pagination) {
        try {
            List<CollectOfferEntity> list = collectOfferService.getList(pagination);
            List<CollectOfferModel> listVO = BeanCopierUtils.copyList(list, CollectOfferModel.class);

            // 补充卖家信息
            if (!listVO.isEmpty()) {
                listVO.forEach(item -> {
                    if (StrXhUtil.isNotBlank(item.getSellerId())) {
                        AllegroSellerEntity sellerEntity = allegroSellerService.getSimpleInfoBySellerId(item.getSellerId());
                        if (sellerEntity != null) {
                            item.setSellerListUrl(sellerEntity.getListingUrl());
                        }
                    }
                });
            }

            PaginationVO page = BeanCopierUtils.copy(pagination, PaginationVO.class);
            return ActionResult.page(listVO, page);
        } catch (Exception e) {
            log.error("查询采集Offer列表失败", e);
            return ActionResult.fail("查询失败: " + e.getMessage());
        }
    }

    @Operation(summary = "采集数据-详情")
    @GetMapping("/{id}")
    @Parameters({
            @Parameter(name = "id", description = "主键", required = true)
    })
    public ActionResult info(@PathVariable("id") String id) {
        CollectOfferEntity entity = collectOfferService.getById(id);
        CollectOfferModel infoVo = BeanCopierUtils.copy(entity, CollectOfferModel.class);
        return ActionResult.success(infoVo);
    }


    @DeleteMapping("/{id}")
    @Parameters({
            @Parameter(name = "id", description = "主键", required = true)
    })
    @Operation(summary = "采集数据-删除")
    public ActionResult delete(@PathVariable("id") String id) throws DataException {
        CollectOfferEntity entity = collectOfferService.getById(id);

        if (entity != null) {
            //判断数据是否被作为外键引用
            ErpProductEntity erpProductEntity = erpProductService.getByCpId(id,userProvider.get().getTenantId());
            if(erpProductEntity != null){
                return ActionResult.fail("数据已被SKU引用，无法删除，请到SKU=>推送产品管理中(V3用户)删除。" );
            }
            collectOfferService.removeById(id);
            collectOfferJsonService.removeById(id);
            log.info("删除采集数据：{}", id);
            return ActionResult.success(MsgCode.SU003.get());
        }
        return ActionResult.fail(MsgCode.FA002.get());
    }


    @Operation(summary = "采集数据统计")
    @PostMapping("/sumList")
    public ActionResult<List<SumModel>> sumList(@RequestBody @Valid SumForm form) {
        try {
            List<SumModel> list = collectOfferService.getSumList(form.getBeginTime(), form.getEndTime());
            return ActionResult.success(list);
        } catch (Exception e) {
            log.error("查询采集数据统计失败", e);
            return ActionResult.fail("查询统计失败: " + e.getMessage());
        }
    }



}
