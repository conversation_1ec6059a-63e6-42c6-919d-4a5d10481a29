<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="0be8adcc-e998-4796-b4dd-a83c45220747" name="更改" comment="#57  fyndiq商品认领初步功能。">
      <change afterPath="$PROJECT_DIR$/51erp-service/xace-java-boot/xh-admin/src/main/java/com/xinghuo/amazon/collect/entity/AmazonListTaskEntity.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/51erp-service/xace-java-boot/xh-admin/src/main/java/com/xinghuo/amazon/collect/entity/AmazonPageTaskEntity.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/51erp-service/xace-java-boot/xh-admin/src/main/java/com/xinghuo/amazon/collect/model/list/AmazonListTaskContentForm.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/51erp-service/xace-java-boot/xh-admin/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/51erp-service/xace-java-boot/xh-admin/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/51erp-service/xace-java-boot/xh-admin/src/main/java/com/xinghuo/allegro/push/controller/AllegroCategoryController.java" beforeDir="false" afterPath="$PROJECT_DIR$/51erp-service/xace-java-boot/xh-admin/src/main/java/com/xinghuo/allegro/push/controller/AllegroCategoryController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/51erp-service/xace-java-boot/xh-admin/src/main/java/com/xinghuo/fruugo/collect/service/impl/FruugoExportServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/51erp-service/xace-java-boot/xh-admin/src/main/java/com/xinghuo/fruugo/collect/service/impl/FruugoExportServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/51erp-service/xace-java-boot/xh-admin/src/main/java/com/xinghuo/fyndiq/data/entity/FyndiqDataProductEntity.java" beforeDir="false" afterPath="$PROJECT_DIR$/51erp-service/xace-java-boot/xh-admin/src/main/java/com/xinghuo/fyndiq/data/entity/FyndiqDataProductEntity.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/51erp-service/xace-java-boot/xh-admin/src/main/java/com/xinghuo/fyndiq/data/service/impl/FyndiqDataProductServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/51erp-service/xace-java-boot/xh-admin/src/main/java/com/xinghuo/fyndiq/data/service/impl/FyndiqDataProductServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/51erp-service/xace-java-boot/xh-admin/src/main/java/com/xinghuo/fyndiq/data/service/impl/FyndiqExportFruugoServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/51erp-service/xace-java-boot/xh-admin/src/main/java/com/xinghuo/fyndiq/data/service/impl/FyndiqExportFruugoServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/51erp-service/xace-java-boot/xh-admin/src/main/resources/application-vpn.yml" beforeDir="false" afterPath="$PROJECT_DIR$/51erp-service/xace-java-boot/xh-admin/src/main/resources/application-vpn.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/51erp-service/xace-java-boot/xh-admin/src/main/resources/application.yml" beforeDir="false" afterPath="$PROJECT_DIR$/51erp-service/xace-java-boot/xh-admin/src/main/resources/application.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/51erp-web/51erp-web-vue3/src/components/registerGlobComp.ts" beforeDir="false" afterPath="$PROJECT_DIR$/51erp-web/51erp-web-vue3/src/components/registerGlobComp.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/51erp-web/51erp-web-vue3/src/views/allegro/push/api.ts" beforeDir="false" afterPath="$PROJECT_DIR$/51erp-web/51erp-web-vue3/src/views/allegro/push/api.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/51erp-web/51erp-web-vue3/src/views/allegro/push/components/editDrawer.vue" beforeDir="false" afterPath="$PROJECT_DIR$/51erp-web/51erp-web-vue3/src/views/allegro/push/components/editDrawer.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/51erp-web/51erp-web-vue3/src/views/fyndiq/collect/product/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/51erp-web/51erp-web-vue3/src/views/fyndiq/collect/product/index.vue" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="CodeInsightWorkspaceSettings">
    <option name="optimizeImportsOnTheFly" value="true" />
  </component>
  <component name="CompilerWorkspaceConfiguration">
    <option name="MAKE_PROJECT_ON_SAVE" value="true" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Enum" />
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/51erp-service" />
    <option name="ROOT_SYNC" value="DONT_SYNC" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="jar://E:/.m2/repository/org/springframework/spring-tx/6.1.5/spring-tx-6.1.5.jar!/org/springframework/dao/DataIntegrityViolationException.class" root0="SKIP_INSPECTION" />
  </component>
  <component name="JRebelWorkspace">
    <option name="jrebelEnabledAutocompile" value="true" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\java\apache-maven-3.6.3" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\java\apache-maven-3.6.3\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="2kC5Ff0hS8RRW7nl7ed8VywYy7d" />
  <component name="ProjectLevelVcsManager">
    <OptionsSetting value="false" id="Update" />
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Maven.51erp-boot [clean].executor": "Run",
    "Maven.51erp-boot [compile].executor": "Run",
    "Maven.51erp-boot [install].executor": "Run",
    "Maven.51erp-boot [package].executor": "Run",
    "Maven.xh-admin [clean].executor": "Run",
    "Maven.xh-admin [compile].executor": "Run",
    "Maven.xh-admin [install].executor": "Run",
    "Maven.xh-admin [package].executor": "Run",
    "Maven.xh-admin [verify].executor": "Run",
    "Maven.xh-java-boot [clean].executor": "Run",
    "Maven.xh-java-boot [package].executor": "Run",
    "Maven.xh-oauth-api [install].executor": "Run",
    "Maven.xh-oauth-biz [install].executor": "Run",
    "Maven.xh-oauth-controller [install].executor": "Run",
    "Maven.xh-oauth-entity [install].executor": "Run",
    "Maven.xh-system [clean].executor": "Run",
    "Maven.xh-system [install].executor": "Run",
    "Maven.xh-system [package].executor": "Run",
    "Maven.xh-tenant [clean].executor": "Run",
    "Maven.xh-tenant [install].executor": "Run",
    "Maven.xh-tenant [package].executor": "Run",
    "Notification.DisplayName-DoNotAsk-Database detector": "数据库检测器",
    "Notification.DisplayName-DoNotAsk-JavaScript: Code Vision Performance": "JavaScript: Code Vision 性能观察程序",
    "Notification.DoNotAsk-Database detector": "true",
    "Notification.DoNotAsk-JavaScript: Code Vision Performance": "true",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "Spring Boot.XhAdminApplication .executor": "Debug",
    "Spring Boot.XhAdminApplication.executor": "Debug",
    "Spring Boot.XhTenantApplication (1).executor": "Debug",
    "Spring Boot.XhTenantApplication.executor": "JRebel Debug",
    "extract.method.default.visibility": "private",
    "git-widget-placeholder": "master",
    "kotlin-language-version-configured": "true",
    "last_directory_selection": "G:/v2/51erp/51erp-service/xace-java-boot/xh-admin/src/main/java/com/xinghuo/niceapi/yanwenexpress",
    "last_opened_file_path": "G:/v2/51erp/51erp-service/xace-java-boot/xh-admin/src/main/java/com/xinghuo/fyndiq/data/entity",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.standard": "true",
    "node.js.detected.package.stylelint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.standard": "",
    "node.js.selected.package.stylelint": "G:\\v2\\51erp\\51erp-web\\51erp-web-vue3\\node_modules\\stylelint",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "库",
    "project.structure.proportion": "0.15",
    "project.structure.side.proportion": "0.45517242",
    "run.configurations.included.in.services": "true",
    "settings.editor.selected.configurable": "preferences.pluginManager",
    "ts.external.directory.path": "G:\\v2\\51erp\\51erp-web\\51erp-web-vue3\\node_modules\\typescript\\lib",
    "vue.rearranger.settings.migration": "true",
    "应用程序.AfterSalesAPI.executor": "Debug",
    "应用程序.AlibabaAPI.executor": "Run",
    "应用程序.AllegroAuthController.executor": "Run",
    "应用程序.AllegroToFruggoConverter.executor": "Debug",
    "应用程序.AmazonHtmlParseTest.executor": "Run",
    "应用程序.BillingEntryAPI.executor": "Run",
    "应用程序.CategoryParser.executor": "Run",
    "应用程序.CheckLabelForm.executor": "Debug",
    "应用程序.CountryListQuery.executor": "Run",
    "应用程序.CoupangCategoryApi.executor": "Run",
    "应用程序.CoupangCategoryMetaApiExample.executor": "Run",
    "应用程序.CoupangProductApi.executor": "Run",
    "应用程序.CoupangReturnShippingExample.executor": "Debug",
    "应用程序.CoupangShippingApiExample.executor": "Run",
    "应用程序.CoupangWingApiClient.executor": "Run",
    "应用程序.DownloadAndParseSitemaps.executor": "Debug",
    "应用程序.ExchangeUtil.executor": "Debug",
    "应用程序.Fpx4Client.executor": "Debug",
    "应用程序.FruugoImageUtil.executor": "Debug",
    "应用程序.HmacReturnbyday.executor": "Run",
    "应用程序.HutoolImageFormatChecker.executor": "Run",
    "应用程序.ImageCompare.executor": "Debug",
    "应用程序.ImageSimilarity.executor": "Run",
    "应用程序.ImageSimilarityHistogram.executor": "Run",
    "应用程序.ImageToBase64.executor": "Run",
    "应用程序.MessageAPI.executor": "Run",
    "应用程序.NewOfferAPI.executor": "Run",
    "应用程序.OfferJsonUtil.executor": "Debug",
    "应用程序.OfferPatchAPI.executor": "Debug",
    "应用程序.OfferSyncAPI.executor": "Debug",
    "应用程序.OrderAPI.executor": "Debug",
    "应用程序.OzonApiRequest.executor": "Run",
    "应用程序.OzonCategoryAPITest.executor": "Run",
    "应用程序.OzonProductAPITest.executor": "Run",
    "应用程序.OzonPromoDemo.executor": "Debug",
    "应用程序.Sample.executor": "Run",
    "应用程序.ShipRuleModel.executor": "Run",
    "应用程序.SignUtil.executor": "Run",
    "应用程序.SitemapParser.executor": "Run",
    "应用程序.SkuIdUtil.executor": "Run",
    "应用程序.TestBASE64.executor": "Debug",
    "应用程序.TestMain.executor": "Run",
    "应用程序.TransApi.executor": "Debug",
    "应用程序.TranslateFileWithMapping.executor": "Run",
    "应用程序.UniqueWordsExtractor.executor": "Run",
    "应用程序.Utf8mb4FilterRegex.executor": "Run",
    "应用程序.WebHookAPI.executor": "Debug",
    "应用程序.YanwenExpressClient.executor": "Run",
    "应用程序.YunexpressUtil.executor": "Run",
    "应用程序.ZtOpenapiSdkTests.executor": "Run"
  },
  "keyToStringList": {
    "GitStage.ChangesTree.GroupingKeys": [
      "directory",
      "module",
      "repository"
    ]
  }
}]]></component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RebelAgentSelection">
    <selection>jr</selection>
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="G:\v2\51erp\51erp-service\xace-java-boot\xh-admin\src\main\java\com\xinghuo\fyndiq\data\entity" />
      <recent name="G:\v2\51erp\51erp-service\xace-resources\DocumentFile" />
      <recent name="G:\v2\51erp\51erp-service\xace-java-boot\xh-admin\src\main\resources\DocumentFile" />
      <recent name="G:\v2\51erp\51erp-web" />
      <recent name="G:\v2\51erp\51erp-service\xace-java-boot" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="G:\v2\51erp\51erp-service\xace-java-boot\xh-admin\src\main\java\com\xinghuo\allegro\collect\dao\mapper" />
      <recent name="G:\v2\51erp\51erp-web\51erp-web-vue3\src\views\allegro\vioword\components" />
      <recent name="G:\v2\51erp\51erp-service\xace-java-boot" />
      <recent name="G:\v2\51erp\51erp-service\xace-java-boot\xh-admin\src\main\java\com\xinghuo\allegro\push\dao\mapper" />
      <recent name="G:\v2\51erp\51erp-service\xace-java-boot\xh-admin\src\main\java\com\xinghuo\allegro\sale\dao\mapper" />
    </key>
    <key name="MoveClassesOrPackagesDialog.RECENTS_KEY">
      <recent name="com.xinghuo.permission.model.user.vo" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.xinghuo.amazon.collect.model.list" />
      <recent name="com.xinghuo.allegro.data.entity" />
      <recent name="com.xinghuo.fruugo.collect.service.impl" />
      <recent name="com.xinghuo.fruugo.collect.service" />
      <recent name="com.xinghuo.fruugo.data.model" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="MicronautRunConfigurationType" />
        <option value="QuarkusRunConfigurationType" />
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
    <option name="hiddenConfigurations">
      <map>
        <entry key="SpringBootApplicationConfigurationType">
          <value>
            <set>
              <option value="XhTenantApplication" />
              <option value="未命名" />
            </set>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="RunManager" selected="应用程序.AmazonHtmlParseTest">
    <configuration name="AllegroToFruggoConverter" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.baidu.translate.AllegroToFruggoConverter" />
      <module name="xh-admin" />
      <shortenClasspath name="ARGS_FILE" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.baidu.translate.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="AmazonHtmlParseTest" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.xinghuo.amazon.test.AmazonHtmlParseTest" />
      <module name="xh-admin" />
      <shortenClasspath name="ARGS_FILE" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.xinghuo.amazon.test.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="FruugoImageUtil" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.xinghuo.fruugo.collect.util.FruugoImageUtil" />
      <module name="xh-admin" />
      <shortenClasspath name="ARGS_FILE" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.xinghuo.fruugo.collect.util.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ImageCompare" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.baidu.translate.ImageCompare" />
      <module name="xh-admin" />
      <shortenClasspath name="ARGS_FILE" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.baidu.translate.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="TestBASE64" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.baidu.translate.TestBASE64" />
      <module name="xh-admin" />
      <shortenClasspath name="ARGS_FILE" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.baidu.translate.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="Application" factoryName="Application">
      <shortenClasspath name="ARGS_FILE" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="未命名" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="SPRING_BOOT_MAIN_CLASS" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="XhAdminApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="xh-admin" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.xinghuo.admin.XhAdminApplication" />
      <option name="VM_PARAMETERS" value="--add-opens java.base/java.net=ALL-UNNAMED --add-opens java.base/sun.net.www.protocol.https=ALL-UNNAMED" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="XhTenantApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="xh-tenant" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.xinghuo.tenant.XhTenantApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="Spring Boot.XhAdminApplication" />
      <item itemvalue="Spring Boot.XhTenantApplication" />
      <item itemvalue="Spring Boot.未命名" />
      <item itemvalue="应用程序.AmazonHtmlParseTest" />
      <item itemvalue="应用程序.FruugoImageUtil" />
      <item itemvalue="应用程序.ImageCompare" />
      <item itemvalue="应用程序.TestBASE64" />
      <item itemvalue="应用程序.AllegroToFruggoConverter" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="应用程序.AmazonHtmlParseTest" />
        <item itemvalue="应用程序.AllegroToFruggoConverter" />
        <item itemvalue="应用程序.FruugoImageUtil" />
        <item itemvalue="应用程序.ImageCompare" />
        <item itemvalue="应用程序.TestBASE64" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-a94e463ab2e7-intellij.indexing.shared.core-IU-243.26053.27" />
        <option value="bundled-js-predefined-d6986cc7102b-1632447f56bf-JavaScript-IU-243.26053.27" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="StructureViewState">
    <option name="selectedTab" value="逻辑" />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="0be8adcc-e998-4796-b4dd-a83c45220747" name="更改" comment="" />
      <created>1722774639099</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1722774639099</updated>
      <workItem from="1722774639111" duration="5354000" />
      <workItem from="1722851642220" duration="8002000" />
      <workItem from="1722905720161" duration="660000" />
      <workItem from="1722914687951" duration="3701000" />
      <workItem from="1722949366022" duration="583000" />
      <workItem from="1722985899717" duration="544000" />
      <workItem from="1723000909310" duration="2528000" />
      <workItem from="1723039341808" duration="1053000" />
      <workItem from="1723078947719" duration="1040000" />
      <workItem from="1723092917010" duration="2933000" />
      <workItem from="1723120430659" duration="4728000" />
      <workItem from="1723197736828" duration="670000" />
      <workItem from="1723201890348" duration="11303000" />
      <workItem from="1723278802856" duration="11517000" />
      <workItem from="1723305499837" duration="24248000" />
      <workItem from="1723375910397" duration="12944000" />
      <workItem from="1723422867439" duration="8371000" />
      <workItem from="1723555014439" duration="5233000" />
      <workItem from="1723592894751" duration="16414000" />
      <workItem from="1723688598931" duration="5024000" />
      <workItem from="1723733990904" duration="1613000" />
      <workItem from="1723769917921" duration="1947000" />
      <workItem from="1723780480080" duration="14670000" />
      <workItem from="1723856298792" duration="608000" />
      <workItem from="1723860942421" duration="23597000" />
      <workItem from="1723936545090" duration="32699000" />
      <workItem from="1724028149984" duration="9662000" />
      <workItem from="1724059102436" duration="2815000" />
      <workItem from="1724071002959" duration="8321000" />
      <workItem from="1724116490719" duration="1091000" />
      <workItem from="1724158615302" duration="8248000" />
      <workItem from="1724168150600" duration="748000" />
      <workItem from="1724209769236" duration="2601000" />
      <workItem from="1724228838324" duration="677000" />
      <workItem from="1724245121812" duration="7074000" />
      <workItem from="1724318262801" duration="7834000" />
      <workItem from="1724331401254" duration="8728000" />
      <workItem from="1724375535483" duration="7292000" />
      <workItem from="1724417632977" duration="130000" />
      <workItem from="1724417809457" duration="100000" />
      <workItem from="1724417974686" duration="8823000" />
      <workItem from="1724455305478" duration="29082000" />
      <workItem from="1724541696275" duration="7756000" />
      <workItem from="1724550865174" duration="17337000" />
      <workItem from="1724580997773" duration="288000" />
      <workItem from="1724581556916" duration="12722000" />
      <workItem from="1724633578467" duration="1645000" />
      <workItem from="1724667053504" duration="8540000" />
      <workItem from="1724714404340" duration="11617000" />
      <workItem from="1724763668497" duration="1604000" />
      <workItem from="1724770815172" duration="2662000" />
      <workItem from="1724821561457" duration="9702000" />
      <workItem from="1724840729134" duration="14016000" />
      <workItem from="1724893088766" duration="1104000" />
      <workItem from="1724933909076" duration="4254000" />
      <workItem from="1724981378690" duration="99000" />
      <workItem from="1725023698939" duration="3754000" />
      <workItem from="1725060441708" duration="21558000" />
      <workItem from="1725145619053" duration="10338000" />
      <workItem from="1725194381828" duration="8795000" />
      <workItem from="1725237345611" duration="2262000" />
      <workItem from="1725249769740" duration="11570000" />
      <workItem from="1725318020120" duration="1413000" />
      <workItem from="1725327311584" duration="23000" />
      <workItem from="1725340451629" duration="1770000" />
      <workItem from="1725367753810" duration="3773000" />
      <workItem from="1725419605474" duration="2163000" />
      <workItem from="1725455566760" duration="2728000" />
      <workItem from="1725499018855" duration="896000" />
      <workItem from="1725531713808" duration="1135000" />
      <workItem from="1725582627972" duration="1122000" />
      <workItem from="1725590035592" duration="32220000" />
      <workItem from="1725803299849" duration="216000" />
      <workItem from="1725803689392" duration="5506000" />
      <workItem from="1725837188079" duration="4168000" />
      <workItem from="1725887748872" duration="1661000" />
      <workItem from="1725939015933" duration="1746000" />
      <workItem from="1725952244554" duration="1077000" />
      <workItem from="1726130582553" duration="3749000" />
      <workItem from="1726180637297" duration="3293000" />
      <workItem from="1726202686509" duration="8396000" />
      <workItem from="1726229060530" duration="6092000" />
      <workItem from="1726273011565" duration="6397000" />
      <workItem from="1726328456751" duration="1187000" />
      <workItem from="1726355187677" duration="27932000" />
      <workItem from="1726397423286" duration="9932000" />
      <workItem from="1726449958146" duration="7779000" />
      <workItem from="1726496481281" duration="29116000" />
      <workItem from="1726614087649" duration="18071000" />
      <workItem from="1726653233776" duration="20347000" />
      <workItem from="1726716268732" duration="17378000" />
      <workItem from="1726737055953" duration="71000" />
      <workItem from="1726737226490" duration="5547000" />
      <workItem from="1726753367355" duration="12545000" />
      <workItem from="1726791607909" duration="19937000" />
      <workItem from="1726879464691" duration="16507000" />
      <workItem from="1726978073245" duration="10860000" />
      <workItem from="1727002090172" duration="1231000" />
      <workItem from="1727003473217" duration="8252000" />
      <workItem from="1727070445493" duration="4990000" />
      <workItem from="1727095339820" duration="4915000" />
      <workItem from="1727138701536" duration="10092000" />
      <workItem from="1727183007801" duration="9728000" />
      <workItem from="1727239646813" duration="14925000" />
      <workItem from="1727310717883" duration="4245000" />
      <workItem from="1727345006632" duration="19518000" />
      <workItem from="1727444925695" duration="3708000" />
      <workItem from="1727477157250" duration="1870000" />
      <workItem from="1727491347892" duration="578000" />
      <workItem from="1727492267918" duration="3798000" />
      <workItem from="1727499310743" duration="17895000" />
      <workItem from="1727565538743" duration="980000" />
      <workItem from="1727570219357" duration="9954000" />
      <workItem from="1727688609154" duration="610000" />
      <workItem from="1727708186319" duration="21964000" />
      <workItem from="1727916206315" duration="12336000" />
      <workItem from="1727934855530" duration="20230000" />
      <workItem from="1728034018149" duration="16213000" />
      <workItem from="1728092840115" duration="3234000" />
      <workItem from="1728098421072" duration="22844000" />
      <workItem from="1728178743794" duration="22532000" />
      <workItem from="1728217065083" duration="6549000" />
      <workItem from="1728228056801" duration="2138000" />
      <workItem from="1728258802596" duration="3929000" />
      <workItem from="1728286185572" duration="9248000" />
      <workItem from="1728354119984" duration="904000" />
      <workItem from="1728363937042" duration="713000" />
      <workItem from="1728383993035" duration="10239000" />
      <workItem from="1728430569939" duration="10166000" />
      <workItem from="1728480287857" duration="1615000" />
      <workItem from="1728488196321" duration="905000" />
      <workItem from="1728553738141" duration="2202000" />
      <workItem from="1728564311515" duration="3085000" />
      <workItem from="1728650738880" duration="2184000" />
      <workItem from="1728733341143" duration="8520000" />
      <workItem from="1728880633714" duration="4262000" />
      <workItem from="1728911202398" duration="8600000" />
      <workItem from="1728952715791" duration="4602000" />
      <workItem from="1728996354966" duration="618000" />
      <workItem from="1728998694718" duration="25346000" />
      <workItem from="1729075425001" duration="7707000" />
      <workItem from="1729088092097" duration="8314000" />
      <workItem from="1729145798083" duration="3508000" />
      <workItem from="1729174398111" duration="4837000" />
      <workItem from="1729211706670" duration="681000" />
      <workItem from="1729224598033" duration="681000" />
      <workItem from="1729225938868" duration="4546000" />
      <workItem from="1729312229351" duration="21762000" />
      <workItem from="1729350730100" duration="1695000" />
      <workItem from="1729386400602" duration="15000" />
      <workItem from="1729397734443" duration="5047000" />
      <workItem from="1729426971023" duration="6602000" />
      <workItem from="1729471873857" duration="2153000" />
      <workItem from="1729502026655" duration="277000" />
      <workItem from="1729502366159" duration="598000" />
      <workItem from="1729515205650" duration="9210000" />
      <workItem from="1729557051095" duration="13000" />
      <workItem from="1729558227997" duration="7062000" />
      <workItem from="1729598757940" duration="5845000" />
      <workItem from="1729686816264" duration="6851000" />
      <workItem from="1729773593167" duration="9385000" />
      <workItem from="1729861698497" duration="4916000" />
      <workItem from="1729902512097" duration="16000" />
      <workItem from="1729903217744" duration="31906000" />
      <workItem from="1730001166432" duration="24166000" />
      <workItem from="1730110613309" duration="236000" />
      <workItem from="1730119935979" duration="9004000" />
      <workItem from="1730162070567" duration="10104000" />
      <workItem from="1730293253825" duration="5615000" />
      <workItem from="1730351465972" duration="2418000" />
      <workItem from="1730368339257" duration="1192000" />
      <workItem from="1730384544082" duration="17862000" />
      <workItem from="1730514542277" duration="3475000" />
      <workItem from="1730533176454" duration="495000" />
      <workItem from="1730533941951" duration="798000" />
      <workItem from="1730534819968" duration="14213000" />
      <workItem from="1730598437012" duration="595000" />
      <workItem from="1730616811709" duration="1477000" />
      <workItem from="1730639804290" duration="138000" />
      <workItem from="1730640032897" duration="9123000" />
      <workItem from="1730716257666" duration="11156000" />
      <workItem from="1730765277185" duration="4750000" />
      <workItem from="1730796786879" duration="11196000" />
      <workItem from="1730890095546" duration="9498000" />
      <workItem from="1730959619416" duration="931000" />
      <workItem from="1730972859129" duration="11598000" />
      <workItem from="1731036919441" duration="5898000" />
      <workItem from="1731065731040" duration="2456000" />
      <workItem from="1731130019284" duration="4581000" />
      <workItem from="1731143157353" duration="300000" />
      <workItem from="1731143878382" duration="14566000" />
      <workItem from="1731199156115" duration="16486000" />
      <workItem from="1731294097837" duration="12752000" />
      <workItem from="1731334165571" duration="291000" />
      <workItem from="1731385706918" duration="13683000" />
      <workItem from="1731459505173" duration="1324000" />
      <workItem from="1731494273027" duration="14305000" />
      <workItem from="1731558737855" duration="1832000" />
      <workItem from="1731586788544" duration="9064000" />
      <workItem from="1731654375952" duration="493000" />
      <workItem from="1731683871560" duration="23170000" />
      <workItem from="1731804186114" duration="29267000" />
      <workItem from="1731889835506" duration="1633000" />
      <workItem from="1731896588009" duration="16417000" />
      <workItem from="1731925950055" duration="2569000" />
      <workItem from="1731984790007" duration="19422000" />
      <workItem from="1732185891315" duration="2177000" />
      <workItem from="1732202531320" duration="25000" />
      <workItem from="1732230195259" duration="2997000" />
      <workItem from="1732245959978" duration="17030000" />
      <workItem from="1732346075466" duration="6767000" />
      <workItem from="1732419417862" duration="13924000" />
      <workItem from="1732536650124" duration="182000" />
      <workItem from="1732537211326" duration="6197000" />
      <workItem from="1732546276850" duration="3100000" />
      <workItem from="1732574967247" duration="13721000" />
      <workItem from="1732626859153" duration="1431000" />
      <workItem from="1732715820913" duration="1345000" />
      <workItem from="1732769904540" duration="1756000" />
      <workItem from="1732786879340" duration="5136000" />
      <workItem from="1732840664301" duration="3473000" />
      <workItem from="1732883945242" duration="167000" />
      <workItem from="1732955787277" duration="4040000" />
      <workItem from="1732977294132" duration="3261000" />
      <workItem from="1733021859746" duration="1310000" />
      <workItem from="1733024315466" duration="298000" />
      <workItem from="1733036880774" duration="21204000" />
      <workItem from="1733095505098" duration="12116000" />
      <workItem from="1733148779865" duration="17000" />
      <workItem from="1733149288025" duration="114000" />
      <workItem from="1733186679551" duration="8545000" />
      <workItem from="1733229533878" duration="547000" />
      <workItem from="1733232243019" duration="32484000" />
      <workItem from="1733419234218" duration="213000" />
      <workItem from="1733421237130" duration="619000" />
      <workItem from="1733451428472" duration="2654000" />
      <workItem from="1733467272720" duration="6073000" />
      <workItem from="1733540801196" duration="3134000" />
      <workItem from="1733558231831" duration="6386000" />
      <workItem from="1733628922358" duration="9310000" />
      <workItem from="1733720848674" duration="5657000" />
      <workItem from="1733752554585" duration="7100000" />
      <workItem from="1733839297337" duration="615000" />
      <workItem from="1733842595292" duration="22256000" />
      <workItem from="1733997743278" duration="42364000" />
      <workItem from="1734133862594" duration="450000" />
      <workItem from="1734134374066" duration="2419000" />
      <workItem from="1734154369867" duration="1279000" />
      <workItem from="1734168630441" duration="666000" />
      <workItem from="1734248583610" duration="5982000" />
      <workItem from="1734306191222" duration="5399000" />
      <workItem from="1734340522524" duration="2134000" />
      <workItem from="1734350942315" duration="413000" />
      <workItem from="1734418113905" duration="10465000" />
      <workItem from="1734516609991" duration="13482000" />
      <workItem from="1734606255190" duration="16500000" />
      <workItem from="1734830714613" duration="588000" />
      <workItem from="1734936158815" duration="9357000" />
      <workItem from="1734999047588" duration="438000" />
      <workItem from="1735023044315" duration="432000" />
      <workItem from="1735031779860" duration="9629000" />
      <workItem from="1735096271015" duration="17000" />
      <workItem from="1735097463622" duration="13632000" />
      <workItem from="1735178528118" duration="593000" />
      <workItem from="1735204182778" duration="8184000" />
      <workItem from="1735292163409" duration="3369000" />
      <workItem from="1735305355475" duration="784000" />
      <workItem from="1735439557098" duration="20578000" />
      <workItem from="1735514416218" duration="3146000" />
      <workItem from="1735537809830" duration="630000" />
      <workItem from="1735548646755" duration="13505000" />
      <workItem from="1735609190170" duration="84000" />
      <workItem from="1735626579769" duration="6202000" />
      <workItem from="1735743494393" duration="164000" />
      <workItem from="1735776763365" duration="1594000" />
      <workItem from="1735782487885" duration="2305000" />
      <workItem from="1735823879349" duration="4297000" />
      <workItem from="1735899382469" duration="1636000" />
      <workItem from="1736159216188" duration="1213000" />
      <workItem from="1736160453461" duration="651000" />
      <workItem from="1736161114968" duration="536000" />
      <workItem from="1736205465712" duration="12653000" />
      <workItem from="1736253986583" duration="7483000" />
      <workItem from="1736295409643" duration="13079000" />
      <workItem from="1736339109744" duration="9556000" />
      <workItem from="1736429653891" duration="3555000" />
      <workItem from="1736516074355" duration="59355000" />
      <workItem from="1736770665028" duration="5619000" />
      <workItem from="1736830567316" duration="3261000" />
      <workItem from="1736901497243" duration="8791000" />
      <workItem from="1736988437566" duration="11298000" />
      <workItem from="1737266648210" duration="2780000" />
      <workItem from="1737349102750" duration="3552000" />
      <workItem from="1737423938265" duration="16971000" />
      <workItem from="1737509738209" duration="11500000" />
      <workItem from="1737608893957" duration="232000" />
      <workItem from="1737641306646" duration="2505000" />
      <workItem from="1737677357018" duration="23249000" />
      <workItem from="1737776679336" duration="26661000" />
      <workItem from="1737891290326" duration="12969000" />
      <workItem from="1737939872445" duration="13929000" />
      <workItem from="1738030573281" duration="13649000" />
      <workItem from="1738135941194" duration="1948000" />
      <workItem from="1738194344306" duration="31143000" />
      <workItem from="1738283075783" duration="15140000" />
      <workItem from="1738316634913" duration="12193000" />
      <workItem from="1738336866671" duration="62000" />
      <workItem from="1738364617570" duration="1201000" />
      <workItem from="1738453196755" duration="114000" />
      <workItem from="1738453410777" duration="20600000" />
      <workItem from="1738497413309" duration="31136000" />
      <workItem from="1738593960837" duration="31793000" />
      <workItem from="1738731810431" duration="8772000" />
      <workItem from="1738826098489" duration="8123000" />
      <workItem from="1738897660232" duration="30727000" />
      <workItem from="1739358747252" duration="32187000" />
      <workItem from="1739504174579" duration="114704000" />
      <workItem from="1739861986473" duration="231000" />
      <workItem from="1739862238790" duration="1961000" />
      <workItem from="1739878334892" duration="5890000" />
      <workItem from="1739889234487" duration="70248000" />
      <workItem from="1740141999339" duration="1031000" />
      <workItem from="1740143403293" duration="45638000" />
      <workItem from="1740363304089" duration="14410000" />
      <workItem from="1740405778489" duration="3424000" />
      <workItem from="1740409603053" duration="444000" />
      <workItem from="1740444398785" duration="6210000" />
      <workItem from="1740471847989" duration="17782000" />
      <workItem from="1740541678431" duration="20019000" />
      <workItem from="1740658589730" duration="5201000" />
      <workItem from="1740665820165" duration="3318000" />
      <workItem from="1740726252359" duration="140000" />
      <workItem from="1740743932704" duration="38508000" />
      <workItem from="1740876857749" duration="31781000" />
      <workItem from="1740965202186" duration="5869000" />
      <workItem from="1741003542333" duration="39642000" />
      <workItem from="1741183136902" duration="8518000" />
      <workItem from="1741262435954" duration="12892000" />
      <workItem from="1741348341879" duration="11065000" />
      <workItem from="1741416970779" duration="46413000" />
      <workItem from="1741609376158" duration="8622000" />
      <workItem from="1741662436738" duration="2014000" />
      <workItem from="1741704207183" duration="4146000" />
      <workItem from="1741754985638" duration="11744000" />
      <workItem from="1741787927436" duration="3328000" />
      <workItem from="1741828027527" duration="11492000" />
      <workItem from="1741872340066" duration="5987000" />
      <workItem from="1741999585651" duration="4663000" />
      <workItem from="1742082124693" duration="12264000" />
      <workItem from="1742109423738" duration="14981000" />
      <workItem from="1742262271904" duration="2412000" />
      <workItem from="1742299971996" duration="7289000" />
      <workItem from="1742344914963" duration="1471000" />
      <workItem from="1742388838299" duration="6497000" />
      <workItem from="1742431247901" duration="11596000" />
      <workItem from="1742563850907" duration="39458000" />
      <workItem from="1742808196518" duration="1675000" />
      <workItem from="1742818845118" duration="5969000" />
      <workItem from="1742903562858" duration="8364000" />
      <workItem from="1742979269955" duration="1116000" />
      <workItem from="1742992791722" duration="6370000" />
      <workItem from="1743036131148" duration="2584000" />
      <workItem from="1743077886439" duration="2406000" />
      <workItem from="1743166839338" duration="289000" />
      <workItem from="1743167139389" duration="1162000" />
      <workItem from="1743168316282" duration="600000" />
      <workItem from="1743168933945" duration="13451000" />
      <workItem from="1743328263287" duration="6078000" />
      <workItem from="1743340123543" duration="7620000" />
      <workItem from="1743426737285" duration="8833000" />
      <workItem from="1743509675605" duration="8034000" />
      <workItem from="1743585493731" duration="4260000" />
      <workItem from="1743687325182" duration="272000" />
      <workItem from="1743687694016" duration="39623000" />
      <workItem from="1744007734633" duration="852000" />
      <workItem from="1744072003910" duration="72000" />
      <workItem from="1744120194879" duration="4413000" />
      <workItem from="1744194231852" duration="6000" />
      <workItem from="1744239017283" duration="71000" />
      <workItem from="1744239541376" duration="331000" />
      <workItem from="1744254887030" duration="850000" />
      <workItem from="1744289797056" duration="6457000" />
      <workItem from="1744377637651" duration="11955000" />
      <workItem from="1744467585356" duration="17497000" />
      <workItem from="1744546718568" duration="223000" />
      <workItem from="1744547148547" duration="4561000" />
      <workItem from="1744592034082" duration="2322000" />
      <workItem from="1744636241958" duration="3289000" />
      <workItem from="1744639890596" duration="2384000" />
      <workItem from="1744685921777" duration="2440000" />
      <workItem from="1744702785244" duration="1244000" />
      <workItem from="1744728837791" duration="3068000" />
      <workItem from="1744777902793" duration="3265000" />
      <workItem from="1744809281453" duration="7929000" />
      <workItem from="1744852481384" duration="5570000" />
      <workItem from="1744895261203" duration="4850000" />
      <workItem from="1745047741673" duration="18474000" />
      <workItem from="1745123686281" duration="13624000" />
      <workItem from="1745215531207" duration="2212000" />
      <workItem from="1745240616302" duration="2642000" />
      <workItem from="1745325348549" duration="3394000" />
      <workItem from="1745387337593" duration="3092000" />
      <workItem from="1745585526125" duration="4991000" />
      <workItem from="1745592394719" duration="15336000" />
      <workItem from="1745719364869" duration="721000" />
      <workItem from="1745755096503" duration="4113000" />
      <workItem from="1745814754244" duration="35000" />
      <workItem from="1745814818915" duration="1796000" />
      <workItem from="1745816640777" duration="4024000" />
      <workItem from="1745841775273" duration="9810000" />
      <workItem from="1745890884043" duration="17837000" />
      <workItem from="1746019732593" duration="18443000" />
      <workItem from="1746085600229" duration="9465000" />
      <workItem from="1746277289817" duration="669000" />
      <workItem from="1746278137429" duration="8379000" />
      <workItem from="1746400606991" duration="2779000" />
      <workItem from="1746497758071" duration="11192000" />
      <workItem from="1746535036410" duration="2866000" />
      <workItem from="1746543004321" duration="5881000" />
      <workItem from="1746621151267" duration="326000" />
      <workItem from="1746621493668" duration="29767000" />
      <workItem from="1746752959820" duration="1083000" />
      <workItem from="1746761088570" duration="1795000" />
      <workItem from="1746832051392" duration="3644000" />
      <workItem from="1746840060251" duration="553000" />
      <workItem from="1746854998319" duration="1088000" />
      <workItem from="1746856632177" duration="5926000" />
      <workItem from="1746877193575" duration="3953000" />
      <workItem from="1746978565030" duration="2450000" />
      <workItem from="1747026090011" duration="4985000" />
      <workItem from="1747051028094" duration="9188000" />
      <workItem from="1747090807739" duration="1967000" />
      <workItem from="1747098855391" duration="3317000" />
      <workItem from="1747127811601" duration="14716000" />
      <workItem from="1747228988297" duration="6009000" />
      <workItem from="1747276382089" duration="9638000" />
      <workItem from="1747311117175" duration="8914000" />
      <workItem from="1747370375879" duration="11000" />
      <workItem from="1747397291679" duration="1810000" />
      <workItem from="1747479622322" duration="1360000" />
      <workItem from="1747616319417" duration="607000" />
      <workItem from="1747716443035" duration="1475000" />
      <workItem from="1747789493454" duration="228000" />
      <workItem from="1747791351255" duration="1976000" />
      <workItem from="1747874311986" duration="232000" />
      <workItem from="1747913186877" duration="6575000" />
      <workItem from="1747979426761" duration="14000" />
      <workItem from="1747980686193" duration="3728000" />
      <workItem from="1748002648982" duration="19219000" />
      <workItem from="1748093592092" duration="15078000" />
      <workItem from="1748182302793" duration="2060000" />
      <workItem from="1748220049226" duration="1869000" />
      <workItem from="1748251097815" duration="1143000" />
      <workItem from="1748254839347" duration="10137000" />
      <workItem from="1748308370021" duration="6000" />
      <workItem from="1748348720633" duration="743000" />
      <workItem from="1748349775222" duration="6811000" />
      <workItem from="1748436956434" duration="8766000" />
      <workItem from="1748570663720" duration="361000" />
      <workItem from="1748584957988" duration="2787000" />
      <workItem from="1748609299520" duration="22376000" />
      <workItem from="1748783365410" duration="2874000" />
      <workItem from="1748919617951" duration="2971000" />
      <workItem from="1748930730958" duration="680000" />
      <workItem from="1748949311832" duration="1521000" />
      <workItem from="1748996946743" duration="12213000" />
      <workItem from="1749044021232" duration="2620000" />
      <workItem from="1749091118990" duration="7717000" />
      <workItem from="1749107961599" duration="140000" />
      <workItem from="1749118499652" duration="4223000" />
      <workItem from="1749135606702" duration="3779000" />
      <workItem from="1749171572653" duration="458000" />
      <workItem from="1749206468580" duration="1619000" />
      <workItem from="1749210588861" duration="12423000" />
      <workItem from="1749277626594" duration="11599000" />
      <workItem from="1749297616684" duration="20995000" />
      <workItem from="1749382914311" duration="4421000" />
      <workItem from="1749445218537" duration="7396000" />
      <workItem from="1749558916947" duration="3084000" />
      <workItem from="1749568784504" duration="329000" />
      <workItem from="1749603728672" duration="2318000" />
      <workItem from="1749627436945" duration="1645000" />
      <workItem from="1749691676390" duration="2329000" />
      <workItem from="1749717168581" duration="487000" />
      <workItem from="1749777382803" duration="731000" />
      <workItem from="1749976950599" duration="8580000" />
    </task>
    <task id="LOCAL-00084" summary="#24 侵权值增加批量删除功能。">
      <option name="closed" value="true" />
      <created>1740485856378</created>
      <option name="number" value="00084" />
      <option name="presentableId" value="LOCAL-00084" />
      <option name="project" value="LOCAL" />
      <updated>1740485856378</updated>
    </task>
    <task id="LOCAL-00085" summary="#45 递四方 获取费用的代码。">
      <option name="closed" value="true" />
      <created>1740486530659</created>
      <option name="number" value="00085" />
      <option name="presentableId" value="LOCAL-00085" />
      <option name="project" value="LOCAL" />
      <updated>1740486530659</updated>
    </task>
    <task id="LOCAL-00086" summary="#45 递四方 创建订单的代码。">
      <option name="closed" value="true" />
      <created>1740493119486</created>
      <option name="number" value="00086" />
      <option name="presentableId" value="LOCAL-00086" />
      <option name="project" value="LOCAL" />
      <updated>1740493119486</updated>
    </task>
    <task id="LOCAL-00087" summary="#45 递四方 初步完成递四方代码">
      <option name="closed" value="true" />
      <created>1740660591062</created>
      <option name="number" value="00087" />
      <option name="presentableId" value="LOCAL-00087" />
      <option name="project" value="LOCAL" />
      <updated>1740660591062</updated>
    </task>
    <task id="LOCAL-00088" summary="#49 初步优化coupang代码，统一接口返回响应值">
      <option name="closed" value="true" />
      <created>1740786259353</created>
      <option name="number" value="00088" />
      <option name="presentableId" value="LOCAL-00088" />
      <option name="project" value="LOCAL" />
      <updated>1740786259353</updated>
    </task>
    <task id="LOCAL-00089" summary="#16 做一些在线发布的优化。支持匈牙利的数据推送。">
      <option name="closed" value="true" />
      <created>1740828978193</created>
      <option name="number" value="00089" />
      <option name="presentableId" value="LOCAL-00089" />
      <option name="project" value="LOCAL" />
      <updated>1740828978193</updated>
    </task>
    <task id="LOCAL-00090" summary="#16 增加翻译缓存">
      <option name="closed" value="true" />
      <created>1740833484242</created>
      <option name="number" value="00090" />
      <option name="presentableId" value="LOCAL-00090" />
      <option name="project" value="LOCAL" />
      <updated>1740833484242</updated>
    </task>
    <task id="LOCAL-00091" summary="#16 增加翻译缓存策略，支持数据库配置。 1d">
      <option name="closed" value="true" />
      <created>1740907395481</created>
      <option name="number" value="00091" />
      <option name="presentableId" value="LOCAL-00091" />
      <option name="project" value="LOCAL" />
      <updated>1740907395481</updated>
    </task>
    <task id="LOCAL-00092" summary="#16 订单增加导出excel功能">
      <option name="closed" value="true" />
      <created>1740926361794</created>
      <option name="number" value="00092" />
      <option name="presentableId" value="LOCAL-00092" />
      <option name="project" value="LOCAL" />
      <updated>1740926361794</updated>
    </task>
    <task id="LOCAL-00093" summary="#16 订单增加导出excel功能">
      <option name="closed" value="true" />
      <created>1740977271266</created>
      <option name="number" value="00093" />
      <option name="presentableId" value="LOCAL-00093" />
      <option name="project" value="LOCAL" />
      <updated>1740977271266</updated>
    </task>
    <task id="LOCAL-00094" summary="#16 修复订单留言功能，必须加上订单id">
      <option name="closed" value="true" />
      <created>1741151621234</created>
      <option name="number" value="00094" />
      <option name="presentableId" value="LOCAL-00094" />
      <option name="project" value="LOCAL" />
      <updated>1741151621234</updated>
    </task>
    <task id="LOCAL-00095" summary="#16 修复订单，增加数据权限判断，增加顾客查询功能。">
      <option name="closed" value="true" />
      <created>1741236162910</created>
      <option name="number" value="00095" />
      <option name="presentableId" value="LOCAL-00095" />
      <option name="project" value="LOCAL" />
      <updated>1741236162910</updated>
    </task>
    <task id="LOCAL-00096" summary="#16 修复订单，增加数据权限判断，增加顾客查询功能。">
      <option name="closed" value="true" />
      <created>1741438999819</created>
      <option name="number" value="00096" />
      <option name="presentableId" value="LOCAL-00096" />
      <option name="project" value="LOCAL" />
      <updated>1741438999819</updated>
    </task>
    <task id="LOCAL-00097" summary="#46 在线产品转产品备用库">
      <option name="closed" value="true" />
      <created>1741482721432</created>
      <option name="number" value="00097" />
      <option name="presentableId" value="LOCAL-00097" />
      <option name="project" value="LOCAL" />
      <updated>1741482721432</updated>
    </task>
    <task id="LOCAL-00098" summary="#50 提交采集箱的初始代码">
      <option name="closed" value="true" />
      <created>1741515030490</created>
      <option name="number" value="00098" />
      <option name="presentableId" value="LOCAL-00098" />
      <option name="project" value="LOCAL" />
      <updated>1741515030490</updated>
    </task>
    <task id="LOCAL-00099" summary="#16 增加收件人姓名和电话查询，数据同步。">
      <option name="closed" value="true" />
      <created>1741612002751</created>
      <option name="number" value="00099" />
      <option name="presentableId" value="LOCAL-00099" />
      <option name="project" value="LOCAL" />
      <updated>1741612002751</updated>
    </task>
    <task id="LOCAL-00100" summary="#16 修复订单排序问题">
      <option name="closed" value="true" />
      <created>1741855088593</created>
      <option name="number" value="00100" />
      <option name="presentableId" value="LOCAL-00100" />
      <option name="project" value="LOCAL" />
      <updated>1741855088594</updated>
    </task>
    <task id="LOCAL-00101" summary="#16 修复订单排序问题">
      <option name="closed" value="true" />
      <created>1742086226149</created>
      <option name="number" value="00101" />
      <option name="presentableId" value="LOCAL-00101" />
      <option name="project" value="LOCAL" />
      <updated>1742086226150</updated>
    </task>
    <task id="LOCAL-00102" summary="#16 报关中文名称从订单明细中获取">
      <option name="closed" value="true" />
      <created>1742910900555</created>
      <option name="number" value="00102" />
      <option name="presentableId" value="LOCAL-00102" />
      <option name="project" value="LOCAL" />
      <updated>1742910900555</updated>
    </task>
    <task id="LOCAL-00103" summary="#53 V0.3 ozon 在线产品编辑">
      <option name="closed" value="true" />
      <created>1744471560650</created>
      <option name="number" value="00103" />
      <option name="presentableId" value="LOCAL-00103" />
      <option name="project" value="LOCAL" />
      <updated>1744471560650</updated>
    </task>
    <task id="LOCAL-00104" summary="#51  fruugo 采集器，自动汇总到fruugo product 中。">
      <option name="closed" value="true" />
      <created>1745936361785</created>
      <option name="number" value="00104" />
      <option name="presentableId" value="LOCAL-00104" />
      <option name="project" value="LOCAL" />
      <updated>1745936361786</updated>
    </task>
    <task id="LOCAL-00105" summary="#51  fruugo 店铺管理功能">
      <option name="closed" value="true" />
      <created>1746094885355</created>
      <option name="number" value="00105" />
      <option name="presentableId" value="LOCAL-00105" />
      <option name="project" value="LOCAL" />
      <updated>1746094885356</updated>
    </task>
    <task id="LOCAL-00106" summary="#51  任务自动提交sku总数。">
      <option name="closed" value="true" />
      <created>1746313966045</created>
      <option name="number" value="00106" />
      <option name="presentableId" value="LOCAL-00106" />
      <option name="project" value="LOCAL" />
      <updated>1746313966045</updated>
    </task>
    <task id="LOCAL-00107" summary="#51  任务自动提交sku总数。">
      <option name="closed" value="true" />
      <created>1746318533796</created>
      <option name="number" value="00107" />
      <option name="presentableId" value="LOCAL-00107" />
      <option name="project" value="LOCAL" />
      <updated>1746318533796</updated>
    </task>
    <task id="LOCAL-00108" summary="#51  分析组件的代码">
      <option name="closed" value="true" />
      <created>1746877251957</created>
      <option name="number" value="00108" />
      <option name="presentableId" value="LOCAL-00108" />
      <option name="project" value="LOCAL" />
      <updated>1746877251957</updated>
    </task>
    <task id="LOCAL-00109" summary="#51  分析组件的代码">
      <option name="closed" value="true" />
      <created>1746878620119</created>
      <option name="number" value="00109" />
      <option name="presentableId" value="LOCAL-00109" />
      <option name="project" value="LOCAL" />
      <updated>1746878620119</updated>
    </task>
    <task id="LOCAL-00110" summary="#51  优化销售分析的销售数据更新">
      <option name="closed" value="true" />
      <created>1747064584589</created>
      <option name="number" value="00110" />
      <option name="presentableId" value="LOCAL-00110" />
      <option name="project" value="LOCAL" />
      <updated>1747064584589</updated>
    </task>
    <task id="LOCAL-00111" summary="#51  优化销售分析的销售数据更新">
      <option name="closed" value="true" />
      <created>1747064941505</created>
      <option name="number" value="00111" />
      <option name="presentableId" value="LOCAL-00111" />
      <option name="project" value="LOCAL" />
      <updated>1747064941505</updated>
    </task>
    <task id="LOCAL-00112" summary="#51  优化销售分析的销售数据更新">
      <option name="closed" value="true" />
      <created>1747066211860</created>
      <option name="number" value="00112" />
      <option name="presentableId" value="LOCAL-00112" />
      <option name="project" value="LOCAL" />
      <updated>1747066211860</updated>
    </task>
    <task id="LOCAL-00113" summary="#51  优化销售分析的销售数据更新">
      <option name="closed" value="true" />
      <created>1747098881967</created>
      <option name="number" value="00113" />
      <option name="presentableId" value="LOCAL-00113" />
      <option name="project" value="LOCAL" />
      <updated>1747098881967</updated>
    </task>
    <task id="LOCAL-00114" summary="#51  优化销售分析的销售数据更新">
      <option name="closed" value="true" />
      <created>1747098899886</created>
      <option name="number" value="00114" />
      <option name="presentableId" value="LOCAL-00114" />
      <option name="project" value="LOCAL" />
      <updated>1747098899886</updated>
    </task>
    <task id="LOCAL-00115" summary="#51  修复报警日志和cos问题">
      <option name="closed" value="true" />
      <created>1747286796206</created>
      <option name="number" value="00115" />
      <option name="presentableId" value="LOCAL-00115" />
      <option name="project" value="LOCAL" />
      <updated>1747286796207</updated>
    </task>
    <task id="LOCAL-00116" summary="#51  fruugo的流程走的功能">
      <option name="closed" value="true" />
      <created>1747397407432</created>
      <option name="number" value="00116" />
      <option name="presentableId" value="LOCAL-00116" />
      <option name="project" value="LOCAL" />
      <updated>1747397407432</updated>
    </task>
    <task id="LOCAL-00117" summary="#51  fruugo的流程走的功能">
      <option name="closed" value="true" />
      <created>1747926596371</created>
      <option name="number" value="00117" />
      <option name="presentableId" value="LOCAL-00117" />
      <option name="project" value="LOCAL" />
      <updated>1747926596371</updated>
    </task>
    <task id="LOCAL-00118" summary="#51  fruugo的流程走的功能">
      <option name="closed" value="true" />
      <created>1747926609720</created>
      <option name="number" value="00118" />
      <option name="presentableId" value="LOCAL-00118" />
      <option name="project" value="LOCAL" />
      <updated>1747926609720</updated>
    </task>
    <task id="LOCAL-00119" summary="#54 allegro 任务采集报表分析">
      <option name="closed" value="true" />
      <created>1748053022193</created>
      <option name="number" value="00119" />
      <option name="presentableId" value="LOCAL-00119" />
      <option name="project" value="LOCAL" />
      <updated>1748053022193</updated>
    </task>
    <task id="LOCAL-00120" summary="#54 fruugo bug修复">
      <option name="closed" value="true" />
      <created>1748256474032</created>
      <option name="number" value="00120" />
      <option name="presentableId" value="LOCAL-00120" />
      <option name="project" value="LOCAL" />
      <updated>1748256474032</updated>
    </task>
    <task id="LOCAL-00121" summary="#51 fruugo 刊登任务修复。">
      <option name="closed" value="true" />
      <created>1748271878586</created>
      <option name="number" value="00121" />
      <option name="presentableId" value="LOCAL-00121" />
      <option name="project" value="LOCAL" />
      <updated>1748271878586</updated>
    </task>
    <task id="LOCAL-00122" summary="#51 fruugo 刊登表修复。">
      <option name="closed" value="true" />
      <created>1748353099213</created>
      <option name="number" value="00122" />
      <option name="presentableId" value="LOCAL-00122" />
      <option name="project" value="LOCAL" />
      <updated>1748353099213</updated>
    </task>
    <task id="LOCAL-00123" summary="#51 fruugo 刊登表修复导出查询条件时效问题，增加认领的时候 判断时候已认领，认领状态的修改。">
      <option name="closed" value="true" />
      <created>1748446784677</created>
      <option name="number" value="00123" />
      <option name="presentableId" value="LOCAL-00123" />
      <option name="project" value="LOCAL" />
      <updated>1748446784677</updated>
    </task>
    <task id="LOCAL-00124" summary="#51 fruugo 刊登模板 替换词的功能。">
      <option name="closed" value="true" />
      <created>1748609710376</created>
      <option name="number" value="00124" />
      <option name="presentableId" value="LOCAL-00124" />
      <option name="project" value="LOCAL" />
      <updated>1748609710376</updated>
    </task>
    <task id="LOCAL-00125" summary="#51 fruugo  增加后台分步功能。">
      <option name="closed" value="true" />
      <created>1748669804886</created>
      <option name="number" value="00125" />
      <option name="presentableId" value="LOCAL-00125" />
      <option name="project" value="LOCAL" />
      <updated>1748669804886</updated>
    </task>
    <task id="LOCAL-00126" summary="#51 fruugo  修复认领问题。  增加fydiq的功能。">
      <option name="closed" value="true" />
      <created>1749016113654</created>
      <option name="number" value="00126" />
      <option name="presentableId" value="LOCAL-00126" />
      <option name="project" value="LOCAL" />
      <updated>1749016113654</updated>
    </task>
    <task id="LOCAL-00127" summary="#51 fruugo  ean导入修复">
      <option name="closed" value="true" />
      <created>1749102231489</created>
      <option name="number" value="00127" />
      <option name="presentableId" value="LOCAL-00127" />
      <option name="project" value="LOCAL" />
      <updated>1749102231489</updated>
    </task>
    <task id="LOCAL-00128" summary="#58 店铺绑定管理">
      <option name="closed" value="true" />
      <created>1749277833475</created>
      <option name="number" value="00128" />
      <option name="presentableId" value="LOCAL-00128" />
      <option name="project" value="LOCAL" />
      <updated>1749277833475</updated>
    </task>
    <task id="LOCAL-00129" summary="#51 增加push offer 设置未导出">
      <option name="closed" value="true" />
      <created>1749280797693</created>
      <option name="number" value="00129" />
      <option name="presentableId" value="LOCAL-00129" />
      <option name="project" value="LOCAL" />
      <updated>1749280797693</updated>
    </task>
    <task id="LOCAL-00130" summary="#51 增加push offer 修复异步导出情况">
      <option name="closed" value="true" />
      <created>1749285974172</created>
      <option name="number" value="00130" />
      <option name="presentableId" value="LOCAL-00130" />
      <option name="project" value="LOCAL" />
      <updated>1749285974172</updated>
    </task>
    <task id="LOCAL-00131" summary="#51 增加push offer 修复异步导出情况">
      <option name="closed" value="true" />
      <created>1749310573691</created>
      <option name="number" value="00131" />
      <option name="presentableId" value="LOCAL-00131" />
      <option name="project" value="LOCAL" />
      <updated>1749310573691</updated>
    </task>
    <task id="LOCAL-00132" summary="#57  fyndiq商品认领初步功能。">
      <option name="closed" value="true" />
      <created>1749342570004</created>
      <option name="number" value="00132" />
      <option name="presentableId" value="LOCAL-00132" />
      <option name="project" value="LOCAL" />
      <updated>1749342570004</updated>
    </task>
    <option name="localTasksCounter" value="133" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="UnknownFeatures">
    <option featureType="dependencySupport" implementationName="java:org.springframework:spring-core" />
    <option featureType="dependencySupport" implementationName="java:jakarta.validation:jakarta.validation-api" />
    <option featureType="dependencySupport" implementationName="javascript:npm:vite" />
    <option featureType="dependencySupport" implementationName="java:io.projectreactor:reactor-core" />
    <option featureType="dependencySupport" implementationName="java:org.springframework.data:spring-data-commons" />
    <option featureType="dependencySupport" implementationName="java:org.hibernate.validator:hibernate-validator" />
    <option featureType="dependencySupport" implementationName="java:org.springframework:spring-webmvc" />
    <option featureType="dependencySupport" implementationName="java:org.projectlombok:lombok" />
    <option featureType="dependencySupport" implementationName="java:org.springframework:spring-websocket" />
    <option featureType="dependencySupport" implementationName="javascript:npm:prettier" />
    <option featureType="dependencySupport" implementationName="java:io.reactivex.rxjava3:rxjava" />
    <option featureType="dependencySupport" implementationName="javascript:npm:vue" />
    <option featureType="dependencySupport" implementationName="java:org.springframework:spring-messaging" />
    <option featureType="dependencySupport" implementationName="java:org.springframework.boot:spring-boot" />
    <option featureType="dependencySupport" implementationName="javascript:npm:postcss" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <option name="CHECK_NEW_TODO" value="false" />
    <MESSAGE value="#50 提交采集箱的初始代码" />
    <MESSAGE value="#16 增加收件人姓名和电话查询，数据同步。" />
    <MESSAGE value="#16 修复订单排序问题" />
    <MESSAGE value="#16 报关中文名称从订单明细中获取" />
    <MESSAGE value="#53 V0.3 ozon 在线产品编辑" />
    <MESSAGE value="#51  fruugo 采集器，自动汇总到fruugo product 中。" />
    <MESSAGE value="#51  fruugo 店铺管理功能" />
    <MESSAGE value="#51  任务自动提交sku总数。" />
    <MESSAGE value="#51  分析组件的代码" />
    <MESSAGE value="#51  优化销售分析的销售数据更新" />
    <MESSAGE value="#51  修复报警日志和cos问题" />
    <MESSAGE value="#51  fruugo的流程走的功能" />
    <MESSAGE value="#54 allegro 任务采集报表分析" />
    <MESSAGE value="#54 fruugo bug修复" />
    <MESSAGE value="#51 fruugo 刊登任务修复。" />
    <MESSAGE value="#51 fruugo 刊登表修复。" />
    <MESSAGE value="#51 fruugo 刊登表修复导出查询条件时效问题，增加认领的时候 判断时候已认领，认领状态的修改。" />
    <MESSAGE value="#51 fruugo 刊登模板 替换词的功能。" />
    <MESSAGE value="#51 fruugo  增加后台分步功能。" />
    <MESSAGE value="#51 fruugo  修复认领问题。  增加fydiq的功能。" />
    <MESSAGE value="#51 fruugo  ean导入修复" />
    <MESSAGE value="#58 店铺绑定管理" />
    <MESSAGE value="#51 增加push offer 设置未导出" />
    <MESSAGE value="#51 增加push offer 修复异步导出情况" />
    <MESSAGE value="#57  fyndiq商品认领初步功能。" />
    <option name="LAST_COMMIT_MESSAGE" value="#57  fyndiq商品认领初步功能。" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint type="java-line">
          <url>jar://E:/.m2/repository/cn/dev33/sa-token-core/1.37.0/sa-token-core-1.37.0-sources.jar!/cn/dev33/satoken/router/SaRouterStaff.java</url>
          <line>314</line>
          <option name="timeStamp" value="230" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/51erp-service/xace-java-boot/xh-admin/src/main/java/com/xinghuo/allegro/order/service/impl/ExcelExportServiceImpl.java</url>
          <line>44</line>
          <option name="timeStamp" value="235" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/51erp-service/xace-java-boot/xh-admin/src/main/java/com/xinghuo/allegro/order/service/impl/ExcelExportServiceImpl.java</url>
          <line>48</line>
          <option name="timeStamp" value="236" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/51erp-service/xace-java-boot/xh-admin/src/main/java/com/xinghuo/allegro/push/service/impl/PushNewOfferServiceImpl.java</url>
          <line>147</line>
          <option name="timeStamp" value="240" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/51erp-service/xace-java-boot/xh-admin/src/main/java/com/xinghuo/allegro/push/service/impl/PushNewOfferServiceImpl.java</url>
          <line>174</line>
          <option name="timeStamp" value="241" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/51erp-service/xace-java-boot/xh-admin/src/main/java/com/xinghuo/allegro/order/controller/ErpOrderController.java</url>
          <line>694</line>
          <option name="timeStamp" value="242" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/51erp-service/xace-java-boot/xh-admin/src/main/java/com/xinghuo/allegro/order/service/impl/ExcelExportServiceImpl.java</url>
          <line>82</line>
          <option name="timeStamp" value="243" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/51erp-service/xace-java-boot/xh-admin/src/main/java/com/xinghuo/allegro/order/service/impl/ErpOrderServiceImpl.java</url>
          <line>1816</line>
          <option name="timeStamp" value="246" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/51erp-service/xace-java-boot/xh-admin/src/main/java/com/xinghuo/allegro/shelf/service/impl/PushOfferLinkServiceImpl.java</url>
          <line>532</line>
          <option name="timeStamp" value="250" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/51erp-service/xace-java-boot/xh-admin/src/main/java/com/xinghuo/allegro/shelf/service/impl/PushOfferLinkServiceImpl.java</url>
          <line>789</line>
          <option name="timeStamp" value="255" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/51erp-service/xace-java-boot/xh-admin/src/main/java/com/xinghuo/ozon/manage/service/impl/OzonProductServiceImpl.java</url>
          <line>140</line>
          <option name="timeStamp" value="262" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/51erp-service/xace-java-boot/xh-admin/src/main/java/com/xinghuo/ozon/manage/service/impl/OzonProductServiceImpl.java</url>
          <line>110</line>
          <option name="timeStamp" value="263" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/51erp-service/xace-java-boot/xh-admin/src/main/java/com/xinghuo/ozon/manage/service/impl/OzonProductServiceImpl.java</url>
          <line>109</line>
          <option name="timeStamp" value="264" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/51erp-service/xace-java-boot/xh-admin/src/main/java/com/xinghuo/ozon/manage/model/product/ShippingRuleUtil.java</url>
          <line>58</line>
          <option name="timeStamp" value="267" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/51erp-service/xace-java-boot/xh-admin/src/main/java/com/xinghuo/ozon/manage/service/impl/OzonProductServiceImpl.java</url>
          <line>76</line>
          <option name="timeStamp" value="268" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/51erp-service/xace-java-boot/xh-admin/src/main/java/com/xinghuo/allegro/push/controller/PushOfferController.java</url>
          <line>422</line>
          <option name="timeStamp" value="269" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/51erp-service/xace-java-boot/xh-admin/src/main/java/com/xinghuo/ozon/offer/service/impl/OzonOfferSyncServiceImpl.java</url>
          <line>195</line>
          <option name="timeStamp" value="277" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/51erp-service/xace-java-boot/xh-admin/src/main/java/com/xinghuo/niceapi/ozon/product/OzonProductAPI.java</url>
          <line>282</line>
          <option name="timeStamp" value="278" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/51erp-service/xace-java-boot/xh-admin/src/main/java/com/xinghuo/ozon/offer/service/impl/OzonOfferSyncServiceImpl.java</url>
          <line>108</line>
          <option name="timeStamp" value="280" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/51erp-service/xace-java-boot/xh-admin/src/main/java/com/xinghuo/fruugo/data/controller/FruugoDataCleanupController.java</url>
          <line>703</line>
          <option name="timeStamp" value="283" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/51erp-service/xace-java-boot/xh-admin/src/main/java/com/xinghuo/allegro/data/controller/AllegroDataOfferController.java</url>
          <line>50</line>
          <option name="timeStamp" value="284" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/51erp-service/xace-java-boot/xh-admin/src/main/java/com/xinghuo/amazon/collect/controller/AmazonListTaskController.java</url>
          <line>225</line>
          <option name="timeStamp" value="285" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
    <pin-to-top-manager>
      <pinned-members>
        <PinnedItemInfo parentTag="com.xinghuo.collect.box.model.CollectForm" memberName="itemList" />
        <PinnedItemInfo parentTag="com.xinghuo.allegro.collect.model.config.CollectConfigModel" memberName="id" />
        <PinnedItemInfo parentTag="com.xinghuo.allegro.sale.model.task.TaskItemForm" memberName="sellerId" />
        <PinnedItemInfo parentTag="com.fasterxml.jackson.databind.node.ObjectNode" memberName="_children" />
        <PinnedItemInfo parentTag="com.xinghuo.niceapi.coupang.CoupangBaseApi$CoupangApiResponse" memberName="message" />
        <PinnedItemInfo parentTag="com.xinghuo.allegro.collect.model.config.CollectConfigModel" memberName="config" />
        <PinnedItemInfo parentTag="com.xinghuo.niceapi.coupang.CoupangBaseApi$CoupangApiResponse" memberName="data" />
      </pinned-members>
    </pin-to-top-manager>
    <watches-manager>
      <configuration name="SpringBootApplicationConfigurationType">
        <watch expression="            String errorMsg = &quot;同步商品下架事件失败: &quot; + e.getMessage();&#10;" />
      </configuration>
    </watches-manager>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>