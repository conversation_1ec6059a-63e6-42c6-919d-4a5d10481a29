package com.xinghuo.allegro.data.model;

import com.fasterxml.jackson.databind.JsonNode;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * Allegro 英文采集商品解析表单
 * 
 * <AUTHOR>
 * @date 2024-11-05
 */
@Data
@Schema(description = "Allegro 英文采集商品解析表单")
public class AllegroDataOfferParseForm {

    @Schema(description = "采集产品ID")
    private String cpId;

    @Schema(description = "商品JSON数据")
    private JsonNode offerJson;

    @Schema(description = "客户端ID")
    private String clientId;
}
