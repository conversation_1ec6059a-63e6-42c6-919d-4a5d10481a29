package com.xinghuo.allegro.home.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("zz_index_data")
public class IndexDataEntity {

    @TableId
    private String indexKey;

    @TableField(value = "index_value")
    private String indexValue;

    @TableField(value = "note")
    private String  note;

    @TableField(value = "update_time")
    private  String updateTime;

}
