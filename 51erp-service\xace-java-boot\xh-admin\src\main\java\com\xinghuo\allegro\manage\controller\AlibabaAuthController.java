package com.xinghuo.allegro.manage.controller;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.Method;
import com.xinghuo.common.annotation.NoDataSourceBind;
import com.xinghuo.common.base.ActionResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 处理Allegro授权流程的相关请求。
 *  access_token  有效期：10个小时
 *  refresh_token  有效期：3个月
 *
 * <AUTHOR>
 */
@Slf4j
@Controller
@Tag(name = "1688授权", description = "1688授权")
@RequestMapping("/api/allegro/alibaba/auth")
public class AlibabaAuthController {




    @Operation(summary = "店铺授权")
    @GetMapping("/authorize")
    @NoDataSourceBind
    public ResponseEntity<String> authorize() {
        try {


            // 构建并返回重定向URL，引导用户前往Allegro授权页面
            String redirectUrl = "https://auth.1688.com/oauth/authorize?client_id=4166443&site=1688&redirect_uri=https://51erp.store/xace-web/api/allegro/alibaba/auth/callback&state=frying";
            return ResponseEntity.status(HttpStatus.FOUND).header("Location", redirectUrl).build();
        } catch (IllegalArgumentException e) {
            String errorMessage = "<html><body><h1>发生错误</h1><p>" + e.getMessage() + "</p></body></html>";
            return ResponseEntity.badRequest().body(errorMessage);
        } catch (Exception e) {
            String errorMessage = "<html><body><h1>发生错误</h1><p>未知错误：" + e.getMessage() + "</p></body></html>";
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorMessage);
        }
    }

    /**
     * 处理Allegro授权回调，交换授权代码并获取访问令牌。
     *
     * @param code  授权代码，由Allegro授权页面返回。
     * @param state 状态参数，用于验证请求的一致性。
     * @return 回调响应字符串，指示授权是否成功。
     */
    @GetMapping("/callback")
    @ResponseBody
    @NoDataSourceBind
    public String callback(@RequestParam String code) {
        // 从state参数中提取商店名称
        //| AppKey  4166443 | AppSecret<br><br>lVYPdi95M5Q |

        //https://gw.open.1688.com/openapi/http/1/system.oauth2/getToken/YOUR_APPKEY?grant_type=authorization_code&need_refresh_token=true&client_id= YOUR_APPKEY&client_secret= YOUR_APPSECRET&redirect_uri=YOUR_REDIRECT_URI&code=CODE
        log.info("code: " + code);
        String url = "https://gw.open.1688.com/openapi/http/1/system.oauth2/getToken/4166443";
        //构造 grant_type=authorization_code&need_refresh_token=true&client_id= YOUR_APPKEY&client_secret= YOUR_APPSECRET&redirect_uri=YOUR_REDIRECT_URI&code=CODE
//       &need_refresh_token=true&client_id= YOUR_APPKEY&client_secret= YOUR_APPSECRET&redirect_uri=YOUR_REDIRECT_URI&code=CODE
        // Construct the body parameters
        Map<String, Object> params = new HashMap<>();
        params.put("grant_type", "authorization_code");
        params.put("need_refresh_token", "true");
        params.put("client_id", "4166443");
        params.put("client_secret", "lVYPdi95M5Q");
        params.put("redirect_uri", "https://51erp.store/xace-web/api/allegro/alibaba/auth/callback");
        params.put("code", code);

        HttpRequest request = HttpRequest.of(url)
                .setMethod(Method.POST)
                .header("Content-Type", "application/x-www-form-urlencoded")
                .form(params);




        try {
            String response = request.execute().body();

            // 使用授权代码交换访问令牌
            // 更新同步的配置信息
            //  {"access_token":"c321d87b-7e1e-460d-a45f-57230ae98dd4","aliId":"21841801","refresh_token":"eee76662-4a5a-40e2-8ceb-4d7086f10952","resource_owner":"fryingpt","expires_in":"35999","refresh_token_timeout":"20250406210927000+0800","memberId":"b2b-21841801"}
            // 返回授权成功的消息
            return response + "，授权成功";
        } catch (Exception e) {
            return  "授权失败" + e.getMessage();
        }
    }

    @Operation(summary = "使用refreshToken重新授权")
    @GetMapping("/refreshStoreToken/{storeId}")
    @Parameters({
            @Parameter(name = "storeId", description = "店铺ID", required = true)
    })
    @ResponseBody
    @NoDataSourceBind
    public ActionResult<String> refreshStoreToken(@PathVariable("storeId") String storeId) {
//        AllegroStoreEntity entity = allegroAuthService.refreshToken(storeId);
//        if (entity == null) {
//            return ActionResult.fail("刷新token不存在！");
//        }
//        log.debug(entity.toString());
        return ActionResult.success(" 刷新Token成功" ) ;
    }





    public static void main(String[] args) throws Exception {
        String state = "state_for_1_测试";
        String apiClientId = state.split("_")[2];
        String storeName = state.split("_")[3];

        System.out.println(apiClientId);
        System.out.println(storeName);
    }
}
