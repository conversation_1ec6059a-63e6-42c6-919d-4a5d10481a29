package com.xinghuo.allegro.manage.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.AbstractBaseEntity;
import lombok.Data;

/**
 * Allegro平台店铺配置实体类
 * 继承自AbstractBaseEntity的AbstractCUBaseEntity子类，参数类型为String，用于标识实体的唯一性。
 * 该类用于封装Allegro平台店铺的配置信息，包括卖家ID、售后服务类型等相关信息。
 */
@TableName("zz_allegro_store_config")
@Data
public class AllegroStoreConfigEntity extends AbstractBaseEntity.AbstractCUBaseEntity<String> {

    /**
     * 卖家ID，用于唯一标识店铺。
     */
    private String sellerId;

    /**
     * 售后服务类型代码，用于标识不同的售后服务类型。
     */
    private String afterSalesType;

    /**
     * 售后服务类型ID，与afterSalesType对应，用于内部标识和处理。
     */
    private String afterSalesTypeId;

    /**
     * 售后服务类型名称，用于用户界面显示。
     */
    private String afterSalesTypeName;

    /**
     * 备注信息，用于记录店铺配置的额外信息。
     */
    private String note;

}
