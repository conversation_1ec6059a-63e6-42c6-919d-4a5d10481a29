package com.xinghuo.allegro.aiservice.service.impl;

import cn.hutool.crypto.digest.DigestUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xinghuo.allegro.aiservice.dao.ServiceTaskMapper;
import com.xinghuo.allegro.aiservice.entity.DescriptionVariantEntity;
import com.xinghuo.allegro.aiservice.entity.ServiceTaskEntity;
import com.xinghuo.allegro.aiservice.entity.TitleVariantEntity;
import com.xinghuo.allegro.aiservice.model.ServiceVariantModel;
import com.xinghuo.allegro.aiservice.service.DescriptionVariantService;
import com.xinghuo.allegro.aiservice.service.ServiceTaskService;
import com.xinghuo.allegro.aiservice.service.TitleVariantService;
import com.xinghuo.allegro.util.AllegroConstant;
import com.xinghuo.common.base.service.BaseServiceImpl;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.locks.ReentrantLock;

/**
 *
 */
@Slf4j
@Service
public class ServiceTaskServiceImpl extends BaseServiceImpl<ServiceTaskMapper, ServiceTaskEntity> implements ServiceTaskService {

    @Resource
    private TitleVariantService titleVariantService;

    @Resource
    private DescriptionVariantService  descriptionVariantService;

    private final ReentrantLock lock = new ReentrantLock();

    @Override
    public ServiceTaskEntity waitGet() {
        //如果锁已被另一个线程持有，当前线程将进入等待状态，直到锁被释放。
        lock.lock();
        try {
            QueryWrapper<ServiceTaskEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda()
                    .select(ServiceTaskEntity::getId, ServiceTaskEntity::getOriginalText,ServiceTaskEntity::getTaskType,ServiceTaskEntity::getProductId)
                    .eq(ServiceTaskEntity::getRequestStatus, AllegroConstant.REQUEST_STATUS_INIT)
                    .orderByDesc(ServiceTaskEntity::getPriority)
                    .last("limit 1");

            // 根据查询条件
            ServiceTaskEntity entity = this.getOne(queryWrapper);
            if (entity != null) {
                entity.setRequestStatus(AllegroConstant.REQUEST_STATUS_PROCESSING);
                entity.setRequestTime(new java.util.Date());
                this.updateById(entity);
            }
            return entity;

        } finally {
            lock.unlock();
        }
    }


    public void saveBatch(ServiceVariantModel  model) {
        if (model == null || model.getVariantTextList().isEmpty()) {
            return;
        }
        Long taskId = model.getTaskId();
        int num = 0;

        if(model.getTaskType().equals(1))
        {
            // 获取有效侵权的词汇列表
            for (String variantText : model.getVariantTextList()) {

                TitleVariantEntity titleVariantEntity = new TitleVariantEntity();
                String digest = DigestUtil.sha256Hex(variantText.trim());
                if (titleVariantService.isExistDigest(digest)) {
                    log.warn("标题变体已存在，title:{}", variantText.trim());
                    continue;
                }
                titleVariantEntity.setDigest(digest);
                titleVariantEntity.setVariantTitle(variantText.trim());
                titleVariantEntity.setTitleId(model.getTaskId());
                titleVariantEntity.setPlatform(model.getPlatform());
                titleVariantService.save(titleVariantEntity);
                num++;
            }
            log.info("保存标题变体数量:{}", num);
        }
        if(model.getTaskType().equals(2))
        {
            // 获取有效侵权的词汇列表
            for (String variantText : model.getVariantTextList()) {

                DescriptionVariantEntity descriptionVariantEntity = new DescriptionVariantEntity();
                String digest = DigestUtil.sha256Hex(variantText.trim());
                if (descriptionVariantService.isExistDigest(digest)) {
                    log.warn("描述变体已存在 ");
                    continue;
                }
                descriptionVariantEntity.setDigest(digest);
                descriptionVariantEntity.setVariantDescription(variantText.trim());
                descriptionVariantEntity.setDescriptionId(model.getTaskId());
                descriptionVariantEntity.setPlatform(model.getPlatform());
                descriptionVariantService.save(descriptionVariantEntity);
                num++;
            }
            log.info("保存描述变体数量:{}", num);
        }
        // 更新状态
        ServiceTaskEntity titleEntity = this.getById(taskId);
        if(titleEntity == null){
            log.error("标题不存在，id:{}", taskId);
            return;
        }
        titleEntity.setRequestStatus(AllegroConstant.REQUEST_STATUS_FINISH);
        titleEntity.setFinishTime(java.time.LocalDateTime.now());
        titleEntity.setVariantNum(num);
        this.updateById(titleEntity);

    }

    @Override
    //判断唯一哈希值是否存在
    public boolean isExistDigest(String digest) {
        QueryWrapper<ServiceTaskEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ServiceTaskEntity::getDigest, digest);
        return this.count(queryWrapper) > 0;
    }


    @Override
    //判断唯一哈希值是否存在
    public List<ServiceTaskEntity> unDigestList() {
        QueryWrapper<ServiceTaskEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().isNull(ServiceTaskEntity::getDigest).last("limit 1000");
        return this.list(queryWrapper) ;
    }
}
