package com.xinghuo.allegro.collect.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@TableName("zz_collect_offer_parse_tmp")
@Data
public class CollectOfferJsonEntity {

    private String id;
//    private String offerId;
    private String offerJson;
//    private String customJson;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @TableField(value = "deal_status")
    private int dealStatus;

    private String productId;

    @TableField(value = "f_tenantId")
    private String tenantId;

}
