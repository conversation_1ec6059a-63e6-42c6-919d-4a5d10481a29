package com.xinghuo.amazon.collect.controller;

import com.xinghuo.amazon.collect.entity.AmazonPageTaskEntity;
import com.xinghuo.amazon.collect.model.page.AmazonPageTaskForm;
import com.xinghuo.amazon.collect.model.page.AmazonPageTaskModel;
import com.xinghuo.amazon.collect.model.page.AmazonPageTaskPagination;
import com.xinghuo.amazon.collect.service.AmazonPageTaskService;
import com.xinghuo.amazon.util.AmazonConstant;
import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.common.base.vo.PaginationVO;

import com.xinghuo.common.exception.DataException;
import com.xinghuo.common.util.UserProvider;
import com.xinghuo.common.util.core.BeanCopierUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * Amazon页面任务Controller，负责处理Amazon页面采集相关的请求。
 * 接受客户端采集的数据上传。
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Slf4j
@RestController
@Tag(name = "Amazon页面采集任务", description = "Amazon页面采集任务")
@RequestMapping("/api/amazon/page/task")
public class AmazonPageTaskController {

    @Resource
    private AmazonPageTaskService amazonPageTaskService;

    @Resource
    private UserProvider userProvider;

    /**
     * 获取Amazon页面任务列表
     *
     * @param pagination 分页参数
     * @return 任务列表
     */
    @Operation(summary = "获取Amazon页面任务列表")
    @GetMapping
    public ActionResult<PageListVO<AmazonPageTaskModel>> getList(AmazonPageTaskPagination pagination) {
        List<AmazonPageTaskEntity> list = amazonPageTaskService.getList(pagination);
        List<AmazonPageTaskModel> listVO = BeanCopierUtils.copyList(list, AmazonPageTaskModel.class);
        PaginationVO page = BeanCopierUtils.copy(pagination, PaginationVO.class);
        return ActionResult.page(listVO, page);
    }

    /**
     * 获取Amazon页面任务详情
     *
     * @param id 任务ID
     * @return 任务详情
     */
    @Operation(summary = "获取Amazon页面任务详情")
    @GetMapping("/{id}")
    public ActionResult<AmazonPageTaskModel> getInfo(@PathVariable("id") String id) {
        AmazonPageTaskEntity entity = amazonPageTaskService.getById(id);
        if (entity == null) {
            throw new DataException("数据不存在");
        }
        AmazonPageTaskModel model = BeanCopierUtils.copy(entity, AmazonPageTaskModel.class);
        return ActionResult.success(model);
    }

    /**
     * 创建Amazon页面任务
     *
     * @param taskForm 任务表单
     * @return 创建结果
     */
    @Operation(summary = "创建Amazon页面任务")
    @PostMapping
    public ActionResult<String> create(@RequestBody AmazonPageTaskForm taskForm) {
        AmazonPageTaskEntity entity = BeanCopierUtils.copy(taskForm, AmazonPageTaskEntity.class);
        entity.setStatus(AmazonConstant.REQUEST_STATUS_INIT);
        entity.setCreatedAt(new Date());
        entity.setUpdatedAt(new Date());
        
        boolean result = amazonPageTaskService.save(entity);
        if (result) {
            return ActionResult.success("创建成功");
        } else {
            return ActionResult.fail("创建失败");
        }
    }

    /**
     * 更新Amazon页面任务
     *
     * @param id 任务ID
     * @param taskForm 任务表单
     * @return 更新结果
     */
    @Operation(summary = "更新Amazon页面任务")
    @PutMapping("/{id}")
    public ActionResult<String> update(@PathVariable("id") String id, @RequestBody AmazonPageTaskForm taskForm) {
        AmazonPageTaskEntity entity = amazonPageTaskService.getById(id);
        if (entity == null) {
            throw new DataException("数据不存在");
        }

        entity = BeanCopierUtils.copy(taskForm, AmazonPageTaskEntity.class);
        entity.setUpdatedAt(new Date());
        
        boolean result = amazonPageTaskService.updateById(entity);
        if (result) {
            return ActionResult.success("更新成功");
        } else {
            return ActionResult.fail("更新失败");
        }
    }

    /**
     * 删除Amazon页面任务
     *
     * @param id 任务ID
     * @return 删除结果
     */
    @Operation(summary = "删除Amazon页面任务")
    @DeleteMapping("/{id}")
    public ActionResult<String> delete(@PathVariable("id") String id) {
        AmazonPageTaskEntity entity = amazonPageTaskService.getById(id);
        if (entity == null) {
            throw new DataException("数据不存在");
        }
        
        boolean result = amazonPageTaskService.removeById(id);
        if (result) {
            return ActionResult.success("删除成功");
        } else {
            return ActionResult.fail("删除失败");
        }
    }

    /**
     * 获取待处理的Amazon页面任务
     *
     * @return 表示操作结果的ActionResult对象，包含待处理的任务
     */
    @Operation(summary = "获取后台待处理数据清单")
    @GetMapping("/waitGets")
    public ActionResult<AmazonPageTaskModel> waitGets(String clientId, String taskType) {
        AmazonPageTaskEntity entity = amazonPageTaskService.waitGets(clientId, taskType, AmazonConstant.PLATFORM_AMAZON);
        AmazonPageTaskModel model = BeanCopierUtils.copy(entity, AmazonPageTaskModel.class);
        return ActionResult.success(model);
    }

    /**
     * 接收并处理前端提交的Amazon页面详情JSON数据
     * 该方法主要负责解析前端上传的JSON数据，提取其中的商品详情信息，
     * 并更新相应的数据库记录。
     *
     * @param taskForm 包含待解析的JSON数据和相关ID的信息表单。
     * @return 成功保存的提示信息。
     */
    @Operation(summary = "前端提交详情页的json数据")
    @PutMapping("/putTaskJson")
    public ActionResult<String> putLinkJson(@RequestBody AmazonPageTaskForm taskForm) {
        log.debug("前端提交详情页的json数据 taskId:{}", taskForm.getId());
        
        // 获取任务实体
        AmazonPageTaskEntity entity = amazonPageTaskService.getById(taskForm.getId());
        if (entity != null) {
            entity.setStatus(AmazonConstant.REQUEST_STATUS_FINISH);
            entity.setUpdatedAt(new Date());
            
            amazonPageTaskService.updateById(entity);
            
            log.info("Amazon页面任务完成: taskId={}, asin={}", taskForm.getId(), taskForm.getEntryAsin());
        } else {
            log.warn("Amazon页面任务数据在系统中不存在，忽略。ID:" + taskForm.getId());
        }
        
        return ActionResult.success("成功保存！" + taskForm.getId());
    }

    /**
     * 获取待处理任务数量
     *
     * @param taskType 任务类型
     * @return 待处理任务数量
     */
    @Operation(summary = "获取待处理任务数量")
    @GetMapping("/pendingCount")
    public ActionResult<Long> getPendingCount(@RequestParam(required = false) String taskType) {
        long count = amazonPageTaskService.getPendingTaskCount(taskType);
        return ActionResult.success(count);
    }

    /**
     * 根据ASIN获取任务
     *
     * @param asin ASIN
     * @return 任务详情
     */
    @Operation(summary = "根据ASIN获取任务")
    @GetMapping("/asin/{asin}")
    public ActionResult<AmazonPageTaskModel> getByAsin(@PathVariable("asin") String asin) {
        AmazonPageTaskEntity entity = amazonPageTaskService.getByAsin(asin);
        if (entity == null) {
            throw new DataException("数据不存在");
        }
        AmazonPageTaskModel model = BeanCopierUtils.copy(entity, AmazonPageTaskModel.class);
        return ActionResult.success(model);
    }

    /**
     * 根据列表任务ID获取页面任务列表
     *
     * @param listTaskId 列表任务ID
     * @return 页面任务列表
     */
    @Operation(summary = "根据列表任务ID获取页面任务列表")
    @GetMapping("/listTask/{listTaskId}")
    public ActionResult<List<AmazonPageTaskModel>> getByListTaskId(@PathVariable("listTaskId") Integer listTaskId) {
        List<AmazonPageTaskEntity> list = amazonPageTaskService.getByListTaskId(listTaskId);
        List<AmazonPageTaskModel> listVO = BeanCopierUtils.copyList(list, AmazonPageTaskModel.class);
        return ActionResult.success(listVO);
    }
}
