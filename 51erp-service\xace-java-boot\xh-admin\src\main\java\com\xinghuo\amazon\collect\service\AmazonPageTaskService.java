package com.xinghuo.amazon.collect.service;

import com.xinghuo.amazon.collect.entity.AmazonPageTaskEntity;
import com.xinghuo.amazon.collect.model.page.AmazonPageTaskPagination;
import com.xinghuo.common.base.service.BaseService;

import java.util.List;

/**
 * Amazon页面任务Service接口
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
public interface AmazonPageTaskService extends BaseService<AmazonPageTaskEntity> {

    /**
     * 分页查询Amazon页面任务列表
     * @param pagination 分页查询参数
     * @return Amazon页面任务列表
     */
    List<AmazonPageTaskEntity> getList(AmazonPageTaskPagination pagination);

    /**
     * 获取待处理的任务
     * @param clientId 客户端ID
     * @param taskType 任务类型
     * @param platform 平台
     * @return 待处理的任务
     */
    AmazonPageTaskEntity waitGets(String clientId, String taskType, String platform);

    /**
     * 更新任务状态
     * @param taskId 任务ID
     * @return 更新结果
     */
    int updateCollectTask(String taskId);

    /**
     * 检查任务是否存在
     * @param asin ASIN
     * @return 是否存在
     */
    boolean existTask(String asin);

    /**
     * 获取待处理任务数量
     * @param taskType 任务类型
     * @return 待处理任务数量
     */
    long getPendingTaskCount(String taskType);

    /**
     * 根据ASIN获取任务
     * @param asin ASIN
     * @return 任务实体
     */
    AmazonPageTaskEntity getByAsin(String asin);

    /**
     * 批量保存页面任务
     * @param taskType 任务类型
     * @param entityList 任务实体列表
     */
    void saveBatch(String taskType, List<AmazonPageTaskEntity> entityList);

    /**
     * 更新任务状态为完成
     * @param taskId 任务ID
     * @param detailData 详情数据JSON
     * @return 更新结果
     */
    int updateTaskCompleted(Integer taskId, String detailData);

    /**
     * 更新任务状态为失败
     * @param taskId 任务ID
     * @param errorMessage 错误信息
     * @return 更新结果
     */
    int updateTaskFailed(Integer taskId, String errorMessage);

    /**
     * 根据列表任务ID获取页面任务列表
     * @param listTaskId 列表任务ID
     * @return 页面任务列表
     */
    List<AmazonPageTaskEntity> getByListTaskId(Integer listTaskId);
}
