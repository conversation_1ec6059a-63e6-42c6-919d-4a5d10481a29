package com.xinghuo.allegro.aiservice.controller;


import cn.hutool.crypto.digest.DigestUtil;
import com.xinghuo.allegro.aiservice.entity.ServiceTaskEntity;
import com.xinghuo.allegro.aiservice.model.ServiceTaskModel;
import com.xinghuo.allegro.aiservice.model.ServiceVariantModel;
import com.xinghuo.allegro.aiservice.service.ServiceTaskService;
import com.xinghuo.common.annotation.NoDataSourceBind;
import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.util.core.BeanCopierUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 标题管理

 * <AUTHOR>
 * 2024-08-19
 */
@Slf4j
@RestController
@Tag(name = "服务管理-标题", description = "服务管理-标题")
@RequestMapping("/api/allegro/service/task")
public class ServiceTaskController {

    @Resource
    private ServiceTaskService serviceTaskService;

    @Operation(summary = "获取后台待处理数据清单")
    @GetMapping("/waitGet")
    @NoDataSourceBind
    public ActionResult<ServiceTaskModel> waitGet() {
        ServiceTaskEntity entity = serviceTaskService.waitGet();
        ServiceTaskModel model = BeanCopierUtils.copy(entity, ServiceTaskModel.class);
        return ActionResult.success(model);
    }


    @Operation(summary = "获取后台待处理数据清单")
    @PutMapping("/accept")
    @NoDataSourceBind
    public ActionResult<String> accept(@RequestBody List<ServiceTaskModel> list) {
        int successNum = 0;
        for (ServiceTaskModel model : list) {
            ServiceTaskEntity entity = new ServiceTaskEntity();
            entity.setOriginalText(model.getOriginalText().trim());
            entity.setDigest(DigestUtil.sha256Hex(entity.getOriginalText()));
            entity.setProductId(model.getProductId());
            if(!serviceTaskService.isExistDigest(entity.getDigest())){
                serviceTaskService.save(entity);
                successNum++;
            }
         }
        log.info("接收总数：{},成功处理数据：{}", list.size(), successNum);
        return ActionResult.success("成功！,总数："+ list.size() + ",成功数：" + successNum);
    }

    /**
     * 模型数据集
     *
     */
    @Operation(summary = "前端模型数据集")
    @PutMapping("/putOfferJson")
    @NoDataSourceBind
    public ActionResult<String> putOfferJson(@RequestBody ServiceVariantModel model) {
        serviceTaskService.saveBatch(model);
        return ActionResult.success("成功处理！");
    }


    @Operation(summary = "获取后台待处理数据清单")
    @GetMapping("/batchDigest")
    @NoDataSourceBind
    public ActionResult<String> batchDigest() {
        List<ServiceTaskEntity> list = serviceTaskService.unDigestList();
        while(!list.isEmpty()){
            for (ServiceTaskEntity entity : list) {
                entity.setOriginalText(entity.getOriginalText().trim());
                entity.setDigest(DigestUtil.sha256Hex(entity.getOriginalText()));
                serviceTaskService.updateById(entity);
            }
            list = serviceTaskService.unDigestList();
        }

        return ActionResult.success("成功！");
    }
}
