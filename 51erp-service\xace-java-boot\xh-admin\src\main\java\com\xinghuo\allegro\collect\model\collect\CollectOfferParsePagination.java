package com.xinghuo.allegro.collect.model.collect;

import com.xinghuo.common.base.model.Pagination;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 店铺查询
 *
 * <AUTHOR>
 * @date 2023-12-13
 */
@Data
public class CollectOfferParsePagination extends Pagination {
    @Schema(description = "查询key")
    private String[] selectKey;
    @Schema(description = "json")
    private String json;
    @Schema(description = "数据类型 0-当前页，1-全部数据")
    private String dataType;
    @Schema(description = "高级查询")
    private String superQueryJson;
    @Schema(description = "功能id")
    private String moduleId;
    @Schema(description = "菜单id")
    private String menuId;


}
