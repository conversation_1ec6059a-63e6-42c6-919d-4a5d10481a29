package com.xinghuo.amazon.util;

/**
 * Amazon采集常量类
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
public class AmazonConstant {

    /**
     * 404 设置状态为-2
     */
    public static int REQUEST_STATUS_404 = -2;

    /**
     * 无需处理
     */
    public static int REQUEST_STATUS_IGNORE = -1;
    
    /**
     * 等待处理
     */
    public static int REQUEST_STATUS_INIT = 0;
    
    /**
     * 请求数据处理中
     */
    public static int REQUEST_STATUS_PROCESSING = 1;
    
    /**
     * 处理完成
     */
    public static int REQUEST_STATUS_FINISH = 2;

    /**
     * 处理失败
     */
    public static int REQUEST_STATUS_FAILED = 3;

    /**
     * ERP处理失败
     */
    public static int REQUEST_STATUS_ERPFAIL = 8;

    /**
     * 被阻止
     */
    public static int REQUEST_STATUS_BLOCKED = 9;

    /**
     * 无效的解析数据
     */
    public static int PARSE_STATUS_UNVALID = 0;
    
    /**
     * 有效的解析数据
     */
    public static int PARSE_STATUS_VALID = 1;

    /**
     * 任务状态 - 待处理
     */
    public static String TASK_STATUS_PENDING = "pending";
    
    /**
     * 任务状态 - 进行中
     */
    public static String TASK_STATUS_IN_PROGRESS = "in_progress";
    
    /**
     * 任务状态 - 已完成
     */
    public static String TASK_STATUS_COMPLETED = "completed";
    
    /**
     * 任务状态 - 失败
     */
    public static String TASK_STATUS_FAILED = "failed";

    /**
     * 平台名称 - Amazon
     */
    public static String PLATFORM_AMAZON = "AMAZON";

    /**
     * 任务类型 - 列表采集
     */
    public static String TASK_TYPE_LIST = "LIST";
    
    /**
     * 任务类型 - 页面采集
     */
    public static String TASK_TYPE_PAGE = "PAGE";

    /**
     * 异常信息 - 产品未找到
     */
    public static final String EXCEPTION_PRODUCT_NOT_FIND = "ProductNotFound";
    
    /**
     * 异常信息 - 页面无法访问
     */
    public static final String EXCEPTION_PAGE_NOT_ACCESSIBLE = "PageNotAccessible";
    
    /**
     * 异常信息 - 数据解析失败
     */
    public static final String EXCEPTION_DATA_PARSE_FAILED = "DataParseFailed";
}
