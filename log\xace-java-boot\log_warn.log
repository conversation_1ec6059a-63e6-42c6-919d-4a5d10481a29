[2025-06-15 17:24:10.862] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:10.875] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:10.882] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:10.966] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceUtil' of type [com.xinghuo.common.database.util.DataSourceUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:10.984] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'config-com.xinghuo.common.util.ConfigValueUtil' of type [com.xinghuo.common.util.ConfigValueUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:11.007] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAssistConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAssistConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:11.013] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$HikariDataSourceCreatorConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$HikariDataSourceCreatorConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:11.017] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'hikariDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.hikaricp.HikariDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:11.021] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$DruidDataSourceCreatorConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$DruidDataSourceCreatorConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:11.036] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'druidDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.druid.DruidDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:11.039] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:11.041] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'basicDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.basic.BasicDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:11.044] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'jndiDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.jndi.JndiDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:11.048] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceInitEvent' of type [com.baomidou.dynamic.datasource.event.EncDataSourceInitEvent] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:11.050] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.DefaultDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:11.053] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'ymlDynamicDataSourceProvider' of type [com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:11.055] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'mybatisPlusConfig' of type [com.xinghuo.common.database.config.MybatisPlusConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:11.073] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'myTenantLineInnerInterceptor' of type [com.xinghuo.common.database.plugins.MyTenantLineInnerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:11.092] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'tenantDataSourceUtil' of type [com.xinghuo.common.database.util.TenantDataSourceUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:11.094] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'myDynamicDatasourceGeneratorAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:16.761] [WARN ] [main] [c.b.mybatisplus.core.metadata.TableInfoHelper] [?] [?] - Can not find table primary key in Class: "java.lang.Object".
[2025-06-15 17:24:16.763] [WARN ] [main] [c.x.common.database.plugins.MyDefaultSqlInjector] [?] [?] - class java.lang.Object ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[2025-06-15 17:24:16.768] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Insert] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
[2025-06-15 17:24:16.775] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Delete] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.delete] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Delete]
[2025-06-15 17:24:16.788] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Update] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
[2025-06-15 17:24:25.149] [WARN ] [main] [o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext] [?] [?] - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'permissionAdminAspect': Unsatisfied dependency expressed through field 'organizeRelationService': Error creating bean with name 'organizeRelationServiceImpl': Unsatisfied dependency expressed through field 'roleService': Error creating bean with name 'roleServiceImpl': Unsatisfied dependency expressed through field 'userService': Error creating bean with name 'userServiceImpl': Unsatisfied dependency expressed through field 'userRelationService': Error creating bean with name 'userRelationServiceImpl': Unsatisfied dependency expressed through field 'synThirdInfoService': Error creating bean with name 'synThirdInfoServiceImpl': Lookup method resolution failed
[2025-06-15 17:48:23.470] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.486] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.494] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.597] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceUtil' of type [com.xinghuo.common.database.util.DataSourceUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.617] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'config-com.xinghuo.common.util.ConfigValueUtil' of type [com.xinghuo.common.util.ConfigValueUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.632] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAssistConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAssistConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.636] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$HikariDataSourceCreatorConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$HikariDataSourceCreatorConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.643] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'hikariDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.hikaricp.HikariDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.647] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$DruidDataSourceCreatorConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$DruidDataSourceCreatorConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.662] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'druidDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.druid.DruidDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.664] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.667] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'basicDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.basic.BasicDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.669] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'jndiDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.jndi.JndiDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.672] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceInitEvent' of type [com.baomidou.dynamic.datasource.event.EncDataSourceInitEvent] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.674] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.DefaultDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.676] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'ymlDynamicDataSourceProvider' of type [com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.678] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'mybatisPlusConfig' of type [com.xinghuo.common.database.config.MybatisPlusConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.696] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'myTenantLineInnerInterceptor' of type [com.xinghuo.common.database.plugins.MyTenantLineInnerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.714] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'tenantDataSourceUtil' of type [com.xinghuo.common.database.util.TenantDataSourceUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.716] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'myDynamicDatasourceGeneratorAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:27.641] [WARN ] [main] [c.b.mybatisplus.core.metadata.TableInfoHelper] [?] [?] - Can not find table primary key in Class: "java.lang.Object".
[2025-06-15 17:48:27.642] [WARN ] [main] [c.x.common.database.plugins.MyDefaultSqlInjector] [?] [?] - class java.lang.Object ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[2025-06-15 17:48:27.643] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Insert] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
[2025-06-15 17:48:27.644] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Delete] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.delete] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Delete]
[2025-06-15 17:48:27.647] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Update] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
[2025-06-15 17:49:15.649] [WARN ] [main] [com.xxl.job.core.executor.XxlJobExecutor] [?] [?] - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
[2025-06-15 17:50:00.801] [WARN ] [scheduling-1] [com.xinghuo.fruugo.analysis.util.WechatWorkUtil] [?] [?] - 企业微信webhook地址未配置，无法发送通知
[2025-06-15 17:50:00.908] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 17:50:00.986] [WARN ] [scheduling-1] [com.xinghuo.fruugo.analysis.util.WechatWorkUtil] [?] [?] - 企业微信webhook地址未配置，无法发送通知
[2025-06-15 17:50:01.060] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30988 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 17:55:00.134] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 17:55:18.893] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30980 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 18:07:13.213] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 18:07:20.476] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30985 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 18:07:39.602] [WARN ] [SpringApplicationShutdownHook] [o.s.c.annotation.CommonAnnotationBeanPostProcessor] [?] [?] - Destroy method on bean with name 'idGeneratorConfig' threw an exception: org.springframework.dao.InvalidDataAccessApiUsageException: Redisson is shutdown
[2025-06-15 18:07:46.925] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:46.937] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:46.945] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.022] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceUtil' of type [com.xinghuo.common.database.util.DataSourceUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.039] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'config-com.xinghuo.common.util.ConfigValueUtil' of type [com.xinghuo.common.util.ConfigValueUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.053] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAssistConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAssistConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.056] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$HikariDataSourceCreatorConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$HikariDataSourceCreatorConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.060] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'hikariDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.hikaricp.HikariDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.063] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$DruidDataSourceCreatorConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$DruidDataSourceCreatorConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.073] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'druidDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.druid.DruidDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.075] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.077] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'basicDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.basic.BasicDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.079] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'jndiDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.jndi.JndiDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.082] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceInitEvent' of type [com.baomidou.dynamic.datasource.event.EncDataSourceInitEvent] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.083] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.DefaultDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.086] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'ymlDynamicDataSourceProvider' of type [com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.087] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'mybatisPlusConfig' of type [com.xinghuo.common.database.config.MybatisPlusConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.098] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'myTenantLineInnerInterceptor' of type [com.xinghuo.common.database.plugins.MyTenantLineInnerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.121] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'tenantDataSourceUtil' of type [com.xinghuo.common.database.util.TenantDataSourceUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.122] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'myDynamicDatasourceGeneratorAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:50.856] [WARN ] [main] [c.b.mybatisplus.core.metadata.TableInfoHelper] [?] [?] - Can not find table primary key in Class: "java.lang.Object".
[2025-06-15 18:07:50.856] [WARN ] [main] [c.x.common.database.plugins.MyDefaultSqlInjector] [?] [?] - class java.lang.Object ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[2025-06-15 18:07:50.857] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Insert] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
[2025-06-15 18:07:50.859] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Delete] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.delete] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Delete]
[2025-06-15 18:07:50.863] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Update] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
[2025-06-15 18:08:39.825] [WARN ] [main] [com.xxl.job.core.executor.XxlJobExecutor] [?] [?] - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
[2025-06-15 18:10:00.457] [WARN ] [scheduling-1] [com.xinghuo.fruugo.analysis.util.WechatWorkUtil] [?] [?] - 企业微信webhook地址未配置，无法发送通知
[2025-06-15 18:10:00.543] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 18:10:00.585] [WARN ] [scheduling-1] [com.xinghuo.fruugo.analysis.util.WechatWorkUtil] [?] [?] - 企业微信webhook地址未配置，无法发送通知
[2025-06-15 18:10:00.657] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30985 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 18:15:00.087] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 18:15:00.159] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 31006 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 18:20:00.085] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 18:20:00.232] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30988 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 18:25:00.178] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 18:25:00.317] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30999 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 18:30:00.137] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 18:30:03.209] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30982 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 18:37:15.036] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 18:37:17.619] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30986 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 18:40:00.168] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 18:40:01.301] [WARN ] [scheduling-1] [com.xinghuo.fruugo.analysis.util.WechatWorkUtil] [?] [?] - 企业微信webhook地址未配置，无法发送通知
[2025-06-15 18:40:01.396] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 31012 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 18:40:57.337] [WARN ] [SpringApplicationShutdownHook] [o.s.c.annotation.CommonAnnotationBeanPostProcessor] [?] [?] - Destroy method on bean with name 'idGeneratorConfig' threw an exception: org.springframework.dao.InvalidDataAccessApiUsageException: Redisson is shutdown
[2025-06-15 18:41:04.789] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.801] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.808] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.891] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceUtil' of type [com.xinghuo.common.database.util.DataSourceUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.910] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'config-com.xinghuo.common.util.ConfigValueUtil' of type [com.xinghuo.common.util.ConfigValueUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.926] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAssistConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAssistConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.930] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$HikariDataSourceCreatorConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$HikariDataSourceCreatorConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.933] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'hikariDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.hikaricp.HikariDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.936] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$DruidDataSourceCreatorConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$DruidDataSourceCreatorConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.948] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'druidDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.druid.DruidDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.950] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.952] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'basicDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.basic.BasicDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.954] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'jndiDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.jndi.JndiDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.957] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceInitEvent' of type [com.baomidou.dynamic.datasource.event.EncDataSourceInitEvent] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.959] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.DefaultDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.962] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'ymlDynamicDataSourceProvider' of type [com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.963] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'mybatisPlusConfig' of type [com.xinghuo.common.database.config.MybatisPlusConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.974] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'myTenantLineInnerInterceptor' of type [com.xinghuo.common.database.plugins.MyTenantLineInnerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.993] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'tenantDataSourceUtil' of type [com.xinghuo.common.database.util.TenantDataSourceUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.994] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'myDynamicDatasourceGeneratorAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:08.872] [WARN ] [main] [c.b.mybatisplus.core.metadata.TableInfoHelper] [?] [?] - Can not find table primary key in Class: "java.lang.Object".
[2025-06-15 18:41:08.873] [WARN ] [main] [c.x.common.database.plugins.MyDefaultSqlInjector] [?] [?] - class java.lang.Object ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[2025-06-15 18:41:08.873] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Insert] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
[2025-06-15 18:41:08.875] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Delete] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.delete] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Delete]
[2025-06-15 18:41:08.879] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Update] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
[2025-06-15 18:41:57.583] [WARN ] [main] [com.xxl.job.core.executor.XxlJobExecutor] [?] [?] - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
[2025-06-15 18:45:04.085] [WARN ] [scheduling-1] [com.xinghuo.fruugo.analysis.util.WechatWorkUtil] [?] [?] - 企业微信webhook地址未配置，无法发送通知
[2025-06-15 18:45:04.152] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 18:45:04.207] [WARN ] [scheduling-1] [com.xinghuo.fruugo.analysis.util.WechatWorkUtil] [?] [?] - 企业微信webhook地址未配置，无法发送通知
[2025-06-15 18:45:04.255] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30986 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 18:50:00.198] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 18:50:00.653] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30987 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 18:55:00.247] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 18:55:19.878] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 31012 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 19:00:00.422] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 19:00:31.864] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30978 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 19:13:43.036] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 19:13:49.849] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 31006 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 19:15:00.111] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 19:15:09.616] [WARN ] [scheduling-1] [com.xinghuo.fruugo.analysis.util.WechatWorkUtil] [?] [?] - 企业微信webhook地址未配置，无法发送通知
[2025-06-15 19:15:09.702] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 31006 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 19:17:56.962] [WARN ] [SpringApplicationShutdownHook] [o.s.c.annotation.CommonAnnotationBeanPostProcessor] [?] [?] - Destroy method on bean with name 'idGeneratorConfig' threw an exception: org.springframework.dao.InvalidDataAccessApiUsageException: Redisson is shutdown
[2025-06-15 19:18:04.690] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 19:18:04.710] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 19:18:04.719] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 19:18:04.806] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceUtil' of type [com.xinghuo.common.database.util.DataSourceUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 19:18:04.825] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'config-com.xinghuo.common.util.ConfigValueUtil' of type [com.xinghuo.common.util.ConfigValueUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 19:18:04.839] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAssistConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAssistConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 19:18:04.843] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$HikariDataSourceCreatorConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$HikariDataSourceCreatorConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 19:18:04.848] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'hikariDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.hikaricp.HikariDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 19:18:04.851] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$DruidDataSourceCreatorConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$DruidDataSourceCreatorConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 19:18:04.864] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'druidDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.druid.DruidDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 19:18:04.867] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 19:18:04.870] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'basicDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.basic.BasicDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 19:18:04.872] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'jndiDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.jndi.JndiDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 19:18:04.874] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceInitEvent' of type [com.baomidou.dynamic.datasource.event.EncDataSourceInitEvent] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 19:18:04.876] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.DefaultDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 19:18:04.878] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'ymlDynamicDataSourceProvider' of type [com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 19:18:04.880] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'mybatisPlusConfig' of type [com.xinghuo.common.database.config.MybatisPlusConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 19:18:04.895] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'myTenantLineInnerInterceptor' of type [com.xinghuo.common.database.plugins.MyTenantLineInnerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 19:18:04.912] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'tenantDataSourceUtil' of type [com.xinghuo.common.database.util.TenantDataSourceUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 19:18:04.914] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'myDynamicDatasourceGeneratorAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 19:18:08.704] [WARN ] [main] [c.b.mybatisplus.core.metadata.TableInfoHelper] [?] [?] - Can not find table primary key in Class: "java.lang.Object".
[2025-06-15 19:18:08.704] [WARN ] [main] [c.x.common.database.plugins.MyDefaultSqlInjector] [?] [?] - class java.lang.Object ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[2025-06-15 19:18:08.705] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Insert] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
[2025-06-15 19:18:08.706] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Delete] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.delete] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Delete]
[2025-06-15 19:18:08.710] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Update] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
[2025-06-15 19:18:57.400] [WARN ] [main] [com.xxl.job.core.executor.XxlJobExecutor] [?] [?] - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
[2025-06-15 19:20:08.541] [WARN ] [scheduling-1] [com.xinghuo.fruugo.analysis.util.WechatWorkUtil] [?] [?] - 企业微信webhook地址未配置，无法发送通知
[2025-06-15 19:20:08.618] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 19:20:08.679] [WARN ] [scheduling-1] [com.xinghuo.fruugo.analysis.util.WechatWorkUtil] [?] [?] - 企业微信webhook地址未配置，无法发送通知
[2025-06-15 19:20:08.724] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30988 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 19:20:14.832] [WARN ] [http-nio-32000-exec-3] [com.xinghuo.amazon.util.AmazonUtil] [?] [?] - 提取整数失败: Price, product page$6.99$6.99 Typical: $7.99 Typical: $7.99$7.99
java.lang.NumberFormatException: For input string: "699699799799799"
	at java.base/java.lang.NumberFormatException.forInputString(NumberFormatException.java:67)
	at java.base/java.lang.Integer.parseInt(Integer.java:665)
	at java.base/java.lang.Integer.parseInt(Integer.java:781)
	at com.xinghuo.amazon.util.AmazonUtil.extractInteger(AmazonUtil.java:105)
	at com.xinghuo.amazon.collect.service.AmazonHtmlParseService.extractRatingInfo(AmazonHtmlParseService.java:292)
	at com.xinghuo.amazon.collect.service.AmazonHtmlParseService.parseProductElement(AmazonHtmlParseService.java:128)
	at com.xinghuo.amazon.collect.service.AmazonHtmlParseService.parseListPageHtml(AmazonHtmlParseService.java:70)
	at com.xinghuo.amazon.collect.controller.AmazonListTaskController.submitForm(AmazonListTaskController.java:243)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:578)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:351)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.aspectj.AspectJAfterAdvice.invoke(AspectJAfterAdvice.java:49)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.xinghuo.admin.aop.RequestLogAspect.doAroundService(RequestLogAspect.java:59)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:578)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:637)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:627)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.xinghuo.admin.aop.DataSourceBindAspect.doAroundService(DataSourceBindAspect.java:61)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:578)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:637)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:627)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.xinghuo.amazon.collect.controller.AmazonListTaskController$$SpringCGLIB$$0.submitForm(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:578)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:925)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:830)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:925)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:593)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at com.github.xiaoymin.knife4j.extend.filter.basic.JakartaServletSecurityBasicAuthFilter.doFilter(JakartaServletSecurityBasicAuthFilter.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at com.xinghuo.common.config.SecurityConfiguration$ClearThreadContextFilter.doFilter(SecurityConfiguration.java:121)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1623)
[2025-06-15 19:20:15.875] [WARN ] [http-nio-32000-exec-3] [com.xinghuo.amazon.util.AmazonUtil] [?] [?] - 提取整数失败: Price, product page$6.69$6.69 List: $7.69 List: $7.69$7.69
java.lang.NumberFormatException: For input string: "669669769769769"
	at java.base/java.lang.NumberFormatException.forInputString(NumberFormatException.java:67)
	at java.base/java.lang.Integer.parseInt(Integer.java:665)
	at java.base/java.lang.Integer.parseInt(Integer.java:781)
	at com.xinghuo.amazon.util.AmazonUtil.extractInteger(AmazonUtil.java:105)
	at com.xinghuo.amazon.collect.service.AmazonHtmlParseService.extractRatingInfo(AmazonHtmlParseService.java:292)
	at com.xinghuo.amazon.collect.service.AmazonHtmlParseService.parseProductElement(AmazonHtmlParseService.java:128)
	at com.xinghuo.amazon.collect.service.AmazonHtmlParseService.parseListPageHtml(AmazonHtmlParseService.java:70)
	at com.xinghuo.amazon.collect.controller.AmazonListTaskController.submitForm(AmazonListTaskController.java:243)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:578)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:351)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.aspectj.AspectJAfterAdvice.invoke(AspectJAfterAdvice.java:49)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.xinghuo.admin.aop.RequestLogAspect.doAroundService(RequestLogAspect.java:59)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:578)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:637)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:627)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.xinghuo.admin.aop.DataSourceBindAspect.doAroundService(DataSourceBindAspect.java:61)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:578)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:637)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:627)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.xinghuo.amazon.collect.controller.AmazonListTaskController$$SpringCGLIB$$0.submitForm(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:578)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:925)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:830)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:925)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:593)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at com.github.xiaoymin.knife4j.extend.filter.basic.JakartaServletSecurityBasicAuthFilter.doFilter(JakartaServletSecurityBasicAuthFilter.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at com.xinghuo.common.config.SecurityConfiguration$ClearThreadContextFilter.doFilter(SecurityConfiguration.java:121)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1623)
