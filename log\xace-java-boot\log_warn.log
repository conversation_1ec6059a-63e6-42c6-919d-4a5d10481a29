[2025-06-15 17:24:10.862] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:10.875] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:10.882] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:10.966] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceUtil' of type [com.xinghuo.common.database.util.DataSourceUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:10.984] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'config-com.xinghuo.common.util.ConfigValueUtil' of type [com.xinghuo.common.util.ConfigValueUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:11.007] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAssistConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAssistConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:11.013] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$HikariDataSourceCreatorConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$HikariDataSourceCreatorConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:11.017] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'hikariDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.hikaricp.HikariDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:11.021] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$DruidDataSourceCreatorConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$DruidDataSourceCreatorConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:11.036] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'druidDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.druid.DruidDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:11.039] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:11.041] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'basicDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.basic.BasicDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:11.044] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'jndiDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.jndi.JndiDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:11.048] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceInitEvent' of type [com.baomidou.dynamic.datasource.event.EncDataSourceInitEvent] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:11.050] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.DefaultDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:11.053] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'ymlDynamicDataSourceProvider' of type [com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:11.055] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'mybatisPlusConfig' of type [com.xinghuo.common.database.config.MybatisPlusConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:11.073] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'myTenantLineInnerInterceptor' of type [com.xinghuo.common.database.plugins.MyTenantLineInnerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:11.092] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'tenantDataSourceUtil' of type [com.xinghuo.common.database.util.TenantDataSourceUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:11.094] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'myDynamicDatasourceGeneratorAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:24:16.761] [WARN ] [main] [c.b.mybatisplus.core.metadata.TableInfoHelper] [?] [?] - Can not find table primary key in Class: "java.lang.Object".
[2025-06-15 17:24:16.763] [WARN ] [main] [c.x.common.database.plugins.MyDefaultSqlInjector] [?] [?] - class java.lang.Object ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[2025-06-15 17:24:16.768] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Insert] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
[2025-06-15 17:24:16.775] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Delete] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.delete] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Delete]
[2025-06-15 17:24:16.788] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Update] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
[2025-06-15 17:24:25.149] [WARN ] [main] [o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext] [?] [?] - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'permissionAdminAspect': Unsatisfied dependency expressed through field 'organizeRelationService': Error creating bean with name 'organizeRelationServiceImpl': Unsatisfied dependency expressed through field 'roleService': Error creating bean with name 'roleServiceImpl': Unsatisfied dependency expressed through field 'userService': Error creating bean with name 'userServiceImpl': Unsatisfied dependency expressed through field 'userRelationService': Error creating bean with name 'userRelationServiceImpl': Unsatisfied dependency expressed through field 'synThirdInfoService': Error creating bean with name 'synThirdInfoServiceImpl': Lookup method resolution failed
[2025-06-15 17:48:23.470] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.486] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.494] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.597] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceUtil' of type [com.xinghuo.common.database.util.DataSourceUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.617] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'config-com.xinghuo.common.util.ConfigValueUtil' of type [com.xinghuo.common.util.ConfigValueUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.632] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAssistConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAssistConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.636] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$HikariDataSourceCreatorConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$HikariDataSourceCreatorConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.643] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'hikariDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.hikaricp.HikariDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.647] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$DruidDataSourceCreatorConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$DruidDataSourceCreatorConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.662] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'druidDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.druid.DruidDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.664] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.667] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'basicDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.basic.BasicDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.669] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'jndiDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.jndi.JndiDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.672] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceInitEvent' of type [com.baomidou.dynamic.datasource.event.EncDataSourceInitEvent] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.674] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.DefaultDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.676] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'ymlDynamicDataSourceProvider' of type [com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.678] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'mybatisPlusConfig' of type [com.xinghuo.common.database.config.MybatisPlusConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.696] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'myTenantLineInnerInterceptor' of type [com.xinghuo.common.database.plugins.MyTenantLineInnerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.714] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'tenantDataSourceUtil' of type [com.xinghuo.common.database.util.TenantDataSourceUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:23.716] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'myDynamicDatasourceGeneratorAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 17:48:27.641] [WARN ] [main] [c.b.mybatisplus.core.metadata.TableInfoHelper] [?] [?] - Can not find table primary key in Class: "java.lang.Object".
[2025-06-15 17:48:27.642] [WARN ] [main] [c.x.common.database.plugins.MyDefaultSqlInjector] [?] [?] - class java.lang.Object ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[2025-06-15 17:48:27.643] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Insert] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
[2025-06-15 17:48:27.644] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Delete] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.delete] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Delete]
[2025-06-15 17:48:27.647] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Update] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
[2025-06-15 17:49:15.649] [WARN ] [main] [com.xxl.job.core.executor.XxlJobExecutor] [?] [?] - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
[2025-06-15 17:50:00.801] [WARN ] [scheduling-1] [com.xinghuo.fruugo.analysis.util.WechatWorkUtil] [?] [?] - 企业微信webhook地址未配置，无法发送通知
[2025-06-15 17:50:00.908] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 17:50:00.986] [WARN ] [scheduling-1] [com.xinghuo.fruugo.analysis.util.WechatWorkUtil] [?] [?] - 企业微信webhook地址未配置，无法发送通知
[2025-06-15 17:50:01.060] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30988 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 17:55:00.134] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 17:55:18.893] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30980 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 18:07:13.213] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 18:07:20.476] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30985 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 18:07:39.602] [WARN ] [SpringApplicationShutdownHook] [o.s.c.annotation.CommonAnnotationBeanPostProcessor] [?] [?] - Destroy method on bean with name 'idGeneratorConfig' threw an exception: org.springframework.dao.InvalidDataAccessApiUsageException: Redisson is shutdown
[2025-06-15 18:07:46.925] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:46.937] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:46.945] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.022] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceUtil' of type [com.xinghuo.common.database.util.DataSourceUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.039] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'config-com.xinghuo.common.util.ConfigValueUtil' of type [com.xinghuo.common.util.ConfigValueUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.053] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAssistConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAssistConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.056] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$HikariDataSourceCreatorConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$HikariDataSourceCreatorConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.060] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'hikariDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.hikaricp.HikariDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.063] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$DruidDataSourceCreatorConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$DruidDataSourceCreatorConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.073] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'druidDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.druid.DruidDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.075] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.077] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'basicDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.basic.BasicDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.079] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'jndiDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.jndi.JndiDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.082] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceInitEvent' of type [com.baomidou.dynamic.datasource.event.EncDataSourceInitEvent] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.083] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.DefaultDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.086] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'ymlDynamicDataSourceProvider' of type [com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.087] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'mybatisPlusConfig' of type [com.xinghuo.common.database.config.MybatisPlusConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.098] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'myTenantLineInnerInterceptor' of type [com.xinghuo.common.database.plugins.MyTenantLineInnerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.121] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'tenantDataSourceUtil' of type [com.xinghuo.common.database.util.TenantDataSourceUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:47.122] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'myDynamicDatasourceGeneratorAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:07:50.856] [WARN ] [main] [c.b.mybatisplus.core.metadata.TableInfoHelper] [?] [?] - Can not find table primary key in Class: "java.lang.Object".
[2025-06-15 18:07:50.856] [WARN ] [main] [c.x.common.database.plugins.MyDefaultSqlInjector] [?] [?] - class java.lang.Object ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[2025-06-15 18:07:50.857] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Insert] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
[2025-06-15 18:07:50.859] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Delete] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.delete] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Delete]
[2025-06-15 18:07:50.863] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Update] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
[2025-06-15 18:08:39.825] [WARN ] [main] [com.xxl.job.core.executor.XxlJobExecutor] [?] [?] - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
[2025-06-15 18:10:00.457] [WARN ] [scheduling-1] [com.xinghuo.fruugo.analysis.util.WechatWorkUtil] [?] [?] - 企业微信webhook地址未配置，无法发送通知
[2025-06-15 18:10:00.543] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 18:10:00.585] [WARN ] [scheduling-1] [com.xinghuo.fruugo.analysis.util.WechatWorkUtil] [?] [?] - 企业微信webhook地址未配置，无法发送通知
[2025-06-15 18:10:00.657] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30985 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 18:15:00.087] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 18:15:00.159] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 31006 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 18:20:00.085] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 18:20:00.232] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30988 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 18:25:00.178] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 18:25:00.317] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30999 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 18:30:00.137] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 18:30:03.209] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30982 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 18:37:15.036] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 18:37:17.619] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30986 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 18:40:00.168] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 18:40:01.301] [WARN ] [scheduling-1] [com.xinghuo.fruugo.analysis.util.WechatWorkUtil] [?] [?] - 企业微信webhook地址未配置，无法发送通知
[2025-06-15 18:40:01.396] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 31012 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 18:40:57.337] [WARN ] [SpringApplicationShutdownHook] [o.s.c.annotation.CommonAnnotationBeanPostProcessor] [?] [?] - Destroy method on bean with name 'idGeneratorConfig' threw an exception: org.springframework.dao.InvalidDataAccessApiUsageException: Redisson is shutdown
[2025-06-15 18:41:04.789] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.801] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.808] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.891] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceUtil' of type [com.xinghuo.common.database.util.DataSourceUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.910] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'config-com.xinghuo.common.util.ConfigValueUtil' of type [com.xinghuo.common.util.ConfigValueUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.926] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAssistConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAssistConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.930] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$HikariDataSourceCreatorConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$HikariDataSourceCreatorConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.933] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'hikariDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.hikaricp.HikariDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.936] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$DruidDataSourceCreatorConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$DruidDataSourceCreatorConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.948] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'druidDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.druid.DruidDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.950] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.952] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'basicDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.basic.BasicDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.954] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'jndiDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.jndi.JndiDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.957] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceInitEvent' of type [com.baomidou.dynamic.datasource.event.EncDataSourceInitEvent] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.959] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.DefaultDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.962] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'ymlDynamicDataSourceProvider' of type [com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.963] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'mybatisPlusConfig' of type [com.xinghuo.common.database.config.MybatisPlusConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.974] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'myTenantLineInnerInterceptor' of type [com.xinghuo.common.database.plugins.MyTenantLineInnerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.993] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'tenantDataSourceUtil' of type [com.xinghuo.common.database.util.TenantDataSourceUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:04.994] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'myDynamicDatasourceGeneratorAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 18:41:08.872] [WARN ] [main] [c.b.mybatisplus.core.metadata.TableInfoHelper] [?] [?] - Can not find table primary key in Class: "java.lang.Object".
[2025-06-15 18:41:08.873] [WARN ] [main] [c.x.common.database.plugins.MyDefaultSqlInjector] [?] [?] - class java.lang.Object ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[2025-06-15 18:41:08.873] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Insert] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
[2025-06-15 18:41:08.875] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Delete] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.delete] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Delete]
[2025-06-15 18:41:08.879] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Update] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
[2025-06-15 18:41:57.583] [WARN ] [main] [com.xxl.job.core.executor.XxlJobExecutor] [?] [?] - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
[2025-06-15 18:45:04.085] [WARN ] [scheduling-1] [com.xinghuo.fruugo.analysis.util.WechatWorkUtil] [?] [?] - 企业微信webhook地址未配置，无法发送通知
[2025-06-15 18:45:04.152] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 18:45:04.207] [WARN ] [scheduling-1] [com.xinghuo.fruugo.analysis.util.WechatWorkUtil] [?] [?] - 企业微信webhook地址未配置，无法发送通知
[2025-06-15 18:45:04.255] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30986 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 18:50:00.198] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 18:50:00.653] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30987 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 18:55:00.247] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 18:55:19.878] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 31012 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 19:00:00.422] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 19:00:31.864] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30978 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 19:13:43.036] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 19:13:49.849] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 31006 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 19:15:00.111] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 19:15:09.616] [WARN ] [scheduling-1] [com.xinghuo.fruugo.analysis.util.WechatWorkUtil] [?] [?] - 企业微信webhook地址未配置，无法发送通知
[2025-06-15 19:15:09.702] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 31006 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 19:17:56.962] [WARN ] [SpringApplicationShutdownHook] [o.s.c.annotation.CommonAnnotationBeanPostProcessor] [?] [?] - Destroy method on bean with name 'idGeneratorConfig' threw an exception: org.springframework.dao.InvalidDataAccessApiUsageException: Redisson is shutdown
[2025-06-15 19:18:04.690] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 19:18:04.710] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 19:18:04.719] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 19:18:04.806] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceUtil' of type [com.xinghuo.common.database.util.DataSourceUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 19:18:04.825] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'config-com.xinghuo.common.util.ConfigValueUtil' of type [com.xinghuo.common.util.ConfigValueUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 19:18:04.839] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAssistConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAssistConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 19:18:04.843] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$HikariDataSourceCreatorConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$HikariDataSourceCreatorConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 19:18:04.848] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'hikariDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.hikaricp.HikariDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 19:18:04.851] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$DruidDataSourceCreatorConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$DruidDataSourceCreatorConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 19:18:04.864] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'druidDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.druid.DruidDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 19:18:04.867] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 19:18:04.870] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'basicDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.basic.BasicDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 19:18:04.872] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'jndiDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.jndi.JndiDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 19:18:04.874] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceInitEvent' of type [com.baomidou.dynamic.datasource.event.EncDataSourceInitEvent] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 19:18:04.876] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.DefaultDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 19:18:04.878] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'ymlDynamicDataSourceProvider' of type [com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 19:18:04.880] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'mybatisPlusConfig' of type [com.xinghuo.common.database.config.MybatisPlusConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 19:18:04.895] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'myTenantLineInnerInterceptor' of type [com.xinghuo.common.database.plugins.MyTenantLineInnerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 19:18:04.912] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'tenantDataSourceUtil' of type [com.xinghuo.common.database.util.TenantDataSourceUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 19:18:04.914] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'myDynamicDatasourceGeneratorAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 19:18:08.704] [WARN ] [main] [c.b.mybatisplus.core.metadata.TableInfoHelper] [?] [?] - Can not find table primary key in Class: "java.lang.Object".
[2025-06-15 19:18:08.704] [WARN ] [main] [c.x.common.database.plugins.MyDefaultSqlInjector] [?] [?] - class java.lang.Object ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[2025-06-15 19:18:08.705] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Insert] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
[2025-06-15 19:18:08.706] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Delete] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.delete] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Delete]
[2025-06-15 19:18:08.710] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Update] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
[2025-06-15 19:18:57.400] [WARN ] [main] [com.xxl.job.core.executor.XxlJobExecutor] [?] [?] - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
[2025-06-15 19:20:08.541] [WARN ] [scheduling-1] [com.xinghuo.fruugo.analysis.util.WechatWorkUtil] [?] [?] - 企业微信webhook地址未配置，无法发送通知
[2025-06-15 19:20:08.618] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 19:20:08.679] [WARN ] [scheduling-1] [com.xinghuo.fruugo.analysis.util.WechatWorkUtil] [?] [?] - 企业微信webhook地址未配置，无法发送通知
[2025-06-15 19:20:08.724] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30988 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 19:20:14.832] [WARN ] [http-nio-32000-exec-3] [com.xinghuo.amazon.util.AmazonUtil] [?] [?] - 提取整数失败: Price, product page$6.99$6.99 Typical: $7.99 Typical: $7.99$7.99
java.lang.NumberFormatException: For input string: "699699799799799"
	at java.base/java.lang.NumberFormatException.forInputString(NumberFormatException.java:67)
	at java.base/java.lang.Integer.parseInt(Integer.java:665)
	at java.base/java.lang.Integer.parseInt(Integer.java:781)
	at com.xinghuo.amazon.util.AmazonUtil.extractInteger(AmazonUtil.java:105)
	at com.xinghuo.amazon.collect.service.AmazonHtmlParseService.extractRatingInfo(AmazonHtmlParseService.java:292)
	at com.xinghuo.amazon.collect.service.AmazonHtmlParseService.parseProductElement(AmazonHtmlParseService.java:128)
	at com.xinghuo.amazon.collect.service.AmazonHtmlParseService.parseListPageHtml(AmazonHtmlParseService.java:70)
	at com.xinghuo.amazon.collect.controller.AmazonListTaskController.submitForm(AmazonListTaskController.java:243)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:578)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:351)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.aspectj.AspectJAfterAdvice.invoke(AspectJAfterAdvice.java:49)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.xinghuo.admin.aop.RequestLogAspect.doAroundService(RequestLogAspect.java:59)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:578)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:637)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:627)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.xinghuo.admin.aop.DataSourceBindAspect.doAroundService(DataSourceBindAspect.java:61)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:578)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:637)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:627)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.xinghuo.amazon.collect.controller.AmazonListTaskController$$SpringCGLIB$$0.submitForm(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:578)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:925)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:830)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:925)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:593)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at com.github.xiaoymin.knife4j.extend.filter.basic.JakartaServletSecurityBasicAuthFilter.doFilter(JakartaServletSecurityBasicAuthFilter.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at com.xinghuo.common.config.SecurityConfiguration$ClearThreadContextFilter.doFilter(SecurityConfiguration.java:121)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1623)
[2025-06-15 19:20:15.875] [WARN ] [http-nio-32000-exec-3] [com.xinghuo.amazon.util.AmazonUtil] [?] [?] - 提取整数失败: Price, product page$6.69$6.69 List: $7.69 List: $7.69$7.69
java.lang.NumberFormatException: For input string: "669669769769769"
	at java.base/java.lang.NumberFormatException.forInputString(NumberFormatException.java:67)
	at java.base/java.lang.Integer.parseInt(Integer.java:665)
	at java.base/java.lang.Integer.parseInt(Integer.java:781)
	at com.xinghuo.amazon.util.AmazonUtil.extractInteger(AmazonUtil.java:105)
	at com.xinghuo.amazon.collect.service.AmazonHtmlParseService.extractRatingInfo(AmazonHtmlParseService.java:292)
	at com.xinghuo.amazon.collect.service.AmazonHtmlParseService.parseProductElement(AmazonHtmlParseService.java:128)
	at com.xinghuo.amazon.collect.service.AmazonHtmlParseService.parseListPageHtml(AmazonHtmlParseService.java:70)
	at com.xinghuo.amazon.collect.controller.AmazonListTaskController.submitForm(AmazonListTaskController.java:243)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:578)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:351)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.aspectj.AspectJAfterAdvice.invoke(AspectJAfterAdvice.java:49)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.xinghuo.admin.aop.RequestLogAspect.doAroundService(RequestLogAspect.java:59)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:578)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:637)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:627)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint.proceed(MethodInvocationProceedingJoinPoint.java:89)
	at com.xinghuo.admin.aop.DataSourceBindAspect.doAroundService(DataSourceBindAspect.java:61)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:578)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethodWithGivenArgs(AbstractAspectJAdvice.java:637)
	at org.springframework.aop.aspectj.AbstractAspectJAdvice.invokeAdviceMethod(AbstractAspectJAdvice.java:627)
	at org.springframework.aop.aspectj.AspectJAroundAdvice.invoke(AspectJAroundAdvice.java:71)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:173)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.xinghuo.amazon.collect.controller.AmazonListTaskController$$SpringCGLIB$$0.submitForm(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)
	at java.base/java.lang.reflect.Method.invoke(Method.java:578)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:925)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:830)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPut(FrameworkServlet.java:925)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:593)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at com.github.xiaoymin.knife4j.extend.filter.basic.JakartaServletSecurityBasicAuthFilter.doFilter(JakartaServletSecurityBasicAuthFilter.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at com.alibaba.druid.support.jakarta.WebStatFilter.doFilter(WebStatFilter.java:113)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at com.xinghuo.common.config.SecurityConfiguration$ClearThreadContextFilter.doFilter(SecurityConfiguration.java:121)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaServletFilter.doFilter(SaServletFilter.java:150)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at cn.dev33.satoken.filter.SaPathCheckFilterForJakartaServlet.doFilter(SaPathCheckFilterForJakartaServlet.java:55)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:109)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:101)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1623)
[2025-06-15 19:25:47.053] [WARN ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3b843fa0] Transaction not enabled
[2025-06-15 19:25:47.372] [WARN ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1069a0ee] Transaction not enabled
[2025-06-15 19:25:47.700] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 19:25:47.969] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 31009 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 19:25:52.980] [WARN ] [SpringApplicationShutdownHook] [o.s.c.annotation.CommonAnnotationBeanPostProcessor] [?] [?] - Destroy method on bean with name 'idGeneratorConfig' threw an exception: org.springframework.dao.InvalidDataAccessApiUsageException: Redisson is shutdown
[2025-06-15 19:26:00.639] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 19:26:00.651] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 19:26:00.658] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 19:26:00.735] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceUtil' of type [com.xinghuo.common.database.util.DataSourceUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 19:26:00.753] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'config-com.xinghuo.common.util.ConfigValueUtil' of type [com.xinghuo.common.util.ConfigValueUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 19:26:00.767] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAssistConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAssistConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 19:26:00.771] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$HikariDataSourceCreatorConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$HikariDataSourceCreatorConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 19:26:00.775] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'hikariDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.hikaricp.HikariDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 19:26:00.778] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$DruidDataSourceCreatorConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$DruidDataSourceCreatorConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 19:26:00.788] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'druidDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.druid.DruidDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 19:26:00.790] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 19:26:00.793] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'basicDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.basic.BasicDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 19:26:00.795] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'jndiDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.jndi.JndiDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 19:26:00.798] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceInitEvent' of type [com.baomidou.dynamic.datasource.event.EncDataSourceInitEvent] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 19:26:00.800] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.DefaultDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 19:26:00.802] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'ymlDynamicDataSourceProvider' of type [com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 19:26:00.804] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'mybatisPlusConfig' of type [com.xinghuo.common.database.config.MybatisPlusConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 19:26:00.814] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'myTenantLineInnerInterceptor' of type [com.xinghuo.common.database.plugins.MyTenantLineInnerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 19:26:00.836] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'tenantDataSourceUtil' of type [com.xinghuo.common.database.util.TenantDataSourceUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 19:26:00.837] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'myDynamicDatasourceGeneratorAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 19:26:04.593] [WARN ] [main] [c.b.mybatisplus.core.metadata.TableInfoHelper] [?] [?] - Can not find table primary key in Class: "java.lang.Object".
[2025-06-15 19:26:04.593] [WARN ] [main] [c.x.common.database.plugins.MyDefaultSqlInjector] [?] [?] - class java.lang.Object ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[2025-06-15 19:26:04.595] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Insert] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
[2025-06-15 19:26:04.597] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Delete] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.delete] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Delete]
[2025-06-15 19:26:04.601] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Update] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
[2025-06-15 19:26:52.975] [WARN ] [main] [com.xxl.job.core.executor.XxlJobExecutor] [?] [?] - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
[2025-06-15 19:27:23.979] [WARN ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3f4c6e3c] Transaction not enabled
[2025-06-15 19:28:35.000] [WARN ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@76dd800c] Transaction not enabled
[2025-06-15 19:29:00.792] [WARN ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@22f221ca] Transaction not enabled
[2025-06-15 19:29:27.949] [WARN ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7ba246ba] Transaction not enabled
[2025-06-15 19:29:55.791] [WARN ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@d49a884] Transaction not enabled
[2025-06-15 19:30:00.259] [WARN ] [scheduling-1] [com.xinghuo.fruugo.analysis.util.WechatWorkUtil] [?] [?] - 企业微信webhook地址未配置，无法发送通知
[2025-06-15 19:30:00.367] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 19:30:06.299] [WARN ] [scheduling-1] [com.xinghuo.fruugo.analysis.util.WechatWorkUtil] [?] [?] - 企业微信webhook地址未配置，无法发送通知
[2025-06-15 19:30:06.357] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30982 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 19:30:24.887] [WARN ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@151529b9] Transaction not enabled
[2025-06-15 19:30:40.724] [WARN ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@60512257] Transaction not enabled
[2025-06-15 19:30:59.762] [WARN ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4683cef7] Transaction not enabled
[2025-06-15 19:39:57.298] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 19:40:35.012] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30982 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 19:45:00.179] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 19:45:21.914] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 31002 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 19:50:00.135] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 19:50:00.502] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30981 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 19:55:00.106] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 19:55:27.979] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30983 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 20:01:41.525] [WARN ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@343203dd] Transaction not enabled
[2025-06-15 20:01:58.042] [WARN ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@20cf4915] Transaction not enabled
[2025-06-15 20:15:42.613] [WARN ] [scheduling-1] [com.xinghuo.fruugo.analysis.util.WechatWorkUtil] [?] [?] - 企业微信webhook地址未配置，无法发送通知
[2025-06-15 20:15:42.771] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 20:16:34.117] [WARN ] [scheduling-1] [com.xinghuo.fruugo.analysis.util.WechatWorkUtil] [?] [?] - 企业微信webhook地址未配置，无法发送通知
[2025-06-15 20:16:34.201] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30982 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 20:19:18.074] [WARN ] [SpringApplicationShutdownHook] [o.s.c.annotation.CommonAnnotationBeanPostProcessor] [?] [?] - Destroy method on bean with name 'idGeneratorConfig' threw an exception: org.springframework.dao.InvalidDataAccessApiUsageException: Redisson is shutdown
[2025-06-15 20:19:25.874] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 20:19:25.888] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 20:19:25.897] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 20:19:25.979] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceUtil' of type [com.xinghuo.common.database.util.DataSourceUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 20:19:25.998] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'config-com.xinghuo.common.util.ConfigValueUtil' of type [com.xinghuo.common.util.ConfigValueUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 20:19:26.012] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAssistConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAssistConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 20:19:26.016] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$HikariDataSourceCreatorConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$HikariDataSourceCreatorConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 20:19:26.020] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'hikariDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.hikaricp.HikariDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 20:19:26.022] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$DruidDataSourceCreatorConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$DruidDataSourceCreatorConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 20:19:26.035] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'druidDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.druid.DruidDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 20:19:26.037] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 20:19:26.047] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'basicDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.basic.BasicDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 20:19:26.049] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'jndiDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.jndi.JndiDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 20:19:26.052] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceInitEvent' of type [com.baomidou.dynamic.datasource.event.EncDataSourceInitEvent] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 20:19:26.054] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.DefaultDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 20:19:26.055] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'ymlDynamicDataSourceProvider' of type [com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 20:19:26.057] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'mybatisPlusConfig' of type [com.xinghuo.common.database.config.MybatisPlusConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 20:19:26.069] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'myTenantLineInnerInterceptor' of type [com.xinghuo.common.database.plugins.MyTenantLineInnerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 20:19:26.086] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'tenantDataSourceUtil' of type [com.xinghuo.common.database.util.TenantDataSourceUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 20:19:26.088] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'myDynamicDatasourceGeneratorAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 20:19:30.158] [WARN ] [main] [c.b.mybatisplus.core.metadata.TableInfoHelper] [?] [?] - Can not find table primary key in Class: "java.lang.Object".
[2025-06-15 20:19:30.158] [WARN ] [main] [c.x.common.database.plugins.MyDefaultSqlInjector] [?] [?] - class java.lang.Object ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[2025-06-15 20:19:30.159] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Insert] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
[2025-06-15 20:19:30.163] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Delete] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.delete] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Delete]
[2025-06-15 20:19:30.171] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Update] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
[2025-06-15 20:20:19.364] [WARN ] [main] [com.xxl.job.core.executor.XxlJobExecutor] [?] [?] - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
[2025-06-15 20:21:45.269] [WARN ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@754fb81] Transaction not enabled
[2025-06-15 20:21:59.549] [WARN ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2d9f45b8] Transaction not enabled
[2025-06-15 20:22:21.425] [WARN ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@435ed8a1] Transaction not enabled
[2025-06-15 20:22:39.477] [WARN ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@15fdbe8e] Transaction not enabled
[2025-06-15 20:22:59.474] [WARN ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@58024541] Transaction not enabled
[2025-06-15 20:23:22.412] [WARN ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@42b6fb4d] Transaction not enabled
[2025-06-15 20:23:35.827] [WARN ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6b948a2] Transaction not enabled
[2025-06-15 20:25:00.200] [WARN ] [scheduling-1] [com.xinghuo.fruugo.analysis.util.WechatWorkUtil] [?] [?] - 企业微信webhook地址未配置，无法发送通知
[2025-06-15 20:25:00.272] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 20:25:10.423] [WARN ] [scheduling-1] [com.xinghuo.fruugo.analysis.util.WechatWorkUtil] [?] [?] - 企业微信webhook地址未配置，无法发送通知
[2025-06-15 20:25:10.467] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30982 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 20:27:01.622] [WARN ] [SpringApplicationShutdownHook] [o.s.c.annotation.CommonAnnotationBeanPostProcessor] [?] [?] - Destroy method on bean with name 'idGeneratorConfig' threw an exception: org.springframework.dao.InvalidDataAccessApiUsageException: Redisson is shutdown
[2025-06-15 20:44:07.405] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 20:44:07.418] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 20:44:07.425] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 20:44:07.506] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceUtil' of type [com.xinghuo.common.database.util.DataSourceUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 20:44:07.523] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'config-com.xinghuo.common.util.ConfigValueUtil' of type [com.xinghuo.common.util.ConfigValueUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 20:44:07.537] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAssistConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAssistConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 20:44:07.541] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$HikariDataSourceCreatorConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$HikariDataSourceCreatorConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 20:44:07.545] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'hikariDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.hikaricp.HikariDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 20:44:07.548] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$DruidDataSourceCreatorConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$DruidDataSourceCreatorConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 20:44:07.558] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'druidDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.druid.DruidDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 20:44:07.560] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 20:44:07.563] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'basicDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.basic.BasicDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 20:44:07.565] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'jndiDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.jndi.JndiDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 20:44:07.568] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceInitEvent' of type [com.baomidou.dynamic.datasource.event.EncDataSourceInitEvent] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 20:44:07.570] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.DefaultDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 20:44:07.573] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'ymlDynamicDataSourceProvider' of type [com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 20:44:07.575] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'mybatisPlusConfig' of type [com.xinghuo.common.database.config.MybatisPlusConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 20:44:07.593] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'myTenantLineInnerInterceptor' of type [com.xinghuo.common.database.plugins.MyTenantLineInnerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 20:44:07.609] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'tenantDataSourceUtil' of type [com.xinghuo.common.database.util.TenantDataSourceUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 20:44:07.611] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'myDynamicDatasourceGeneratorAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 20:44:11.447] [WARN ] [main] [c.b.mybatisplus.core.metadata.TableInfoHelper] [?] [?] - Can not find table primary key in Class: "java.lang.Object".
[2025-06-15 20:44:11.447] [WARN ] [main] [c.x.common.database.plugins.MyDefaultSqlInjector] [?] [?] - class java.lang.Object ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[2025-06-15 20:44:11.448] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Insert] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
[2025-06-15 20:44:11.450] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Delete] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.delete] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Delete]
[2025-06-15 20:44:11.453] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Update] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
[2025-06-15 20:45:44.019] [WARN ] [main] [com.xxl.job.core.executor.XxlJobExecutor] [?] [?] - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
[2025-06-15 20:48:27.132] [WARN ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4cdfa698] Transaction not enabled
[2025-06-15 20:48:37.985] [WARN ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@56a9202e] Transaction not enabled
[2025-06-15 20:50:00.428] [WARN ] [scheduling-1] [com.xinghuo.fruugo.analysis.util.WechatWorkUtil] [?] [?] - 企业微信webhook地址未配置，无法发送通知
[2025-06-15 20:50:00.521] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 20:50:00.618] [WARN ] [scheduling-1] [com.xinghuo.fruugo.analysis.util.WechatWorkUtil] [?] [?] - 企业微信webhook地址未配置，无法发送通知
[2025-06-15 20:50:00.668] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30982 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 20:52:03.786] [WARN ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@44f8f595] Transaction not enabled
[2025-06-15 20:52:09.210] [WARN ] [SpringApplicationShutdownHook] [o.s.c.annotation.CommonAnnotationBeanPostProcessor] [?] [?] - Destroy method on bean with name 'idGeneratorConfig' threw an exception: org.springframework.dao.InvalidDataAccessApiUsageException: Redisson is shutdown
[2025-06-15 20:52:16.754] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 20:52:16.766] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 20:52:16.773] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 20:52:16.848] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceUtil' of type [com.xinghuo.common.database.util.DataSourceUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 20:52:16.865] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'config-com.xinghuo.common.util.ConfigValueUtil' of type [com.xinghuo.common.util.ConfigValueUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 20:52:16.880] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAssistConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAssistConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 20:52:16.885] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$HikariDataSourceCreatorConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$HikariDataSourceCreatorConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 20:52:16.888] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'hikariDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.hikaricp.HikariDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 20:52:16.891] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$DruidDataSourceCreatorConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$DruidDataSourceCreatorConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 20:52:16.903] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'druidDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.druid.DruidDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 20:52:16.905] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 20:52:16.907] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'basicDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.basic.BasicDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 20:52:16.908] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'jndiDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.jndi.JndiDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 20:52:16.911] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceInitEvent' of type [com.baomidou.dynamic.datasource.event.EncDataSourceInitEvent] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 20:52:16.913] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.DefaultDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 20:52:16.916] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'ymlDynamicDataSourceProvider' of type [com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 20:52:16.917] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'mybatisPlusConfig' of type [com.xinghuo.common.database.config.MybatisPlusConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 20:52:16.928] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'myTenantLineInnerInterceptor' of type [com.xinghuo.common.database.plugins.MyTenantLineInnerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 20:52:16.952] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'tenantDataSourceUtil' of type [com.xinghuo.common.database.util.TenantDataSourceUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 20:52:16.953] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'myDynamicDatasourceGeneratorAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 20:52:20.831] [WARN ] [main] [c.b.mybatisplus.core.metadata.TableInfoHelper] [?] [?] - Can not find table primary key in Class: "java.lang.Object".
[2025-06-15 20:52:20.831] [WARN ] [main] [c.x.common.database.plugins.MyDefaultSqlInjector] [?] [?] - class java.lang.Object ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[2025-06-15 20:52:20.832] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Insert] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
[2025-06-15 20:52:20.835] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Delete] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.delete] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Delete]
[2025-06-15 20:52:20.839] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Update] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
[2025-06-15 20:53:56.075] [WARN ] [main] [com.xxl.job.core.executor.XxlJobExecutor] [?] [?] - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
[2025-06-15 20:55:00.236] [WARN ] [scheduling-1] [com.xinghuo.fruugo.analysis.util.WechatWorkUtil] [?] [?] - 企业微信webhook地址未配置，无法发送通知
[2025-06-15 20:55:00.332] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 20:55:21.060] [WARN ] [scheduling-1] [com.xinghuo.fruugo.analysis.util.WechatWorkUtil] [?] [?] - 企业微信webhook地址未配置，无法发送通知
[2025-06-15 20:55:21.153] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30986 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 20:55:21.649] [WARN ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@30878500] Transaction not enabled
[2025-06-15 20:55:37.435] [WARN ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@14ed2ca9] Transaction not enabled
[2025-06-15 20:55:53.476] [WARN ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@f4bd03a] Transaction not enabled
[2025-06-15 20:56:08.395] [WARN ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4dd2d772] Transaction not enabled
[2025-06-15 20:56:21.552] [WARN ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@21d07113] Transaction not enabled
[2025-06-15 20:56:34.358] [WARN ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@235d7b05] Transaction not enabled
[2025-06-15 20:56:58.547] [WARN ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@f2b4814] Transaction not enabled
[2025-06-15 20:57:11.071] [WARN ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@491553a1] Transaction not enabled
[2025-06-15 20:57:24.643] [WARN ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@299e57a6] Transaction not enabled
[2025-06-15 20:57:38.458] [WARN ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@d2d6ba0] Transaction not enabled
[2025-06-15 20:57:52.543] [WARN ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@364ebffa] Transaction not enabled
[2025-06-15 20:58:16.317] [WARN ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@52f680e9] Transaction not enabled
[2025-06-15 20:58:47.354] [WARN ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@75e7949a] Transaction not enabled
[2025-06-15 20:59:19.785] [WARN ] [http-nio-32000-exec-5] [c.x.amazon.collect.service.AmazonHtmlParseService] [?] [?] - 未找到商品元素，可能页面结构已变化
[2025-06-15 20:59:19.785] [WARN ] [http-nio-32000-exec-5] [c.x.a.collect.controller.AmazonListTaskController] [?] [?] - HTML解析未找到任何商品信息，taskId: 47
[2025-06-15 20:59:52.658] [WARN ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1a31d9bd] Transaction not enabled
[2025-06-15 21:00:00.141] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 21:00:23.796] [WARN ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6ddf4516] Transaction not enabled
[2025-06-15 21:00:29.102] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30972 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 21:00:53.348] [WARN ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2d47fc23] Transaction not enabled
[2025-06-15 21:01:19.861] [WARN ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@627daab2] Transaction not enabled
[2025-06-15 21:01:37.797] [WARN ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@401f657b] Transaction not enabled
[2025-06-15 21:01:52.574] [WARN ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@77b713d8] Transaction not enabled
[2025-06-15 21:02:11.874] [WARN ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@75888426] Transaction not enabled
[2025-06-15 21:02:41.931] [WARN ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@32a33f90] Transaction not enabled
[2025-06-15 21:02:59.027] [WARN ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7ff3f4b] Transaction not enabled
[2025-06-15 21:03:16.437] [WARN ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@504e4f58] Transaction not enabled
[2025-06-15 21:03:50.155] [WARN ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6f94e3f7] Transaction not enabled
[2025-06-15 21:04:15.630] [WARN ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@16c36d61] Transaction not enabled
[2025-06-15 21:04:42.090] [WARN ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6f4b214d] Transaction not enabled
[2025-06-15 21:05:12.632] [WARN ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@c77ec81] Transaction not enabled
[2025-06-15 21:05:26.416] [WARN ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2128e6c8] Transaction not enabled
[2025-06-15 21:05:39.106] [WARN ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@497a481] Transaction not enabled
[2025-06-15 21:05:51.273] [WARN ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@59ab9f0a] Transaction not enabled
[2025-06-15 21:06:06.213] [WARN ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7319440] Transaction not enabled
[2025-06-15 21:06:19.577] [WARN ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@60842578] Transaction not enabled
[2025-06-15 21:06:31.776] [WARN ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1b1e2a11] Transaction not enabled
[2025-06-15 21:06:44.122] [WARN ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@14a621e8] Transaction not enabled
[2025-06-15 21:06:57.331] [WARN ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@795bbe45] Transaction not enabled
[2025-06-15 21:07:13.731] [WARN ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1af18d02] Transaction not enabled
[2025-06-15 21:07:27.531] [WARN ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@32791177] Transaction not enabled
[2025-06-15 21:07:40.979] [WARN ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4c8bc0df] Transaction not enabled
[2025-06-15 21:08:20.426] [WARN ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@347b23c1] Transaction not enabled
[2025-06-15 21:08:36.563] [WARN ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@54d900d7] Transaction not enabled
[2025-06-15 21:08:51.847] [WARN ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1fcc18e9] Transaction not enabled
[2025-06-15 21:09:05.609] [WARN ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@43e97499] Transaction not enabled
[2025-06-15 21:09:18.468] [WARN ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@58bf67eb] Transaction not enabled
[2025-06-15 21:09:33.430] [WARN ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@47a2a6fc] Transaction not enabled
[2025-06-15 21:10:15.501] [WARN ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@36a858ba] Transaction not enabled
[2025-06-15 21:10:30.291] [WARN ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4af4e6d1] Transaction not enabled
[2025-06-15 21:10:45.192] [WARN ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@220f217e] Transaction not enabled
[2025-06-15 21:11:01.310] [WARN ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1f760b3a] Transaction not enabled
[2025-06-15 21:11:21.908] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 21:11:23.623] [WARN ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@252b84e2] Transaction not enabled
[2025-06-15 21:11:46.679] [WARN ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7ae8407c] Transaction not enabled
[2025-06-15 21:11:49.795] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30991 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 21:12:06.809] [WARN ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@375e4b1f] Transaction not enabled
[2025-06-15 21:12:21.580] [WARN ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3d4d063e] Transaction not enabled
[2025-06-15 21:12:38.098] [WARN ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3b6737f8] Transaction not enabled
[2025-06-15 21:12:53.613] [WARN ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7eb442d1] Transaction not enabled
[2025-06-15 21:13:14.642] [WARN ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7e22c0d3] Transaction not enabled
[2025-06-15 21:13:31.531] [WARN ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@163832c4] Transaction not enabled
[2025-06-15 21:13:48.802] [WARN ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@69378ea2] Transaction not enabled
[2025-06-15 21:13:50.047] [WARN ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - 所有任务都已存在，无需保存
[2025-06-15 21:15:00.109] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 21:15:00.213] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30978 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 21:20:00.133] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 21:20:00.186] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30985 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 21:20:31.657] [WARN ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@346de8e8] Transaction not enabled
[2025-06-15 21:20:44.599] [WARN ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@711cf6b5] Transaction not enabled
[2025-06-15 21:20:56.804] [WARN ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1e247660] Transaction not enabled
[2025-06-15 21:21:22.418] [WARN ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@682331cc] Transaction not enabled
[2025-06-15 21:21:25.540] [WARN ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - 所有任务都已存在，无需保存
[2025-06-15 21:21:44.008] [WARN ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@360e2913] Transaction not enabled
[2025-06-15 21:21:45.574] [WARN ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@98a6e45] Transaction not enabled
[2025-06-15 21:22:01.600] [WARN ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@48561cf4] Transaction not enabled
[2025-06-15 21:22:05.000] [WARN ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@41be85b5] Transaction not enabled
[2025-06-15 21:22:19.776] [WARN ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@d49ee6] Transaction not enabled
[2025-06-15 21:22:23.115] [WARN ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3ad4ecc7] Transaction not enabled
[2025-06-15 21:22:35.823] [WARN ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@67dddb94] Transaction not enabled
[2025-06-15 21:22:37.672] [WARN ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - 所有任务都已存在，无需保存
[2025-06-15 21:25:00.060] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 21:25:00.107] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30983 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 21:30:00.113] [WARN ] [scheduling-1] [com.xinghuo.fruugo.analysis.util.WechatWorkUtil] [?] [?] - 企业微信webhook地址未配置，无法发送通知
[2025-06-15 21:30:00.315] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 21:30:00.416] [WARN ] [scheduling-1] [com.xinghuo.fruugo.analysis.util.WechatWorkUtil] [?] [?] - 企业微信webhook地址未配置，无法发送通知
[2025-06-15 21:30:00.669] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30979 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 21:36:06.418] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 21:36:10.793] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30985 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 21:40:00.073] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 21:40:00.121] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30986 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 21:45:00.091] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 21:45:00.154] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30982 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 21:50:00.072] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 21:50:00.111] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30982 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 21:50:24.052] [WARN ] [SpringApplicationShutdownHook] [o.s.c.annotation.CommonAnnotationBeanPostProcessor] [?] [?] - Destroy method on bean with name 'idGeneratorConfig' threw an exception: org.springframework.dao.InvalidDataAccessApiUsageException: Redisson is shutdown
[2025-06-15 21:50:33.091] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAopConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 21:50:33.104] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 21:50:33.111] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 21:50:33.203] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceUtil' of type [com.xinghuo.common.database.util.DataSourceUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 21:50:33.221] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'config-com.xinghuo.common.util.ConfigValueUtil' of type [com.xinghuo.common.util.ConfigValueUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 21:50:33.235] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAssistConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAssistConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 21:50:33.239] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$HikariDataSourceCreatorConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$HikariDataSourceCreatorConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 21:50:33.243] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'hikariDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.hikaricp.HikariDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 21:50:33.246] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$DruidDataSourceCreatorConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$DruidDataSourceCreatorConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 21:50:33.260] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'druidDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.druid.DruidDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 21:50:33.262] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceCreatorAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 21:50:33.264] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'basicDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.basic.BasicDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 21:50:33.266] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'jndiDataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.jndi.JndiDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 21:50:33.269] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceInitEvent' of type [com.baomidou.dynamic.datasource.event.EncDataSourceInitEvent] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 21:50:33.270] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'dataSourceCreator' of type [com.baomidou.dynamic.datasource.creator.DefaultDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 21:50:33.273] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'ymlDynamicDataSourceProvider' of type [com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 21:50:33.277] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'mybatisPlusConfig' of type [com.xinghuo.common.database.config.MybatisPlusConfig$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 21:50:33.290] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'myTenantLineInnerInterceptor' of type [com.xinghuo.common.database.plugins.MyTenantLineInnerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 21:50:33.307] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'tenantDataSourceUtil' of type [com.xinghuo.common.database.util.TenantDataSourceUtil] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 21:50:33.308] [WARN ] [main] [o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker] [?] [?] - Bean 'myDynamicDatasourceGeneratorAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.
[2025-06-15 21:50:37.349] [WARN ] [main] [c.b.mybatisplus.core.metadata.TableInfoHelper] [?] [?] - Can not find table primary key in Class: "java.lang.Object".
[2025-06-15 21:50:37.349] [WARN ] [main] [c.x.common.database.plugins.MyDefaultSqlInjector] [?] [?] - class java.lang.Object ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[2025-06-15 21:50:37.351] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Insert] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
[2025-06-15 21:50:37.353] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Delete] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.delete] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Delete]
[2025-06-15 21:50:37.356] [WARN ] [main] [c.b.mybatisplus.core.injector.methods.Update] [?] [?] - [com.xinghuo.common.database.dao.JdbcMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
[2025-06-15 21:51:27.555] [WARN ] [main] [com.xxl.job.core.executor.XxlJobExecutor] [?] [?] - >>>>>>>>>>> xxl-job accessToken is empty. To ensure system security, please set the accessToken.
[2025-06-15 21:51:48.642] [WARN ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@61c4e655] Transaction not enabled
[2025-06-15 21:52:03.189] [WARN ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7adde7de] Transaction not enabled
[2025-06-15 21:52:29.361] [WARN ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@62050a3c] Transaction not enabled
[2025-06-15 21:52:44.298] [WARN ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@e4f2540] Transaction not enabled
[2025-06-15 21:52:58.407] [WARN ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4bf19fd] Transaction not enabled
[2025-06-15 21:53:12.334] [WARN ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@9e09918] Transaction not enabled
[2025-06-15 21:53:47.395] [WARN ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2735f806] Transaction not enabled
[2025-06-15 21:54:23.867] [WARN ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4566f5c5] Transaction not enabled
[2025-06-15 21:54:39.538] [WARN ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2ff02f8] Transaction not enabled
[2025-06-15 21:54:56.697] [WARN ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7a00da61] Transaction not enabled
[2025-06-15 21:55:00.253] [WARN ] [scheduling-1] [com.xinghuo.fruugo.analysis.util.WechatWorkUtil] [?] [?] - 企业微信webhook地址未配置，无法发送通知
[2025-06-15 21:55:00.373] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 21:55:13.513] [WARN ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4100be55] Transaction not enabled
[2025-06-15 21:55:13.944] [WARN ] [scheduling-1] [com.xinghuo.fruugo.analysis.util.WechatWorkUtil] [?] [?] - 企业微信webhook地址未配置，无法发送通知
[2025-06-15 21:55:14.020] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30983 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 21:55:28.794] [WARN ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4eac1a3e] Transaction not enabled
[2025-06-15 21:55:43.068] [WARN ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1a08505a] Transaction not enabled
[2025-06-15 21:55:57.035] [WARN ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3fddf401] Transaction not enabled
[2025-06-15 21:56:14.415] [WARN ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@68280879] Transaction not enabled
[2025-06-15 21:56:30.410] [WARN ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4a9e8a8a] Transaction not enabled
[2025-06-15 21:56:47.395] [WARN ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5107059c] Transaction not enabled
[2025-06-15 21:57:01.409] [WARN ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@206f4152] Transaction not enabled
[2025-06-15 21:57:15.431] [WARN ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@30e0d9ce] Transaction not enabled
[2025-06-15 21:57:33.630] [WARN ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5f55587b] Transaction not enabled
[2025-06-15 21:57:50.418] [WARN ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@cc36348] Transaction not enabled
[2025-06-15 21:58:05.409] [WARN ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7da698c4] Transaction not enabled
[2025-06-15 21:58:37.459] [WARN ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@60371d9c] Transaction not enabled
[2025-06-15 21:58:53.458] [WARN ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@e95f8b2] Transaction not enabled
[2025-06-15 21:59:05.974] [WARN ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3b193a9e] Transaction not enabled
[2025-06-15 21:59:18.798] [WARN ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7668284f] Transaction not enabled
[2025-06-15 21:59:31.434] [WARN ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4369e814] Transaction not enabled
[2025-06-15 21:59:43.846] [WARN ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@24e4f942] Transaction not enabled
[2025-06-15 21:59:56.313] [WARN ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@56fb952c] Transaction not enabled
[2025-06-15 22:00:00.150] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 22:00:00.202] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30977 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 22:00:08.620] [WARN ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@64bea6f8] Transaction not enabled
[2025-06-15 22:00:21.746] [WARN ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@514fd978] Transaction not enabled
[2025-06-15 22:00:33.628] [WARN ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@35300cfc] Transaction not enabled
[2025-06-15 22:00:44.853] [WARN ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3ea7e14c] Transaction not enabled
[2025-06-15 22:00:57.599] [WARN ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4c66dcf0] Transaction not enabled
[2025-06-15 22:01:09.683] [WARN ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7d6915bb] Transaction not enabled
[2025-06-15 22:01:23.398] [WARN ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@62e386d2] Transaction not enabled
[2025-06-15 22:01:36.870] [WARN ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5b54a4c9] Transaction not enabled
[2025-06-15 22:01:49.960] [WARN ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1ce146df] Transaction not enabled
[2025-06-15 22:02:02.637] [WARN ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2a795b8e] Transaction not enabled
[2025-06-15 22:02:16.507] [WARN ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3ff71ab] Transaction not enabled
[2025-06-15 22:02:30.583] [WARN ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3058b927] Transaction not enabled
[2025-06-15 22:02:43.920] [WARN ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@f6ababd] Transaction not enabled
[2025-06-15 22:02:57.928] [WARN ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@23da55c5] Transaction not enabled
[2025-06-15 22:03:09.404] [WARN ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4a4891ea] Transaction not enabled
[2025-06-15 22:03:23.156] [WARN ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6f904dd7] Transaction not enabled
[2025-06-15 22:03:34.355] [WARN ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@68644224] Transaction not enabled
[2025-06-15 22:03:48.208] [WARN ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1c2c8084] Transaction not enabled
[2025-06-15 22:04:01.871] [WARN ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@39878804] Transaction not enabled
[2025-06-15 22:04:13.768] [WARN ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@17163507] Transaction not enabled
[2025-06-15 22:04:26.618] [WARN ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1e3e9cf7] Transaction not enabled
[2025-06-15 22:04:38.279] [WARN ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@124ff766] Transaction not enabled
[2025-06-15 22:04:51.537] [WARN ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@31bf6ea0] Transaction not enabled
[2025-06-15 22:05:05.665] [WARN ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@33eaa381] Transaction not enabled
[2025-06-15 22:05:16.670] [WARN ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@34674b5b] Transaction not enabled
[2025-06-15 22:05:29.473] [WARN ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3f752065] Transaction not enabled
[2025-06-15 22:05:41.572] [WARN ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4c283085] Transaction not enabled
[2025-06-15 22:05:52.741] [WARN ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@32b90cf0] Transaction not enabled
[2025-06-15 22:06:04.758] [WARN ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1d5e2042] Transaction not enabled
[2025-06-15 22:06:17.122] [WARN ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@20662dda] Transaction not enabled
[2025-06-15 22:06:28.641] [WARN ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@26f9082] Transaction not enabled
[2025-06-15 22:06:41.425] [WARN ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@612a57f8] Transaction not enabled
[2025-06-15 22:06:54.066] [WARN ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@15cc8bbb] Transaction not enabled
[2025-06-15 22:07:03.949] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 22:07:06.024] [WARN ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4d355851] Transaction not enabled
[2025-06-15 22:07:08.736] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30980 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 22:07:19.354] [WARN ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4565e7d6] Transaction not enabled
[2025-06-15 22:07:32.194] [WARN ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3d0bb67] Transaction not enabled
[2025-06-15 22:07:44.548] [WARN ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@14d3e4a1] Transaction not enabled
[2025-06-15 22:07:56.009] [WARN ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5f4a9d9b] Transaction not enabled
[2025-06-15 22:08:08.592] [WARN ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@62c9fca9] Transaction not enabled
[2025-06-15 22:09:21.394] [WARN ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - 所有任务都已存在，无需保存
[2025-06-15 22:09:34.367] [WARN ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - 所有任务都已存在，无需保存
[2025-06-15 22:09:47.431] [WARN ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - 所有任务都已存在，无需保存
[2025-06-15 22:10:00.089] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 22:10:00.122] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30982 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 22:10:01.492] [WARN ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@a98fbb4] Transaction not enabled
[2025-06-15 22:10:13.439] [WARN ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - 所有任务都已存在，无需保存
[2025-06-15 22:10:26.460] [WARN ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - 所有任务都已存在，无需保存
[2025-06-15 22:10:38.360] [WARN ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - 所有任务都已存在，无需保存
[2025-06-15 22:10:51.450] [WARN ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@65a5c63] Transaction not enabled
[2025-06-15 22:11:03.353] [WARN ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - 所有任务都已存在，无需保存
[2025-06-15 22:11:19.456] [WARN ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@9ebc963] Transaction not enabled
[2025-06-15 22:11:32.353] [WARN ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - 所有任务都已存在，无需保存
[2025-06-15 22:11:45.394] [WARN ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - 所有任务都已存在，无需保存
[2025-06-15 22:11:59.465] [WARN ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - 所有任务都已存在，无需保存
[2025-06-15 22:12:13.446] [WARN ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - 所有任务都已存在，无需保存
[2025-06-15 22:12:27.359] [WARN ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - 所有任务都已存在，无需保存
[2025-06-15 22:12:42.486] [WARN ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5171e63f] Transaction not enabled
[2025-06-15 22:12:58.387] [WARN ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - 所有任务都已存在，无需保存
[2025-06-15 22:13:12.354] [WARN ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - 所有任务都已存在，无需保存
[2025-06-15 22:13:26.482] [WARN ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3abaf204] Transaction not enabled
[2025-06-15 22:13:41.387] [WARN ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - 所有任务都已存在，无需保存
[2025-06-15 22:13:55.318] [WARN ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - 所有任务都已存在，无需保存
[2025-06-15 22:14:08.401] [WARN ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - 所有任务都已存在，无需保存
[2025-06-15 22:14:20.401] [WARN ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - 所有任务都已存在，无需保存
[2025-06-15 22:14:34.458] [WARN ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - 所有任务都已存在，无需保存
[2025-06-15 22:14:47.426] [WARN ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - 所有任务都已存在，无需保存
[2025-06-15 22:15:00.061] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行
[2025-06-15 22:15:00.099] [WARN ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 产品数据堆积严重，当前有 30980 条待处理产品数据，超过阈值(10000)，请检查产品处理系统
[2025-06-15 22:15:02.351] [WARN ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - 所有任务都已存在，无需保存
[2025-06-15 22:15:18.518] [WARN ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonPageTaskServiceImpl] [?] [?] - SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@55ed40c9] Transaction not enabled
