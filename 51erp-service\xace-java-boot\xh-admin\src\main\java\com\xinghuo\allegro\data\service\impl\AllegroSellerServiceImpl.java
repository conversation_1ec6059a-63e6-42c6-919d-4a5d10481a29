package com.xinghuo.allegro.data.service.impl;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.databind.JsonNode;
import com.xinghuo.allegro.collect.model.collect.CollectTaskForm;
import com.xinghuo.allegro.data.dao.AllegroSellerMapper;
import com.xinghuo.allegro.data.entity.AllegroSellerEntity;
import com.xinghuo.allegro.data.model.seller.AllegroSellerPagination;
import com.xinghuo.allegro.data.service.AllegroSellerService;
import com.xinghuo.allegro.push.entity.ErpProductVioEntity;
import com.xinghuo.allegro.push.service.ErpProductService;
import com.xinghuo.allegro.push.service.ErpProductVioService;
import com.xinghuo.common.base.service.BaseServiceImpl;
import com.xinghuo.common.util.core.StrXhUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

import static com.xinghuo.allegro.util.AllegroConstant.*;

@Service
@Slf4j
public class AllegroSellerServiceImpl extends BaseServiceImpl<AllegroSellerMapper, AllegroSellerEntity> implements AllegroSellerService {


    @Resource
    private ErpProductVioService erpProductVioService;

    @Resource
    private ErpProductService erpProductService;

    @Override
    public List<AllegroSellerEntity> getList(AllegroSellerPagination pagination) {
        return getTypeList(pagination, pagination.getDataType());
    }

    /**
     * 列表查询
     */

    public List<AllegroSellerEntity> getTypeList(AllegroSellerPagination pagination, String dataType) {
        QueryWrapper<AllegroSellerEntity> queryWrapper = new QueryWrapper<>();
        //查询条件
        if (StrXhUtil.isNotBlank(pagination.getSearchKey())){
            String[] searchFields = pagination.getSearchType().split("\\|");
            String field= searchFields[0];
            String searchType = searchFields[1];

            if (searchType.equalsIgnoreCase("EQ")) {
                queryWrapper.eq(field, pagination.getSearchKey().trim());
            } else if (searchType.equalsIgnoreCase("LIKERIGHT")) {
                queryWrapper.likeRight(field, pagination.getSearchKey().trim());
            } else if (searchType.equalsIgnoreCase("LIKE")) {
                queryWrapper.like(field, pagination.getSearchKey().trim());
            }
        }
        if(pagination.getSellType()!=null){
            queryWrapper.lambda().in(AllegroSellerEntity::getSellType, pagination.getSellType());
        }
        if(StrXhUtil.isNotBlank(pagination.getSellerId())){
            queryWrapper.lambda().eq(AllegroSellerEntity::getSellerId, pagination.getSellerId());
        }


        //排序
        sort(queryWrapper, pagination, new AllegroSellerEntity());
        return processDataType(queryWrapper, pagination);


    }


    @Override
    public AllegroSellerEntity getInfo(String id) {
        QueryWrapper<AllegroSellerEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(AllegroSellerEntity::getId, id);
        return this.getOne(queryWrapper);
    }


    @Override
    public AllegroSellerEntity getSimpleInfoBySellerId(String sellerId){
        QueryWrapper<AllegroSellerEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().select(AllegroSellerEntity::getId, AllegroSellerEntity::getSellerId, AllegroSellerEntity::getLogin, AllegroSellerEntity::getListingUrl, AllegroSellerEntity::getSellType);
        queryWrapper.lambda().eq(AllegroSellerEntity::getSellerId, sellerId);
        return this.getOne(queryWrapper);

    }

    @Override
    public AllegroSellerEntity getInfoBySellerId(String sellerId) {
        QueryWrapper<AllegroSellerEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(AllegroSellerEntity::getSellerId, sellerId);
        return this.getOne(queryWrapper);
    }




    @Override
    public boolean save(AllegroSellerEntity entity) {
        return super.save(entity);
    }

    @Override
    public int updateSellerOfferCount(CollectTaskForm taskForm){
        String sellerId = taskForm.getCategoryId();
        log.info("updateSellerOfferCount 更新店铺ID:{}，报价总数是{}",sellerId,taskForm.getSumCount());
        int result = baseMapper.updateSellerOfferCount(sellerId,taskForm.getSumCount());
        AllegroSellerEntity allegroSellerEntity = this.getInfoBySellerId(sellerId);
        log.info(allegroSellerEntity.getSellerName()+ ",sellerId: "+sellerId+",totalnum:"+taskForm.getSumCount()
                +",suppendStatus："+taskForm.getSuppendStatus() +",sell_type："+taskForm.getSellType());
        if(taskForm.getTotalNum()==0){
                if(taskForm.getSuppendStatus()==1) {
                    log.warn("店铺ID:{}，报价总数是0，应该是挂店了",sellerId);
                    allegroSellerEntity.setEndDate(new Date());
                    if(StrXhUtil.isBlank(taskForm.getSellType())) {
                        allegroSellerEntity.setSellType("END");
                    } else {
                        allegroSellerEntity.setSellType(taskForm.getSellType());
                    }
                    allegroSellerEntity.setStatus(0);
                    allegroSellerEntity.setNote(StrXhUtil.blankToDefault(allegroSellerEntity.getNote(),"")+",状态：挂店");

                }
        }else{
            allegroSellerEntity.setStatus(1);
            allegroSellerEntity.setSellType("NORMAL");
            allegroSellerEntity.setEndDate(null);
            allegroSellerEntity.setNote(null);

        }
        allegroSellerEntity.setTotalNum(taskForm.getSumCount());
        this.update(allegroSellerEntity.getId(),allegroSellerEntity);
        return result;
    }





    @Override
    @DSTransactional
    public boolean update(String id, AllegroSellerEntity entity) {
        entity.setId(id);
        QueryWrapper<AllegroSellerEntity> entryWrapper = new QueryWrapper<>();
        entryWrapper.lambda().eq(AllegroSellerEntity::getId, entity.getId());
        return this.updateById(entity);
    }


    public AllegroSellerEntity syncSellerData(JsonNode offerJsonNode) {
        // 检查offerJsonNode中是否包含seller信息
        if (offerJsonNode!=null  &&   offerJsonNode.get("seller") != null) {
            JsonNode sellerJsonNode = offerJsonNode.get("seller");
            String sellerId = sellerJsonNode.get("id").asText();
            // 尝试根据sellerId获取现有的卖家实体，如果不存在则创建新的
            AllegroSellerEntity sellerEntity = this.getInfoBySellerId(sellerId);
            if (sellerEntity == null) {
                // 新建卖家实体，并设置基础信息
                sellerEntity = new AllegroSellerEntity();
                sellerEntity.setSellerId(sellerId);
                sellerEntity.setSellType(SELLER_TYPE_NORMAL);
                sellerEntity.setLogin(sellerJsonNode.get("login").asText());
                sellerEntity.setSellerName(sellerJsonNode.get("name").asText());

                // 处理公司信息，如果存在
                if (sellerJsonNode.get("company").has("name")) {
                    sellerEntity.setCompanyName(sellerJsonNode.get("company").get("name").asText());
                    sellerEntity.setCompanyAddressCity(sellerJsonNode.get("company").get("address").get("city").asText());
                    sellerEntity.setCompanyAddressCountry(sellerJsonNode.get("company").get("address").get("country").asText());
                    sellerEntity.setCompanyAddressCountryCode(sellerJsonNode.get("company").get("address").get("countryCode").asText());
                    sellerEntity.setCompanyAddressStreet(sellerJsonNode.get("company").get("address").get("street").asText());
                    sellerEntity.setCompanyAddressZipCode(sellerJsonNode.get("company").get("address").get("zipCode").asText());
                    sellerEntity.setCompanyTaxId(sellerJsonNode.get("company").get("taxIdentificationNumber").asText());
                    sellerEntity.setCompanyVerified(sellerJsonNode.get("company").get("verified").asBoolean());
                }
                // 处理联系电子邮件和电话
                String emails = "";
                if (sellerJsonNode.get("contact").has("emails")) {
                    emails = sellerJsonNode.get("contact").get("emails").asText();
                }
                sellerEntity.setContactEmails(emails);
                String phones = "";
                if (sellerJsonNode.get("contact").has("phones")) {
                    phones = sellerJsonNode.get("contact").get("phones").asText();
                }
                sellerEntity.setContactPhones(phones);
                // 处理品牌描述和主要描述，确保主要描述不超过4000字节
//                sellerEntity.setBrandDescription(sellerJsonNode.get("brand").get("description").asText());
//                String main = "";
//                if (sellerJsonNode.get("descriptions").get("main").has("text")) {
//                    main = sellerJsonNode.get("descriptions").get("main").get("text").asText();
//                }
//                String mainTruncated = main.substring(0, Math.min(main.length(), 4000));
//                sellerEntity.setDescriptionMain(mainTruncated);
                sellerEntity.setRatings(sellerJsonNode.get("ratings").asText());
                sellerEntity.setListingUrl(sellerJsonNode.path("listing").path("url").asText());
                // 设置卖家状态为正常
                sellerEntity.setStatus(1);
                // 保存新卖家的信息
                super.save(sellerEntity);
            } else {
                // 如果不是黑名单的卖家，则更新卖家信息
                if (!sellerEntity.getSellType().equals(SELLER_TYPE_BLOCKED)) {

                    //20241016 加上只有状态不是正常的，才会进行更新，如果本来是正常的，则不用处理 ,之前这一块经常卡死
                    if(sellerEntity.getStatus()!=1){
                        sellerEntity.setStatus(1);
                        if (sellerEntity.getSellType().equals(SELLER_TYPE_END)) {
                            sellerEntity.setSellType(SELLER_TYPE_NORMAL);
                            sellerEntity.setEndDate(null);
                        }
                        // 更新现有卖家的信息
                        this.update(sellerEntity.getId(), sellerEntity);
                    }
                }
            }
            // 返回卖家实体
            return sellerEntity;
        }
        // 如果offerJsonNode中不包含seller信息，返回null
        return null;
    }

//    @Async
    public    void checkBlockSeller(String tenantId){
//        try {
//            Thread.sleep(2000);
//        } catch (InterruptedException e) {
//            throw new RuntimeException(e);
//        }
        this.baseMapper.checkBlockSeller(tenantId);
    }

    public void checkBlockSeller(String tenantId,String sellerId,boolean newBlock){
        //isBlock true 为黑名单，false为非黑名单
        if(newBlock){
            erpProductVioService.insertVioRecords(sellerId,tenantId);
       }else{
            //删除黑名单数据
            QueryWrapper<ErpProductVioEntity> queryWrapper = new QueryWrapper<>();
            if(StrXhUtil.isNotEmpty(tenantId)){
                queryWrapper.lambda().eq(ErpProductVioEntity::getTenantId,tenantId);
            }
            queryWrapper.lambda().eq(ErpProductVioEntity::getVioType,"SELLER");
            queryWrapper.lambda().eq(ErpProductVioEntity::getVio1,sellerId);
            erpProductVioService.getBaseMapper().delete(queryWrapper);

        }


        erpProductService.updateProductStatusBySellerId(sellerId,tenantId);



    }


    @Override
    public long validCount(){
        QueryWrapper<AllegroSellerEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(AllegroSellerEntity::getStatus,1);
        queryWrapper.lambda().eq(AllegroSellerEntity::getCompanyAddressCountryCode,"CN");
        return this.baseMapper.selectCount(queryWrapper);

    }

}
