package com.xinghuo.allegro.manage.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.AbstractBaseEntity;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Allegro 店铺实体，包名token信息。
 * <AUTHOR>
 */
@TableName("zz_allegro_store")
@Data
public class AllegroStoreEntity extends AbstractBaseEntity.AbstractCBaseEntity<String> {

    /**
     * 用户id
     */
    @TableField("user_id")
    private String userId;

    @TableField("platform")
    private String platform;
    /**
     * 分组名称
     */
    @TableField("group_name")
    private String groupName;
    /**
     * 店铺标识(自定义)
     */
    @TableField("store_name")
    private String storeName;

    @TableField("seller_id")
    private String sellerId;

    @TableField("platform_email")
    private String platformEmail;
    /**
     * allegro login
     */
    @TableField("platform_login")
    private String platformLogin;

    /**
     * 公司名称
     */
    @TableField("platform_company")
    private String platformCompany;
    /**
     * 税号
     */
    @TableField("platform_tax_id")
    private String platformTaxId;
    /**
     * 授权状态 1-正常 0-异常
     */
    @TableField("token_status")
    private Boolean tokenStatus;
    //店铺类型 1-普通店，2-广告店，0-已封店
    @TableField("store_type")
    private Integer storeType;
    /**
     * access_token
     */
    @TableField("access_token")
    private String accessToken;
    /**
     * 过期时间
     */
    @TableField("expire_date")
    private Date expireDate;
    /**
     * 刷新token
     */
    @TableField("refresh_token")
    private String refreshToken;
    /**
     * 授权范围
     */
    @TableField("scope")
    private String scope;
    /**
     * token类型
     */
    @TableField("token_type")
    private String tokenType;
    /**
     * 备注
     */
    @TableField("note")
    private String note;

    @TableField("online_offers")
    private Integer onlineOffers;
    @TableField("online_end_offers")
    private Integer onlineEndOffers;
    @TableField("online_inactive_offers")
    private Integer onlineInactiveOffers;

   @TableField("sync_all_offer_time")
    private Date  syncAllOfferTime;

    @TableField("sync_active_offer_time")
    private Date  syncActiveOfferTime;

    @TableField("orders")
    private Integer orders;

    @TableField("today_orders")
    private Integer todayOrders;

    @TableField("api_client_id")
    private String apiClientId;

    @TableField("end_type")
    private String endType;

    @TableField("end_time")
    private Date endTime;

    @TableField("end_reason")
    private String endReason;

    @TableField("adv_expire_date")
    private Date advExpireDate;


    @TableField("sync_flag")
    private Boolean syncFlag;


    @TableField("recommended_num")
    private Integer recommendedNum;
    @TableField("not_recommended_num")
    private Integer notRecommendedNum;
    @TableField("recommended_percentage")
    private BigDecimal recommendedPercentage;
    @TableField("service_rate")
    private BigDecimal serviceRate;
    @TableField("description_rate")
    private BigDecimal descriptionRate;







    @TableField("allegro_url")
    private String allegroUrl;




    // 平台费率
    @TableField("platform_rate")
    private BigDecimal platformRate;

    // OCID
    @TableField("ocid")
    private String ocid;

    // 转账费率
    @TableField("transfer_rate")
    private BigDecimal transferRate;

    // 转账费率类型
    @TableField("transfer_rate_type")
    private Integer transferRateType;

    // 转账费率计算方式
    @TableField("transfer_rate_method")
    private Integer transferRateMethod;

    // 欧盟税号类型
    @TableField("ioss_type")
    private Integer iossType;

    // IOSS税号
    @TableField("ioss_no")
    private String iossNo;

    // VAT税费率
    @TableField("vat_rate")
    private BigDecimal vatRate;

    // VAT税费类型
    @TableField("vat_type")
    private Integer vatType;

    // 英国税号
    @TableField("vat_no")
    private String vatNo;

    // 挪威税号
    @TableField("norway_number")
    private String norwayNumber;

    @TableField("balance_text")
    private String balanceText;

    @TableField("need_pay_billing")
    private Boolean needPayBilling;

    @TableField("allow_vio")
    private Boolean allowVio;



    @TableField("aftersale_warranties")
    private String aftersaleWarranties;

    @TableField("aftersale_return_policies")
    private String aftersaleReturnPolicies;

    @TableField("aftersale_implied_warranties")
    private String aftersaleImpliedWarranties;

    @TableField("shipping_id")
    private String shippingId;

    @TableField("need_sync_offer")
    private Boolean needSyncOffer;

    @TableField("is_auto_shelf")
    private Boolean isAutoShelf;

    @TableField("next_shelf_time")
    private Date nextShelfTime;

    @TableField("period_days")
    private Integer periodDays;

}
