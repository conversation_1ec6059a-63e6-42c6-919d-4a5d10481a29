package com.xinghuo.allegro.collect.model.seller;

import lombok.Data;

import java.util.Date;

/**
 * 卖家列表查询内容
 * <AUTHOR>
 *
 */

@Data
public class CollectSellerModel {

    private String id;

    // 卖家ID
    private String sellerId;
    // 登录名
    private String login;
    // 卖家姓名
    private String sellerName;
    // 公司名称
    private String companyName;
    // 公司地址城市
    private String companyAddressCity;
    // 公司地址国家
    private String companyAddressCountry;
    // 公司地址国家代码
    private String companyAddressCountryCode;
    // 公司地址街道
    private String companyAddressStreet;
    // 公司地址邮政编码
    private String companyAddressZipCode;
    // 公司税务识别号
    private String companyTaxId;
    // 公司是否已验证
    private boolean companyVerified;
    // 联系邮箱列表
    private String contactEmails;
    // 联系电话列表
    private String contactPhones;
    // 品牌描述
    private String brandDescription;
    // 主要描述
    private String descriptionMain;
    // 评分
    private String ratings;
    // 列表URL
    private String listingUrl;
    // 分组
    private String groupName;

    private String sellType;

    private String note;

    //禁用时间
    private Date endDate;

    //状态
    private Integer status;

    //当前总数
    private Integer totalNum;

    private Date creatorTime;

}
