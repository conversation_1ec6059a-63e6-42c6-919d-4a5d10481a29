package com.xinghuo.allegro.collect.controller;

import com.xinghuo.allegro.collect.entity.CollectTaskEntity;
import com.xinghuo.allegro.collect.model.collect.CollectTaskForm;
import com.xinghuo.allegro.collect.model.collect.CollectTaskModel;
import com.xinghuo.allegro.data.service.AllegroSellerService;
import com.xinghuo.allegro.collect.service.CollectTaskService;
import com.xinghuo.allegro.util.AllegroConstant;
import com.xinghuo.common.annotation.NoDataSourceBind;
import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.util.core.BeanCopierUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

/**
 * 烽火的采集任务
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@Tag(name = "采集任务", description = "采集任务")
@RequestMapping("/api/allegro/fhtask")
public class CollectFhTaskController {

    @Resource
    private CollectTaskService collectTaskService;

    @Resource
    private AllegroSellerService allegroSellerService;

    /**
     * 获取待处理的产品链接列表。
     *
     * @return 表示操作结果的ActionResult对象，包含待处理的产品链接列表。
     */
    @Operation(summary = "获取后台待处理数据清单")
    @GetMapping("/waitGets")
    @NoDataSourceBind
    public ActionResult<CollectTaskModel> waitGets(String clientId, String taskType) {
        CollectTaskEntity entity  = collectTaskService.waitGets(clientId,taskType,"ALLEGRO");
        CollectTaskModel collectLinkModel = BeanCopierUtils.copy(entity, CollectTaskModel.class);
        return ActionResult.success(collectLinkModel);
    }

    /**
     * 接收并处理前端提交的商品信息解析请求。
     * 该方法主要负责解析前端上传的JSON数据，提取其中的商家和产品信息，
     * 并更新相应的数据库记录。
     *
     * @param taskForm 包含待解析的商品JSON数据和相关ID的信息表单。
     * @return 成功保存的提示信息。
     */
    @Operation(summary = "前端提交详情页的json数据")
    @PutMapping("/putTaskJson")
    @NoDataSourceBind
    public ActionResult<String> putLinkJson(@RequestBody CollectTaskForm taskForm) {
        log.debug("前端提交详情页的json数据 taskId:{}", taskForm.getId());
        //满足条件的数据，设置已采集。
        CollectTaskEntity collectTaskEntity = collectTaskService.getById(taskForm.getId());
        if (collectTaskEntity != null) {
            collectTaskEntity.setStatus(AllegroConstant.REQUEST_STATUS_FINISH);
            collectTaskEntity.setTotalNum(taskForm.getTotalNum());
            collectTaskEntity.setTotalSalesNum(taskForm.getTotalSalesNum());
            collectTaskEntity.setFinishTime(new Date());
            collectTaskEntity.setSumCount(taskForm.getSumCount());
            collectTaskService.updateById(collectTaskEntity);

            if("S".equals(collectTaskEntity.getTaskType())){
                allegroSellerService.updateSellerOfferCount(taskForm);
            }
        }
        else {
            log.warn("任务数据在系统中不存在已存在，忽略。ID:" + taskForm.getId());
        }
        return ActionResult.success("成功保存！" + taskForm.getId());
    }
}
