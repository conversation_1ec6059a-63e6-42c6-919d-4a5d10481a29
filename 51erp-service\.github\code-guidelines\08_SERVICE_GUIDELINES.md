# Service 层规范

## 基本结构

Service层负责处理业务逻辑，是连接Controller和DAO/Mapper的桥梁，分为接口定义和实现类两部分。

### 服务接口

#### 位置与命名

* **包路径:** `com.xinghuo.[模块名].service`
* **命名规范:** 使用名词加Service，如 `UserService`, `ProductService`

#### 接口示例

```java
/**
 * 产品分类服务接口
 *
 * <AUTHOR>
 * date 2023-05-15
 */
public interface ProductCategoryService extends BaseService<ProductCategoryEntity> {
    
    /**
     * 分页查询产品分类
     *
     * @param pagination 查询条件
     * @return 分页数据
     */
    List<ProductCategoryEntity> getList(ProductCategoryPagination pagination);
    
    /**
     * 根据ID查询产品分类
     *
     * @param id 分类ID
     * @return 产品分类
     */
    ProductCategoryEntity getInfo(String id);
    
    /**
     * 创建产品分类
     *
     * @param entity 分类信息
     * @return 创建结果
     */
    boolean saveInfo(ProductCategoryEntity entity);
    
    /**
     * 更新产品分类
     *
     * @param entity 更新信息
     * @return 更新结果
     */
    boolean updateInfo(ProductCategoryEntity entity);
    
    /**
     * 删除产品分类
     *
     * @param id 分类ID
     * @return 删除结果
     */
    boolean deleteById(String id);
    
    /**
     * 获取分类树结构
     *
     * @return 分类树
     */
    List<ProductCategoryTreeVO> getCategoryTree();
}
```

### 服务实现类

#### 位置与命名

* **包路径:** `com.xinghuo.[模块名].service.impl`
* **命名规范:** 接口名 + `Impl`，如 `UserServiceImpl`，`ProductCategoryServiceImpl`

#### 基础注解

* `@Service` - 标识为服务组件
* `@Slf4j` - Lombok提供的日志注解
* `@Transactional` - 事务管理(可在类或方法级别使用)
* `@Resource` - 依赖注入

#### 实现类示例

```java
/**
 * 产品分类服务实现类
 *
 * <AUTHOR>
 * @date 2023-05-15
 */
@Slf4j
@Service
public class ProductCategoryServiceImpl extends ExtendedBaseServiceImpl<ProductCategoryMapper, ProductCategoryEntity> implements ProductCategoryService {
    
    @Resource
    private ProductCategoryMapper productCategoryMapper;
    
    @Resource
    private UserProvider userProvider;
    
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    
    /**
     * 分页查询产品分类
     *
     * @param pagination 查询条件
     * @return 分页数据
     */
    @Override
    public List<ProductCategoryEntity> getList(ProductCategoryPagination pagination) {
        QueryWrapper<ProductCategoryEntity> queryWrapper = new QueryWrapper<>();
        
        // 构建查询条件
        if (StrXhUtil.isNotBlank(pagination.getName())) {
            queryWrapper.lambda().like(ProductCategoryEntity::getName, pagination.getName().trim());
        }
        
        if (pagination.getParentId() != null) {
            queryWrapper.lambda().eq(ProductCategoryEntity::getParentId, pagination.getParentId());
        }
        
        if (pagination.getStatus() != null) {
            queryWrapper.lambda().eq(ProductCategoryEntity::getStatus, pagination.getStatus());
        }
        
        // 排序
        if (StrXhUtil.isBlank(pagination.getListOrder())) {
            pagination.setSidx("createdAt");
            pagination.setSort("desc");
            sort(queryWrapper, pagination, new ProductCategoryEntity());
        } else {
            String[] desc = pagination.getListOrder().split("\\|");
            if (desc.length > 1 && desc[1].equalsIgnoreCase("desc")) {
                queryWrapper.orderByDesc(desc[0]);
            } else {
                queryWrapper.orderByAsc(desc[0]);
            }
        }
        
        // 执行查询并分页
        Page<ProductCategoryEntity> page = new Page<>(pagination.getCurrentPage(), pagination.getPageSize());
        IPage<ProductCategoryEntity> userIpage = this.page(page, queryWrapper);
        return pagination.setDataList(userIpage.getRecords(), userIpage.getTotal());
    }
    
    /**
     * 根据ID查询产品分类
     *
     * @param id 分类ID
     * @return 产品分类
     */
    @Override
    public ProductCategoryEntity getInfo(String id) {
        if (StrXhUtil.isBlank(id)) {
            log.warn("查询产品分类信息ID为空");
            return null;
        }
        
        return this.getById(id);
    }
    
    /**
     * 创建产品分类
     *
     * @param entity 分类信息
     * @return 创建结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveInfo(ProductCategoryEntity entity) {
        if (entity == null) {
            log.warn("保存产品分类信息为空");
            return false;
        }
        
        // 检查同级分类名称是否重复
        checkNameDuplicate(entity.getName(), entity.getParentId(), null);
        
        // 设置默认值
        entity.setId(RandomUtil.snowId());
        entity.setCreatedBy(userProvider.get().getUserName());
        entity.setCreatedAt(new Date());
        
        return this.save(entity);
    }
    
    /**
     * 更新产品分类
     *
     * @param entity 更新信息
     * @return 更新结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateInfo(ProductCategoryEntity entity) {
        if (entity == null || StrXhUtil.isBlank(entity.getId())) {
            log.warn("更新产品分类信息ID为空");
            return false;
        }
        
        // 查询原记录是否存在
        ProductCategoryEntity dbEntity = this.getById(entity.getId());
        if (dbEntity == null) {
            log.warn("更新的产品分类不存在, ID: {}", entity.getId());
            return false;
        }
        
        // 检查同级分类名称是否重复
        checkNameDuplicate(entity.getName(), entity.getParentId(), entity.getId());
        
        // 检查是否为自己的父级
        if (entity.getParentId() != null && entity.getParentId().equals(entity.getId())) {
            log.warn("父级分类不能是自己, ID: {}", entity.getId());
            return false;
        }
        
        // 设置更新信息
        entity.setUpdatedBy(userProvider.get().getUserName());
        entity.setUpdatedAt(new Date());
        
        return this.updateById(entity);
    }
    
    /**
     * 删除产品分类
     *
     * @param id 分类ID
     * @return 删除结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteById(String id) {
        if (StrXhUtil.isBlank(id)) {
            log.warn("删除产品分类ID为空");
            return false;
        }
        
        // 检查是否有子分类
        QueryWrapper<ProductCategoryEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(ProductCategoryEntity::getParentId, id);
        int count = this.count(wrapper);
        if (count > 0) {
            log.warn("该分类下有子分类，无法删除, ID: {}", id);
            return false;
        }
        
        return this.removeById(id);
    }
    
    /**
     * 获取分类树结构
     *
     * @return 分类树
     */
    @Override
    public List<ProductCategoryTreeVO> getCategoryTree() {
        // 查询所有分类
        List<ProductCategoryEntity> allCategories = this.list();
        
        // 构建树结构逻辑
        return buildCategoryTree(allCategories, "0");
    }
    
    /**
     * 检查同级分类名称是否重复
     *
     * @param name 分类名称
     * @param parentId 父级ID
     * @param excludeId 排除ID
     */
    private void checkNameDuplicate(String name, String parentId, String excludeId) {
        if (StrXhUtil.isBlank(name)) {
            return;
        }
        
        QueryWrapper<ProductCategoryEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda().eq(ProductCategoryEntity::getName, name);
        wrapper.lambda().eq(ProductCategoryEntity::getParentId, parentId != null ? parentId : "0");
        
        if (StrXhUtil.isNotBlank(excludeId)) {
            wrapper.lambda().ne(ProductCategoryEntity::getId, excludeId);
        }
        
        int count = this.count(wrapper);
        if (count > 0) {
            log.warn("同级分类下已存在相同名称: {}", name);
            throw new RuntimeException("同级分类下已存在相同名称");
        }
    }
    
    /**
     * 构建分类树结构
     *
     * @param categories 所有分类
     * @param parentId 父级ID
     * @return 树结构
     */
    private List<ProductCategoryTreeVO> buildCategoryTree(List<ProductCategoryEntity> categories, String parentId) {
        return categories.stream()
            .filter(entity -> StrXhUtil.equals(entity.getParentId(), parentId))
            .map(entity -> {
                ProductCategoryTreeVO treeNode = new ProductCategoryTreeVO();
                BeanCopierUtils.copy(entity, treeNode);
                
                // 递归查找子节点
                List<ProductCategoryTreeVO> children = buildCategoryTree(categories, entity.getId());
                treeNode.setChildren(children);
                
                return treeNode;
            })
            .collect(Collectors.toList());
    }
}
```

## 事务管理

### 事务注解

```java
// 类级别事务
@Transactional(rollbackFor = Exception.class)
public class SomeServiceImpl extends ExtendedBaseServiceImpl<SomeMapper, SomeEntity> implements SomeService {
    // 所有方法都具有事务特性
}

// 方法级别事务
@Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
public void someMethod() {
    // 方法具有事务特性
}
```

### 事务传播行为

* `Propagation.REQUIRED` - 默认，如果有事务则加入，无则创建
* `Propagation.REQUIRES_NEW` - 创建新事务，挂起当前事务
* `Propagation.SUPPORTS` - 有则加入，无则非事务执行
* `Propagation.NOT_SUPPORTED` - 以非事务方式执行，挂起当前事务
* `Propagation.MANDATORY` - 必须在已有事务中执行，否则抛异常
* `Propagation.NEVER` - 必须在非事务环境执行，有事务则抛异常
* `Propagation.NESTED` - 嵌套事务，独立提交或回滚

### 事务隔离级别

* `Isolation.DEFAULT` - 使用数据库默认隔离级别
* `Isolation.READ_UNCOMMITTED` - 读未提交
* `Isolation.READ_COMMITTED` - 读已提交
* `Isolation.REPEATABLE_READ` - 可重复读
* `Isolation.SERIALIZABLE` - 串行化

## 依赖注入方式

项目中推荐使用 `@Resource` 进行依赖注入：

```java
@Service
public class ProductCategoryServiceImpl extends ExtendedBaseServiceImpl<ProductCategoryMapper, ProductCategoryEntity> implements ProductCategoryService {
    
    @Resource
    private ProductCategoryMapper productCategoryMapper;
    
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    
    @Resource
    private UserProvider userProvider;
}
```

## 异常处理

业务异常应使用自定义异常：

```java
// 抛出业务异常
if (condition) {
    log.warn("业务逻辑错误: {}", errorMsg);
    throw new RuntimeException("业务逻辑错误描述");
}

// 参数校验
if (StrXhUtil.isBlank(id)) {
    log.warn("ID不能为空");
    return null;
}

if (amount <= 0) {
    log.warn("金额必须大于0");
    return false;
}
```

## 日志记录

关键业务和异常应记录日志：

```java
@Slf4j
@Service
public class ProductCategoryServiceImpl extends ExtendedBaseServiceImpl<ProductCategoryMapper, ProductCategoryEntity> implements ProductCategoryService {
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveInfo(ProductCategoryEntity entity) {
        try {
            log.info("开始创建产品分类: {}", entity.getName());
            
            // 业务逻辑...
            
            log.info("创建产品分类成功, ID: {}", entity.getId());
            return true;
        } catch (RuntimeException e) {
            log.warn("创建产品分类失败: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("创建产品分类异常", e);
            throw new RuntimeException("创建产品分类失败");
        }
    }
}
```

## 设计原则

1. **单一职责原则**
   * 一个服务类应当只负责一个业务领域
   * 一个方法应当只做一件事情
   * 避免大而全的"上帝类"

2. **接口隔离原则**
   * 服务接口应该小而专，而非大而全
   * 可以根据不同客户端需求提供不同的接口方法

3. **依赖倒置原则**
   * 依赖于抽象而非具体实现
   * 使用依赖注入而非直接创建依赖对象

4. **方法设计原则**
   * 方法名应明确表达业务意图
   * 参数列表简洁清晰
   * 返回值明确且类型一致
   * 单个方法不宜过长(建议小于50行)

## 最佳实践

1. 服务层应集中处理业务逻辑，不包含表示层逻辑
2. 事务应在服务层管理，而非控制器层
3. 使用 `@Resource` 进行依赖注入
4. 关键业务记录详细日志，包括开始、结束和异常情况
5. 业务校验应在服务层进行，不完全依赖控制器层的参数校验
6. 复杂业务拆分为多个私有方法，保持主方法流程清晰
7. 避免在服务层直接操作HTTP上下文
8. 敏感操作考虑添加幂等性检查
9. 批量操作考虑性能和事务边界
10. 对频繁访问的数据考虑添加缓存机制
