# 后台代码架构指南

## 1. 架构概述

51erp 后台采用分层架构设计，清晰划分各层职责，提高代码可维护性和可扩展性。系统主要分为以下几层：

```
表示层（Controller）→ 业务层（Service）→ 数据访问层（DAO/Mapper）→ 数据库
```

### 1.1 核心技术栈

- **基础框架**：Spring Boot 2.7.x
- **ORM 框架**：MyBatis-Plus 3.5.x
- **数据库**：MySQL 8.x
- **缓存**：Redis
- **API 文档**：Swagger 3 (OpenAPI)
- **工具库**：Lombok, Hutool, Apache Commons
- **日志框架**：SLF4J + Logback

### 1.2 项目结构

```
com.xinghuo.[模块名]
  ├── controller      # 控制器层
  ├── dao             # 数据访问层
  │   └── mapper      # MyBatis Mapper XML 文件
  ├── entity          # 实体层
  ├── model           # 模型层
  │   ├── [业务功能1]  # 按业务功能组织
  │   └── [业务功能2]
  └── service         # 服务层
      └── impl        # 服务实现类
```

### 1.3 命名规范

| 类型 | 命名规则 | 示例 |
|------|---------|------|
| Entity | [实体名]Entity | UserEntity |
| DTO | [实体名][操作]DTO | UserCreateDTO |
| VO | [实体名]VO | UserVO |
| Pagination | [实体名]Pagination | UserPagination |
| Mapper | [实体名]Mapper | UserMapper |
| Service | [实体名]Service | UserService |
| ServiceImpl | [实体名]ServiceImpl | UserServiceImpl |
| Controller | [实体名]Controller | UserController |

## 2. 分层详解

### 2.1 实体层 (Entity)

实体层负责映射数据库表结构，是 ORM 的基础。

**位置**：`com.xinghuo.[模块名].entity`

**核心特点**：
- 使用 `@TableName` 注解指定表名
- 使用 `@TableField` 注解映射字段
- 继承 `BaseEntityV2` 的不同变体获取通用字段
- 使用 `@Data` 等 Lombok 注解简化代码

**示例**：
```java
package com.xinghuo.product.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.BaseEntityV2;
import lombok.Data;

import java.math.BigDecimal;

@Data
@TableName("product_info")
public class ProductEntity extends BaseEntityV2.CUBaseEntityV2<String> {

    /**
     * 产品名称
     */
    @TableField("product_name")
    private String productName;

    /**
     * 产品价格
     */
    @TableField("price")
    private BigDecimal price;

    // 其他字段...
}
```

### 2.2 数据访问层 (DAO/Mapper)

数据访问层负责与数据库交互，执行 CRUD 操作。

**位置**：`com.xinghuo.[模块名].dao`

**核心特点**：
- 接口继承 `XHBaseMapper` 或 `BaseMapper`
- 提供基础的 CRUD 方法
- 可定义自定义查询方法

**示例**：
```java
public interface ProductMapper extends XHBaseMapper<ProductEntity> {

    /**
     * 根据分类ID查询产品列表
     */
    List<ProductEntity> selectByCategoryId(@Param("categoryId") String categoryId);

    /**
     * 批量更新产品状态
     */
    int updateBatchStatus(@Param("ids") List<String> ids, @Param("status") Integer status);
}
```

### 2.3 模型层 (Model)

模型层包含各种数据传输对象，用于层间数据传递和前后端交互。

**位置**：`com.xinghuo.[模块名].model.[业务功能]`

**核心特点**：
- 使用 `@Data` 注解
- 使用 Swagger 注解进行 API 文档标注
- 按业务功能组织

**主要类型**：
- **DTO (Data Transfer Object)**：用于接收前端请求数据
- **VO (View Object)**：用于返回给前端的数据
- **Pagination**：分页查询参数对象

**示例**：
```java
@Data
@Schema(description = "产品创建DTO")
public class ProductCreateDTO {

    @NotBlank(message = "产品名称不能为空")
    @Schema(description = "产品名称")
    private String productName;

    @NotNull(message = "产品价格不能为空")
    @Schema(description = "产品价格")
    private BigDecimal price;

    // 其他字段...
}
```

### 2.4 服务层 (Service)

服务层负责实现业务逻辑，是系统的核心。

**位置**：
- 接口：`com.xinghuo.[模块名].service`
- 实现：`com.xinghuo.[模块名].service.impl`

**核心特点**：
- 接口继承 `BaseService`
- 实现类继承 `ExtendedBaseServiceImpl`
- 使用 `@Service` 和 `@Slf4j` 注解
- 使用 `@Resource` 进行依赖注入
- 使用 `@Transactional` 管理事务
- 业务逻辑应放在 ServiceImpl 中，而非 Controller

**示例 - 接口**：
```java
public interface ProductService extends BaseService<ProductEntity> {

    /**
     * 创建产品
     */
    ProductEntity createProduct(ProductCreateDTO dto);

    /**
     * 分页查询产品
     */
    List<ProductVO> getProductList(ProductPagination pagination);

    /**
     * 更新产品状态
     */
    boolean updateProductStatus(String id, Integer status);
}
```

**示例 - 实现类**：
```java
@Slf4j
@Service
public class ProductServiceImpl extends ExtendedBaseServiceImpl<ProductMapper, ProductEntity> implements ProductService {

    @Resource
    private CategoryService categoryService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ProductEntity createProduct(ProductCreateDTO dto) {
        // 参数校验
        if (dto.getPrice().compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException("产品价格必须大于0");
        }

        // 业务逻辑
        ProductEntity entity = new ProductEntity();
        BeanUtils.copyProperties(dto, entity);
        entity.setStatus(ProductStatusEnum.NORMAL.getValue());
        entity.setCreateTime(new Date());

        // 保存数据
        this.save(entity);

        // 返回结果
        return entity;
    }

    @Override
    public List<ProductVO> getProductList(ProductPagination pagination) {
        // 构建查询条件
        LambdaQueryWrapper<ProductEntity> queryWrapper = new LambdaQueryWrapper<>();

        // 添加查询条件
        if (StrXhUtil.isNotEmpty(pagination.getProductName())) {
            queryWrapper.like(ProductEntity::getProductName, pagination.getProductName());
        }

        if (pagination.getCategoryId() != null) {
            queryWrapper.eq(ProductEntity::getCategoryId, pagination.getCategoryId());
        }

        // 排序
        queryWrapper.orderByDesc(ProductEntity::getCreateTime);

        // 分页查询
        Page<ProductEntity> page = new Page<>(pagination.getCurrentPage(), pagination.getPageSize());
        IPage<ProductEntity> iPage = this.page(page, queryWrapper);

        // 转换为VO
        List<ProductVO> voList = iPage.getRecords().stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());

        // 设置分页信息
        pagination.setDataList(voList, iPage.getTotal());

        return voList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateProductStatus(String id, Integer status) {
        ProductEntity entity = this.getById(id);
        if (entity == null) {
            throw new BusinessException("产品不存在");
        }

        entity.setStatus(status);
        entity.setUpdateTime(new Date());

        return this.updateById(entity);
    }

    /**
     * 将实体转换为VO
     */
    private ProductVO convertToVO(ProductEntity entity) {
        if (entity == null) {
            return null;
        }

        ProductVO vo = new ProductVO();
        BeanUtils.copyProperties(entity, vo);

        // 设置额外信息
        CategoryEntity category = categoryService.getById(entity.getCategoryId());
        if (category != null) {
            vo.setCategoryName(category.getCategoryName());
        }

        return vo;
    }
}
```

### 2.5 控制器层 (Controller)

控制器层负责处理 HTTP 请求，参数校验，调用服务层，返回结果。

**位置**：`com.xinghuo.[模块名].controller`

**核心特点**：
- 使用 `@RestController` 和 `@RequestMapping` 注解
- 使用 Swagger 注解进行 API 文档标注
- 返回统一的 `ActionResult` 对象
- 控制器方法应简洁，复杂业务逻辑应放在 Service 层
- 使用 `@Valid` 进行参数校验

**示例**：
```java
@Slf4j
@RestController
@RequestMapping("/api/product")
@Tag(name = "产品管理接口")
public class ProductController {

    @Resource
    private ProductService productService;

    /**
     * 创建产品
     */
    @PostMapping("/create")
    @Operation(summary = "创建产品", description = "创建新产品")
    public ActionResult<ProductVO> createProduct(@RequestBody @Valid ProductCreateDTO dto) {
        try {
            ProductEntity entity = productService.createProduct(dto);
            ProductVO vo = new ProductVO();
            BeanUtils.copyProperties(entity, vo);
            return ActionResult.success(vo);
        } catch (BusinessException e) {
            log.error("创建产品失败: {}", e.getMessage());
            return ActionResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("创建产品异常", e);
            return ActionResult.error("系统异常，请稍后重试");
        }
    }

    /**
     * 获取产品列表
     */
    @PostMapping("/list")
    @Operation(summary = "获取产品列表", description = "分页查询产品列表")
    public ActionResult<List<ProductVO>> getProductList(@RequestBody ProductPagination pagination) {
        try {
            List<ProductVO> list = productService.getProductList(pagination);
            return ActionResult.success(list, pagination);
        } catch (Exception e) {
            log.error("获取产品列表异常", e);
            return ActionResult.error("系统异常，请稍后重试");
        }
    }

    /**
     * 获取产品详情
     */
    @GetMapping("/detail/{id}")
    @Operation(summary = "获取产品详情", description = "根据ID获取产品详情")
    public ActionResult<ProductVO> getProductDetail(@PathVariable("id") String id) {
        try {
            ProductEntity entity = productService.getById(id);
            if (entity == null) {
                return ActionResult.error("产品不存在");
            }

            ProductVO vo = new ProductVO();
            BeanUtils.copyProperties(entity, vo);

            return ActionResult.success(vo);
        } catch (Exception e) {
            log.error("获取产品详情异常", e);
            return ActionResult.error("系统异常，请稍后重试");
        }
    }

    /**
     * 更新产品状态
     */
    @PostMapping("/updateStatus")
    @Operation(summary = "更新产品状态", description = "更新产品状态")
    public ActionResult<Boolean> updateProductStatus(@RequestParam("id") String id,
                                                    @RequestParam("status") Integer status) {
        try {
            boolean result = productService.updateProductStatus(id, status);
            return ActionResult.success(result);
        } catch (BusinessException e) {
            log.error("更新产品状态失败: {}", e.getMessage());
            return ActionResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("更新产品状态异常", e);
            return ActionResult.error("系统异常，请稍后重试");
        }
    }
}
```

## 3. 最佳实践

### 3.1 查询条件构建

在 Service 层构建查询条件时，应遵循以下规范：

1. **使用 LambdaQueryWrapper**：优先使用 LambdaQueryWrapper 而非 QueryWrapper，以获得类型安全的查询条件

2. **条件判断**：在添加条件前进行非空判断，避免无效条件

3. **分页查询**：使用 MyBatis-Plus 的 Page 对象进行分页查询

4. **排序**：明确指定排序字段和顺序

**示例**：
```java
// 构建查询条件
LambdaQueryWrapper<ProductEntity> queryWrapper = new LambdaQueryWrapper<>();

// 添加查询条件（注意条件判断）
if (StrXhUtil.isNotEmpty(productName)) {
    queryWrapper.like(ProductEntity::getProductName, productName);
}

if (categoryId != null) {
    queryWrapper.eq(ProductEntity::getCategoryId, categoryId);
}

if (minPrice != null && maxPrice != null) {
    queryWrapper.between(ProductEntity::getPrice, minPrice, maxPrice);
} else if (minPrice != null) {
    queryWrapper.ge(ProductEntity::getPrice, minPrice);
} else if (maxPrice != null) {
    queryWrapper.le(ProductEntity::getPrice, maxPrice);
}

// 排序
queryWrapper.orderByDesc(ProductEntity::getCreateTime);

// 分页查询
Page<ProductEntity> page = new Page<>(currentPage, pageSize);
IPage<ProductEntity> iPage = this.page(page, queryWrapper);
```

### 3.2 异常处理

系统异常处理应遵循以下规范：

1. **业务异常**：使用 BusinessException 表示业务逻辑异常，包含错误消息

2. **系统异常**：捕获并记录系统异常，返回友好的错误信息

3. **事务管理**：在 Service 层使用 @Transactional 注解管理事务，指定 rollbackFor = Exception.class

4. **日志记录**：使用 SLF4J 记录异常信息，包含上下文数据

**示例**：
```java
try {
    // 业务逻辑
    if (condition) {
        throw new BusinessException("业务错误信息");
    }

    // 可能抛出异常的代码
    riskyOperation();

    return result;
} catch (BusinessException e) {
    // 业务异常，记录日志并返回错误信息
    log.warn("业务异常: {}, 参数: {}", e.getMessage(), JSON.toJSONString(params));
    throw e;
} catch (Exception e) {
    // 系统异常，记录日志并转换为友好的错误信息
    log.error("系统异常", e);
    throw new BusinessException("系统异常，请稍后重试");
}
```

### 3.3 数据校验

数据校验应遵循以下规范：

1. **参数校验**：使用 JSR-303 注解进行参数校验，如 @NotNull, @NotBlank, @Size 等

2. **业务校验**：在 Service 层进行业务规则校验，抛出 BusinessException

3. **统一处理**：使用全局异常处理器处理校验异常，返回友好的错误信息

**示例**：
```java
// DTO 类中的参数校验
@Data
public class ProductCreateDTO {

    @NotBlank(message = "产品名称不能为空")
    @Size(max = 100, message = "产品名称长度不能超过100")
    private String productName;

    @NotNull(message = "产品价格不能为空")
    @DecimalMin(value = "0.01", message = "产品价格必须大于0")
    private BigDecimal price;

    @NotNull(message = "分类ID不能为空")
    private String categoryId;
}

// Service 层中的业务校验
@Override
public ProductEntity createProduct(ProductCreateDTO dto) {
    // 业务校验
    CategoryEntity category = categoryService.getById(dto.getCategoryId());
    if (category == null) {
        throw new BusinessException("分类不存在");
    }

    if (category.getStatus() != CategoryStatusEnum.NORMAL.getValue()) {
        throw new BusinessException("分类已禁用，无法创建产品");
    }

    // 检查产品名称是否重复
    LambdaQueryWrapper<ProductEntity> queryWrapper = new LambdaQueryWrapper<>();
    queryWrapper.eq(ProductEntity::getProductName, dto.getProductName());
    if (this.count(queryWrapper) > 0) {
        throw new BusinessException("产品名称已存在");
    }

    // 创建产品
    ProductEntity entity = new ProductEntity();
    BeanUtils.copyProperties(dto, entity);
    this.save(entity);

    return entity;
}
```

## 4. 代码生成提示词

以下是用于生成符合项目架构的代码的提示词模板：

### 4.1 实体类生成

```
请生成一个名为{实体名}Entity的实体类，用于映射{表名}表，包含以下字段：
- {字段1}：{类型}，{描述}
- {字段2}：{类型}，{描述}
- ...

实体类应继承BaseEntityV2.CUBaseEntityV2<String>，使用@Data注解，并使用@TableName和@TableField注解映射表和字段。
```

### 4.2 Mapper接口生成

```
请生成一个名为{实体名}Mapper的Mapper接口，用于{实体名}Entity的数据访问。
接口应继承XHBaseMapper<{实体名}Entity>，并包含以下自定义方法：
- {方法1}：{描述}
- {方法2}：{描述}
- ...
```

### 4.3 Service接口和实现类生成

```
请生成{实体名}Service接口和{实体名}ServiceImpl实现类，用于实现{业务功能}。

Service接口应继承BaseService<{实体名}Entity>，并包含以下方法：
- {方法1}：{描述}
- {方法2}：{描述}
- ...

ServiceImpl实现类应继承ExtendedBaseServiceImpl<{实体名}Mapper, {实体名}Entity>，实现{实体名}Service接口，
并使用@Service和@Slf4j注解。实现类中应包含完整的业务逻辑实现，包括参数校验、查询条件构建、事务管理等。
```

### 4.4 Controller生成

```
请生成一个名为{实体名}Controller的控制器类，用于处理{业务功能}相关的HTTP请求。

控制器应使用@RestController、@RequestMapping和@Tag注解，并包含以下接口：
- 创建{实体名}：POST请求，接收{实体名}CreateDTO，返回ActionResult<{实体名}VO>
- 获取{实体名}列表：POST请求，接收{实体名}Pagination，返回ActionResult<List<{实体名}VO>>
- 获取{实体名}详情：GET请求，接收ID参数，返回ActionResult<{实体名}VO>
- 更新{实体名}：PUT请求，接收{实体名}UpdateDTO，返回ActionResult<Boolean>
- 删除{实体名}：DELETE请求，接收ID参数，返回ActionResult<Boolean>

控制器方法应包含异常处理和日志记录，并使用@Operation注解进行API文档标注。
```

### 4.5 完整CRUD功能生成

```
请为{业务功能}生成完整的CRUD功能代码，包括：

1. {实体名}Entity实体类，映射{表名}表，包含以下字段：
   - {字段1}：{类型}，{描述}
   - {字段2}：{类型}，{描述}
   - ...

2. {实体名}Mapper接口，继承XHBaseMapper<{实体名}Entity>

3. 模型类：
   - {实体名}CreateDTO：创建请求DTO
   - {实体名}UpdateDTO：更新请求DTO
   - {实体名}VO：返回视图对象
   - {实体名}Pagination：分页查询参数

4. {实体名}Service接口和{实体名}ServiceImpl实现类，包含以下方法：
   - create{实体名}：创建
   - update{实体名}：更新
   - delete{实体名}：删除
   - get{实体名}List：分页查询
   - get{实体名}Detail：获取详情

5. {实体名}Controller控制器，包含对应的HTTP接口

所有代码应符合项目架构规范，包括命名约定、异常处理、参数校验等。
```

### 4.6 业务场景代码生成

```
请为{业务场景}生成代码，具体需求如下：

1. 业务背景：
   {详细描述业务背景和需求}

2. 数据模型：
   - {实体1}：{描述}
   - {实体2}：{描述}
   - ...

3. 功能点：
   - {功能1}：{描述}
   - {功能2}：{描述}
   - ...

4. API接口：
   - {接口1}：{描述}
   - {接口2}：{描述}
   - ...

请生成完整的代码实现，包括实体类、Mapper接口、Service接口和实现类、Controller类，以及必要的模型类。
代码应符合项目架构规范，包括命名约定、异常处理、参数校验等。
```

## 5. 工具类和通用组件

### 5.1 常用工具类

项目中提供了多种工具类，应优先使用这些工具类而非重复实现：

1. **字符串工具**：`StrXhUtil`
   - 提供字符串判空、截取、替换等操作
   - 示例：`StrXhUtil.isNotEmpty(str)`, `StrXhUtil.equals(str1, str2)`

2. **日期工具**：`DateXhUtil`
   - 提供日期格式化、计算、比较等操作
   - 示例：`DateXhUtil.format(date, "yyyy-MM-dd")`, `DateXhUtil.addDays(date, 7)`

3. **JSON工具**：`JsonXhUtil`
   - 提供JSON序列化、反序列化等操作
   - 示例：`JsonXhUtil.toJsonString(obj)`, `JsonXhUtil.parseObject(jsonStr, Class)`

4. **随机数工具**：`RandomUtil`
   - 提供随机字符串、数字等生成操作
   - 示例：`RandomUtil.randomString(10)`, `RandomUtil.randomInt(1, 100)`

5. **集合工具**：`CollectionXhUtil`
   - 提供集合判空、转换等操作
   - 示例：`CollectionXhUtil.isEmpty(list)`, `CollectionXhUtil.toList(array)`

### 5.2 通用组件

项目中提供了多种通用组件，应优先使用这些组件：

1. **分页组件**：`Pagination`
   - 提供分页查询参数和结果封装
   - 示例：`pagination.setDataList(list, total)`

2. **结果封装**：`ActionResult`
   - 提供统一的接口返回结果封装
   - 示例：`ActionResult.success(data)`, `ActionResult.error("错误信息")`

3. **异常类**：`BusinessException`
   - 提供业务异常封装
   - 示例：`throw new BusinessException("业务错误信息")`

4. **枚举基类**：`BaseEnum`
   - 提供枚举值和描述的统一处理
   - 示例：`StatusEnum.NORMAL.getValue()`, `StatusEnum.NORMAL.getInfo()`

### 5.3 常用设计模式

在项目开发中，应合理使用以下设计模式：

1. **工厂模式**：创建对象时不暴露创建逻辑
   ```java
   public class ProductFactory {
       public static Product createProduct(String type) {
           if ("A".equals(type)) {
               return new ProductA();
           } else if ("B".equals(type)) {
               return new ProductB();
           }
           return new DefaultProduct();
       }
   }
   ```

2. **策略模式**：定义一系列算法，封装每个算法，并使它们可互换
   ```java
   public interface PriceStrategy {
       BigDecimal calculatePrice(Product product);
   }

   public class NormalPriceStrategy implements PriceStrategy {
       @Override
       public BigDecimal calculatePrice(Product product) {
           return product.getPrice();
       }
   }

   public class DiscountPriceStrategy implements PriceStrategy {
       @Override
       public BigDecimal calculatePrice(Product product) {
           return product.getPrice().multiply(new BigDecimal("0.8"));
       }
   }
   ```

3. **模板方法模式**：定义算法骨架，将一些步骤延迟到子类中实现
   ```java
   public abstract class AbstractExportService {
       public final void export() {
           // 1. 准备数据
           Object data = prepareData();
           // 2. 格式化数据
           String formattedData = formatData(data);
           // 3. 写入文件
           writeToFile(formattedData);
       }

       protected abstract Object prepareData();

       protected abstract String formatData(Object data);

       private void writeToFile(String data) {
           // 通用的文件写入逻辑
       }
   }
   ```

4. **建造者模式**：构建复杂对象
   ```java
   public class QueryBuilder {
       private LambdaQueryWrapper<ProductEntity> queryWrapper = new LambdaQueryWrapper<>();

       public QueryBuilder withName(String name) {
           if (StrXhUtil.isNotEmpty(name)) {
               queryWrapper.like(ProductEntity::getProductName, name);
           }
           return this;
       }

       public QueryBuilder withCategory(String categoryId) {
           if (categoryId != null) {
               queryWrapper.eq(ProductEntity::getCategoryId, categoryId);
           }
           return this;
       }

       public QueryBuilder withPriceRange(BigDecimal min, BigDecimal max) {
           if (min != null && max != null) {
               queryWrapper.between(ProductEntity::getPrice, min, max);
           } else if (min != null) {
               queryWrapper.ge(ProductEntity::getPrice, min);
           } else if (max != null) {
               queryWrapper.le(ProductEntity::getPrice, max);
           }
           return this;
       }

       public QueryBuilder orderByCreateTimeDesc() {
           queryWrapper.orderByDesc(ProductEntity::getCreateTime);
           return this;
       }

       public LambdaQueryWrapper<ProductEntity> build() {
           return queryWrapper;
       }
   }
   ```