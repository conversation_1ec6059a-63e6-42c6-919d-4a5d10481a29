package com.xinghuo.allegro.collect.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.BaseEntityV2;
import lombok.Data;

import java.util.Date;

/**
 * 采集报表历史数据实体
 * <AUTHOR>
 */
@Data
@TableName("zz_report_list_collect_offer")
public class ReportCollectOfferEntity extends BaseEntityV2.CUBaseEntityV2<String> {

    @TableField("report_date")
    private Date reportDate;

    @TableField("date_str")
    private String dateStr;

    @TableField("month_str")
    private String monthStr;

    @TableField("daily_collected_count")
    private Long dailyCollectedCount;

    @TableField("daily_sales_count")
    private Long dailySalesCount;

    @TableField("daily_active_clients")
    private Long dailyActiveClients;

    @TableField("daily_completed_tasks")
    private Long dailyCompletedTasks;

    @TableField("total_collected_count")
    private Long totalCollectedCount;

    @TableField("total_sales_count")
    private Long totalSalesCount;
}
