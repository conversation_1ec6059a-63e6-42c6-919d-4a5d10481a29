//package com.baidu.translate;
//
//import org.opencv.core.Core;
//import org.opencv.core.Mat;
//import org.opencv.core.MatOfFloat;
//import org.opencv.core.MatOfInt;
//import org.opencv.imgcodecs.Imgcodecs;
//import org.opencv.imgproc.Imgproc;
//
//import java.util.Arrays;
//
//public class ImageSimilarityHistogram {
//
//    static {
//        // 加载原生OpenCV库
//        String opencvLibPath = "C:\\Users\\<USER>\\Downloads\\opencv\\build\\java\\x64\\opencv_java470.dll"; // 确保路径正确
//        System.load(opencvLibPath); // 直接加载DLL文件
//        System.out.println("OpenCV 库加载成功");
//    }
//
//    public static double compareImages(String filePath1, String filePath2) {
//        Mat img1 = Imgcodecs.imread(filePath1, Imgcodecs.IMREAD_COLOR);
//        Mat img2 = Imgcodecs.imread(filePath2, Imgcodecs.IMREAD_COLOR);
//
//        if (img1.empty() || img2.empty()) {
//            System.err.println("无法加载图像");
//            return -1;
//        }
//
//        // 转换为HSV颜色空间可提高比较准确性
//        Mat hsv1 = new Mat();
//        Mat hsv2 = new Mat();
//        Imgproc.cvtColor(img1, hsv1, Imgproc.COLOR_BGR2HSV);
//        Imgproc.cvtColor(img2, hsv2, Imgproc.COLOR_BGR2HSV);
//
//        // 分别计算H和S通道的直方图
//        int hBins = 50, sBins = 60;
//        int[] histSize = {hBins, sBins};
//        MatOfInt channels = new MatOfInt(0, 1); // H和S通道
//        MatOfInt histSizeMat = new MatOfInt(histSize);
//        MatOfFloat ranges = new MatOfFloat(0, 180, 0, 256); // H的范围是0-180，S的范围是0-256
//
//        Mat hist1 = new Mat();
//        Mat hist2 = new Mat();
//
//        Imgproc.calcHist(Arrays.asList(hsv1), channels, new Mat(), hist1, histSizeMat, ranges, true);
//        Imgproc.calcHist(Arrays.asList(hsv2), channels, new Mat(), hist2, histSizeMat, ranges, true);
//
//        Core.normalize(hist1, hist1, 0, 1, Core.NORM_MINMAX);
//        Core.normalize(hist2, hist2, 0, 1, Core.NORM_MINMAX);
//
//        double correlation = Imgproc.compareHist(hist1, hist2, Imgproc.HISTCMP_CORREL);
//        return correlation;
//    }
//
//    public static void main(String[] args) {
//        String filePath1 = "D:\\image1\\04.jpg";
//        String filePath2 = "D:\\image1\\03.jpg";
//        long startTime = System.currentTimeMillis();
//        double similarity = compareImages(filePath1, filePath2);
//        if (similarity >= 0) {
//            System.out.println("相似度: " + similarity);
//
//            if (similarity > 0.8) {
//                System.out.println("图像非常相似");
//            } else if (similarity > 0.5) {
//                System.out.println("图像有一定相似度");
//            } else {
//                System.out.println("图像差异较大");
//            }
//        }
//        long endTime = System.currentTimeMillis();
//        System.out.println("耗时: " + (endTime - startTime) + "ms");
//    }
//}