package com.xinghuo.allegro.data.controller;

import com.xinghuo.allegro.data.model.sale.SaleSkuModel;
import com.xinghuo.allegro.data.service.AllegroSaleService;
import com.xinghuo.common.base.ActionResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 产品采集Controller，负责处理产品采集相关的请求。
 * 接受客户端采集的数据上传。
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@Tag(name = "销量数据", description = "销量数据")
@RequestMapping("/api/allegro/collect/sale")
public class AllegroSaleController {

    @Resource
    private AllegroSaleService allegroSaleService;



    /**
     * 获取待处理的产品链接列表。
     *
     * @return 表示操作结果的ActionResult对象，包含待处理的产品链接列表。
     */
    @Operation(summary = "获取有销量的数据列表")
    @GetMapping("/skuList")
    public ActionResult<List<SaleSkuModel>> skuList() {
        List<SaleSkuModel>  list = allegroSaleService.getSkuList();
        return ActionResult.success(list);
    }



}
