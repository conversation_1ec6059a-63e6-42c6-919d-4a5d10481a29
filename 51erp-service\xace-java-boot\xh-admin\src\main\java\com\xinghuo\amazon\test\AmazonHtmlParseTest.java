package com.xinghuo.amazon.test;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import java.io.IOException;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

/**
 * Amazon HTML解析测试类
 * 用于测试解析51erp-service/log/1.html文件中的Amazon商品信息
 */
public class AmazonHtmlParseTest {

    public static void main(String[] args) {
        AmazonHtmlParseTest test = new AmazonHtmlParseTest();
        test.testParseHtml();
    }

    /**
     * 测试解析HTML文件
     */
    public void testParseHtml() {
        try {
            // 读取HTML文件 - 使用绝对路径
            String htmlContent = readHtmlFile("G:/v2/51erp/51erp-service/log/2.html");
            if (htmlContent == null || htmlContent.trim().isEmpty()) {
                System.out.println("❌ HTML文件内容为空或读取失败");
                return;
            }

            System.out.println("✅ 成功读取HTML文件，内容长度: " + htmlContent.length());
            System.out.println("==========================================");

            // 解析HTML内容
            List<ProductInfo> products = parseListPageHtml(htmlContent);

            // 打印解析结果
            printParseResults(products);

        } catch (Exception e) {
            System.err.println("❌ 测试过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 读取HTML文件内容
     */
    private String readHtmlFile(String filePath) {
        try {
            return new String(Files.readAllBytes(Paths.get(filePath)));
        } catch (IOException e) {
            System.err.println("❌ 读取HTML文件失败: " + e.getMessage());
            return null;
        }
    }

    /**
     * 解析Amazon列表页HTML内容，提取商品信息
     */
    public List<ProductInfo> parseListPageHtml(String htmlContent) {
        List<ProductInfo> productList = new ArrayList<>();

        if (htmlContent == null || htmlContent.trim().isEmpty()) {
            System.out.println("⚠️ HTML内容为空");
            return productList;
        }

        try {
            Document doc = Jsoup.parse(htmlContent);
            System.out.println("✅ HTML解析成功");

            // 验证商品卡片是否在 <div role="listitem" 中
            Elements listItems = doc.select("div[role=listitem]");
            System.out.println("🔍 找到 role='listitem' 的div数量: " + listItems.size());

            // 查找商品容器，使用多种选择器策略
            Elements productElements = doc.select("[data-component-type='s-search-result']");
            System.out.println("🔍 使用 [data-component-type='s-search-result'] 找到商品数量: " + productElements.size());

            if (productElements.isEmpty()) {
                productElements = doc.select(".s-result-item");
                System.out.println("🔍 使用 .s-result-item 找到商品数量: " + productElements.size());
            }

            if (productElements.isEmpty()) {
                productElements = doc.select("[data-asin]");
                System.out.println("🔍 使用 [data-asin] 找到商品数量: " + productElements.size());
            }

            if (productElements.isEmpty()) {
                System.out.println("❌ 未找到任何商品元素，可能页面结构已变化");
                return productList;
            }

            System.out.println("==========================================");

            // 解析每个商品
            for (int i = 0; i < productElements.size(); i++) {
                Element productElement = productElements.get(i);
                System.out.println("📦 解析第 " + (i + 1) + " 个商品:");

                ProductInfo product = parseProductElement(productElement);
                if (product != null) {
                    productList.add(product);
                    printProductInfo(product, i + 1);
                } else {
                    System.out.println("⚠️ 第 " + (i + 1) + " 个商品解析失败");
                }
                System.out.println("------------------------------------------");
            }

        } catch (Exception e) {
            System.err.println("❌ 解析HTML失败: " + e.getMessage());
            e.printStackTrace();
        }

        return productList;
    }

    /**
     * 解析单个商品元素
     */
    private ProductInfo parseProductElement(Element productElement) {
        if (productElement == null) {
            return null;
        }

        ProductInfo product = new ProductInfo();

        try {
            // 验证是否在 role="listitem" 中
            Element listItemParent = productElement.closest("div[role=listitem]");
            product.setInListItem(listItemParent != null);

            // 提取ASIN
            String asin = productElement.attr("data-asin");
            product.setAsin(asin);

            // 提取商品URL
            String productUrl = extractProductUrl(productElement);
            product.setUrl(productUrl);

            // 提取商品标题
            String title = extractProductTitle(productElement);
            product.setTitle(title);

            // 提取价格信息
            extractPriceInfo(productElement, product);

            // 提取评分和评论数
            extractRatingInfo(productElement, product);

            // 提取图片URL
            String imageUrl = extractImageUrl(productElement);
            product.setImageUrl(imageUrl);

            // 提取Prime信息
            String primeInfo = extractPrimeInfo(productElement);
            product.setPrimeInfo(primeInfo);

            // 检查是否为赞助商品
            boolean isSponsored = checkIfSponsored(productElement);
            product.setSponsored(isSponsored);

            return product;

        } catch (Exception e) {
            System.err.println("❌ 解析商品元素失败: " + e.getMessage());
            return null;
        }
    }

    /**
     * 提取商品URL
     */
    private String extractProductUrl(Element element) {
        Elements linkElements = element.select("a[href*='/dp/']");
        if (!linkElements.isEmpty()) {
            return linkElements.first().attr("href");
        }
        return null;
    }

    /**
     * 提取商品标题
     */
    private String extractProductTitle(Element element) {
        // 尝试多种选择器
        Elements titleElements = element.select("h2 a span, .s-size-mini span, [data-cy='title-recipe'] a span");
        if (!titleElements.isEmpty()) {
            return titleElements.first().text().trim();
        }
        return null;
    }

    /**
     * 提取价格信息
     */
    private void extractPriceInfo(Element element, ProductInfo product) {
        // 当前价格
        Elements priceElements = element.select(".a-price-whole, .a-price .a-offscreen");
        if (!priceElements.isEmpty()) {
            String priceText = priceElements.first().text();
            BigDecimal price = extractPrice(priceText);
            product.setPrice(price);
        }

        // 原价（划线价格）
        Elements originalPriceElements = element.select(".a-text-price .a-offscreen");
        if (!originalPriceElements.isEmpty()) {
            String originalPriceText = originalPriceElements.first().text();
            BigDecimal originalPrice = extractPrice(originalPriceText);
            product.setOriginalPrice(originalPrice);
        }
    }

    /**
     * 提取评分和评论数信息
     */
    private void extractRatingInfo(Element element, ProductInfo product) {
        // 评分
        Elements ratingElements = element.select(".a-icon-alt");
        for (Element ratingElement : ratingElements) {
            String ratingText = ratingElement.text();
            if (ratingText.contains("out of 5 stars")) {
                try {
                    String ratingStr = ratingText.split(" ")[0];
                    BigDecimal rating = new BigDecimal(ratingStr);
                    product.setRating(rating);
                    break;
                } catch (Exception e) {
                    // 忽略解析错误
                }
            }
        }

        // 评论数
        Elements reviewElements = element.select("a[href*='#customerReviews'] span");
        if (!reviewElements.isEmpty()) {
            String reviewText = reviewElements.first().text();
            Integer reviewCount = extractReviewCount(reviewText);
            product.setReviewCount(reviewCount);
        }
    }

    /**
     * 提取图片URL
     */
    private String extractImageUrl(Element element) {
        Elements imgElements = element.select("img.s-image");
        if (!imgElements.isEmpty()) {
            return imgElements.first().attr("src");
        }
        return null;
    }

    /**
     * 提取Prime信息
     */
    private String extractPrimeInfo(Element element) {
        Elements primeElements = element.select(".a-icon-prime, [data-cy='delivery-recipe']");
        if (!primeElements.isEmpty()) {
            return primeElements.first().text().trim();
        }
        return null;
    }

    /**
     * 检查是否为赞助商品
     */
    private boolean checkIfSponsored(Element element) {
        return element.select("[data-component-type*='sponsored'], .s-sponsored").size() > 0;
    }

    /**
     * 从价格文本中提取数字
     */
    private BigDecimal extractPrice(String priceText) {
        if (priceText == null || priceText.trim().isEmpty()) {
            return null;
        }
        try {
            String cleanPrice = priceText.replaceAll("[^0-9.]", "");
            if (!cleanPrice.isEmpty()) {
                return new BigDecimal(cleanPrice);
            }
        } catch (Exception e) {
            // 忽略解析错误
        }
        return null;
    }

    /**
     * 从评论文本中提取数字
     */
    private Integer extractReviewCount(String reviewText) {
        if (reviewText == null || reviewText.trim().isEmpty()) {
            return null;
        }
        try {
            String cleanCount = reviewText.replaceAll("[^0-9]", "");
            if (!cleanCount.isEmpty()) {
                return Integer.parseInt(cleanCount);
            }
        } catch (Exception e) {
            // 忽略解析错误
        }
        return null;
    }

    /**
     * 打印解析结果
     */
    private void printParseResults(List<ProductInfo> products) {
        System.out.println("==========================================");
        System.out.println("📊 解析结果汇总:");
        System.out.println("总共解析到商品数量: " + products.size());

        long inListItemCount = products.stream().mapToLong(p -> p.isInListItem() ? 1 : 0).sum();
        System.out.println("位于 role='listitem' 中的商品数量: " + inListItemCount);

        long hasAsinCount = products.stream().mapToLong(p -> p.getAsin() != null && !p.getAsin().isEmpty() ? 1 : 0).sum();
        System.out.println("有ASIN的商品数量: " + hasAsinCount);

        long hasTitleCount = products.stream().mapToLong(p -> p.getTitle() != null && !p.getTitle().isEmpty() ? 1 : 0).sum();
        System.out.println("有标题的商品数量: " + hasTitleCount);

        long hasPriceCount = products.stream().mapToLong(p -> p.getPrice() != null ? 1 : 0).sum();
        System.out.println("有价格的商品数量: " + hasPriceCount);

        System.out.println("==========================================");
    }

    /**
     * 打印单个商品信息
     */
    private void printProductInfo(ProductInfo product, int index) {
        System.out.println("  📍 位于 role='listitem': " + (product.isInListItem() ? "✅" : "❌"));
        System.out.println("  🏷️  ASIN: " + (product.getAsin() != null ? product.getAsin() : "未找到"));
        System.out.println("  📝 标题: " + (product.getTitle() != null ? product.getTitle() : "未找到"));
        System.out.println("  💰 价格: " + (product.getPrice() != null ? "$" + product.getPrice() : "未找到"));
        System.out.println("  💸 原价: " + (product.getOriginalPrice() != null ? "$" + product.getOriginalPrice() : "未找到"));
        System.out.println("  ⭐ 评分: " + (product.getRating() != null ? product.getRating() + "/5" : "未找到"));
        System.out.println("  💬 评论数: " + (product.getReviewCount() != null ? product.getReviewCount() : "未找到"));
        System.out.println("  🖼️  图片: " + (product.getImageUrl() != null ? "有" : "无"));
        System.out.println("  🚚 Prime: " + (product.getPrimeInfo() != null ? product.getPrimeInfo() : "无"));
        System.out.println("  📢 赞助: " + (product.isSponsored() ? "是" : "否"));
        System.out.println("  🔗 URL: " + (product.getUrl() != null ? product.getUrl() : "未找到"));
    }

    /**
     * 商品信息数据类
     */
    static class ProductInfo {
        private boolean inListItem;
        private String asin;
        private String url;
        private String title;
        private BigDecimal price;
        private BigDecimal originalPrice;
        private BigDecimal rating;
        private Integer reviewCount;
        private String imageUrl;
        private String primeInfo;
        private boolean sponsored;

        // Getters and Setters
        public boolean isInListItem() { return inListItem; }
        public void setInListItem(boolean inListItem) { this.inListItem = inListItem; }

        public String getAsin() { return asin; }
        public void setAsin(String asin) { this.asin = asin; }

        public String getUrl() { return url; }
        public void setUrl(String url) { this.url = url; }

        public String getTitle() { return title; }
        public void setTitle(String title) { this.title = title; }

        public BigDecimal getPrice() { return price; }
        public void setPrice(BigDecimal price) { this.price = price; }

        public BigDecimal getOriginalPrice() { return originalPrice; }
        public void setOriginalPrice(BigDecimal originalPrice) { this.originalPrice = originalPrice; }

        public BigDecimal getRating() { return rating; }
        public void setRating(BigDecimal rating) { this.rating = rating; }

        public Integer getReviewCount() { return reviewCount; }
        public void setReviewCount(Integer reviewCount) { this.reviewCount = reviewCount; }

        public String getImageUrl() { return imageUrl; }
        public void setImageUrl(String imageUrl) { this.imageUrl = imageUrl; }

        public String getPrimeInfo() { return primeInfo; }
        public void setPrimeInfo(String primeInfo) { this.primeInfo = primeInfo; }

        public boolean isSponsored() { return sponsored; }
        public void setSponsored(boolean sponsored) { this.sponsored = sponsored; }
    }
}
