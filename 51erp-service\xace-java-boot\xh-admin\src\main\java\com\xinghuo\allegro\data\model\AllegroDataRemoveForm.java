package com.xinghuo.allegro.data.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * Allegro 英文采集商品删除表单
 * 
 * <AUTHOR>
 * @date 2024-11-05
 */
@Data
@Schema(description = "Allegro 英文采集商品删除表单")
public class AllegroDataRemoveForm {

    @Schema(description = "采集产品ID")
    private String cpId;

    @Schema(description = "删除原因")
    private String reason;

    @Schema(description = "客户端ID")
    private String clientId;
}
