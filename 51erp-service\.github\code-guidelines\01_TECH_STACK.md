# erp-service 核心技术栈与架构

## 核心技术栈

* **语言:** Java
* **构建工具:** Maven
* **主要框架:**
  * Spring Boot3
  * MybatisPlus (用于数据持久化)
  * Lombok (用于简化代码)

## 项目架构

* **多模块 Maven 项目架构**
  * `xh-admin` - 系统管理与业务主模块
  * `xh-system` - 系统功能支持模块
  * `xh-oauth` - 身份验证与授权模块
  * 其他业务模块

## 环境要求

* **JDK:** 17及以上版本
* **Maven:** 3.6.3及以上版本
* **数据库:** MySQL 5.7+/SQLServer 2012+/Oracle 11g+/PostgreSQL 12+

## IDEA 推荐插件

* `Lombok`(必须)
* `Alibaba Java Coding Guidelines`
* `MybatisX`

## 参考链接

* [Spring Boot 官方文档](https://spring.io/projects/spring-boot)
* [MyBatis-Plus 官方文档](https://baomidou.com/)
* [Lombok 官方文档](https://projectlombok.org/features/)
