package com.xinghuo.allegro.home.controller;

import com.xinghuo.allegro.collect.service.CollectOfferService;
import com.xinghuo.allegro.data.service.AllegroSellerService;
import com.xinghuo.allegro.collect.service.CollectTaskService;
import com.xinghuo.allegro.home.model.*;
import com.xinghuo.allegro.home.service.IndexDataService;
import com.xinghuo.allegro.manage.entity.AllegroStoreEntity;
import com.xinghuo.allegro.manage.entity.StoreCheckEntity;
import com.xinghuo.allegro.manage.service.AllegroStoreService;
import com.xinghuo.allegro.manage.service.StoreCheckService;
import com.xinghuo.allegro.msg.service.*;
import com.xinghuo.allegro.order.service.ErpOrderService;
import com.xinghuo.allegro.push.model.erp.VioStatusEnum;
import com.xinghuo.allegro.push.service.CollectVioWordService;
import com.xinghuo.allegro.push.service.ErpProductVioService;
import com.xinghuo.common.annotation.NoDataSourceBind;
import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.util.UserProvider;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.common.util.core.DateXhUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.permission.service.UserService;
import com.xinghuo.tenant.entity.TenantEntity;
import com.xinghuo.tenant.service.TenantService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@Tag(name = "店铺首页", description = "店铺首页")
@RequestMapping("/api/allegro/home")
public class HomeController {

    @Resource
    private TenantService tenantService;

    @Resource
    private UserProvider userProvider;

    @Resource
    private UserService userService;

    @Resource
    private AllegroStoreService allegroStoreService;
    @Resource
    private CollectVioWordService collectVioWordService;

    @Resource
    private ErpProductVioService erpProductVioService;
    @Resource
    private MsgRatingService msgRatingService;
    @Resource
    private MsgThreadService msgThreadService;
    @Resource
    private MsgDisputeService msgDisputeService;

    @Resource
    private StoreCheckService storeCheckService;

    @Resource
    private DisputeMessageService disputeMessageService;

    @Resource
    private MsgMessageService msgMessageService;

    @Resource
    private ErpOrderService erpOrderService;
    @Resource
    private CollectTaskService collectTaskService;

    @Resource
    private AllegroSellerService allegroSellerService;
    @Resource
    private IndexDataService indexDataService;

    @Resource
    private CollectOfferService collectOfferService;



    @Operation(summary = "店铺首页")
    @GetMapping("/index")
    public ActionResult index() {
        return ActionResult.success();
    }

    @Operation(summary = "店铺侵权词数据")
    @GetMapping("/vio")
    public ActionResult vio() {
        List<Map<String, Object>> vioList = collectVioWordService.getSumList();
        Integer totalCount = 0, vioCount=0, lossCount = 0, notFoundCount = 0;
        Integer vioErpCount = 0, lossErpCount = 0, notFoundErpCount = 0;
        for(Map<String, Object> map: vioList){
            if(map.get("vioType").toString().equals("1")){
                vioCount = Integer.parseInt(map.get("count").toString());
            }else if(map.get("vioType").toString().equals("2")){
                lossCount = Integer.parseInt(map.get("count").toString());
            }else if(map.get("vioType").toString().equals("3")){
                notFoundCount = Integer.parseInt(map.get("count").toString());
            }
            totalCount += Integer.parseInt(map.get("count").toString());
        }
        List<Map<String, Object>> vioErpList = erpProductVioService.getVioSumList();
        for(Map<String, Object> map: vioErpList){
            if(map.get("vioType").toString().equals(VioStatusEnum.VIOWORD.getValue())){
                vioErpCount = Integer.parseInt(map.get("count").toString());
            }else if(map.get("vioType").toString().equals(VioStatusEnum.LOSS.getValue())){
                lossErpCount = Integer.parseInt(map.get("count").toString());
            }else if(map.get("vioType").toString().equals(VioStatusEnum.NOTFOUND.getValue())){
                notFoundErpCount = Integer.parseInt(map.get("count").toString());
            }
        }
        VioStatModel model =     VioStatModel.builder()
                .totalCount(totalCount)
//                .todayIncrease(10L)
//                .monthIncrease(200l)
                .metrics(VioStatModel.Metrics.builder()
                        .violations(vioCount)
                        .lossMaking(lossCount)
                        .notFound(notFoundCount)
                        .build())
                .productStats(VioStatModel.ProductStats.builder()
                        .violationProducts(vioErpCount)
                        .lossMakingProducts(lossErpCount)
                        .others(notFoundErpCount)
                        .build())
                .build();



        return ActionResult.success(model);
    }

    @Operation(summary = "客服中心数据")
    @GetMapping("/msg")
    public ActionResult msg() {
        long size1 = 10;

        Long todayDealMsgCount = msgMessageService.todayCount();
        Long todayDealDisputeCount = disputeMessageService.todaySellerRepayCount();
        Long todayDealRatingCount = msgRatingService.todayCount();

        MsgDeskStatModel model =  MsgDeskStatModel.builder()
                .pendingMessages(msgThreadService.countByStatus(0))
                .pendingDisputes(msgDisputeService.countByStatus(0))
                .pendingComments(msgRatingService.countByStatus(0))
                .todayStats(MsgDeskStatModel.TodayStats.builder()
                        .messages(todayDealMsgCount)
                        .disputes(todayDealDisputeCount)
                        .comments(todayDealRatingCount)
                        .total(todayDealMsgCount+todayDealDisputeCount+todayDealRatingCount)
                        .build()).build();
        return ActionResult.success(model);
    }

    @Operation(summary = "今日销售额数据")
    @GetMapping("/sale")
    public ActionResult sale() {
        log.info(userProvider.getRoleList().toString());
        if(userProvider.getRoleList().contains("ROLE_ADMIN") || userProvider.get().getIsAdministrator()) {

            List<Map<String, Object>> list = erpOrderService.selectOrderSummary(DateXhUtil.today());
            //

            Long shopCount = list.stream().count();
            Long productCount = list.stream().mapToLong(map -> Long.parseLong(map.get("itemCount").toString())).sum();
            Long orderCount = list.stream().mapToLong(map -> Long.parseLong(map.get("count").toString())).sum();
            BigDecimal totalAmount = list.stream().map(map -> new BigDecimal(map.get("payment").toString())).reduce(BigDecimal.ZERO, BigDecimal::add);
            List<Map<String, Object>> yeslist = erpOrderService.selectOrderSummary(DateXhUtil.formatDate(DateXhUtil.yesterday()));
            BigDecimal yestodayTotalAmount = yeslist.stream().map(map -> new BigDecimal(map.get("payment").toString())).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (yestodayTotalAmount == null || yestodayTotalAmount.compareTo(BigDecimal.ZERO) == 0) {
                yestodayTotalAmount = BigDecimal.ONE;
            }
            //计算一个 yesterRate 一下
            Double yestodayRate = totalAmount.divide(yestodayTotalAmount, 2, BigDecimal.ROUND_HALF_UP).doubleValue();

            SalesStatModel model = SalesStatModel.builder()
                    .totalAmount(totalAmount)
                    .yesterdayRate(yestodayRate * 100)
                    .yesterdayAmount(yestodayTotalAmount)
                    .metrics(SalesStatModel.Metrics.builder()
                            .orderCount(orderCount)
                            .productCount(orderCount)
                            .shopCount(shopCount)
                            .build())
                    .build();
            return ActionResult.success(model);
        }else {
            SalesStatModel model = SalesStatModel.builder()
                    .totalAmount(BigDecimal.ZERO)
                    .yesterdayRate(0.0)
                    .yesterdayAmount(BigDecimal.ZERO)
                    .metrics(SalesStatModel.Metrics.builder()
                            .orderCount(0L)
                            .productCount(0L)
                            .shopCount(0L)
                            .build())
                    .build();
            return ActionResult.success(model);
        }

    }

    @Operation(summary = "店铺数据")
    @GetMapping("/team")
    public ActionResult team() {
        String encode = "0";
        if(StrXhUtil.isNotEmpty(userProvider.get().getTenantId())){
            encode = userProvider.get().getTenantId();
        }
        TenantEntity tenantEntity = tenantService.getInfoByEnCode(encode);
        TeamInfoModel model =  TeamInfoModel.builder().build();
        if(tenantEntity!=null){
            Long shopCount = allegroStoreService.list().stream().count();
            long memberCount = userService.count()-1;


             model = TeamInfoModel.builder()
                    .teamName(tenantEntity.getFullName())
                    .vipLevel(tenantEntity.getLevel())
                    .maxShopCount(tenantEntity.getLimitStoreNum())
                    .memberCount(memberCount)
                    .currentShopCount(shopCount)
                    .expireDate(DateXhUtil.formatDate(tenantEntity.getExpiresTime()))
                    .loginAccount(userProvider.get().getUserName())
                     .percent(BigDecimal.valueOf(shopCount).divide(BigDecimal.valueOf(tenantEntity.getLimitStoreNum()),2,BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100)))

                    .build();
        }

        return ActionResult.success(model);
    }


    @Operation(summary = "授权过期店铺")
    @GetMapping("/expireStore")
    public ActionResult<List<ExpireStoreModel>> expireStore() {
         List<AllegroStoreEntity> list = allegroStoreService.getExpiredStores();
         List<ExpireStoreModel> listVo = BeanCopierUtils.copyList(list, ExpireStoreModel.class);
         return ActionResult.success(listVo);
    }

    @Operation(summary = "异常店铺")
    @GetMapping("/abnorStore")
    public ActionResult<StoreCheckModel> abnorStore() {
        List<StoreCheckEntity> list = storeCheckService.list();
        Long activeStoreNum = allegroStoreService.sumActiveStoreNum(userProvider.get().getTenantId());
        StoreCheckModel model = new StoreCheckModel();
        model.setActiveStoreNum(activeStoreNum);
        model.setStorelist(list);
        model.setAbnorNum(list.size());
        return ActionResult.success(model);
    }


    @Operation(summary = "采集任务")
    @GetMapping("/task")
    @NoDataSourceBind
    public ActionResult task() {
        //对collectTask 未完成的任务进行统计
        TaskPanelModel model = TaskPanelModel.builder()
                .pendSellerCount(collectTaskService.getPendingTaskCount("S"))
                .pendProductCount(collectTaskService.getPendingTaskCount("P"))
                .pendCateItemCount(collectTaskService.getPendingTaskCount("C"))
                .pendNewItemCount(collectTaskService.getPendingTaskCount("CNEW"))
                .todayCollectDesc(indexDataService.getValue("24H_WAIT_COLLECT"))
                .sellerCount(allegroSellerService.validCount())
                .build();
          return ActionResult.success(model);
    }

    @Operation(summary = "报价采集统计")
    @GetMapping("/quotationStats")
    @NoDataSourceBind
    public ActionResult quotationStats() {
        QuotationStatModel model = QuotationStatModel.builder()
                .todayOfferAddAndSale(indexDataService.getValue("TODAY_OFFER_ADD_AND_SALE"))
                .yesterdayOfferAddAndSale(indexDataService.getValue("YESTERDAY_OFFER_ADD_AND_SALE"))
                .todayCollectClientIdCount(indexDataService.getValue("TODAY_COLLECT_CLIENTID_COUNT"))
                .allCollect(indexDataService.getValue("ALL_COLLECT"))
                .build();
        return ActionResult.success(model);
    }


    @Operation(summary = "后台详情采集统计")
    @GetMapping("/detailStats")
    @NoDataSourceBind
    public ActionResult detailStats() {
        DetailStatModel model = DetailStatModel.builder()
                .waitSaleSum(indexDataService.getValue("WAIT_SALE_SUM"))
                .allWaitCollectSum(indexDataService.getValue("ALL_WAIT_COLLECT_SUM"))
                .allCollectSum(indexDataService.getValue("ALL_COLLECT_SUM"))
                .todayCollect(collectOfferService.todayCollect())
                .todayDelete(collectOfferService.todayDelete())
                .yesterdayCollect(collectOfferService.yesterdayCollect())

                .build();
        return ActionResult.success(model);
    }
}
