package com.xinghuo.allegro.collect.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xinghuo.allegro.collect.dao.CollectProductMapper;
import com.xinghuo.allegro.collect.entity.CollectProductEntity;
import com.xinghuo.allegro.collect.model.product.CollectProductPagination;
import com.xinghuo.allegro.collect.service.CollectProductService;
import com.xinghuo.common.base.service.BaseServiceImpl;
import com.xinghuo.common.util.core.StrXhUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class CollectProductServiceImpl extends BaseServiceImpl<CollectProductMapper, CollectProductEntity> implements CollectProductService {

    @Override
    public List<CollectProductEntity> getList(CollectProductPagination pagination) {
        QueryWrapper<CollectProductEntity> queryWrapper = new QueryWrapper<>();

        // 关键词搜索
        if (StrXhUtil.isNotEmpty(pagination.getKeyword())) {
            queryWrapper.and(wrapper -> {
                wrapper.or().like("product_id", pagination.getKeyword())
                       .or().like("product_name", pagination.getKeyword())
                       .or().like("offer_name", pagination.getKeyword())
                       .or().like("category_id", pagination.getKeyword());
            });
        }

        // 搜索类型和关键字
        if (StrXhUtil.isNotEmpty(pagination.getSearchType()) && StrXhUtil.isNotEmpty(pagination.getSearchKey())) {
            String[] searchParts = pagination.getSearchType().split("\\|");
            if (searchParts.length == 2) {
                String field = searchParts[0];
                String operator = searchParts[1];

                switch (operator.toUpperCase()) {
                    case "EQ":
                        queryWrapper.eq(field, pagination.getSearchKey());
                        break;
                    case "LIKE":
                        queryWrapper.like(field, pagination.getSearchKey());
                        break;
                    case "LIKERIGHT":
                        queryWrapper.likeRight(field, pagination.getSearchKey());
                        break;
                }
            }
        }

        // 产品ID
        if (StrXhUtil.isNotEmpty(pagination.getProductId())) {
            queryWrapper.lambda().eq(CollectProductEntity::getProductId, pagination.getProductId());
        }

        // SKU ID
        if (pagination.getSkuId() != null) {
            queryWrapper.lambda().eq(CollectProductEntity::getSkuId, pagination.getSkuId());
        }

        // 分类ID
        if (StrXhUtil.isNotEmpty(pagination.getCategoryId())) {
            queryWrapper.lambda().eq(CollectProductEntity::getCategoryId, pagination.getCategoryId());
        }

        // 状态
        if (pagination.getStatus() != null) {
            queryWrapper.lambda().eq(CollectProductEntity::getStatus, pagination.getStatus());
        }

        // 时间范围查询
        if (StrXhUtil.isNotEmpty(pagination.getDateType()) && pagination.getStartTime() != null) {
            queryWrapper.ge(pagination.getDateType(), pagination.getStartTime());
        }
        if (StrXhUtil.isNotEmpty(pagination.getDateType()) && pagination.getEndTime() != null) {
            queryWrapper.le(pagination.getDateType(), pagination.getEndTime());
        }

        // 排序
        if (StrXhUtil.isNotEmpty(pagination.getListOrder())) {
            String[] orderParts = pagination.getListOrder().split("\\|");
            if (orderParts.length == 2) {
                String field = orderParts[0];
                String direction = orderParts[1];
                if ("desc".equalsIgnoreCase(direction)) {
                    queryWrapper.orderByDesc(field);
                } else {
                    queryWrapper.orderByAsc(field);
                }
            }
        } else {
            queryWrapper.orderByDesc("f_created_at");
        }

        Page<CollectProductEntity> page = new Page<>(pagination.getCurrentPage(), pagination.getPageSize());
        IPage<CollectProductEntity> userIpage = this.page(page, queryWrapper);
        return pagination.setDataList(userIpage.getRecords(), userIpage.getTotal());

    }

    @Override
    public CollectProductEntity getByProductId(String productId) {
        QueryWrapper<CollectProductEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(CollectProductEntity::getProductId, productId).last("limit 1");
        return this.getOne(queryWrapper);
    }
}
