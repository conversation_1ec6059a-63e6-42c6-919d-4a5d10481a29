package com.baidu.translate;
import java.util.Base64;
import java.io.ByteArrayInputStream;
import java.util.zip.GZIPInputStream;
import java.io.ByteArrayOutputStream;

public class DecompressExample {
    public static void main(String[] args) {
        try {
            // 原始的base64编码字符串
            String data = "H4sIAAAAAAAAAKtWSs5PSVWyMtBRKi5JLAGxagFsAzyZFAAAAA==";

            // Base64解码
            byte[] decodedBytes = Base64.getDecoder().decode(data);

            // 使用GZIP解压缩
            try (GZIPInputStream gzipInputStream = new GZIPInputStream(new ByteArrayInputStream(decodedBytes));
                 ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {

                byte[] buffer = new byte[1024];
                int len;
                while ((len = gzipInputStream.read(buffer)) > 0) {
                    outputStream.write(buffer, 0, len);
                }

                String result = new String(outputStream.toByteArray(), "UTF-8");
                System.out.println("解压后的结果: " + result);
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}