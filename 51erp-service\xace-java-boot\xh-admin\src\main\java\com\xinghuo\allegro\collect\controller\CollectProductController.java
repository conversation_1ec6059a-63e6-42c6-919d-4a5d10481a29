package com.xinghuo.allegro.collect.controller;

import com.xinghuo.allegro.collect.entity.CollectProductEntity;
import com.xinghuo.allegro.collect.model.product.CollectProductForm;
import com.xinghuo.allegro.collect.model.product.CollectProductModel;
import com.xinghuo.allegro.collect.model.product.CollectProductPagination;
import com.xinghuo.allegro.collect.service.CollectProductService;
import com.xinghuo.allegro.push.service.AllegroCategoryService;
import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.constant.MsgCode;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.common.util.core.StrXhUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 采集产品管理控制器
 * <AUTHOR>
 */
@Slf4j
@Tag(name = "采集产品管理", description = "CollectProductController")
@RestController
@RequestMapping("/api/allegro/collect/product")
public class CollectProductController {

    @Resource
    private CollectProductService collectProductService;

    @Resource
    private AllegroCategoryService allegroCategoryService;

    /**
     * 采集产品列表
     */
    @Operation(summary = "采集产品列表")
    @PostMapping("/getList")
    public ActionResult<PageListVO<CollectProductModel>> list(@RequestBody CollectProductPagination pagination) {
        try {
            List<CollectProductEntity> list = collectProductService.getList(pagination);
            List<CollectProductModel> listVO = BeanCopierUtils.copyList(list, CollectProductModel.class);
            
            // 补充分类路径和状态文本
            if (!listVO.isEmpty()) {
                listVO.forEach(item -> {
                    // 设置状态文本
                    item.setStatusText(getStatusText(item.getStatus()));
                    
                    // 设置分类路径
                    if (StrXhUtil.isNotBlank(item.getCategoryId())) {
                        try {
                            var category = allegroCategoryService.getSimpleInfoByCategoryId(item.getCategoryId(), null);
                            if (category != null) {
                                item.setCategoryPath(category.getPath());
                            }
                        } catch (Exception e) {
                            log.warn("获取分类路径失败: categoryId={}", item.getCategoryId(), e);
                        }
                    }
                });
            }
            
            PaginationVO page = BeanCopierUtils.copy(pagination, PaginationVO.class);
            return ActionResult.page(listVO, page);
        } catch (Exception e) {
            log.error("查询采集产品列表失败", e);
            return ActionResult.fail("查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取采集产品详情
     */
    @Operation(summary = "采集产品-详情")
    @GetMapping("/{id}")
    @Parameters({
            @Parameter(name = "id", description = "主键", required = true)
    })
    public ActionResult<CollectProductModel> info(@PathVariable("id") Integer id) {
        try {
            CollectProductEntity entity = collectProductService.getById(id);
            if (entity == null) {
                return ActionResult.fail("数据不存在");
            }
            
            CollectProductModel infoVo = BeanCopierUtils.copy(entity, CollectProductModel.class);
            
            // 设置状态文本
            infoVo.setStatusText(getStatusText(entity.getStatus()));
            
            // 设置分类路径
            if (StrXhUtil.isNotBlank(entity.getCategoryId())) {
                try {
                    var category = allegroCategoryService.getSimpleInfoByCategoryId(entity.getCategoryId(), null);
                    if (category != null) {
                        infoVo.setCategoryPath(category.getPath());
                    }
                } catch (Exception e) {
                    log.warn("获取分类路径失败: categoryId={}", entity.getCategoryId(), e);
                }
            }
            
            return ActionResult.success(infoVo);
        } catch (Exception e) {
            log.error("获取采集产品详情失败: id={}", id, e);
            return ActionResult.fail("获取详情失败: " + e.getMessage());
        }
    }

    /**
     * 编辑采集产品
     */
    @PutMapping("/{id}")
    @Parameters({
            @Parameter(name = "id", description = "主键", required = true)
    })
    @Operation(summary = "采集产品-更新")
    public ActionResult update(@PathVariable("id") Integer id, @RequestBody @Valid CollectProductForm productForm) {
        try {
            CollectProductEntity entity = collectProductService.getById(id);
            if (entity == null) {
                return ActionResult.fail("数据不存在");
            }
            
            // 复制表单数据到实体
            entity =   BeanCopierUtils.copy(productForm, CollectProductEntity.class);
            entity.setId(id);
            entity.setLastModifiedAt(new java.util.Date());
            
            collectProductService.updateById(entity);
            return ActionResult.success(MsgCode.SU004.get());
        } catch (Exception e) {
            log.error("更新采集产品失败: id={}", id, e);
            return ActionResult.fail("更新失败: " + e.getMessage());
        }
    }

    /**
     * 删除采集产品
     */
    @DeleteMapping("/{id}")
    @Parameters({
            @Parameter(name = "id", description = "主键", required = true)
    })
    @Operation(summary = "采集产品删除")
    public ActionResult delete(@PathVariable("id") Integer id) {
        try {
            CollectProductEntity entity = collectProductService.getById(id);
            if (entity == null) {
                return ActionResult.fail("数据不存在");
            }
            
            collectProductService.removeById(id);
            return ActionResult.success(MsgCode.SU003.get());
        } catch (Exception e) {
            log.error("删除采集产品失败: id={}", id, e);
            return ActionResult.fail("删除失败: " + e.getMessage());
        }
    }

    /**
     * 根据产品ID获取采集产品信息
     */
    @Operation(summary = "根据产品ID获取采集产品信息")
    @GetMapping("/getByProductId/{productId}")
    @Parameters({
            @Parameter(name = "productId", description = "产品ID", required = true)
    })
    public ActionResult<CollectProductModel> getByProductId(@PathVariable("productId") String productId) {
        try {
            CollectProductEntity entity = collectProductService.getByProductId(productId);
            if (entity == null) {
                return ActionResult.fail("产品不存在");
            }
            
            CollectProductModel model = BeanCopierUtils.copy(entity, CollectProductModel.class);
            model.setStatusText(getStatusText(entity.getStatus()));
            
            return ActionResult.success(model);
        } catch (Exception e) {
            log.error("根据产品ID获取采集产品信息失败: productId={}", productId, e);
            return ActionResult.fail("查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取状态文本
     */
    private String getStatusText(Integer status) {
        if (status == null) {
            return "未知";
        }
        
        switch (status) {
            case 0:
                return "待处理";
            case 1:
                return "处理中";
            case 2:
                return "已完成";
            case -1:
                return "已失效";
            default:
                return "未知状态";
        }
    }
}
