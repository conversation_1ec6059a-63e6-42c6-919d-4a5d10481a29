package com.xinghuo.allegro.data.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * Allegro 英文采集商品数据传输对象
 * 
 * <AUTHOR>
 * @date 2024-11-05
 */
@Data
@Schema(description = "Allegro 英文采集商品数据")
public class AllegroDataOfferModel {

    @Schema(description = "报价ID")
    private String offerId;

    @Schema(description = "商品变体源ID")
    private String variantId;

    @Schema(description = "商品链接")
    private String offerLink;

    @Schema(description = "商品名称")
    private String offerName;

    @Schema(description = "商品首图URL")
    private String imageUrl;

    @Schema(description = "商品价格")
    private BigDecimal price;

    @Schema(description = "运费价格")
    private BigDecimal shipFee;

    @Schema(description = "商品总价")
    private BigDecimal totalPrice;

    @Schema(description = "类目ID")
    private String categoryId;

    @Schema(description = "商品分类名称")
    private String categoryName;

    @Schema(description = "类目路径名称")
    private String categoryPath;

    @Schema(description = "任务ID")
    private String taskId;

    @Schema(description = "卖家ID")
    private String sellerId;

    @Schema(description = "商品已售数量")
    private Integer buyersQuantity;

    @Schema(description = "SKU_ID")
    private Integer skuId;

    @Schema(description = "列表页采集人/客户端标识")
    private String listClientId;

    @Schema(description = "采集优先级")
    private Integer priority;

    @Schema(description = "侵权状态")
    private Boolean vioStatus;

    @Schema(description = "详情页采集人/客户端ID")
    private String detailClientId;

    @Schema(description = "备注信息")
    private String note;

    @Schema(description = "数据请求状态")
    private Integer requestStatus;

    @Schema(description = "任务类型")
    private String taskType;

    @Schema(description = "客户端ID")
    private String clientId;

    @Schema(description = "产品名称")
    private String productName;

    @Schema(description = "产品报价数量")
    private Integer productOffersCount;

    @Schema(description = "产品ID")
    private String productId;

    @Schema(description = "新产品ID")
    private String newProductId;

    @Schema(description = "发货国家")
    private String country;

    @Schema(description = "发货国家(新)")
    private String dispatchCountry;

    @Schema(description = "预计送达时间描述")
    private String deliveryTime;

    @Schema(description = "送达时间相关的数量")
    private Short deliveryCount;
}
