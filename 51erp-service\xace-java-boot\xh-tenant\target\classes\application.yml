# 应用服务器
server:
  tomcat:
    uri-encoding: UTF-8
  port: 30006

# Spring
spring:
  profiles:
    # 环境配置
    active: dev
management:
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: always
    # 开启在线日志查看功能
    logfile:
      enabled: true
config:
  ApiDomain: http://127.0.0.1:32000 #后端域名(文档预览中使用)
  FrontDomain: http://127.0.0.1:3000 #前端域名(文档预览中使用)
  AppDomain: http://127.0.0.1:8080 #app/h5端域名配置(文档预览中使用)
  #===================== 多租户 =====================
  MultiTenancy: true #是否开启
  MultiTenancyUrl: http://127.0.0.1:30006/api/tenant/DbName/ #多租户项目地址
  #COLUMN、SCHEMA模式
  MultiTenantType: COLUMN
# 日志配置
logging:
  config: classpath:logback-spring.xml
  level:
    #自定义第三方包名日志等级
    # 解除注释后Druid连接池打印SQL语句
    druid.sql.Statement: debug
    #    druid.sql.DataSource: debug
    #    druid.sql.Connection: debug
    druid.sql.ResultSet: info
#    druid.sql.DataSource: debug
#    druid.sql.Connection: debug
#    druid.sql.ResultSet: debug
log:
  level:
    # 等级 TRACE,DEBUG,INFO,WARN,ERROR(不区分大小写)
    root: DEBUG
  path: log/${spring.application.name}