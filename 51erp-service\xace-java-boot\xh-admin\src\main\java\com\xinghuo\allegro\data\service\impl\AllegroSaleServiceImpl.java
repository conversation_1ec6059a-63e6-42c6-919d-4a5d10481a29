package com.xinghuo.allegro.data.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xinghuo.allegro.collect.entity.CollectOfferEntity;
import com.xinghuo.allegro.data.dao.AllegroSaleMapper;
import com.xinghuo.allegro.data.entity.AllegroSaleEntity;
import com.xinghuo.allegro.data.model.sale.AllegroSalePagination;
import com.xinghuo.allegro.data.model.sale.SaleSkuModel;
import com.xinghuo.allegro.data.service.AllegroSaleService;
import com.xinghuo.common.base.service.BaseServiceImpl;
import com.xinghuo.common.util.core.RandomUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 收藏销售服务的实现类，继承自基本服务实现类，处理收藏销售相关的业务逻辑。
 *
 * TODO 批量更新 历史删除的数据，到新的saleId中。
 * <AUTHOR>
 */

@Slf4j
@Service
public class AllegroSaleServiceImpl extends BaseServiceImpl<AllegroSaleMapper, AllegroSaleEntity> implements AllegroSaleService {


    @Override
    public List<AllegroSaleEntity> getList(AllegroSalePagination pagination) {

        // 构建查询条件
        QueryWrapper<AllegroSaleEntity> queryWrapper = new QueryWrapper<>();

        // 添加卖家ID筛选条件
        if (pagination.getSellerId() != null) {
            queryWrapper.lambda().eq(AllegroSaleEntity::getSellerId, pagination.getSellerId());
        }

        // 添加产品ID筛选条件
        if (pagination.getProductId() != null) {
            queryWrapper.lambda().eq(AllegroSaleEntity::getOfferId, pagination.getProductId().toString());
        }

        // 添加SKU ID筛选条件
        if (pagination.getSkuId() != null) {
            queryWrapper.lambda().eq(AllegroSaleEntity::getSkuId, pagination.getSkuId());
        }

        // 添加销售日期范围筛选条件
        if (pagination.getStartDate() != null) {
            queryWrapper.lambda().ge(AllegroSaleEntity::getSaleDate, pagination.getStartDate());
        }

        if (pagination.getEndDate() != null) {
            queryWrapper.lambda().le(AllegroSaleEntity::getSaleDate, pagination.getEndDate());
        }

        // 添加分类ID筛选条件
        if (pagination.getCategoryId() != null) {
            queryWrapper.lambda().eq(AllegroSaleEntity::getCategoryId, pagination.getCategoryId());
        }

        // 添加搜索关键字筛选条件
        if (StrXhUtil.isNotEmpty(pagination.getSearchKey())) {
            if ("offerId".equals(pagination.getSearchType())) {
                queryWrapper.lambda().like(AllegroSaleEntity::getOfferId, pagination.getSearchKey());
            } else if ("offerLink".equals(pagination.getSearchType())) {
                queryWrapper.lambda().like(AllegroSaleEntity::getOfferLink, pagination.getSearchKey());
            }
        }

        // 添加销售数量范围筛选条件
        if (pagination.getMinSalesNum() != null) {
            queryWrapper.lambda().ge(AllegroSaleEntity::getSalesNum, pagination.getMinSalesNum());
        }

        if (pagination.getMaxSalesNum() != null) {
            queryWrapper.lambda().le(AllegroSaleEntity::getSalesNum, pagination.getMaxSalesNum());
        }

        // 添加排序条件
        if (StrXhUtil.isNotEmpty(pagination.getListOrder())) {
            String[] orderParams = pagination.getListOrder().split("\\|");
            if (orderParams.length == 2) {
                String field = orderParams[0];
                String order = orderParams[1];

                if ("saleDate".equals(field)) {
                    if ("asc".equals(order)) {
                        queryWrapper.lambda().orderByAsc(AllegroSaleEntity::getSaleDate);
                    } else {
                        queryWrapper.lambda().orderByDesc(AllegroSaleEntity::getSaleDate);
                    }
                } else if ("salesNum".equals(field)) {
                    if ("asc".equals(order)) {
                        queryWrapper.lambda().orderByAsc(AllegroSaleEntity::getSalesNum);
                    } else {
                        queryWrapper.lambda().orderByDesc(AllegroSaleEntity::getSalesNum);
                    }
                } else if ("createTime".equals(field)) {
                    if ("asc".equals(order)) {
                        queryWrapper.lambda().orderByAsc(AllegroSaleEntity::getCreateTime);
                    } else {
                        queryWrapper.lambda().orderByDesc(AllegroSaleEntity::getCreateTime);
                    }
                }
            }
        } else {
            // 默认按销售日期降序排序
            queryWrapper.lambda().orderByDesc(AllegroSaleEntity::getSaleDate);
        }

        Page<AllegroSaleEntity> page = new Page<>(pagination.getCurrentPage(), pagination.getPageSize());
        IPage<AllegroSaleEntity> userIpage = this.page(page, queryWrapper);
        return pagination.setDataList(userIpage.getRecords(), userIpage.getTotal());
    }

    /**
     * 根据收藏的商品实体创建收藏销售记录。
     * <p>
     * 该方法用于处理新收藏商品的销售数据记录，通过从收藏商品实体中提取信息，
     * 创建并保存一个收藏销售实体。
     *
     * @param collectOfferEntity 收藏的商品实体，包含商品ID、卖家ID和购买数量等信息。
     */
    @Override
    public void saveFromOffer(CollectOfferEntity collectOfferEntity){
        log.info("保存新销售数据！");
        // 创建收藏销售实体
        AllegroSaleEntity saleEntity = new AllegroSaleEntity();
        // 为收藏销售实体设置唯一ID
        saleEntity.setId(RandomUtil.snowId());
        // 设置收藏销售实体的商品ID、卖家ID等信息
        saleEntity.setOfferId(collectOfferEntity.getOfferId());
        saleEntity.setSellerId(collectOfferEntity.getSellerId());
        saleEntity.setSkuId(collectOfferEntity.getSkuId());

        // 设置新的销售数量和采集时间
        saleEntity.setNewSales(collectOfferEntity.getBuyersQuantity());
        saleEntity.setNewSalesCollectTime(new Date());
        saleEntity.setSalesNum(collectOfferEntity.getBuyersQuantity());
        // 设置销售日期和创建时间
        saleEntity.setSaleDate(new Date());
        saleEntity.setCreateTime(new Date());
        saleEntity.setOfferLink(collectOfferEntity.getOfferLink());
        saleEntity.setImageUrl(collectOfferEntity.getImageUrl());

        // 保存收藏销售实体
        this.save(saleEntity);
    }

    /**
     * 根据新的和已存在的收藏商品实体更新收藏销售记录。
     * <p>
     * 该方法用于处理已收藏商品的销售数据更新，当已收藏商品的购买数量发生变化时，
     * 根据新的和已存在的收藏商品实体信息，创建并保存收藏销售记录来反映销售变化。
     *
     * @param collectOfferEntity 新收藏商品实体，包含最新的商品购买数量等信息。
     * @param existOfferEntity   已存在的收藏商品实体，用于对比和计算销售变化。
     */
    @Override
    public void saveFromExistOffer(CollectOfferEntity collectOfferEntity,CollectOfferEntity existOfferEntity){

        // 判断新购买数量是否大于已存在的购买数量
        if(collectOfferEntity.getBuyersQuantity()>existOfferEntity.getBuyersQuantity()){
            log.info("购买人数发生变动，主动新增销售数据！");
            // 创建收藏销售实体
            AllegroSaleEntity saleEntity = new AllegroSaleEntity();
            // 为收藏销售实体设置唯一ID
            saleEntity.setId(RandomUtil.snowId());
            // 设置收藏销售实体的商品ID、卖家ID等信息
            saleEntity.setOfferId(collectOfferEntity.getOfferId());
            saleEntity.setSellerId(collectOfferEntity.getSellerId());
            saleEntity.setSkuId(collectOfferEntity.getSkuId());
            // 记录旧的销售数量和采集时间
            saleEntity.setOldSales(existOfferEntity.getBuyersQuantity());
            saleEntity.setOldSalesCollectTime(existOfferEntity.getLastModifyTime());
            // 设置新的销售数量和采集时间
            saleEntity.setNewSales(collectOfferEntity.getBuyersQuantity());
            saleEntity.setNewSalesCollectTime(new Date());
            // 计算销售数量的变化
            saleEntity.setSalesNum(collectOfferEntity.getBuyersQuantity()-existOfferEntity.getBuyersQuantity());
            // 设置销售日期和创建时间
            saleEntity.setSaleDate(new Date());
            saleEntity.setCreateTime(new Date());
            saleEntity.setOfferLink(existOfferEntity.getOfferLink());
            saleEntity.setImageUrl(existOfferEntity.getImageUrl());

            // 保存收藏销售实体
            this.save(saleEntity);
        }


    }

    @Override
    public void updateSkuId(String  offerId, Integer skuId){
        UpdateWrapper<AllegroSaleEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(AllegroSaleEntity::getOfferId,offerId).isNull(AllegroSaleEntity::getSkuId);
        updateWrapper.lambda().set(AllegroSaleEntity::getSkuId,skuId);
        this.update(updateWrapper);
    }

    public void updateDeleteReasonType(String  offerId, String deleteReasonType){
        UpdateWrapper<AllegroSaleEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().eq(AllegroSaleEntity::getOfferId,offerId);
        updateWrapper.lambda().set(AllegroSaleEntity::getDeleteReasonType,deleteReasonType);
        this.update(updateWrapper);
    }

    @Override
   public List<SaleSkuModel> getSkuList(){
        return this.baseMapper.getSkuList();
    }
}