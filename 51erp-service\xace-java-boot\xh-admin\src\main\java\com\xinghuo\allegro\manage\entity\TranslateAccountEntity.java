package com.xinghuo.allegro.manage.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.BaseEntityV2;
import lombok.Data;

import java.util.Date;

@Data
@TableName("zz_translate_account")
public class TranslateAccountEntity  extends BaseEntityV2.CUBaseEntityV2 {

    @TableField("name")
    private String name;

    @TableField("provider")
    private String provider;

    @TableField("api_url")
    private String apiUrl;

    @TableField("api_key")
    private String apiKey;

    @TableField("api_secret")
    private String apiSecret;

    @TableField("daily_limit")
    private Long dailyLimit;

    @TableField("monthly_limit")
    private Long monthlyLimit;

    @TableField("daily_used")
    private Long dailyUsed;

    @TableField("monthly_used")
    private Long monthlyUsed;

    @TableField("need_split_sentence")
    private Boolean needSplitSentence;

    @TableField("max_text_length")
    private Integer maxTextLength;

    @TableField("status")
    private String status;

    @TableField("last_used_at")
    private Date lastUsedAt;

    @TableField("error_count")
    private Integer errorCount;

    @TableField("last_error_msg")
    private String lastErrorMsg;

    @TableField("last_error_time")
    private Date lastErrorTime;

    @TableField("priority")
    private Integer priority;
}
