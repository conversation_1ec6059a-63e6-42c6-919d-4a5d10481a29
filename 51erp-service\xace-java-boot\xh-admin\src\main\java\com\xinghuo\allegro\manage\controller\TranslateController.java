package com.xinghuo.allegro.manage.controller;

import com.xinghuo.allegro.manage.model.TranslateForm;
import com.xinghuo.allegro.manage.model.TranslateModel;
import com.xinghuo.allegro.manage.service.TranslateService;
import com.xinghuo.common.annotation.NoDataSourceBind;
import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.util.core.StrXhUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * 百度翻译
 */
@Slf4j
@RestController
@Tag(name = "工具类", description = "工具类")
@RequestMapping("/api/allegro/tool/translate")
public class TranslateController {
    @Resource
    private TranslateService translateService;

    @Operation(summary = "翻译文本")
    @PostMapping("/baidu")
    @NoDataSourceBind
    public ActionResult translate(@RequestBody  List<TranslateForm> scrTextList) {
        try {
            if (scrTextList == null || scrTextList.isEmpty()) {
                return ActionResult.fail("翻译内容不能为空");
            }

            List<TranslateModel> resultList = new ArrayList<>();
            for(TranslateForm form : scrTextList){
                String sourceLang = StrXhUtil.blankToDefault(form.getFromLang(), "auto");
                String targetLang = StrXhUtil.blankToDefault(form.getToLang(), "zh");

                String result = translateService.translate(form.getSrc(), sourceLang, targetLang);

                TranslateModel model = new TranslateModel();
                model.setId(form.getId());
                model.setDst(result);
                resultList.add(model);
            }




            return ActionResult.success(resultList);
        } catch (Exception e) {
            log.error("翻译失败: {}", e.getMessage(), e);
            return ActionResult.fail("翻译失败: " + e.getMessage());
        }
    }
}
