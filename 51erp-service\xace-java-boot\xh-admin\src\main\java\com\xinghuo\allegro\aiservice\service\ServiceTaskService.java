package com.xinghuo.allegro.aiservice.service;

import com.xinghuo.allegro.aiservice.entity.ServiceTaskEntity;
import com.xinghuo.allegro.aiservice.model.ServiceVariantModel;
import com.xinghuo.common.base.service.BaseService;

import java.util.List;

/**
 * Ean 服务
 *
 * <AUTHOR>
 */
public interface ServiceTaskService extends BaseService<ServiceTaskEntity> {


    ServiceTaskEntity waitGet();
    boolean isExistDigest(String digest);
    void saveBatch(ServiceVariantModel model);

    List<ServiceTaskEntity> unDigestList();
}
