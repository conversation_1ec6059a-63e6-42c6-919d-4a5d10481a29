package com.xinghuo.allegro.data.model.sale;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * Allegro销售数据表单模型
 *
 * <AUTHOR>
 * @date 2024-08-20
 */
@Data
public class AllegroSaleForm {

    @Schema(description = "主键ID")
    private String id;

    @Schema(description = "卖家ID")
    private String sellerId;

    @Schema(description = "产品ID")
    private Long productId;

    @Schema(description = "SKU ID")
    private Integer skuId;

    @Schema(description = "报价链接")
    private String offerLink;

    @Schema(description = "图片URL")
    private String imageUrl;

    @Schema(description = "销售日期")
    private Date saleDate;

    @Schema(description = "旧库存（当天开始时的库存）")
    private Integer oldStock;

    @Schema(description = "新库存（当天结束时的库存）")
    private Integer newStock;

    @Schema(description = "销售数量")
    private Integer salesNum;

    @Schema(description = "分类ID")
    private String categoryId;

    @Schema(description = "分类路径")
    private String categoryPath;
}
