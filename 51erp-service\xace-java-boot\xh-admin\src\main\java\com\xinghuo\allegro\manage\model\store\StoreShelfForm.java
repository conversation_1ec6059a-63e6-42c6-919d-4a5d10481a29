package com.xinghuo.allegro.manage.model.store;

import lombok.Data;

import java.util.Date;

@Data
public class StoreShelfForm {

    private String id;

    private Boolean isAutoShelf;

    private Date nextShelfTime;

    private Integer periodDays;


    public void validate() {
        if (Boolean.TRUE.equals(isAutoShelf)) {
            if (nextShelfTime == null) {
                throw new IllegalArgumentException("自动上架时，下一次上架时间必填。");
            }
            if (periodDays == null) {
                throw new IllegalArgumentException("自动上架时，上架周期必填。");
            }
        }
    }
}
