<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile default="true" name="Default" enabled="true" />
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="xh-oauth-controller" />
        <module name="xh-admin" />
        <module name="xh-oauth-api" />
        <module name="xh-oauth-entity" />
        <module name="xh-tenant" />
        <module name="xh-system" />
        <module name="xh-oauth-biz" />
      </profile>
    </annotationProcessing>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="51erp-boot" options="-parameters" />
      <module name="xh-admin" options="-parameters" />
      <module name="xh-oauth" options="-parameters" />
      <module name="xh-oauth-api" options="-parameters" />
      <module name="xh-oauth-biz" options="-parameters" />
      <module name="xh-oauth-controller" options="-parameters" />
      <module name="xh-oauth-entity" options="-parameters" />
      <module name="xh-system" options="-parameters" />
      <module name="xh-tenant" options="-parameters" />
    </option>
  </component>
</project>