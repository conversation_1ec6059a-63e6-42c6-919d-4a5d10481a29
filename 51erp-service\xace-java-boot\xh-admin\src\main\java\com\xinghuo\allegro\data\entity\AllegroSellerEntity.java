package com.xinghuo.allegro.data.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.AbstractBaseEntity;
import lombok.Data;

import java.util.Date;

/**
 * 采集卖家实体
 * <AUTHOR>
 */
@TableName("zz_allegro_seller")
@Data
public class AllegroSellerEntity extends AbstractBaseEntity.AbstractCUBaseEntity<String> {

    // 卖家ID
    @TableField("seller_id")
    private String sellerId;
    // 登录名
    @TableField("login")
    private String login;
    // 卖家姓名
    @TableField("seller_name")
    private String sellerName;
    // 公司名称
    @TableField("company_name")
    private String companyName;
    // 公司地址城市
    @TableField("company_address_city")
    private String companyAddressCity;
    // 公司地址国家
    @TableField("company_address_country")
    private String companyAddressCountry;
    // 公司地址国家代码
    @TableField("company_address_country_code")
    private String companyAddressCountryCode;
    // 公司地址街道
    @TableField("company_address_street")
    private String companyAddressStreet;
    // 公司地址邮政编码
    @TableField("company_address_zip_code")
    private String companyAddressZipCode;
    // 公司税务识别号
    @TableField("company_tax_id")
    private String companyTaxId;
    // 公司是否已验证
    @TableField("company_verified")
    private boolean companyVerified;
    // 联系邮箱列表
    @TableField("contact_emails")
    private String contactEmails;
    // 联系电话列表
    @TableField("contact_phones")
    private String contactPhones;
    // 品牌描述
//    private String brandDescription;
    // 主要描述
//    private String descriptionMain;
    // 评分
    @TableField("ratings")
    private String ratings;
    // 列表URL
    @TableField("listing_url")
    private String listingUrl;
    // 分组
    @TableField("group_name")
    private String groupName;

    @TableField("sell_type")
    private String sellType;

    @TableField("note")
    private String note;

    //挂店日期
    @TableField("end_date")
    private Date endDate;

    //状态
    @TableField("status")
    private Integer status;

    //店铺在线数
    @TableField("total_num")
    private Integer totalNum;

    // 有销售额的数量
    @TableField("total_sales_num")
    private Integer totalSalesNum;

    // 后台采集条目
    @TableField("offer_all_num")
    private Integer offerAllNum;

    // 历史总销量
    @TableField("total_sales_sum")
    private Integer totalSalesSum;

    // 中国发货条目
    @TableField("china_num")
    private Integer chinaNum;

}
