package com.xinghuo.allegro.manage.controller;


import com.xinghuo.allegro.manage.entity.AllegroStoreConfigEntity;
import com.xinghuo.allegro.manage.entity.AllegroStoreEntity;
import com.xinghuo.allegro.manage.model.StoreConfigModel;
import com.xinghuo.allegro.manage.model.storeConfig.AllegroStoreConfigModel;
import com.xinghuo.allegro.manage.model.storeConfig.AllegroStoreConfigPagination;
import com.xinghuo.allegro.manage.service.AllegroStoreConfigService;
import com.xinghuo.allegro.manage.service.AllegroStoreService;
import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.util.core.BeanCopierUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 配置信息
 *
 * <AUTHOR>
 *  2023-12-13
 */
@Slf4j
@RestController
@Tag(name = "Allegro店铺配置管理", description = "Allegro店铺配置管理，包含运费，售后政策等")
@RequestMapping("/api/allegro/store/config")
public class AllegroStoreConfigController {

    @Resource
    private AllegroStoreConfigService allegroStoreConfigService;
   @Resource
    private AllegroStoreService allegroStoreService;

    /**
     * 列表
     */
//    @Operation(summary = "店铺同步设置列表")
    @PostMapping("/getList")
    public ActionResult list(@RequestBody AllegroStoreConfigPagination allegroStoreConfigPagination) {
        List<AllegroStoreConfigEntity> list = allegroStoreConfigService.getList(allegroStoreConfigPagination);
        List<AllegroStoreConfigModel> listVO = BeanCopierUtils.copyList(list, AllegroStoreConfigModel.class);
        PaginationVO page = BeanCopierUtils.copy(allegroStoreConfigPagination, PaginationVO.class);
        return ActionResult.page(listVO, page);
    }

    @Operation(summary = "店铺售后政策列表")
    @GetMapping("/configList")
    @Parameters({
            @Parameter(name = "sellerId", description = "allegro的卖家ID", required = true),
            @Parameter(name = "afterSalesType", description = "售后类型： warranties-保修政策，returnPolicies-退货条件，shippingRates--运费，impliedWarranties-投诉政策", required = true)
    })
    public ActionResult configList(@RequestParam String sellerId, @RequestParam String afterSalesType) {
        List<AllegroStoreConfigEntity> list = allegroStoreConfigService.configList(sellerId, afterSalesType);
        List<StoreConfigModel> modelList = BeanCopierUtils.copyList(list, StoreConfigModel.class);
        return ActionResult.success(modelList);
    }

    @PutMapping("/syncConfig/{storeId}")
    @Operation(summary = "同步店铺售后政策列表")
    @Parameters({
            @Parameter(name = "storeId", description = "店铺ID", required = true)
    })
    public ActionResult syncConfig(@PathVariable("storeId") String storeId) {
        AllegroStoreEntity store = allegroStoreService.getById(storeId);
        if(store == null)
        {
            store = allegroStoreService.getInfoBySellerId(storeId);
        }
        allegroStoreConfigService.syncConfigFromAllegro(store);
        return ActionResult.success("已成功同步配置！");
    }
}
