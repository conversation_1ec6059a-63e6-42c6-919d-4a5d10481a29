package com.xinghuo.allegro.data.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * Allegro 英文采集链接数据传输对象
 * 
 * <AUTHOR>
 * @date 2024-11-05
 */
@Data
@Schema(description = "Allegro 英文采集链接数据")
public class AllegroDataLinkModel {

    @Schema(description = "报价ID")
    private String offerId;

    @Schema(description = "商品链接")
    private String offerLink;

    @Schema(description = "商品名称")
    private String offerName;

    @Schema(description = "商品首图URL")
    private String imageUrl;

    @Schema(description = "商品价格")
    private BigDecimal price;

    @Schema(description = "运费价格")
    private BigDecimal shipFee;

    @Schema(description = "商品总价")
    private BigDecimal totalPrice;

    @Schema(description = "类目ID")
    private String categoryId;

    @Schema(description = "卖家ID")
    private String sellerId;

    @Schema(description = "商品已售数量")
    private Integer buyersQuantity;

    @Schema(description = "采集优先级")
    private Integer priority;

    @Schema(description = "数据请求状态")
    private Integer requestStatus;
}
