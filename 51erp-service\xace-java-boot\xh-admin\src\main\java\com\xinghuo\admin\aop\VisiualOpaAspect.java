package com.xinghuo.admin.aop;

import com.xinghuo.common.redis.util.RedisUtil;
import com.xinghuo.common.util.UserProvider;
import com.xinghuo.common.util.extra.ServletUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Set;

/**
 * 可视化开发缓存数据处理切面
 * 用于处理可视化开发相关的缓存数据，清除缓存操作在对应的增、删、改方法执行后触发
 *
 * <AUTHOR>
 * @date 2023-10-05
 */
@Slf4j
@Aspect
@Component
public class VisiualOpaAspect {

    private static final String HTTP_METHOD_PUT = "put";
    private static final String HTTP_METHOD_DELETE = "delete";
    private static final String HTTP_METHOD_POST = "post";
    @Autowired
    UserProvider userProvider;
    @Autowired
    private RedisUtil redisUtil;

    /**
     * 定义切入点，匹配可视化开发模块的Controller中的所有方法
     */
    @Pointcut("(execution(* com.xinghuo.visualdev.onlinedev.controller.VisualdevModelDataController.*(..))) || execution(* com.xinghuo.visualdev.onlinedev.controller.VisualdevModelAppController.*(..)))" +
            "|| execution(* com.xinghuo.generater.controller.VisualdevGenController.*(..)))")
    public void visiualOpa() {

    }

    /**
     * 后置通知，方法执行后触发，用于清除缓存
     */
    @After("visiualOpa()")
    public void doAroundService() {
        String method = ServletUtil.getRequest().getMethod().toLowerCase();
        // 如果是增、删、改操作，则清除所有相关缓存
        if (HTTP_METHOD_PUT.equals(method) || HTTP_METHOD_DELETE.equals(method) || HTTP_METHOD_POST.equals(method)) {
            Set<String> allKey = new HashSet<>(16);
            allKey.addAll(redisUtil.getAllVisiualKeys());
            for (String key : allKey) {
                redisUtil.remove(key);
            }
        }
    }
}
