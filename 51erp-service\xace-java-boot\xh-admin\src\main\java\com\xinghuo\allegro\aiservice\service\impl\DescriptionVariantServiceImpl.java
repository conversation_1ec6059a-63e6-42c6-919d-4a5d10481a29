package com.xinghuo.allegro.aiservice.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xinghuo.allegro.aiservice.dao.DescriptionVariantMapper;
import com.xinghuo.allegro.aiservice.entity.DescriptionVariantEntity;
import com.xinghuo.allegro.aiservice.service.DescriptionVariantService;
import com.xinghuo.common.base.service.BaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 *
 */
@Slf4j
@Service
public class DescriptionVariantServiceImpl extends BaseServiceImpl<DescriptionVariantMapper, DescriptionVariantEntity> implements DescriptionVariantService {

    @Override
    //判断唯一哈希值是否存在
    public boolean isExistDigest(String digest) {
        QueryWrapper<DescriptionVariantEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DescriptionVariantEntity::getDigest, digest);
        return this.count(queryWrapper) > 0;
    }
}
