package com.xinghuo.allegro.collect.controller;


import com.xinghuo.allegro.collect.service.CollectEventService;
import com.xinghuo.allegro.manage.service.AllegroStoreService;
import com.xinghuo.allegro.order.service.ErpOrderService;
import com.xinghuo.allegro.push.service.PushBatchService;
import com.xinghuo.allegro.sale.service.AllegroOfferService;
import com.xinghuo.common.annotation.NoDataSourceBind;
import com.xinghuo.common.base.ActionResult;
import com.xinghuo.tenant.entity.TenantEntity;
import com.xinghuo.tenant.service.TenantService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

/**
 * 检查事件处理
 *
 * <AUTHOR>
 * @date 2023-12-13
 */
@Slf4j
@RestController
@Tag(name = "Allegro店铺推送", description = "Allegro店铺推送")
@RequestMapping("/api/allegro/collect/event")
public class CollectEventController {

    @Autowired
    private CollectEventService collectEventService;

    @Autowired
    private AllegroStoreService allegroStoreService;

    @Autowired
    private ErpOrderService erpOrderService;

    @Autowired
    private AllegroOfferService allegroOfferService;

    @Autowired
    private PushBatchService pushBatchService;

    @Autowired
    private TenantService tenantService;

    /**
     * 全量检查产品是否包含侵权词
     * 如果发现侵权词，则把request_status 设置未 -1
     * 如果vio_status ,则不进行后台数据的采集操作
     */
    @Operation(summary = "全量检查未后台采集的链接，是否包含侵权词")
    @GetMapping("/checkCollectOfferVio")
    public ActionResult checkCollectOfferVio() {
        int vioSize = collectEventService.checkCollectOfferVio();
        return ActionResult.success("成功更新条目：" + vioSize);
    }

    @Operation(summary = "全量检查产品的关键词")
    @GetMapping("/checkProductVio")
    public ActionResult checkProductVio() {
        int vioSize = collectEventService.checkNewProductVio(null);
        return ActionResult.success("成功更新条目：" + vioSize);
    }

    @Operation(summary = "全量检查ERP产品的关键词")
    @GetMapping("/checkErpProductVio")
    @NoDataSourceBind
    public ActionResult checkErpProductVio() {
        List<TenantEntity> list = tenantService.list();
        for(TenantEntity tenantEntity:list){
            if(tenantEntity.getExpiresTime().compareTo(new Date())<0){
                log.info("租户：{}，已经过期，不进行检查。",tenantEntity.getEnCode());
                continue;
            }
            int vioSize = collectEventService.checkErpProductVio(tenantEntity.getEnCode());
            log.info("租户：{}，成功更新条目：{}",tenantEntity.getEnCode(),vioSize);
        }
        return ActionResult.success("成功更新条目：" );
    }




    @Operation(summary = "全量检查报价的侵权词")
    @GetMapping("/checkOfferVio")
    public ActionResult checkOfferVio() {
        int vioSize = collectEventService.checkOfferVio();
        return ActionResult.success("成功更新条目：" + vioSize);
    }


    /**
     * 每日总结
     * 获取店铺的基本信息 ，按照店铺来获取。
     */
    @Operation(summary = "每日总结")
    @GetMapping("/todaySummary")
    public ActionResult todaySummary() {
        collectEventService.todaySummary(true);
        return ActionResult.success("等待后台执行完成。");
    }


    @Operation(summary = "全量同步")
    @GetMapping("/syncCheck")
    public ActionResult syncCheck() {
        log.info("正在执行每日全量同步，不会生成summary。");
        collectEventService.todaySummary(false);
        return ActionResult.success("等待后台执行完成。");
    }

}
