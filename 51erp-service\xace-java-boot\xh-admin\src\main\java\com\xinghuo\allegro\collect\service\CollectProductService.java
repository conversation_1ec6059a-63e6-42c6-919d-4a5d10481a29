package com.xinghuo.allegro.collect.service;

import com.xinghuo.allegro.collect.entity.CollectProductEntity;
import com.xinghuo.allegro.collect.model.product.CollectProductPagination;
import com.xinghuo.common.base.service.BaseService;

import java.util.List;

public interface CollectProductService extends BaseService<CollectProductEntity> {

    /**
     * 根据产品ID获取采集产品信息
     * @param productId 产品ID
     * @return 采集产品实体
     */
    CollectProductEntity getByProductId(String productId);

    /**
     * 分页查询采集产品列表
     * @param pagination 分页查询参数
     * @return 采集产品列表
     */
    List<CollectProductEntity> getList(CollectProductPagination pagination);
}
