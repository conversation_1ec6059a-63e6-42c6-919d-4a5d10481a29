package com.xinghuo.allegro.collect.model.analysis;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 任务状态统计模型
 * <AUTHOR>
 */
@Data
public class TaskStatusSummaryModel {

    @Schema(description = "采集类别")
    private String statusCategory;

    @Schema(description = "状态描述")
    private String statusDesc;

    @Schema(description = "优先级")
    private Integer priority;

    @Schema(description = "任务数")
    private Long taskCount;

    @Schema(description = "累计已采集数量")
    private Long totalCollectedCount;

    @Schema(description = "累计已采集有销售额数量")
    private Long totalSalesCount;

    @Schema(description = "任务类型")
    private String taskType;

    @Schema(description = "任务类型描述")
    private String taskTypeDesc;
}
