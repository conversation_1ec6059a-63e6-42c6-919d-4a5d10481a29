package com.xinghuo.allegro.collect.model.analysis;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 当日完成任务分析模型
 * <AUTHOR>
 */
@Data
public class DailyCompletionModel {

    @Schema(description = "时间区间（小时）")
    private String timeSlot;

    @Schema(description = "小时（0-23）")
    private Integer hour;

    @Schema(description = "任务类型")
    private String taskType;

    @Schema(description = "任务类型描述")
    private String taskTypeDesc;

    @Schema(description = "客户端ID")
    private String clientId;

    @Schema(description = "完成任务数")
    private Long completedTaskCount;

    @Schema(description = "有销量任务数")
    private Long salesTaskCount;

    @Schema(description = "分组维度")
    private String groupBy;
}
