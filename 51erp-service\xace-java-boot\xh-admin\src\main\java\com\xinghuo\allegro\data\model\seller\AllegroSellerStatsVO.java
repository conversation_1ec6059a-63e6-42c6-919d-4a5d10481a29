package com.xinghuo.allegro.data.model.seller;

import com.xinghuo.allegro.collect.entity.CollectOfferEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * Allegro卖家统计信息VO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "Allegro卖家统计信息VO")
public class AllegroSellerStatsVO {

    @Schema(description = "卖家信息")
    private AllegroSellerModel seller;

    @Schema(description = "产品总数")
    private Long productCount;

    @Schema(description = "SKU总数")
    private Long skuCount;

    @Schema(description = "产品列表")
    private List<CollectOfferEntity> products;

    @Schema(description = "当前页码")
    private Integer page;

    @Schema(description = "每页条数")
    private Integer limit;

    @Schema(description = "总记录数")
    private Long total;

    @Schema(description = "同地址卖家数量")
    private Integer sameAddressCount;

    @Schema(description = "同地址卖家列表")
    private List<AllegroSellerModel> sameAddressSellers;
}
