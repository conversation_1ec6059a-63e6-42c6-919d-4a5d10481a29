package com.xinghuo.allegro.data.dao;

import com.xinghuo.allegro.data.entity.AllegroSellerEntity;
import com.xinghuo.common.base.dao.XHBaseMapper;

/**
 * 店铺token mapper
 *
 * <AUTHOR>
 * @date 2024-06-02
 */
public interface AllegroSellerMapper extends XHBaseMapper<AllegroSellerEntity> {

    int  updateSellerOfferCount(String sellerId,int totalNum);

    void checkBlockSeller(String tenantId);

}

