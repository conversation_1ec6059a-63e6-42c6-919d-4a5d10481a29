package com.xinghuo.allegro.aiservice.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

@Data
@TableName("zz_service_title_variant")
public class TitleVariantEntity {
    /**
     * 主键，自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 标题ID，关联到 zz_service_title 表
     */
    @TableField("title_id")
    private Long titleId;

    /**
     * 变体标题
     */
    @TableField("variant_title")
    private String variantTitle;

    @TableField("digest")
    private String digest;

    /**
     * 标题长度
     */
    @TableField("length")
    private Integer length;

    /**
     * 所属平台（如 QWEN, XUNFEI）
     */
    @TableField("platform")
    private String platform;

    /**
     * 质量标记，0表示不合格，1表示合格
     */
    @TableField("quality_flag")
    private Boolean qualityFlag;

    /**
     * 创建时间，默认当前时间
     */
    @TableField(value = "created_at")
    private Date createdAt;
}
