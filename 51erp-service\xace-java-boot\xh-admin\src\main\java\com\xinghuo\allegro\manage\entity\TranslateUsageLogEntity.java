package com.xinghuo.allegro.manage.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.BaseEntityV2;
import lombok.Data;

@Data
@TableName("zz_translate_usage_log")
public class TranslateUsageLogEntity extends BaseEntityV2.CBaseEntityV2 {

    @TableField("account_id")
    private String accountId;

    @TableField("provider")
    private String provider;

    @TableField("char_count")
    private Integer charCount;

    @TableField("source_text")
    private String sourceText;

    @TableField("source_lang")
    private String sourceLang;

    @TableField("target_text")
    private String targetText;

    @TableField("target_lang")
    private String targetLang;

    @TableField("success")
    private Integer success;  // 0-失败 1-成功

    @TableField("error_msg")
    private String errorMsg;

    @TableField("request_time")
    private Long requestTime;  // 请求耗时(毫秒)

    @TableField("request_ip")
    private String requestIp;

}
