<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinghuo.allegro.collect.dao.CollectOfferMapper">
    <update id="updateDealStatusClear">
        UPDATE zz_collect_offer
        set deal_status=0
        WHERE request_status in (0, 1)
          and vio_status = 0
    </update>

    <select id="collectListByOfferId" parameterType="String"
            resultType="com.xinghuo.allegro.collect.entity.CollectOfferEntity">
        select offer_name,
               category_id,
               offer_id,
               price,
               ship_fee,
               total_price,
               buyers_quantity,
               create_time,
               status
        from zz_collect_offer
        where new_product_id in (select product_id from zz_allegro_offer where id = #{offerId})
    </select>

    <select id="blockedSellerOfferList" parameterType="String"
            resultType="com.xinghuo.allegro.collect.entity.CollectOfferEntity">
        SELECT distinct sku_id,seller_id FROM zz_collect_offer  WHERE seller_id  IN (select seller_id from zz_allegro_seller where sell_type='BLOCKED')
              and sku_id is not null
    </select>


</mapper>