package com.xinghuo.allegro.collect.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("zz_collect_product")
public class CollectProductEntity {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    @TableField(value = "product_id")
    private String productId;
    @TableField(value = "sku_id")
    private Integer skuId;
    @TableField(value = "category_id")
    private String categoryId;
    @TableField(value = "offer_name")
    private String offerName;
    @TableField(value = "product_name")
    private String productName;
    @TableField(value = "buyer_quantity")
    private Integer buyerQuantity;
    @TableField(value = "sale_date")
    private String saleDate;
    @TableField(value = "product_offers_count")
    private Integer productOffersCount;
    @TableField(value = "status")
    private Integer status;
    @TableField(value = "f_created_at")
    private Date createdAt;
    @TableField(value = "f_last_modified_at")
    private Date lastModifiedAt;


}
