package com.xinghuo.allegro.collect.controller;

import com.xinghuo.allegro.collect.entity.*;
import com.xinghuo.allegro.collect.model.OfferJsonUtil;
import com.xinghuo.allegro.collect.model.collect.CollectLinkModel;
import com.xinghuo.allegro.collect.model.collect.CollectOfferModel;
import com.xinghuo.allegro.collect.model.collect.CollectRemoveForm;
import com.xinghuo.allegro.collect.model.collect.OfferParseForm;
import com.xinghuo.allegro.collect.service.*;
import com.xinghuo.allegro.data.entity.AllegroSellerEntity;
import com.xinghuo.allegro.data.service.AllegroSellerService;
import com.xinghuo.allegro.push.entity.AllegroCategoryEntity;
import com.xinghuo.allegro.push.service.AllegroCategoryService;
import com.xinghuo.allegro.util.AllegroConstant;
import com.xinghuo.common.annotation.NoDataSourceBind;
import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.common.util.core.DateXhUtil;
import com.xinghuo.common.util.core.RandomUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.common.util.json.JsonXhUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import static com.xinghuo.allegro.util.AllegroConstant.SELLER_TYPE_BLOCKED;
import static com.xinghuo.allegro.util.AllegroConstant.SELLER_TYPE_NORMAL;

/**
 * 产品采集Controller，负责处理产品采集相关的请求。
 * 接受客户端采集的数据上传。
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@Tag(name = "产品采集-烽火", description = "产品采集-烽火")
@RequestMapping("/api/allegro/fhcollect")
public class CollectFhOfferController {

    @Resource
    private ThreadPoolTaskExecutor taskExecutor;

    @Resource
    private CollectOfferService collectOfferService;

    @Resource
    private CollectTaskService collectTaskService;

    @Resource
    private CollectOfferJsonService collectOfferParseTmpService;

    @Resource
    private AllegroSellerService allegroSellerService;

    @Resource
    private CollectZeroOfferService collectZeroOfferService;

    @Resource
    private CollectProductService collectProductService;

    @Resource
    private AllegroCategoryService allegroCategoryService;



    private final String  tenantId = "8";
    /**
     * 批量收集产品信息--目录采集
     *
     * @param list 包含待收集的产品信息的列表。
     * @return 表示操作结果的ActionResult对象，包含成功接收的数据条数。
     */
    @Operation(summary = "批量收集数据")
    @PutMapping("/batch")
    @NoDataSourceBind
    public ActionResult<String> batchCollect(@RequestBody List<CollectOfferModel> list) {
        List<CollectOfferEntity> entityList = new ArrayList<>();
        String taskType = "S";
        for (CollectOfferModel model : list) {
            CollectOfferEntity entity = BeanCopierUtils.copy(model, CollectOfferEntity.class);
            entity.setTenantId("8");
            entity.setListClientId(model.getClientId());
            //如果是卖家采集
            if ("S".equalsIgnoreCase(model.getTaskType())) {
                entity.setSellerId(model.getCategoryId());
                entity.setCategoryId(null);
            }
            taskType = model.getTaskType();
            entityList.add(entity);
        }
        collectOfferService.saveBatch(taskType, entityList);
        if (!list.isEmpty()) {
            CollectOfferModel model = list.get(0);
            if (StrXhUtil.isNotBlank(model.getTaskId())) {
                collectTaskService.updateCollectTask(model.getTaskId());
            }
        }
        return ActionResult.success("成功接收数据条数：" + entityList.size());
    }


    @Operation(summary = "新链接数据")
    @PutMapping("/newSellerCollect/{taskType}")
    @NoDataSourceBind
    public ActionResult<String> newSellerCollect(@PathVariable("taskType") String taskType,  @RequestBody List<CollectOfferModel> list) {
        //采集新卖家，马上进行数据采集

         if("C".equals(taskType)){
            //类目数据列表
            // 根据productId的数据，进行判断 ，如果productId 已经存在，则比较跟上次的数量，如果数量有变动，则更新数据，并且准备进入产品列表，准备取获取更新数据，形成销量列表。
            //如果productId 被禁用，则 忽略数据。
            for (CollectOfferModel model : list) {
                boolean needTask = false;
                CollectOfferEntity entity = BeanCopierUtils.copy(model, CollectOfferEntity.class);
                entity.setListClientId(model.getClientId());
                String productId = model.getProductId();
                if (StrXhUtil.isNotBlank(productId)) {
                    CollectProductEntity productEntity = collectProductService.getByProductId(productId);
                    if (productEntity != null) {
                        if (productEntity.getStatus().intValue() == 2) {
                            log.warn("产品已经被禁用，忽略数据。productId:{}", productId);
                            continue;
                        }
                        if (model.getBuyersQuantity().equals(productEntity.getBuyerQuantity())) {
                            log.warn("产品销量没有变动，忽略数据。productId:{}", productId);
                            continue;
                        }
                        AllegroCategoryEntity categoryEntity = allegroCategoryService.getSimpleInfoByCategoryId(model.getCategoryId(),tenantId);
                        if(categoryEntity != null &&categoryEntity.getPushStatus().equals(0)){
                            log.warn( "{} 目录设置不推送，忽略数据。productId:{}", categoryEntity.getPath(), productId);
                            continue;
                        }


                        needTask = true;
                        productEntity.setBuyerQuantity(model.getBuyersQuantity());
                        productEntity.setProductOffersCount(model.getProductOffersCount());
                        productEntity.setSaleDate(DateXhUtil.today());
                        productEntity.setCategoryId(model.getCategoryId());
                        productEntity.setLastModifiedAt(new Date());
                        collectProductService.updateById(productEntity);
                    } else {
                        productEntity = new CollectProductEntity();
                        productEntity.setProductName(model.getProductName());
                        productEntity.setCategoryId(model.getCategoryId());
                        productEntity.setOfferName(model.getOfferName());
                        productEntity.setProductId(model.getProductId());
                        productEntity.setBuyerQuantity(model.getBuyersQuantity());
                        productEntity.setProductOffersCount(model.getProductOffersCount());
                        productEntity.setStatus(0);
                        productEntity.setSaleDate(DateXhUtil.today());
                        productEntity.setCreatedAt(new Date());
                        productEntity.setLastModifiedAt(new Date());
                        needTask = true;
                        collectProductService.save(productEntity);
                    }

                    if (needTask) {
                        //判断是否有未处理的produkt
                        boolean existTask = collectTaskService.existTask(productId);
                        if (existTask) {
                            log.warn("产品已经存在未处理的任务，忽略数据。productId:{}", productId);
                            continue;
                        }

                        CollectTaskEntity taskEntity = new CollectTaskEntity();
                        taskEntity.setId(RandomUtil.snowId());
                        taskEntity.setTaskType("P");
                        taskEntity.setCategoryId(productId);
                        taskEntity.setPath(productId);
//                        taskEntity.setLink("https://allegro.pl/produkt/"+productId+"?miejsce-wysylki=chiny&offerTypeBuyNow=1&price_from=70&price_to=200&stan=nowe&order=qd");
                        taskEntity.setLink("https://allegro.pl/produkt/"+productId+"?price_from=70&price_to=600&stan=nowe&order=qd");
                         taskEntity.setStatus(0);
                        taskEntity.setPriority(3);
                        taskEntity.setMaxPages(5);
                        taskEntity.setCreatedAt(new Date());
                        collectTaskService.save(taskEntity);
                    }
                }
            }
        }else if("P".equals(taskType)){
            // 产品列表数据
            List<CollectOfferEntity> entityList = new ArrayList<>();
            for (CollectOfferModel model : list) {
                CollectOfferEntity entity = BeanCopierUtils.copy(model, CollectOfferEntity.class);
                entity.setListClientId(model.getClientId());
                entity.setTenantId(tenantId);
                entityList.add(entity);
            }
            collectOfferService.saveBatch(taskType, entityList);
        }



        return ActionResult.success("成功处理接收数据条数：");
    }

    /**
     * 获取待处理的产品链接列表。
     *
     * @return 表示操作结果的ActionResult对象，包含待处理的产品链接列表。
     */
    @Operation(summary = "获取后台待处理数据清单")
    @GetMapping("/waitGets")
    @NoDataSourceBind
    public ActionResult<List<CollectLinkModel>> waitGets() {
        List<CollectOfferEntity> entityList = collectOfferService.waitGets();
        List<CollectLinkModel> collectLinkModels = BeanCopierUtils.copyList(entityList, CollectLinkModel.class);
        return ActionResult.success(collectLinkModels);
    }



    /**
     * 数据校验：检查数据库中是否存在该offerId的数据。
     * 状态更新：若存在，则更新其状态为“已采集”，并记录完成时间和客户端ID。
     * 波兰产品处理：检测是否为波兰本地销售产品，若是则删除该条目。
     * 黑名单卖家检查：检查卖家是否在黑名单中，若是则标记为黑名单并删除。
     * 图片URL处理：若首页图片为空，则尝试从JSON数据中提取并更新。
     * 临时表存储：将处理后的数据存入临时表，并同步卖家信息。
     * 销量检查：若销量为0且存在相同数据，则删除重复数据。
     * 异步处理：保存临时数据后，异步处理数据。
     *
     * @param offerParseForm
     */
    @Operation(summary = "前端提交详情页的json数据")
    @PutMapping("/putOfferJson")
    @Transactional
    @NoDataSourceBind
    public ActionResult<String> putOfferJson(@RequestBody OfferParseForm offerParseForm) {
        log.debug("前端提交详情页的json数据 offerId:{}", offerParseForm.getCpId());
        //更新卖家数据数据
        if (offerParseForm.getOfferJson() != null && offerParseForm.getOfferJson().has("offer") ) {
            allegroSellerService.syncSellerData(offerParseForm.getOfferJson().get("offer"));
        }

        //满足条件的数据，设置已采集。
        CollectOfferEntity collectOfferEntity = collectOfferService.getById(offerParseForm.getCpId());
        if (collectOfferEntity != null) {
            collectZeroOfferService.saveOfferId(collectOfferEntity.getOfferId());

            OfferJsonUtil.constructOfferEntity(offerParseForm, collectOfferEntity);
            collectOfferService.updateById(collectOfferEntity);


            String productId = OfferJsonUtil.checkProductIdExists(offerParseForm.getOfferJson());
            if (StrXhUtil.isBlank(productId)) {
                log.warn("详情中productId不存在，删除offer，ID:" + offerParseForm.getCpId());
                collectOfferEntity.setNote("详情中productId不存在，删除offer，ID：" + StrXhUtil.blankToDefault(collectOfferEntity.getNote(), ""));
                collectOfferEntity.setDeleteReasonType("productId不存在");
                collectOfferService.updateById(collectOfferEntity);
                collectOfferService.removeById(offerParseForm.getCpId());
                return ActionResult.success("productId不存在，不处理,ID:" + offerParseForm.getCpId());
            }

            boolean isPL = OfferJsonUtil.isPL(offerParseForm.getOfferJson());
            if (isPL) {
                log.warn("波兰本地售卖产品，删除offer，ID:" + offerParseForm.getCpId()+" offerLink:"+collectOfferEntity.getOfferLink());
                collectOfferEntity.setNote("波兰本地售卖产品，删除offer;" + StrXhUtil.blankToDefault(collectOfferEntity.getNote(), ""));
                collectOfferEntity.setDeleteReasonType("波兰发货");
                collectOfferService.updateById(collectOfferEntity);
                collectOfferService.removeById(offerParseForm.getCpId());
                return ActionResult.success("波兰本地售卖产品，删除offer，不处理,ID:" + offerParseForm.getCpId());
            }
            boolean isFZ = OfferJsonUtil.isFZ(offerParseForm.getOfferJson());
            if (isFZ) {
                log.warn("可能不发货卖家，删除offer，ID:" + offerParseForm.getCpId());
                AllegroSellerEntity sellerEntity = allegroSellerService.getInfoBySellerId(collectOfferEntity.getSellerId());
                if (sellerEntity != null && sellerEntity.getSellType().equals(SELLER_TYPE_NORMAL)) {
                    sellerEntity.setSellType(SELLER_TYPE_BLOCKED);
                    sellerEntity.setStatus(0);
                    sellerEntity.setEndDate(new Date());
                    sellerEntity.setNote("可能不发货卖家");
                    sellerEntity.setLastModifyTime(new Date());
                    allegroSellerService.updateById(sellerEntity);
                }

                collectOfferEntity.setNote("可能不发货卖家，删除offer;" + StrXhUtil.blankToDefault(collectOfferEntity.getNote(), ""));
                collectOfferEntity.setDeleteReasonType("福州不发货");
                collectOfferService.updateById(collectOfferEntity);
                collectOfferService.removeById(offerParseForm.getCpId());
                return ActionResult.success("福州不发货，删除offer，不处理,ID:" + offerParseForm.getCpId());
            }

            boolean isContinue = true;
            //判断是否是黑名单卖家的数据，如果是，则不处理。


            if (StrXhUtil.isNotBlank(collectOfferEntity.getSellerId())) {
                AllegroSellerEntity sellerEntity = allegroSellerService.getInfo(collectOfferEntity.getSellerId());
                if (sellerEntity != null && sellerEntity.getSellType().equals(SELLER_TYPE_BLOCKED)) {
                    log.warn("黑名单卖家数据，删除offer，ID:" + offerParseForm.getCpId());
                    collectOfferEntity.setRequestStatus(AllegroConstant.REQUEST_STATUS_BLOCKED);
                    collectOfferEntity.setDeleteReasonType("卖家黑名单");
                    collectOfferEntity.setNote(collectOfferEntity.getNote() + ",卖家黑名单");
                    collectOfferEntity.setLastModifyTime(new Date());
                    collectOfferService.updateById(collectOfferEntity);
                    isContinue = false;
                }
            }

            if (!isContinue) {
                collectOfferService.removeById(offerParseForm.getCpId());
                return ActionResult.success("黑名单卖家数据，不处理,ID:" + offerParseForm.getCpId());
            }

            //20241028 构造数据


            //20241028 进行非零数据判断
            if(collectOfferEntity.getBuyersQuantity()>0){
                CollectOfferJsonEntity offerParseJsonEntity = collectOfferParseTmpService.getById(offerParseForm.getCpId());
                if (offerParseJsonEntity == null) {
                    offerParseJsonEntity = new CollectOfferJsonEntity();
                    offerParseJsonEntity.setProductId(collectOfferEntity.getProductId());
                    offerParseJsonEntity.setId(offerParseForm.getCpId());
                    offerParseJsonEntity.setOfferJson(OfferJsonUtil.minOfferJson(offerParseForm.getOfferJson().toString()));
                    offerParseJsonEntity.setCreateTime(new Date());
                    collectOfferParseTmpService.save(offerParseJsonEntity);
                }
                //异步处理数据
                collectOfferService.dealDataFh(offerParseJsonEntity, collectOfferEntity);
                log.info("成功保存JSON！生成SKU。CPID:{},价格：{}，销量：{}" , collectOfferEntity.getId(), collectOfferEntity.getTotalPrice(),collectOfferEntity.getBuyersQuantity());
                return ActionResult.success("成功保存！正在生成SKU。CPID:" + collectOfferEntity.getId());
            }
            log.info("成功保存！不生成SKU。CPID:{},价格：{}，销量：{}" , collectOfferEntity.getId(),collectOfferEntity.getTotalPrice(),collectOfferEntity.getBuyersQuantity());
            return ActionResult.success("成功保存！不生成SKU。CPID:{}" + collectOfferEntity.getId());
        } else {
            log.warn("获取报价详情：数据不存在，忽略。采集offerId:" + offerParseForm.getCpId());
            return ActionResult.fail("数据不存在，忽略");

        }
    }

    @Operation(summary = "404或者销售结束删除offer")
    @PutMapping("/remove")
    @NoDataSourceBind
    public ActionResult<String> putOfferRemove(@RequestBody CollectRemoveForm form) {
        log.warn("404或者销售结束直接删除。报404的数据 cpId:{}", form.getCpId());
        CollectOfferEntity collectOfferEntity = collectOfferService.getById(form.getCpId());
        if (collectOfferEntity != null) {
            collectZeroOfferService.saveOfferId(collectOfferEntity.getOfferId());

            if (StrXhUtil.isBlank(form.getReason())) {
                form.setReason("404错误");
            }
            collectOfferEntity.setNote(form.getReason() + ";" + StrXhUtil.blankToDefault(collectOfferEntity.getNote(), ""));
            collectOfferEntity.setDeleteReasonType(form.getReason());
            collectOfferEntity.setDetailClientId(form.getClientId());
            collectOfferEntity.setLastModifyTime(new Date());
            collectOfferService.updateById(collectOfferEntity);
            collectOfferService.removeById(form.getCpId());
            collectOfferParseTmpService.removeById(form.getCpId());
            return ActionResult.success("404错误或者销售结束，成功删除！" + form.getCpId());
        }
        return ActionResult.fail("未找到数据！" + form.getCpId());
    }


    @Operation(summary = "处理")
    @PutMapping("/dealErpData/{id}")
    public ActionResult<String> dealErpData(@PathVariable("id") String id) {
        CollectOfferJsonEntity offerParseTmpEntity = collectOfferParseTmpService.getById(id);
        CollectOfferEntity collectOfferEntity = collectOfferService.getById(id);
        collectOfferService.dealData(offerParseTmpEntity, collectOfferEntity);
        return ActionResult.success("成功处理！" + id);
    }


    /**
     * 批量处理未解析的产品信息。
     *
     * @return 表示操作结果的ActionResult对象，包含处理成功的消息。
     */
    @Operation(summary = "处理未解析的数据")
    @PutMapping("/dealBatch/{batchNum}")
    public ActionResult<String> dealBatch(@PathVariable("batchNum") Integer batchNum) {
        List<CollectOfferEntity> list;

        do {
            int queueSize = taskExecutor.getThreadPoolExecutor().getQueue().size();
            log.info("当前队列中的排队数量 :{}", queueSize);
            while (queueSize > 10) {
                try {
                    Thread.sleep(2000);
                    queueSize = taskExecutor.getThreadPoolExecutor().getQueue().size();
                    log.info("当前队列中的排队数量 :{}", queueSize);
                    log.info("当前活跃数量 :{}", taskExecutor.getThreadPoolExecutor().getActiveCount());
                    log.info("当前队列任务数量 :{}", taskExecutor.getThreadPoolExecutor().getTaskCount());
                } catch (Exception e) {

                }

            }

            list = collectOfferService.noDetailDealList();

            for (CollectOfferEntity entity : list) {
                try {
                    log.info("待处理的详情页ID :{}", entity.getId());
                    CollectOfferJsonEntity tmpEntity = collectOfferParseTmpService.getById(entity.getId());
                    if (tmpEntity == null) {
                        log.warn("处理详情无法获取到详情数据，tmpEntity is null :{}", entity.getId());
                        entity.setRequestStatus(0);
                        entity.setReqFinishTime(null);
                        collectOfferService.updateById(entity);
                    } else {
                        collectOfferService.dealData(tmpEntity, entity);
                    }
                } catch (Exception e) {
                    log.error("Error processing PARSE with id:" + entity.getId(), e);
                }

            }
        } while (!list.isEmpty());


        return ActionResult.success("成功处理！");

    }


    @Operation(summary = "处理未解析的数据")
    @PutMapping("/dealBatch2")
    public ActionResult<String> dealBatch2() {
        List<CollectOfferEntity> list;
        ExecutorService executorService = Executors.newFixedThreadPool(15);
        try {
            do {

                list = collectOfferService.noDetailDeal2List();


                    List<Future<?>> futures = list.stream().map(entity -> executorService.submit(() -> {

                        try {
                            log.info("待处理的详情页ID :{}", entity.getId());
                            CollectOfferJsonEntity tmpEntity = collectOfferParseTmpService.getById(entity.getId());


                            if (tmpEntity != null) {
                                String productId = OfferJsonUtil.checkProductIdExists(JsonXhUtil.parseObject(tmpEntity.getOfferJson()));
                                if (StrXhUtil.isNotBlank(productId)) {
                                    log.info("处理详情  :{}", entity.getId());

                                    collectOfferService.dealDataOld(tmpEntity, entity);
                                } else {
                                    log.warn("处理详情无法获取到详情数据，productid is null :{}", entity.getId());
                                    entity.setRequestStatus(6);
                                    entity.setNote("无法获取到详情数据productId为空");
                                    collectOfferService.updateById(entity);
                                }
                            }
                        } catch (Exception e) {
                            log.error("Error processing PARSE with id:" + entity.getId(), e);
                        }

                    })).collect(Collectors.toList());
                    for (Future<?> future : futures) {
                        try {
                            // 设置每个任务的超时时间为60秒
                            future.get(60, TimeUnit.SECONDS);
                        } catch (TimeoutException e) {
                            log.warn("任务超时：", e);
                            // 取消超时的任务
                            future.cancel(true);
                        } catch (InterruptedException | ExecutionException e) {
                            log.error("执行任务时发生错误: ", e);
                            Thread.currentThread().interrupt();
                        }
                    }

            } while (!list.isEmpty());
        } finally {
            // 关闭线程池
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(3, TimeUnit.MINUTES)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }

        return ActionResult.success("成功处理！");

    }


}
