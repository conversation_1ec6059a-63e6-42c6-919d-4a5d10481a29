package com.xinghuo.allegro.data.model.seller;

import com.xinghuo.common.base.model.Pagination;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Allegro卖家分页查询参数
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "Allegro卖家分页查询参数")
public class AllegroSellerPagination extends Pagination {

    @Schema(description = "卖家ID")
    private String sellerId;

    @Schema(description = "卖家名称")
    private String sellerName;

    @Schema(description = "卖家登录名")
    private String login;

    @Schema(description = "公司名称")
    private String companyName;

    @Schema(description = "国家")
    private String country;

    @Schema(description = "地址")
    private String address;

    @Schema(description = "分组名称")
    private String groupName;

    @Schema(description = "卖家类型")
    private String sellType;

    @Schema(description = "状态")
    private Integer status;

    @Schema(description = "优先级")
    private Integer priority;

    @Schema(description = "是否有相同地址")
    private Boolean hasSameAddress;

    @Schema(description = "排序字段")
    private String sortField;

    @Schema(description = "排序方向")
    private String sortOrder;

    @Schema(description = "关键字搜索")
    private String keyword;
    @Schema(description = "搜索类型  （SKU:externalId|EQ, 分类ID:category_id|EQ,分类名称模糊：categoryName|LIKE，失败信息： error_msg|LIKE, 备注：note|LIKE）,  示例：offer_id|EQ   ,方式：EQ-精确，LIKERIGHT-右模糊 ，LIKE-全模糊")
    private String searchType;

    @Schema(description = "搜索关键字 ")
    private String searchKey;

    @Schema(description = "最小在线数")
    private Integer minOnlineCount;

    @Schema(description = "最大在线数")
    private Integer maxOnlineCount;

    @Schema(description = "最小有销售额数量")
    private Integer minTotalSalesNum;

    @Schema(description = "最大有销售额数量")
    private Integer maxTotalSalesNum;

    @Schema(description = "最小后台采集条目")
    private Integer minOfferAllNum;

    @Schema(description = "最大后台采集条目")
    private Integer maxOfferAllNum;

    @Schema(description = "最小历史总销量")
    private Integer minTotalSalesSum;

    @Schema(description = "最大历史总销量")
    private Integer maxTotalSalesSum;

    @Schema(description = "最小中国发货条目")
    private Integer minChinaNum;

    @Schema(description = "最大中国发货条目")
    private Integer maxChinaNum;

}
