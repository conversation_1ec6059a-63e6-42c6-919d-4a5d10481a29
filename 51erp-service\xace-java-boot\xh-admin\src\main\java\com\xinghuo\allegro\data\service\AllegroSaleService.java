package com.xinghuo.allegro.data.service;

import com.xinghuo.allegro.collect.entity.CollectOfferEntity;
import com.xinghuo.allegro.data.entity.AllegroSaleEntity;
import com.xinghuo.allegro.data.model.sale.AllegroSalePagination;
import com.xinghuo.allegro.data.model.sale.SaleSkuModel;
import com.xinghuo.common.base.service.BaseService;

import java.util.List;

public interface AllegroSaleService extends BaseService<AllegroSaleEntity> {

    List<AllegroSaleEntity> getList(AllegroSalePagination pagination);

    void saveFromOffer(CollectOfferEntity collectOfferEntity);

    void   saveFromExistOffer(CollectOfferEntity collectOfferEntity,CollectOfferEntity existOfferEntity);

    void updateSkuId(String  offerId, Integer skuId);

    List<SaleSkuModel> getSkuList();
}
