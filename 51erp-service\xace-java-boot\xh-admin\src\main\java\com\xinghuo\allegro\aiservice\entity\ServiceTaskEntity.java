package com.xinghuo.allegro.aiservice.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

@Data
@TableName("zz_service_task")
public class ServiceTaskEntity {

    /**
     * 主键，自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 产品ID，关联到产品表
     */
    @TableField("product_id")
    private String productId;

    /**
     * 任务类型，1：标题，2：描述
     */
    private Integer taskType;

    /**
     * 原始标题，最多150个字符
     */
    @TableField("original_text")
    private String originalText;

    /**
     * 标题摘要，用于检索
     */
    @TableField("digest")
    private String digest;

    /**
     * 所属平台（如 QWEN, XUNFEI）
     */
    @TableField("platform")
    private String platform;

    /**
     * 优先级
     */
    @TableField("priority")
    private Integer priority;

    @TableField("request_status")
    private Integer requestStatus;

    @TableField("request_time")
    private Date requestTime;
    /**
     * 完成时间
     */
    @TableField("finish_time")
    private LocalDateTime finishTime;

    /**
     * 变体数量
     */
    @TableField("variant_num")
    private Integer variantNum;

    /**
     * 创建时间，默认当前时间
     */
    @TableField("created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间，自动更新为当前时间
     */
    @TableField("updated_at")
    private LocalDateTime updatedAt;
}
