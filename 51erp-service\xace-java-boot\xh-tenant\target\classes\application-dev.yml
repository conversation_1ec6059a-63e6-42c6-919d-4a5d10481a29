spring:
  application:
    # 应用名称
    name: xh-tenant
  mvc:
    servlet:
      #  启动之后立即加载servlet
      load-on-startup: 0
    async:
      request-timeout: 500000000

  main:
    #解决bean重复定义的。设置为true时，后定义的bean会覆盖之前定义的相同名称的bean
    allow-bean-definition-overriding: true
    allow-circular-references: true
    # 多租户创库脚本目录
  file: E:\JNPF-v5.0.5\jnpf-database\MySQL\
  # 域名 http://或https://后加入%s为占位符
  domain: http://%sfanyi.baidu.com
  # 主项目地址
#  mainDomain: 192.168.50.22/jnpf-test/api/
#  mainDomain: http://**************:30002/api/
  mainDomain: http://127.0.0.1:32000/api/
  # ===================== 数据源配置 =====================
  exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure #排除自动配置，手动配置druid
  datasource:
    #数据库类型(可选值 MySQL、SQLServer、Oracle、DM8、KingbaseES、PostgreSQL，请严格按可选值填写)
    db-type: MySQL
    dbinit: jnpf_db_init
    columnDbName: xace200_test
    host: **************
    port: 22888
    username: xace200test
    password: xace200test123
    db-name: xace200_tenant
    prepare-url: ***********************************************************************************************************************************************************************************************************************************************************************************************************

    # ===================== 动态多数据源 =====================
    dynamic:
      primary: master #设置默认的数据源或者数据源组,默认值即为master
      strict: true #严格匹配数据源,默认false. true未匹配到指定数据源时抛异常,false使用默认数据源
      druid:
        # 空闲时执行连接测试
        test-while-idle: true
        # 连接测试最小间隔
        time-between-eviction-runs-millis: 60000
        # 获取连接等待3秒 根据网络情况设定
        max-wait: 3000
        # 初始化4个连接
        initial-size: 4
        # 最大20个连接
        max-active: 20
        # 最少保持4个空闲连接
        min-idle: 4
        # 空闲连接保活, 超过配置的空闲时间会进行连接检查完成保活操作(数据库自身会断开达到空闲时间的连接， 程序使用断开的连接会报错)
        keep-alive: true
        # 解除注释后Druid连接池打印SQL语句 忽略日志等级配置
        #filters: slf4j
        filter:
          stat: # SQL统计过滤器配置
            enabled: true # 启用SQL统计过滤器
            log-slow-sql: true # 启用慢SQL日志记录
            slow-sql-millis: 10 # 慢SQL的执行时间阈值（单位：毫秒
          wall:
            enabled: true
            config:
              # 打开SQL执行耗时的详细信息
              wall-statistics: true
              # SQL执行超过该时间（毫秒）会被打印出来
              wall-sql-execute-millis: 10
        # 解除注释后Druid连接池打印SQL语句 忽略日志等级配置
        filters: slf4j,wall,stat
        slf4j:
          statementLogEnabled: true  #SQL语句的日志记录
          resultSetLogEnabled: false   #结果集的日志记录
          connectionLogEnabled: false  #数据库连接的日志记录
          dataSourceLogEnabled: false #数据源的日志记录
          statementCreateAfterLogEnabled: false #SQL语句创建后的日志记
          statementCloseAfterLogEnabled: false #SQL语句关闭后的日志记录
          statementExecuteAfterLogEnabled: false #SQL语句执行后的日志记录
          #打印SQL替换参数
          statementExecutableSqlLogEnable: true #可执行SQL语句的日志记录
          statementPrepareAfterLogEnabled: false  #SQL语句准备后的日志记录
          statementPrepareCallAfterLogEnabled: false #SQL语句准备调用后的日志记录
          statementParameterSetLogEnabled: false #SQL语句参数设置的日志记录

  # ===================== Redis配置 =====================
  # redis单机模式
  data:
    redis:
      database: 6 #缓存库编号
      host: 127.0.0.1
      port: 8001
      password: RedisXHGig123@pass    # 密码为空时，请将本行注释
      timeout: 3000 #超时时间(单位：秒)
      lettuce: #Lettuce为Redis的Java驱动包
        pool:
          max-active: 8 # 连接池最大连接数
          max-wait: -1ms  # 连接池最大阻塞等待时间（使用负值表示没有限制）
          min-idle: 0 # 连接池中的最小空闲连接
          max-idle: 8 # 连接池中的最大空闲连接

  #maxkey租户同步api接口相关配置
  sso:
    enabled: false
    url: http://localhost:8526/sso-mgt-api
    appId: 745057899234983936
    secret: r12FMTQwNzIwMjIyMDM1MTEzMTUzoh
jetcache:
  statIntervalMinutes: 15
  areaInCacheName: false
  local:
    default:
      type: linkedhashmap
      keyConvertor: jackson
  remote:
    default:
      type: redis.springdata
      keyConvertor: jackson
      broadcastChannel: projectA
      keyPrefix: projectA
      valueEncoder: jackson
      valueDecoder: jackson
      defaultExpireInMillis: 86400000