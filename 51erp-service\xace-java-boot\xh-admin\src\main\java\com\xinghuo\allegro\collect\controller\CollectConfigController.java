package com.xinghuo.allegro.collect.controller;

import com.xinghuo.allegro.collect.entity.CollectConfigEntity;
import com.xinghuo.allegro.collect.model.config.CollectConfigModel;
import com.xinghuo.allegro.collect.service.CollectConfigService;
import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.util.UserProvider;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.common.util.core.RandomUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

/**
 * 采集设置详情
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@Tag(name = "采集设置", description = "采集设置")
@RequestMapping("/api/allegro/collect/config")
public class CollectConfigController {

    @Resource
    private CollectConfigService collectConfigService;

    @Resource
    private UserProvider userProvider;



    @Operation(summary = "保存采集配置详情")
    @PutMapping("/{id}")
    @Parameters({
            @Parameter(name = "id", description = "主键", required = true)
    })
    @Transactional
    public ActionResult<String> save(@PathVariable("id") String id, @RequestBody @Valid CollectConfigModel model) {
            CollectConfigEntity configEntity = collectConfigService.getById(id);
            if (configEntity == null) {
                configEntity = new CollectConfigEntity();
                configEntity.setId(id);
                configEntity.setConfigType(model.getConfigType());
                configEntity.setConfig(model.getConfig());
                collectConfigService.save(configEntity);
            }
            else{
                configEntity.setConfig(model.getConfig());
                collectConfigService.update(configEntity);
            }

            return ActionResult.success("成功保存！" );
    }

    @Operation(summary = "获取采集设置-详情")
    @GetMapping("/{configType}")
    public ActionResult info(@PathVariable("configType") String configType) {
        CollectConfigEntity entity = collectConfigService.getInfo(configType,userProvider.get().getTenantId());
        if(entity == null){
            entity = new CollectConfigEntity();
            entity.setId(RandomUtil.snowId());
            entity.setConfigType(configType);
            collectConfigService.save(entity);
        }
        CollectConfigModel infoVo = BeanCopierUtils.copy(entity, CollectConfigModel.class);
        return ActionResult.success(infoVo);
    }








}
