package com.xinghuo.allegro.data.dao;

import com.xinghuo.allegro.push.entity.ErpProductEntity;
import com.xinghuo.common.base.dao.XHBaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * Allegro产品Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-18
 */
public interface AllegroProductMapper extends XHBaseMapper<ErpProductEntity> {

    /**
     * 根据产品ID查询产品信息
     *
     * @param productId 产品ID
     * @param tenantId 租户ID
     * @return 产品信息
     */
    @Select("SELECT * FROM zz_erp_product WHERE product_id = #{productId} AND f_tenantId = #{tenantId}")
    ErpProductEntity getByProductId(@Param("productId") String productId, @Param("tenantId") String tenantId);

    /**
     * 根据英文标题模糊查询产品列表
     *
     * @param titleEn 英文标题
     * @param tenantId 租户ID
     * @return 产品列表
     */
    @Select("SELECT * FROM zz_erp_product WHERE title_en LIKE CONCAT('%', #{titleEn}, '%') AND f_tenantId = #{tenantId}")
    List<ErpProductEntity> listByTitleEn(@Param("titleEn") String titleEn, @Param("tenantId") String tenantId);

    /**
     * 根据Fruugo分类ID查询产品列表
     *
     * @param fruugoCategoryId Fruugo分类ID
     * @param tenantId 租户ID
     * @return 产品列表
     */
    @Select("SELECT * FROM zz_erp_product WHERE fruugo_category_id = #{fruugoCategoryId} AND f_tenantId = #{tenantId}")
    List<ErpProductEntity> listByFruugoCategoryId(@Param("fruugoCategoryId") String fruugoCategoryId, @Param("tenantId") String tenantId);

    /**
     * 更新产品的Fruugo相关信息
     *
     * @param skuId 产品SKU ID
     * @param titleEn 英文标题
     * @param parametersEn 英文参数
     * @param descriptionEn 英文描述
     * @param imagesEn 英文图片地址
     * @param fruugoCategoryId Fruugo分类ID
     * @param fruugoCategoryPath Fruugo分类路径
     * @return 更新结果
     */
    @Update("UPDATE zz_erp_product SET " +
            "title_en = #{titleEn}, " +
            "parameters_en = #{parametersEn}, " +
            "description_en = #{descriptionEn}, " +
            "images_en = #{imagesEn}, " +
            "fruugo_category_id = #{fruugoCategoryId}, " +
            "fruugo_category_path = #{fruugoCategoryPath}, " +
            "update_time = NOW() " +
            "WHERE sku_id = #{skuId}")
    int updateFruugoInfo(@Param("skuId") Integer skuId,
                         @Param("titleEn") String titleEn,
                         @Param("parametersEn") String parametersEn,
                         @Param("descriptionEn") String descriptionEn,
                         @Param("imagesEn") String imagesEn,
                         @Param("fruugoCategoryId") String fruugoCategoryId,
                         @Param("fruugoCategoryPath") String fruugoCategoryPath);
}
