package com.xinghuo.allegro.aiservice.controller;

import cn.hutool.http.HttpRequest;
import com.fasterxml.jackson.databind.JsonNode;
import com.xinghuo.allegro.aiservice.model.AiUtil;
import com.xinghuo.allegro.aiservice.model.ServiceTaskModel;
import com.xinghuo.allegro.collect.entity.CollectOfferJsonEntity;
import com.xinghuo.allegro.collect.model.OfferJsonUtil;
import com.xinghuo.allegro.collect.service.CollectOfferJsonService;
import com.xinghuo.allegro.push.entity.ErpProductEntity;
import com.xinghuo.allegro.push.service.ErpProductService;
import com.xinghuo.common.annotation.NoDataSourceBind;
import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.util.json.JsonXhUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * TODO 推送标题和描述到ai服务
 */
@Slf4j
@RestController
@Tag(name = "服务管理-标题", description = "服务管理-标题")
@RequestMapping("/api/allegro/service/aiPush")
public class AiPushController  {


    @Resource
    private ErpProductService erpProductService;

    @Resource
    private CollectOfferJsonService collectOfferJsonService;

    private String TITLE_URL= "http://**********:30004/api/allegro/service/title/accept";
    private String DESCRIPTION_URL= "http://**********:30004/api/allegro/service/desc/accept";
//    private String TITLE_URL= "http://127.0.0.1:32000/api/allegro/service/title/accept";
//    private String DESCRIPTION_URL= "http://127.0.0.1:32000/api/allegro/service/desc/accept";



    @Operation(summary = "获取待处理的数据，每次获取100条，推送到ai服务")
    @GetMapping("")
    @NoDataSourceBind
    public ActionResult<ServiceTaskModel> aipush() {

        List<ErpProductEntity> list = erpProductService.unPushList();
        List<Integer> skuList = new ArrayList<>();
        List<ServiceTaskModel> titleList = new ArrayList<>();
        List<ServiceTaskModel> descList = new ArrayList<>();
        while(list.size() > 0) {
            log.info("执行AI推送： list size:{}",list.size());
            try {
                for (ErpProductEntity entity : list) {

                    skuList.add(entity.getSkuId());
                    ServiceTaskModel serviceTaskModel = new ServiceTaskModel();
                    serviceTaskModel.setTaskType(1);
                    serviceTaskModel.setOriginalText(entity.getOfferName().trim());
                    serviceTaskModel.setProductId(entity.getProductId());
                    titleList.add(serviceTaskModel);

                    CollectOfferJsonEntity collectOfferJsonEntity = collectOfferJsonService.getInfo(entity.getFromCpId());
                    if (collectOfferJsonEntity != null) {
                        JsonNode offerJson = JsonXhUtil.parseObject(collectOfferJsonEntity.getOfferJson());
                        JsonNode ss = JsonXhUtil.parseObject(OfferJsonUtil.refactorDescription(offerJson.get("offer").get("descriptions").get("standardized").toString(),null
                        ));

                        if (ss.get("sections") != null) {
                            JsonNode sections = ss.get("sections");
                            for (JsonNode section : sections) {
                                JsonNode items = section.get("items");
                                for (JsonNode item : items) {
                                    if (item.get("type").asText().equals("TEXT")) {
                                        String content = item.get("content").asText();
                                        if (AiUtil.isNotProcess(content)) {
//                                        log.info("不需要处理content:{}  ",content);
                                        } else {
//                                        log.info("需要处理 content:{}  ",content);



                                            ServiceTaskModel serviceTaskDescModel = new ServiceTaskModel();
                                            serviceTaskDescModel.setTaskType(2);
                                            serviceTaskDescModel.setOriginalText(content);
                                            serviceTaskDescModel.setProductId(entity.getProductId());
                                            descList.add(serviceTaskDescModel);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                //需要http post 数据到指定地址
                HttpRequest.put(TITLE_URL)
                        .header("Content-Type", "application/json")
                        .body(JsonXhUtil.toJSONString(titleList))
                        .execute().body();
                HttpRequest.put(DESCRIPTION_URL)
                        .header("Content-Type", "application/json")
                        .body(JsonXhUtil.toJSONString(descList))
                        .execute().body();
                log.info("执行AI推送：{}， list size:{}", TITLE_URL, titleList.size());
                log.info("执行AI推送：{}， list size:{}", DESCRIPTION_URL, descList.size());

                titleList.clear();
                descList.clear();


                erpProductService.updateDealStatus2(skuList);
            }catch(Exception e){
                log.error("执行AI推送异常：{},等等2分钟",e);
                try {
                    Thread.sleep(120000);
                } catch (InterruptedException ex) {
                    throw new RuntimeException(ex);
                }
            }
            list = erpProductService.unPushList();
        }



        return ActionResult.success("success");
    }
}
