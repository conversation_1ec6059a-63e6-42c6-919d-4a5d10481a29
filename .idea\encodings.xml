<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="Encoding">
    <file url="file://$PROJECT_DIR$/51erp-service/xace-java-boot/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/51erp-service/xace-java-boot/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/51erp-service/xace-java-boot/xh-admin/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/51erp-service/xace-java-boot/xh-admin/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/51erp-service/xace-java-boot/xh-oauth/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/51erp-service/xace-java-boot/xh-oauth/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/51erp-service/xace-java-boot/xh-oauth/xh-oauth-api/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/51erp-service/xace-java-boot/xh-oauth/xh-oauth-api/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/51erp-service/xace-java-boot/xh-oauth/xh-oauth-biz/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/51erp-service/xace-java-boot/xh-oauth/xh-oauth-biz/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/51erp-service/xace-java-boot/xh-oauth/xh-oauth-controller/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/51erp-service/xace-java-boot/xh-oauth/xh-oauth-controller/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/51erp-service/xace-java-boot/xh-oauth/xh-oauth-entity/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/51erp-service/xace-java-boot/xh-oauth/xh-oauth-entity/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/51erp-service/xace-java-boot/xh-system/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/51erp-service/xace-java-boot/xh-system/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/51erp-service/xace-java-boot/xh-tenant/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/51erp-service/xace-java-boot/xh-tenant/src/main/resources" charset="UTF-8" />
  </component>
</project>