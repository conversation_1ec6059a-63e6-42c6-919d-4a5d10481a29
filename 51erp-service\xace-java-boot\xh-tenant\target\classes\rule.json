{"description": "字段替换规则及忽略表集合", "ruleList": [{"tableName": "base_module", "columnName": "f_property_json"}, {"tableName": "base_module_scheme", "columnName": "f_condition_json"}, {"tableName": "base_organize", "columnName": "f_property_json,f_organize_id_tree"}, {"tableName": "base_visual_dev", "columnName": "f_form_data,f_column_data,f_app_column_data"}, {"tableName": "blade_visual_config", "columnName": "component"}, {"tableName": "flow_form", "columnName": "f_property_json,f_draft_json"}, {"tableName": "flow_template_json", "columnName": "f_flow_template_json"}, {"tableName": "base_visual_release", "columnName": "f_form_data,f_column_data,f_app_column_data"}, {"tableName": "base_integrate", "columnName": "f_template_json"}, {"tableName": "base_user", "columnName": "f_portal_id"}], "ignoreList": []}