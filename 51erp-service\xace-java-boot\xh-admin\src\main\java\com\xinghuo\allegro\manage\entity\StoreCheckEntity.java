package com.xinghuo.allegro.manage.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xinghuo.common.base.entity.BaseEntityV2;
import lombok.Data;

import java.util.Date;

@Data
@TableName("zz_store_check")
public class StoreCheckEntity  extends BaseEntityV2.TBaseEntityV2 {

            private String storeName;
            private String sellerId;
            private String note;
            private Date checkTime;


}
