package com.xinghuo.allegro.manage.controller;

import com.xinghuo.allegro.manage.entity.AllegroApiClientEntity;
import com.xinghuo.allegro.manage.entity.AllegroStoreEntity;
import com.xinghuo.allegro.manage.model.ApiClientModel;
import com.xinghuo.allegro.manage.service.AllegroApiClientService;
import com.xinghuo.allegro.manage.service.AllegroAuthService;
import com.xinghuo.allegro.manage.service.AllegroStoreConfigService;
import com.xinghuo.allegro.manage.service.AllegroStoreService;
import com.xinghuo.common.annotation.NoDataSourceBind;
import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.database.util.NotTenantPluginHolder;
import com.xinghuo.common.util.UserProvider;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.common.util.core.DateXhUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.niceapi.allegro.WebHookAPI;
import com.xinghuo.permission.entity.UserEntity;
import com.xinghuo.permission.service.UserService;
import com.xinghuo.tenant.entity.TenantEntity;
import com.xinghuo.tenant.service.TenantService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

/**
 * 处理Allegro授权流程的相关请求。
 *  access_token  有效期：10个小时
 *  refresh_token  有效期：3个月
 *
 * <AUTHOR>
 */
@Slf4j
@Controller
@Tag(name = "Allegro店铺授权", description = "Allegro店铺授权")
@RequestMapping("/api/allegro/auth")
public class AllegroAuthController {

    @Resource
    private AllegroAuthService allegroAuthService;

    @Resource
    private UserProvider userProvider;
    @Autowired
    private UserService userService;

    @Resource
    private AllegroStoreService allegroStoreService;

    @Resource
    private AllegroStoreConfigService allegroStoreConfigService;

    @Resource
    private AllegroApiClientService allegroApiClientService;

    @Resource
    private TenantService tenantService;


    @Operation(summary = "授权KEY列表")
    @GetMapping("/apiClients")
    @ResponseBody
    public ActionResult apiClients( ) {
        String telantId = StrXhUtil.blankToDefault(userProvider.get().getTenantId(),"") ;
        NotTenantPluginHolder.setNotSwitchFlag();
        List<AllegroApiClientEntity> list = allegroApiClientService.list();
        List<ApiClientModel> modelList = new ArrayList<>();
        for(AllegroApiClientEntity apiClientEntity : list){
            if(apiClientEntity.getStatus()==1){
                if(StrXhUtil.isNotBlank(apiClientEntity.getTenantId()) && apiClientEntity.getTenantId().compareTo(telantId)!=0){
                    continue;
                }
                ApiClientModel model = BeanCopierUtils.copy(apiClientEntity, ApiClientModel.class);
                modelList.add(model);
            }
        }
        // Check for models with tenantId equal to null and set default to 0 if there is an apiClientEntity with the same tenantId
        boolean hasTenantSpecificApiClient = modelList.stream()
                .anyMatch(model -> StrXhUtil.isNotBlank(model.getTenantId()) && model.getTenantId().compareTo(telantId) == 0);

        if (hasTenantSpecificApiClient) {
            modelList.stream()
                    .filter(model -> StrXhUtil.isBlank(model.getTenantId()))
                    .forEach(model -> model.setIsDefault(false));
        }
        return ActionResult.success(modelList);
    }

    /**
     * 处理授权请求，重定向到Allegro授权页面。
     *
     * @param storeName 商店名称，用于生成授权页面的state参数，以保持请求的一致性。
     * @return 重定向URL，指向Allegro的授权页面。
     */
//    @Operation(summary = "店铺授权")
//    @GetMapping("/authorize")
//    @Parameters({
//            @Parameter(name = "storeName", description = "店铺名称", required = true),
//            @Parameter(name = "apiClientId", description = "API编号", required = true),
//            @Parameter(name = "type", description = "授权类型，0-第一次授权，1-重新授权", required = true)
//    })
//    @NoDataSourceBind
//    public String authorize(@RequestParam String storeName, @RequestParam String apiClientId,@RequestParam int type,@RequestParam String userId) {
//        if (storeName == null || storeName.trim().isEmpty()) {
//            throw new IllegalArgumentException("StoreName 不能是空的。");
//        }
//
//        UserEntity userEntity = userService.getInfo(userId);
//
//        //根据userId 获取到用户的租户信息
//        if(userEntity==null){
//            throw new IllegalArgumentException("用户不存在");
//        }
//        if(StrXhUtil.isNotBlank(userEntity.getTenantId())){
//            TenantEntity tenantEntity = tenantService.getInfoByEnCode(userEntity.getTenantId());
//            if(tenantEntity==null){
//                throw new IllegalArgumentException("租户不存在");
//            }
//            long storeCount = allegroStoreService.sumStoreNum(userEntity.getTenantId());
//            if(storeCount>=tenantEntity.getLimitStoreNum().longValue()){
////                throw new IllegalArgumentException("店铺数量已达上限。限制绑定数："+tenantEntity.getLimitStoreNum());
//                return "店铺数量已达上限。限制绑定数："+tenantEntity.getLimitStoreNum();
//            }
//        }
//
//
//
//        // 生成state参数，用于防止CSRF攻击和确认回调时的请求来源
//        String state = "state_for_" + apiClientId + "_" + URLEncoder.encode(storeName, StandardCharsets.UTF_8)+ "_" + type+"_"+userId;
//        if (state.length() > 512) {
//            return state.substring(0, 512);
//        }
//        // 构建并返回重定向URL，引导用户前往Allegro授权页面
//        return "redirect:" + allegroAuthService.buildAuthorizationUrl(state, apiClientId);
//    }


    @Operation(summary = "店铺授权")
    @GetMapping("/authorize")
    @Parameters({
            @Parameter(name = "storeName", description = "店铺名称", required = true),
            @Parameter(name = "apiClientId", description = "API编号", required = true),
            @Parameter(name = "type", description = "授权类型，0-第一次授权，1-重新授权", required = true)
    })
    @NoDataSourceBind
    public ResponseEntity<String> authorize(@RequestParam String storeName, @RequestParam String apiClientId, @RequestParam int type, @RequestParam String userId) {
        try {
            if (StrXhUtil.isBlank(storeName)) {
                throw new IllegalArgumentException("StoreName 不能是空的。");
            }

            UserEntity userEntity = userService.getInfo(userId);

            //根据userId 获取到用户的租户信息
            if (userEntity == null) {
                throw new IllegalArgumentException("用户不存在");
            }
            if (StrXhUtil.isNotBlank(userEntity.getTenantId())) {
                TenantEntity tenantEntity = tenantService.getInfoByEnCode(userEntity.getTenantId());
                if (tenantEntity == null) {
                    throw new IllegalArgumentException("租户不存在");
                }
                long storeCount = allegroStoreService.sumStoreNum(userEntity.getTenantId());
                if (storeCount >= tenantEntity.getLimitStoreNum()) {
                    throw new IllegalArgumentException("店铺数量已达上限。限制绑定数：" + tenantEntity.getLimitStoreNum());
                }
            }

            // 生成state参数，用于防止CSRF攻击和确认回调时的请求来源
            String state = String.format("state_for_%s_%s_%d_%s", apiClientId, URLEncoder.encode(storeName, StandardCharsets.UTF_8), type, userId);
            if (state.length() > 512) {
                state = state.substring(0, 512);
            }

            // 构建并返回重定向URL，引导用户前往Allegro授权页面
            String redirectUrl = allegroAuthService.buildAuthorizationUrl(state, apiClientId);
            return ResponseEntity.status(HttpStatus.FOUND).header("Location", redirectUrl).build();
        } catch (IllegalArgumentException e) {
            String errorMessage = "<html><body><h1>发生错误</h1><p>" + e.getMessage() + "</p></body></html>";
            return ResponseEntity.badRequest().body(errorMessage);
        } catch (Exception e) {
            String errorMessage = "<html><body><h1>发生错误</h1><p>未知错误：" + e.getMessage() + "</p></body></html>";
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorMessage);
        }
    }

    /**
     * 处理Allegro授权回调，交换授权代码并获取访问令牌。
     *
     * @param code  授权代码，由Allegro授权页面返回。
     * @param state 状态参数，用于验证请求的一致性。
     * @return 回调响应字符串，指示授权是否成功。
     */
    @GetMapping("/callback")
    @ResponseBody
    @NoDataSourceBind
    public String callback(@RequestParam String code, @RequestParam String state) {
        // 从state参数中提取商店名称
        String apiClientId = state.split("_")[2];
        String storeName = state.split("_")[3];
        Integer type = Integer.valueOf(state.split("_")[4]);
        String userId = state.split("_")[5];
        try {
            // 使用授权代码交换访问令牌
            AllegroStoreEntity entity = allegroAuthService.exchangeCodeForToken(code, storeName, apiClientId,type,userId);
            // 更新同步的配置信息
            log.info("同步店铺配置信息：{},租户：{}", entity.getStoreName(), entity.getTenantId());
            allegroStoreConfigService.syncConfigFromAllegro(entity);
            // 返回授权成功的消息
            return storeName + "，授权成功";
        } catch (Exception e) {
            log.error(storeName + "授权失败：" + e);
            return storeName + "，授权失败" + e.getMessage();
        }
    }

    @Operation(summary = "使用refreshToken重新授权")
    @GetMapping("/refreshStoreToken/{storeId}")
    @Parameters({
            @Parameter(name = "storeId", description = "店铺ID", required = true)
    })
    @ResponseBody
    @NoDataSourceBind
    public ActionResult<String> refreshStoreToken(@PathVariable("storeId") String storeId) {
        AllegroStoreEntity entity = allegroAuthService.refreshToken(storeId);
        if (entity == null) {
            return ActionResult.fail("刷新token不存在！");
        }
        log.debug(entity.toString());
        return ActionResult.success(entity.getStoreName()+" 刷新Token成功" ) ;
    }


    @GetMapping("/refreshToken")
    @ResponseBody
    @NoDataSourceBind
    public ActionResult<String> refreshToken() {
        // 获取当前时间
        Calendar now = Calendar.getInstance();
        int currentHour = now.get(Calendar.HOUR_OF_DAY);

        // 检查是否是23点之后
        boolean isAfter23 = currentHour >= 23;

        List<AllegroStoreEntity> list = allegroStoreService.list();
        StringBuffer sbuf = new StringBuffer();
        int i = 0,j=0;
        for (AllegroStoreEntity store : list) {
            String storeName = store.getStoreName();
            if (! store.getSyncFlag()  || store.getApiClientId().compareTo("5")==0) {
                log.info("店铺: {} 同步忽略，跳过", storeName);
                continue;
            }

            log.info((j++) + "、店铺:{}  当前过期时间：{} ", store.getStoreName(),DateXhUtil.formatDateTime(store.getExpireDate()));

            // 计算到期时间与当前时间的差值（小时）
            long hoursUntilExpire = (store.getExpireDate().getTime() - System.currentTimeMillis()) / 3600000;
            log.info("当前时间：{}点，是否23点后：{}, 距离过期小时数：{}",
                    currentHour,  isAfter23, hoursUntilExpire);
            // 判断刷新条件
            boolean shouldRefresh = false;
            if (isAfter23 && hoursUntilExpire < 8) {
                // 只有在23点后执行过且距离过期少于8小时时才刷新
                    shouldRefresh = true;
            } else {
                // 正常时间段，距离过期1小时内就刷新
                if (hoursUntilExpire < 2) {
                    shouldRefresh = true;
                }
            }

            if (shouldRefresh) {
//                i++;
                log.info("店铺:{} 正在重新获取token,当前过期时间：{} ", store.getStoreName(),DateXhUtil.formatDateTime(store.getExpireDate()));
//                sbuf.append(i + "、" + storeName + "重新获取，当前过期时间：<font color=\"warning\">"+ DateXhUtil.formatTime(store.getExpireDate()) +"</font>\n");
                store = allegroAuthService.refreshToken(store.getId());
                if (store == null) {
                    i++;
                    sbuf.append(i + "、<font color=\"warning\"> " + storeName + "</font>token获取失败\n");
                    log.info("店铺: {} token获取失败，请检查。", storeName);
                }
            }
        }
        if(i>0) {
            WebHookAPI.sendMessage("刷新授权店铺数："+i+"\n>"+sbuf.toString());
        }
        return ActionResult.success(sbuf.toString() ) ;
    }


    @GetMapping("/refreshToken5")
    @ResponseBody
    public ActionResult<String> refreshToken5() {
        List<AllegroStoreEntity> list = allegroStoreService.list();
        StringBuffer sbuf = new StringBuffer();
        int i = 0,j=0;
        for (AllegroStoreEntity store : list) {
            String storeName = store.getStoreName();
            if ( !store.getSyncFlag()   || store.getApiClientId().compareTo("5")!=0) {
                log.info("店铺: {} 同步忽略，跳过", storeName);
                continue;
            }

            log.info((j++) + "、店铺:{}  当前过期时间：{} ", store.getStoreName(),DateXhUtil.formatDateTime(store.getExpireDate()));
            if (store.getExpireDate().getTime()-7200000 < System.currentTimeMillis()) {
//                i++;
                log.info("店铺:{} 正在重新获取token,当前过期时间：{} ", store.getStoreName(),DateXhUtil.formatDateTime(store.getExpireDate()));
//                sbuf.append(i + "、" + storeName + "重新获取，当前过期时间：<font color=\"warning\">"+ DateXhUtil.formatTime(store.getExpireDate()) +"</font>\n");
                store = allegroAuthService.refreshToken(store.getId());
                if (store == null) {
                    i++;
                    sbuf.append(i + "、<font color=\"warning\"> " + storeName + "</font>token获取失败\n");
                    log.info("店铺: {} token获取失败，请检查。", storeName);
                }
            }
        }
        if(i>0) {
            WebHookAPI.sendMessage("刷新授权店铺数："+i+"\n>"+sbuf.toString());
        }
        return ActionResult.success(sbuf.toString() ) ;
    }


    public static void main(String[] args) throws Exception {
        String state = "state_for_1_测试";
        String apiClientId = state.split("_")[2];
        String storeName = state.split("_")[3];

        System.out.println(apiClientId);
        System.out.println(storeName);
    }
}
