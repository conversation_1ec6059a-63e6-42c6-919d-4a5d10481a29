package com.xinghuo.allegro.collect.model.product;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xinghuo.common.base.model.Pagination;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 采集产品分页查询参数
 * <AUTHOR>
 */
@Data
public class CollectProductPagination extends Pagination {
    
    /**
     * 查询key
     */
    private String[] selectKey;
    
    /**
     * json
     */
    private String json;
    
    /**
     * 数据类型 0-当前页，1-全部数据
     */
    private String dataType;
    
    /**
     * 高级查询
     */
    private String superQueryJson;
    
    /**
     * 功能id
     */
    private String moduleId;
    
    /**
     * 菜单id
     */
    private String menuId;

    @Schema(description = "关键词搜索")
    private String keyword;

    @Schema(description = "产品ID")
    private String productId;

    @Schema(description = "SKU ID")
    private Integer skuId;

    @Schema(description = "分类ID")
    private String categoryId;

    @Schema(description = "产品状态")
    private Integer status;

    @Schema(description = "搜索类型：产品ID(product_id|EQ)，产品名称(product_name|LIKE)，Offer名称(offer_name|LIKE)，分类ID(category_id|EQ)")
    private String searchType;

    @Schema(description = "搜索关键字")
    private String searchKey;

    @Schema(description = "时间类型：创建时间(f_created_at)，修改时间(f_last_modified_at)")
    private String dateType;

    @Schema(description = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @Schema(description = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    @Schema(description = "排序字段，例如：f_created_at|desc")
    private String listOrder;
}
