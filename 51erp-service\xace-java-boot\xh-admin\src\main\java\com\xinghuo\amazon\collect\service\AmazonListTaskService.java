package com.xinghuo.amazon.collect.service;

import com.xinghuo.amazon.collect.entity.AmazonListTaskEntity;
import com.xinghuo.amazon.collect.model.list.AmazonListTaskPagination;
import com.xinghuo.common.base.service.BaseService;

import java.util.List;

/**
 * Amazon列表任务Service接口
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
public interface AmazonListTaskService extends BaseService<AmazonListTaskEntity> {

    /**
     * 分页查询Amazon列表任务列表
     * @param pagination 分页查询参数
     * @return Amazon列表任务列表
     */
    List<AmazonListTaskEntity> getList(AmazonListTaskPagination pagination);

    /**
     * 获取待处理的任务
     * @param clientId 客户端ID
     * @param taskType 任务类型
     * @param platform 平台
     * @return 待处理的任务
     */
    AmazonListTaskEntity waitGets(String clientId, String taskType, String platform);

    /**
     * 更新任务状态
     * @param taskId 任务ID
     * @return 更新结果
     */
    int updateCollectTask(String taskId);

    /**
     * 检查任务是否存在
     * @param categoryId 分类ID
     * @return 是否存在
     */
    boolean existTask(String categoryId);

    /**
     * 获取待处理任务数量
     * @param taskType 任务类型
     * @return 待处理任务数量
     */
    long getPendingTaskCount(String taskType);

    /**
     * 更新任务状态为完成
     * @param taskId 任务ID
     * @param crawledPages 已爬取页数
     * @param totalProducts 总商品数
     * @return 更新结果
     */
    int updateTaskCompleted(Integer taskId, Integer crawledPages, Integer totalProducts);

    /**
     * 更新任务状态为失败
     * @param taskId 任务ID
     * @param errorMessage 错误信息
     * @return 更新结果
     */
    int updateTaskFailed(Integer taskId, String errorMessage);
}
