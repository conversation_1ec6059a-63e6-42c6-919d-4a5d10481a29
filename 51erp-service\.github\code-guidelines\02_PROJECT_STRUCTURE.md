# erp-service 项目结构与包命名

## 基础包名

`com.xinghuo` 是所有代码的基础包名，所有代码都应该组织在这个包名下。

## 模块化原则

代码应根据功能划分到相应的子模块中。主要模块包括：

* `xh-admin` - 系统管理与业务主模块
* `xh-system` - 系统功能支持模块
* `xh-oauth` - 身份验证与授权模块
* `xh-tenant` - 多租户支持模块

每个模块应相对独立，通过接口进行交互，避免紧耦合依赖。

## 子模块包结构

在基础包名下，应按功能领域和层级组织代码，遵循以下结构：

* `com.xinghuo.[模块名].controller` - 控制器层，处理HTTP请求
* `com.xinghuo.[模块名].service` - 服务接口定义
* `com.xinghuo.[模块名].service.impl` - 服务接口实现
* `com.xinghuo.[模块名].dao`  - 数据访问对象
* `com.xinghuo.[模块名].entity` - 数据库实体
* `com.xinghuo.[模块名].model` - 数据传输对象 (DTO) 和视图对象 (VO)
* `com.xinghuo.[模块名].config` - 模块配置类
* `com.xinghuo.[模块名].util` - 工具类
* `com.xinghuo.[模块名].enums` - 枚举类
* `com.xinghuo.[模块名].constant` - 常量类

## 命名规则示例

| 类型 | 命名规则 | 示例 |
|------|----------|------|
| 控制器 | `[业务名]Controller` | `UserController`, `ProductCategoryController` |
| 服务接口 | `[业务名]Service` | `UserService`, `ProductCategoryService` |
| 服务实现 | `[业务名]ServiceImpl` | `UserServiceImpl`, `ProductCategoryServiceImpl` |
| 数据访问 | `[业务名]Mapper` | `UserMapper`, `ProductCategoryMapper` |
| 实体 | `[业务名]Entity` | `UserEntity`, `ProductCategoryEntity` |
| DTO | `[业务名][操作]DTO` | `UserCreateDTO`, `ProductCategoryUpdateDTO` |
| VO | `[业务名]VO` | `UserVO`, `ProductCategoryVO` |
| 查询参数 | `[业务名]Pagination` | `UserPagination`, `ProductCategoryPagination` |

## 资源文件结构

* `src/main/resources/application.yml` - 应用主配置文件
* `src/main/resources/application-{profile}.yml` - 环境特定配置文件
