package com.xinghuo.allegro.data.model.seller;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * Allegro卖家表单
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "Allegro卖家表单")
public class AllegroSellerForm {

    @Schema(description = "卖家ID")
    private String sellerId;

    @Schema(description = "卖家名称")
    private String sellerName;

    @Schema(description = "卖家登录名")
    private String login;

    @Schema(description = "公司名称")
    private String companyName;

    @Schema(description = "公司地址城市")
    private String companyAddressCity;

    @Schema(description = "公司地址国家")
    private String companyAddressCountry;

    @Schema(description = "公司地址国家代码")
    private String companyAddressCountryCode;

    @Schema(description = "公司地址街道")
    private String companyAddressStreet;

    @Schema(description = "公司地址邮政编码")
    private String companyAddressZipCode;

    @Schema(description = "公司税务识别号")
    private String companyTaxId;

    @Schema(description = "公司是否已验证")
    private Boolean companyVerified;

    @Schema(description = "联系邮箱列表")
    private String contactEmails;

    @Schema(description = "联系电话列表")
    private String contactPhones;

    @Schema(description = "评分")
    private String ratings;

    @Schema(description = "列表URL")
    private String listingUrl;

    @Schema(description = "分组")
    private String groupName;

    @Schema(description = "卖家类型")
    private String sellType;

    @Schema(description = "备注")
    private String note;

    @Schema(description = "状态")
    private Integer status;

    @Schema(description = "优先级")
    private Integer priority;

    @Schema(description = "卖家ID列表，用于批量操作")
    private List<String> ids;

    @Schema(description = "挂店日期")
    private Date endDate;

    @Schema(description = "店铺在线数")
    private Integer totalNum;

    @Schema(description = "有销售额的数量")
    private Integer totalSalesNum;

    @Schema(description = "后台采集条目")
    private Integer offerAllNum;

    @Schema(description = "历史总销量")
    private Integer totalSalesSum;

    @Schema(description = "中国发货条目")
    private Integer chinaNum;
}
