package com.xinghuo.allegro.collect.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.AbstractBaseEntity;
import lombok.Data;

import java.util.Date;

/**
 * 采集账号管理
 */
@TableName("zz_collect_account")
@Data
public class CollectAccountEntity extends AbstractBaseEntity.AbstractCUBaseEntity<String> {

    private String name;

    private Date expireDate;

    private String activeCode;

    /**
     * 0:未使用 1:已使用
     */
    private Integer usedStatus;

    private Date activeTime;

    private String activeFromIp;

    private String activeUniqueId;

    private String note;

    private String lastConnectIp;

    private Date lastConnectTime;


}
