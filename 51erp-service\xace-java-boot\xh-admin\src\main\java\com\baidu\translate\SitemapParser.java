package com.baidu.translate;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.File;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Comparator;

public class SitemapParser {

    private static final Logger logger = LoggerFactory.getLogger(SitemapParser.class);
//    private static final String JDBC_URL = "***************************************";
    private static final String JDBC_URL = "**********************************";
    private static final String DB_USER = "root";
    private static final String DB_PASSWORD = "xh884813@@XH";
    private static final String PROCESSED_DIR = "D:/processed_sitemaps";

    public static void main(String[] args) {
        logger.info("开始站点地图解析处理");
        File folder = new File("D:/sitemaps");
        File[] listOfFiles = folder.listFiles((dir, name) -> name.startsWith("sitemap_part_") && name.endsWith(".xml"));

        if (listOfFiles != null) {
            Arrays.sort(listOfFiles, Comparator.comparing(File::getName));
            logger.info("找到 {} 个站点地图文件待处理", listOfFiles.length);
            try (Connection conn = DriverManager.getConnection(JDBC_URL, DB_USER, DB_PASSWORD)) {
                logger.info("数据库连接成功建立");
                conn.setAutoCommit(false);

                int totalProcessed = 0;

                for (File file : listOfFiles) {
                    logger.info("正在处理文件: {}", file.getName());
                    int count = parseAndInsert(file, conn);
                    totalProcessed += count;
                    logger.info("从文件 {} 中处理了 {} 个URL", file.getName(), count);
                    conn.commit();

                    // Move the processed file to the processed directory
                    File processedFile = new File(PROCESSED_DIR, file.getName());
                    if (file.renameTo(processedFile)) {
                        logger.info("文件 {} 已成功移动到 {}", file.getName(), PROCESSED_DIR);
                    } else {
                        logger.warn("文件 {} 移动失败", file.getName());
                    }
                }

                logger.info("数据插入成功完成！总共处理URL数量: {}", totalProcessed);
            } catch (SQLException e) {
                logger.error("数据库错误: {}", e.getMessage(), e);
            }
        } else {
            logger.warn("在目录 D:/sitemaps 中未找到站点地图文件");
        }
    }

    private static int parseAndInsert(File file, Connection conn) {
        int processedCount = 0;
        try {
            DocumentBuilderFactory dbFactory = DocumentBuilderFactory.newInstance();
            DocumentBuilder dBuilder = dbFactory.newDocumentBuilder();
            Document doc = dBuilder.parse(file);
            doc.getDocumentElement().normalize();

            NodeList urlList = doc.getElementsByTagName("url");
            logger.debug("在文件 {} 中找到 {} 个URL元素", file.getName(), urlList.getLength());

            String sql = "INSERT INTO zz_fruugo_sitemap_entries (loc, lastmod, changefreq, priority, image_loc, image_title, file_name) VALUES (?, ?, ?, ?, ?, ?, ?)";
            try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
                for (int i = 0; i < urlList.getLength(); i++) {
                    Node urlNode = urlList.item(i);
                    if (urlNode.getNodeType() == Node.ELEMENT_NODE) {
                        Element urlElement = (Element) urlNode;

                        String lastmodStr = getTagValue("lastmod", urlElement);
                        if (lastmodStr == null) {
                            logger.debug("跳过索引 {} 处的URL - 缺少lastmod标签", i);
                            continue; // Skip this <url> element if <lastmod> is missing
                        }

                        try {
                            String loc = getTagValue("loc", urlElement);
                            OffsetDateTime lastmod = OffsetDateTime.parse(lastmodStr, DateTimeFormatter.ISO_OFFSET_DATE_TIME);
                            String changefreq = getTagValue("changefreq", urlElement);
                            String priorityStr = getTagValue("priority", urlElement);
                            Double priority = priorityStr != null ? Double.parseDouble(priorityStr) : null;

                            Element imageElement = (Element) urlElement.getElementsByTagName("image:image").item(0);
                            String imageLoc = imageElement != null ? getTagValue("image:loc", imageElement) : null;
                            String imageTitle = imageElement != null ? getTagValue("image:title", imageElement) : null;

                            pstmt.setString(1, loc);
                            pstmt.setObject(2, java.sql.Timestamp.from(lastmod.toInstant()));
                            pstmt.setString(3, changefreq);
                            pstmt.setDouble(4, priority != null ? priority : 0.0);
                            pstmt.setString(5, imageLoc);
                            pstmt.setString(6, imageTitle);
                            pstmt.setString(7, file.getName());

                            pstmt.addBatch();
                            processedCount++;

                            // 每处理1000条记录执行一次批量提交
                            if (processedCount > 0 && processedCount % 10000 == 0) {
                                pstmt.executeBatch();
                                conn.commit();

                                logger.info("文件 {} 中已处理 {} 个URL，已批量提交", file.getName(), processedCount);
                            }
                        } catch (Exception e) {
                            logger.error("处理索引 {} 处的URL时出错: {}", i, e.getMessage());
                            logger.info("URL内容: {}", urlElement.getTextContent().substring(0, Math.min(100, urlElement.getTextContent().length())));
                        }
                    }
                }

                // 处理剩余不足1000条的记录
                int remainingRecords = processedCount % 1000;
                if (remainingRecords > 0) {
                    pstmt.executeBatch();
                    conn.commit();
                    logger.info("文件 {} 中处理完毕，最后 {} 条记录已提交", file.getName(), remainingRecords);
                }

                if (processedCount > 0) {
                    logger.info("成功插入 {} 个URL，来自文件 {}", processedCount, file.getName());
                } else {
                    logger.warn("文件 {} 中未找到有效的URL", file.getName());
                }
            }
        } catch (Exception e) {
            logger.error("解析文件 {} 时出错: {}", file.getName(), e.getMessage(), e);
        }
        return processedCount;
    }

    private static String getTagValue(String tag, Element element) {
        NodeList nodeList = element.getElementsByTagName(tag);
        if (nodeList != null && nodeList.getLength() > 0) {
            Node node = nodeList.item(0).getChildNodes().item(0);
            return node != null ? node.getNodeValue() : null;
        }
        return null;
    }
}