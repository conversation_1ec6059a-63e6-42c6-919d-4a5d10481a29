package com.xinghuo.allegro.data.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xinghuo.allegro.data.dao.AllegroProductMapper;
import com.xinghuo.allegro.data.service.AllegroProductService;
import com.xinghuo.allegro.push.entity.ErpProductEntity;
import com.xinghuo.allegro.sale.entity.AllegroNewProductEnEntity;
import com.xinghuo.allegro.sale.service.AllegroNewProductEnService;
import com.xinghuo.base.service.ExtendedBaseServiceImpl;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.fruugo.collect.entity.FruugoCollectSaleEntity;
import com.xinghuo.fruugo.collect.entity.FruugoSellerEntity;
import com.xinghuo.fruugo.collect.service.FruugoCollectSaleService;
import com.xinghuo.fruugo.collect.service.FruugoPushOfferService;
import com.xinghuo.fruugo.collect.service.FruugoSellerService;
import com.xinghuo.fruugo.data.model.AllegroProductClaimForm;
import com.xinghuo.fruugo.data.model.AllegroProductPagination;
import com.xinghuo.fruugo.data.model.AllegroProductUpdateModel;
import com.xinghuo.fruugo.manage.service.FruugoShelfTemplateService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * Allegro产品Service实现类
 *
 * <AUTHOR>
 * date 2025-05-18
 */
@Slf4j
@Service
public class AllegroProductServiceImpl extends ExtendedBaseServiceImpl<AllegroProductMapper, ErpProductEntity> implements AllegroProductService {

    @Resource
    private AllegroNewProductEnService allegroNewProductEnService;

    @Resource
    private FruugoPushOfferService fruugoPushOfferService;

    @Resource
    private FruugoShelfTemplateService fruugoShelfTemplateService;

    @Resource
    private FruugoSellerService fruugoSellerService;

    @Resource
    private FruugoCollectSaleService fruugoCollectSaleService;

    @Override
    public List<ErpProductEntity> getList(AllegroProductPagination pagination) {
        QueryWrapper<ErpProductEntity> queryWrapper = new QueryWrapper<>();
        

        // 添加查询条件
        if (StrXhUtil.isNotBlank(pagination.getProductId())) {
            queryWrapper.lambda().eq(ErpProductEntity::getProductId, pagination.getProductId());
        }
        
        if (StrXhUtil.isNotBlank(pagination.getOfferName())) {
            queryWrapper.lambda().like(ErpProductEntity::getOfferName, pagination.getOfferName());
        }
        
        if (StrXhUtil.isNotBlank(pagination.getTitleEn())) {
            queryWrapper.lambda().like(ErpProductEntity::getTitleEn, pagination.getTitleEn());
        }
        
        if (StrXhUtil.isNotBlank(pagination.getCategoryId())) {
            queryWrapper.lambda().eq(ErpProductEntity::getCategoryId, pagination.getCategoryId());
        }
        
        if (StrXhUtil.isNotBlank(pagination.getFruugoCategoryId())) {
            queryWrapper.lambda().eq(ErpProductEntity::getFruugoCategoryId, pagination.getFruugoCategoryId());
        }
        
        if (StrXhUtil.isNotBlank(pagination.getFromSellerId())) {
            queryWrapper.lambda().eq(ErpProductEntity::getFromSellerId, pagination.getFromSellerId());
        }
        
        if (StrXhUtil.isNotBlank(pagination.getOwnEan())) {
            queryWrapper.lambda().eq(ErpProductEntity::getOwnEan, pagination.getOwnEan());
        }
        
        if (StrXhUtil.isNotBlank(pagination.getGroupId())) {
            queryWrapper.lambda().eq(ErpProductEntity::getGroupId, pagination.getGroupId());
        }
        
        if (pagination.getMinPrice() != null) {
            queryWrapper.lambda().ge(ErpProductEntity::getTotalPrice, pagination.getMinPrice());
        }
        
        if (pagination.getMaxPrice() != null) {
            queryWrapper.lambda().le(ErpProductEntity::getTotalPrice, pagination.getMaxPrice());
        }
        
        if (pagination.getStatus() != null) {
            queryWrapper.lambda().eq(ErpProductEntity::getStatus, pagination.getStatus());
        }
        
        if (pagination.getForcePush() != null) {
            queryWrapper.lambda().eq(ErpProductEntity::getForcePush, pagination.getForcePush());
        }
        
        if (pagination.getIsAudit() != null) {
            queryWrapper.lambda().eq(ErpProductEntity::getIsAudit, pagination.getIsAudit());
        }
        
        if (pagination.getSkuIds() != null && !pagination.getSkuIds().isEmpty()) {
            queryWrapper.lambda().in(ErpProductEntity::getSkuId, pagination.getSkuIds());
        }
        
        // 设置分页
        queryWrapper.lambda().orderByDesc(ErpProductEntity::getUpdateTime);

        Page<ErpProductEntity> page = new Page<>(pagination.getCurrentPage(), pagination.getPageSize());
        IPage<ErpProductEntity> userIpage = this.page(page, queryWrapper);
        return pagination.setDataList(userIpage.getRecords(), userIpage.getTotal());
    }

    @Override
    public ErpProductEntity getInfo(Integer skuId) {
        return this.getById(skuId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateProduct(AllegroProductUpdateModel model) {
        ErpProductEntity entity = this.getById(model.getSkuId());
        if (entity == null) {
            log.error("产品不存在, skuId: {}", model.getSkuId());
            return false;
        }
        
        // 更新基本信息
        if (StrXhUtil.isNotBlank(model.getOfferName())) {
            entity.setOfferName(model.getOfferName());
        }
        

        
        if (StrXhUtil.isNotBlank(model.getTitleEn())) {
            entity.setTitleEn(model.getTitleEn());
        }
        
        if (StrXhUtil.isNotBlank(model.getParametersEn())) {
            entity.setParametersEn(model.getParametersEn());
        }
        
        if (StrXhUtil.isNotBlank(model.getDescriptionEn())) {
            entity.setDescriptionEn(model.getDescriptionEn());
        }
        
        if (StrXhUtil.isNotBlank(model.getImagesEn())) {
            entity.setImagesEn(model.getImagesEn());
        }
        
        if (StrXhUtil.isNotBlank(model.getFruugoCategoryId())) {
            entity.setFruugoCategoryId(model.getFruugoCategoryId());
        }
        
        if (StrXhUtil.isNotBlank(model.getFruugoCategoryPath())) {
            entity.setFruugoCategoryPath(model.getFruugoCategoryPath());
        }
        
        if (StrXhUtil.isNotBlank(model.getNote())) {
            entity.setNote(model.getNote());
        }
        
        if (StrXhUtil.isNotBlank(model.getGroupId())) {
            entity.setGroupId(model.getGroupId());
        }
        
        if (model.getForcePush() != null) {
            entity.setForcePush(model.getForcePush());
        }
        
        if (model.getManualTotalPrice() != null) {
            entity.setManualTotalPrice(model.getManualTotalPrice());
        }
        
        if (model.getIsAudit() != null) {
            entity.setIsAudit(model.getIsAudit());
        }
        
        if (StrXhUtil.isNotBlank(model.getSkuCategoryCode())) {
            entity.setSkuCategoryCode(model.getSkuCategoryCode());
        }
        
        return this.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchDelete(List<Integer> skuIds) {
        if (skuIds == null || skuIds.isEmpty()) {
            return 0;
        }
        
        // 逻辑删除，设置租户ID为DELETE
        UpdateWrapper<ErpProductEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda()
                .set(ErpProductEntity::getTenantId, "DELETE")
                .in(ErpProductEntity::getSkuId, skuIds);
        
        return this.baseMapper.update(null, updateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int claimToFruugoPushOffer(AllegroProductClaimForm form) {
        // 实现从Allegro产品认领到Fruugo推送报价的逻辑
        // 这里需要调用FruugoPushOfferService的相关方法
        return 0; // 暂时返回0，后续实现
    }

    @Override
    public boolean syncProductEnData(Integer skuId) {
        ErpProductEntity entity = this.getById(skuId);
        if (entity == null) {
            log.error("产品不存在, skuId: {}", skuId);
            return false;
        }
        
        // 调用AllegroNewProductEnService获取英文数据
        AllegroNewProductEnEntity enEntity = allegroNewProductEnService.fetchOrRetrieveProductEn(entity.getProductId());
        if (enEntity == null) {
            log.error("获取英文数据失败, productId: {}", entity.getProductId());
            return false;
        }
        
        // 更新产品英文数据
        entity.setTitleEn(enEntity.getName());
        // 其他英文数据更新...
        
        return this.updateById(entity);
    }

    @Override
    public List<?> getProductSaleData(Integer skuId) {
        ErpProductEntity entity = this.getById(skuId);
        if (entity == null) {
            log.error("产品不存在, skuId: {}", skuId);
            return new ArrayList<>();
        }
        
        // 查询关联的销售数据
        LambdaQueryWrapper<FruugoCollectSaleEntity> queryWrapper = new LambdaQueryWrapper<>();
        // 根据实际情况设置查询条件
        
        return new ArrayList<>(); // 暂时返回空列表，后续实现
    }

    @Override
    public List<?> getProductSellerData(Integer skuId) {
        ErpProductEntity entity = this.getById(skuId);
        if (entity == null) {
            log.error("产品不存在, skuId: {}", skuId);
            return new ArrayList<>();
        }
        
        // 查询关联的店铺数据
        LambdaQueryWrapper<FruugoSellerEntity> queryWrapper = new LambdaQueryWrapper<>();
        // 根据实际情况设置查询条件
        
        return new ArrayList<>(); // 暂时返回空列表，后续实现
    }
}
