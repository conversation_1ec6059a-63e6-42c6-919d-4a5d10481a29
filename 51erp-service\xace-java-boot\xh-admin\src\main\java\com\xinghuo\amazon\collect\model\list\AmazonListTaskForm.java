package com.xinghuo.amazon.collect.model.list;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * Amazon列表任务表单
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
public class AmazonListTaskForm {

    @Schema(description = "主键ID")
    private Integer id;

    @Schema(description = "URL")
    private String url;

    @Schema(description = "源分类ID")
    private Long sourceCategoryId;

    @Schema(description = "任务状态")
    private Integer status;

    @Schema(description = "最大爬取页数")
    private Integer maxPagesToCrawl;

    @Schema(description = "已爬取页数")
    private Integer crawledPages;

    @Schema(description = "错误信息")
    private String errorMessage;

    @Schema(description = "源分类路径")
    private String sourceCategoryPath;

    @Schema(description = "Temu分类ID")
    private Integer temuCategoryId;

    @Schema(description = "Temu分类路径")
    private String temuCategoryPath;

    @Schema(description = "总商品数")
    private Integer totalNum;

    @Schema(description = "有销售额数")
    private Integer totalSalesNum;

    @Schema(description = "JSON数据")
    private String jsonData;
}
