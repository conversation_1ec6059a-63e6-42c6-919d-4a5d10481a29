com\xinghuo\fruugo\image\service\FruugoImageService.class
com\xinghuo\allegro\collect\service\CollectAccountService.class
com\xinghuo\allegro\sale\model\offer\OfferAPIDataModel.class
com\xinghuo\coupang\manage\service\CoupangStoreConfigService.class
com\xinghuo\allegro\order\service\OrderItemService.class
com\xinghuo\niceapi\ozon\model\delivery\TrackingNumberRequest$TrackingNumberItem$TrackingNumberItemBuilder.class
com\xinghuo\niceapi\coupang\api\CoupangReturnShippingApi.class
com\xinghuo\fruugo\collect\dao\FruugoCollectOfferMapper.class
com\xinghuo\niceapi\ozon\model\promo\PromoProductsDeactivateRequest$PromoProductsDeactivateRequestBuilder.class
com\xinghuo\allegro\order\entity\OrderProfitEntity.class
com\xinghuo\coupang\manage\service\CoupangTemplateService.class
com\xinghuo\coupang\manage\controller\CoupangTemplateController.class
com\xinghuo\niceapi\ozon\model\fbs\CancelPostingRequest.class
com\xinghuo\niceapi\fpx\Fpx4Client$11.class
com\xinghuo\niceapi\ozon\web\OzonApiRequest.class
com\xinghuo\fruugo\collect\controller\FruugoBoughtOfferController.class
com\xinghuo\fruugo\data\service\impl\FruugoDataSellerServiceImpl.class
com\xinghuo\allegro\msg\service\impl\MsgDisputeServiceImpl.class
com\xinghuo\fruugo\analysis\controller\FruugoProductMonitorController.class
com\xinghuo\fruugo\data\service\FruugoDataSkuService.class
com\xinghuo\allegro\push\service\impl\ShelfTemplateServiceImpl.class
com\xinghuo\niceapi\ozon\model\product\ProductInfoListResponse$ModelInfo.class
com\xinghuo\coupang\offer\dao\CoupangOfferMapper.class
com\xinghuo\niceapi\ozon\category\OzonCategoryAPI.class
com\xinghuo\niceapi\ozon\model\returns\RfbsReturnVerifyRequest$RfbsReturnVerifyRequestBuilder.class
com\xinghuo\allegro\data\model\AllegroDataLinkModel.class
com\xinghuo\niceapi\ozon\model\product\ProductInfoResponse$PriceIndexes.class
com\xinghuo\fruugo\collect\model\FruugoProductModel.class
com\xinghuo\niceapi\fpx\model\Fpx4LabelRequest$Fpx4LabelRequestBuilder.class
com\xinghuo\niceapi\yunexpress\OpenapiHjService.class
com\xinghuo\allegro\collect\model\seller\CollectSellerPagination.class
com\xinghuo\niceapi\ozon\model\product\ProductDeleteResponse.class
com\xinghuo\allegro\order\model\rule\ExpressRulePagination.class
com\xinghuo\allegro\order\model\order\OrderStatusEnum.class
com\xinghuo\allegro\push\entity\AllegroBrandEntity.class
com\xinghuo\allegro\push\dao\ErpGroupMapper.class
com\xinghuo\allegro\manage\model\store\BuckFeeForm.class
com\xinghuo\amazon\collect\model\list\AmazonListTaskForm.class
com\xinghuo\allegro\data\model\sale\SaleSkuModel.class
com\xinghuo\fruugo\analysis\controller\FruugoCollectHistoryController.class
com\xinghuo\fruugo\analysis\model\SalesTrendVO.class
com\xinghuo\fruugo\analysis\task\FruugoMonitorRefreshTask.class
com\xinghuo\niceapi\ozon\model\category\CategoryAttributeResponse$Attribute.class
com\xinghuo\allegro\home\model\ExpireStoreModel.class
com\xinghuo\niceapi\ozon\model\fbs\ProductWeightChangeRequest$ProductWeightItem.class
com\xinghuo\fruugo\collect\model\FruugoPushOfferPagination.class
com\xinghuo\allegro\manage\controller\SysExchangeRateController.class
com\xinghuo\fruugo\data\service\impl\FruugoDataProductServiceImpl.class
com\xinghuo\allegro\order\model\order\ShipmentModel.class
com\xinghuo\fruugo\manage\model\store\StoreGroupModel.class
com\xinghuo\niceapi\ozon\model\delivery\PostingCutoffRequest.class
com\xinghuo\coupang\manage\model\template\CoupangTemplateDetailModel.class
com\xinghuo\allegro\shelf\service\impl\AllegroShelfLogServiceImpl.class
com\xinghuo\ozon\offer\model\vo\OzonOfferListVO.class
com\xinghuo\analy\service\AnalyOfferInfoService.class
com\xinghuo\fyndiq\manage\entity\FyndiqCategoryEntity.class
com\xinghuo\collect\box\model\CollectBoxPagination.class
com\xinghuo\coupang\offer\service\CoupangOfferItemService.class
com\xinghuo\allegro\data\model\AllegroDataOfferParseForm.class
com\xinghuo\allegro\push\model\category\AllegroCategoryPagination.class
com\xinghuo\allegro\collect\service\impl\CollectEventServiceImpl.class
com\xinghuo\niceapi\ozon\model\product\ProductPictureImportResponse$Result.class
com\xinghuo\niceapi\ozon\model\returns\RfbsReturnsListResponse$State.class
com\xinghuo\allegro\order\entity\ExpressWaybillEntity.class
com\xinghuo\niceapi\ozon\model\fbs\UnfulfilledPostingsRequest.class
com\xinghuo\allegro\push\controller\MenuController$SubModuleConfig.class
com\xinghuo\niceapi\fpx\model\create\LogisticsServiceInfo$LogisticsServiceInfoBuilder.class
com\xinghuo\allegro\order\model\order\LockOrderForm.class
com\xinghuo\niceapi\fpx\model\Fpx4OrderQueryResponse$ParcelListConfirmInfo.class
com\xinghuo\allegro\order\model\order\LabelModel.class
com\xinghuo\fruugo\analysis\model\DataProcessStatsModel$EntityStats.class
com\xinghuo\niceapi\coupang\model\shipping\ShippingPlaceResponse$Pagination.class
com\xinghuo\allegro\home\model\VioStatModel$ProductStats.class
com\xinghuo\allegro\msg\service\MsgRatingService.class
com\xinghuo\allegro\msg\controller\DisputeController.class
com\xinghuo\allegro\push\dao\PushOfferMapper.class
com\xinghuo\allegro\push\model\erp\ForcePushEnum.class
com\xinghuo\fruugo\analysis\model\FruugoDataMonitorVO.class
com\xinghuo\allegro\push\model\erp\ErpProductVioModel.class
com\xinghuo\ozon\msg\model\OzonChatMessageVO.class
com\xinghuo\fruugo\collect\model\FruugoSitemapPagination.class
com\xinghuo\allegro\order\service\impl\OrderShipmentServiceImpl$1.class
com\xinghuo\ozon\manage\service\OzonDataMigrationService$1.class
com\xinghuo\allegro\order\dao\RefundItemMapper.class
com\xinghuo\fruugo\data\model\AllegroProductUpdateModel.class
com\xinghuo\allegro\order\entity\RefundEntity.class
com\xinghuo\niceapi\ozon\model\product\ProductDescriptionRequest.class
com\xinghuo\niceapi\ozon\model\price\DiscountedProductInfoRequest.class
com\xinghuo\admin\aop\SqlCostInterceptor.class
com\xinghuo\niceapi\ozon\model\chat\ChatListRequest.class
com\xinghuo\allegro\push\service\impl\ErpGroupServiceImpl.class
com\xinghuo\niceapi\ozon\model\stock\FBSStocksByWarehouseResponse$FBSWarehouseStock.class
com\xinghuo\fruugo\collect\dao\FruugoProductSkuMapper.class
com\xinghuo\allegro\msg\entity\MsgThreadEntity.class
com\xinghuo\fruugo\manage\model\store\FruugoStoreModel.class
com\xinghuo\allegro\aiservice\model\ServiceVariantModel.class
com\xinghuo\allegro\push\entity\CollectVioWordEntity.class
com\xinghuo\allegro\sale\model\offer\AllegroOfferPagination.class
com\xinghuo\amazon\collect\service\AmazonDataParseService.class
com\xinghuo\niceapi\ozon\promo\OzonPromoDemo.class
com\xinghuo\allegro\push\dao\AllegroBrandMapper.class
com\xinghuo\amazon\collect\service\AmazonHtmlParseService.class
com\xinghuo\allegro\aiservice\model\DescriptionVariantModel.class
com\xinghuo\allegro\manage\entity\AllegroStoreConfigEntity.class
com\xinghuo\allegro\sale\service\SyncTaskItemService.class
com\xinghuo\allegro\manage\service\AllegroBillTypeService.class
com\xinghuo\allegro\order\model\address\ExpressAddressPagination.class
com\xinghuo\allegro\order\model\profit\ProfitSummaryVO.class
com\xinghuo\allegro\order\service\PDFService.class
com\baidu\translate\SitemapParser.class
com\xinghuo\allegro\collect\model\collect\OfferParseForm.class
com\xinghuo\allegro\data\model\seller\AllegroSellerAnalysisVO.class
com\xinghuo\allegro\collect\dao\CollectTaskMapper.class
com\xinghuo\allegro\msg\entity\MsgTemplateEntity.class
com\xinghuo\allegro\order\service\ExpressRuleService.class
com\xinghuo\admin\aop\PermissionUserAspect.class
com\xinghuo\dmj\shop\dao\ShopBindingMapper.class
com\xinghuo\niceapi\ozon\model\product\ProductArchiveResponse.class
com\xinghuo\ozon\manage\entity\OzonProductEntity.class
com\xinghuo\allegro\data\dao\AllegroSaleMapper.class
com\xinghuo\niceapi\coupang\model\product\CoupangProductCreateRequest$ContentDetail.class
com\xinghuo\niceapi\yunexpress\TokenManager$AppCredentials.class
com\xinghuo\admin\aop\RequestLogAspect.class
com\xinghuo\allegro\msg\controller\MsgTemplateController.class
com\xinghuo\niceapi\ozon\product\OzonProductAPI.class
com\xinghuo\niceapi\ozon\model\product\ProductPicturesResponse$ProductPictures.class
com\xinghuo\ozon\manage\service\impl\ImageAliServiceImpl.class
com\xinghuo\allegro\shelf\service\impl\AllegroShelfBatchSummaryServiceImpl.class
com\xinghuo\fruugo\collect\model\FruugoSitemapForm.class
com\xinghuo\amazon\collect\controller\AmazonListTaskController.class
com\xinghuo\fyndiq\manage\model\category\FyndiqCategoryPagination.class
com\xinghuo\niceapi\ozon\model\product\ProductImportRequest$AttributeValue.class
com\xinghuo\allegro\collect\controller\CollectFhOfferController.class
com\xinghuo\allegro\push\service\impl\AllegroCategoryParameterServiceImpl.class
com\xinghuo\dmj\shop\service\impl\ShopBindingServiceImpl.class
com\xinghuo\collect\box\model\CollectBoxBatchUpdateForm.class
com\xinghuo\fruugo\manage\dao\FruugoShelfTemplateMapper.class
com\xinghuo\allegro\manage\model\store\StoreShelfForm.class
com\xinghuo\allegro\order\dao\OrderBuyerMapper.class
com\xinghuo\allegro\order\controller\ExpressAuthorizeController.class
com\xinghuo\allegro\sale\service\SyncTimeService.class
com\xinghuo\fruugo\image\entity\FruugoImageEntity.class
com\xinghuo\niceapi\allegro\order\model\ShipmentModel$LineItem.class
com\xinghuo\niceapi\coupang\model\shipping\ShippingPlaceResponse$PlaceAddress.class
com\xinghuo\niceapi\allegro\sale\model\OfferEventResponse$OfferEventPublication.class
com\xinghuo\coupang\manage\dao\CoupangTemplateMapper.class
com\xinghuo\fruugo\manage\service\FruugoStoreService.class
com\xinghuo\niceapi\ozon\model\product\ProductListRequest.class
com\xinghuo\coupang\manage\dao\CoupangStoreMapper.class
com\xinghuo\niceapi\ozon\model\product\ProductInfoListResponse$ErrorParam.class
com\xinghuo\allegro\sale\dao\AllegroOfferEndEventMapper.class
com\xinghuo\allegro\data\dao\AllegroSellerAnalysisMapper.class
com\xinghuo\niceapi\ozon\model\promo\HotSalesActivateRequest.class
com\xinghuo\allegro\order\entity\ExpressProductConfigEntity.class
com\xinghuo\niceapi\ozon\model\product\ProductInfoListResponse$PriceIndexes.class
com\xinghuo\allegro\collect\model\collectOffer\CollectSettingModel$NonDeliveryMode.class
com\xinghuo\niceapi\ozon\model\chat\ChatRequest$ChatRequestBuilder.class
com\xinghuo\fyndiq\data\model\FyndiqFruugoTemplateConfig.class
com\xinghuo\niceapi\allegro\sale\OfferTranslationAPI$OfferTitleTranslation.class
com\xinghuo\admin\aop\VisiualOpaAspect.class
com\xinghuo\allegro\sale\model\product\ProductModel.class
com\xinghuo\allegro\manage\dao\ExportTaskMapper.class
com\xinghuo\niceapi\ozon\model\cancellation\ConditionalCancellationRequest$ConditionalCancellationRequestBuilder.class
com\xinghuo\allegro\sale\model\product\ProductModel$RangeValue.class
com\xinghuo\ozon\manage\controller\OzonTaskController.class
com\xinghuo\allegro\order\entity\ExpressRuleEntity.class
com\xinghuo\allegro\manage\dao\StoreCheckMapper.class
com\xinghuo\allegro\data\service\impl\AllegroDataTaskServiceImpl.class
com\xinghuo\allegro\push\service\PushOfferService.class
com\xinghuo\niceapi\ozon\model\fbs\PostingsRequest$With$WithBuilder.class
com\xinghuo\niceapi\ozon\model\product\ProductDescriptionRequest$ProductDescriptionRequestBuilder.class
com\xinghuo\allegro\order\service\OrderDeliveryService.class
com\xinghuo\niceapi\ozon\model\promo\HotSalesResponse$HotSale.class
com\xinghuo\niceapi\coupang\api\test\CoupangCategoryMetaApiExample.class
com\xinghuo\fruugo\analysis\model\FruugoMonitorVO$DailyStats.class
com\xinghuo\coupang\manage\service\impl\CoupangCategoryMetaServiceImpl.class
com\xinghuo\allegro\manage\dao\AllegroStoreUserMapper.class
com\xinghuo\allegro\order\model\authorize\ExpressAuthorizeTreeModel.class
com\xinghuo\coupang\manage\entity\CoupangStoreConfigEntity.class
com\xinghuo\niceapi\fpx\Fpx4Client$5.class
com\xinghuo\allegro\order\model\order\BuyerModel.class
com\xinghuo\allegro\sale\model\product\ProductModel$Description.class
com\xinghuo\allegro\collect\controller\CollectOfferController.class
com\xinghuo\allegro\shelf\service\AllegroShelfLogService.class
com\xinghuo\niceapi\ozon\model\product\ProductInfoListResponse$CommissionInfo.class
com\xinghuo\niceapi\fpx\model\create\AddressInfo.class
com\xinghuo\admin\aop\DataSourceBindAspect.class
com\xinghuo\visualdev\portal\model\FlowTodoCountVO.class
com\xinghuo\niceapi\coupang\api\CoupangWingApiClient$2.class
com\xinghuo\allegro\manage\service\translate\TranslateRateLimiter.class
com\xinghuo\niceapi\yunexpress\model\ApiResponse.class
com\xinghuo\allegro\msg\dao\MsgDisputeMapper.class
com\xinghuo\allegro\sale\model\product\OfferParamModel.class
com\xinghuo\allegro\manage\service\AllegroLogService.class
com\xinghuo\allegro\push\service\EanService.class
com\xinghuo\fruugo\analysis\enums\NotifyTypeEnum.class
com\xinghuo\allegro\push\model\ean\EanModel.class
com\xinghuo\niceapi\allegro\sale\model\OfferEventResponse$OfferEventOffer.class
com\xinghuo\admin\util\BaseServiceUtil.class
com\xinghuo\fruugo\data\model\FruugoSkuInfo.class
com\xinghuo\fruugo\analysis\model\FruugoMonitorVO$CollectOfferStats.class
com\xinghuo\allegro\manage\entity\AllegroApiClientEntity.class
com\xinghuo\allegro\stock\controller\StockController.class
com\xinghuo\allegro\data\dao\AllegroDataTaskMapper.class
com\xinghuo\allegro\push\model\ean\EanImportForm.class
com\xinghuo\niceapi\fpx\model\create\InsuranceInfo.class
com\baidu\translate\AllegroToFruggoConverter$FruggoProduct.class
com\xinghuo\allegro\aiservice\service\TitleVariantService.class
com\xinghuo\niceapi\fpx\Fpx4Client.class
com\xinghuo\allegro\order\service\OrderBuyerService.class
com\xinghuo\allegro\order\model\declaration\ExpressDeclarationSelectorModel.class
com\xinghuo\niceapi\coupang\model\product\CoupangProductCreateRequest$CoupangProductItem.class
com\xinghuo\ozon\manage\service\OzonStoreService.class
com\xinghuo\niceapi\ozon\model\fbs\UnfulfilledPostingsRequest$With.class
com\xinghuo\allegro\data\dao\AllegroDataOfferMapper.class
com\xinghuo\allegro\manage\model\TranslateForm.class
com\xinghuo\allegro\msg\dao\MsgTemplateMapper.class
com\xinghuo\niceapi\ozon\model\fbs\ProductCancelRequest.class
com\xinghuo\niceapi\ozon\model\returns\RfbsReturnsListRequest$DateRange.class
com\xinghuo\allegro\aiservice\controller\ServiceTaskController.class
com\xinghuo\fruugo\collect\controller\FruugoTaskController.class
com\xinghuo\allegro\order\service\impl\ErpOrderServiceImpl$1.class
com\xinghuo\coupang\manage\model\product\CoupangProductStatus.class
com\xinghuo\fyndiq\data\util\FyndiqExcelExportUtil.class
com\xinghuo\niceapi\ozon\model\stock\UpdateStockResponse.class
com\xinghuo\fruugo\analysis\controller\FruugoSellerMonitorController.class
com\xinghuo\fruugo\collect\model\FruugoCollectOfferModel.class
com\xinghuo\allegro\manage\service\AllegroAuthService.class
com\xinghuo\niceapi\allegro\sale\model\FeeModel.class
com\xinghuo\niceapi\ozon\model\delivery\TrackingNumberRequest.class
com\xinghuo\niceapi\ozon\model\promo\PromoTaskRequest$PromoTaskRequestBuilder.class
com\xinghuo\fruugo\collect\service\FruugoCollectOfferService.class
com\xinghuo\niceapi\ozon\model\price\UpdateProductDiscountRequest.class
com\xinghuo\allegro\push\model\ean\EanImportResult.class
com\xinghuo\common\swagger\MySpringWebMvcProvider.class
com\xinghuo\allegro\collect\service\impl\CollectTaskAnalysisServiceImpl.class
com\xinghuo\analy\service\impl\AnalyOfferInfoServiceImpl.class
com\xinghuo\niceapi\ozon\constants\OzonApiConstants$Price.class
com\xinghuo\niceapi\fpx\util\SignUtil.class
com\xinghuo\allegro\manage\service\AllegroStoreConfigService.class
com\xinghuo\allegro\collect\entity\CollectOfferPLEntity.class
com\xinghuo\fruugo\analysis\service\impl\FruugoAlertServiceImpl.class
com\xinghuo\collect\box\model\DuplicateForm.class
com\xinghuo\fruugo\analysis\service\impl\FruugoProductMonitorServiceImpl.class
com\xinghuo\niceapi\ozon\model\fbs\PostingsResponse.class
com\xinghuo\allegro\push\model\shelfTemplate\ShelfTemplateForm.class
com\xinghuo\allegro\push\service\ErpGroupService.class
com\xinghuo\allegro\msg\service\MsgThreadService.class
com\xinghuo\allegro\sale\service\impl\StoreMoveServiceImpl.class
com\xinghuo\allegro\collect\model\collect\CollectTaskForm.class
com\xinghuo\amazon\collect\model\page\AmazonPageTaskForm.class
com\xinghuo\fruugo\collect\dao\FruugoPushOfferMapper.class
com\xinghuo\niceapi\allegro\sale\OfferTranslationAPI$DescriptionSectionItem.class
com\xinghuo\fruugo\analysis\model\CategoryDetailVO.class
com\xinghuo\niceapi\ozon\model\promo\HotSalesResponse.class
com\xinghuo\ozon\manage\service\OzonCategoryAttributeValueService.class
com\xinghuo\allegro\aiservice\service\impl\ServiceTaskServiceImpl.class
com\xinghuo\amazon\collect\service\impl\AmazonPageTaskServiceImpl.class
com\xinghuo\allegro\push\entity\AllegroCategoryEntity.class
com\xinghuo\allegro\msg\model\dispute\DisputeStatusEnum.class
com\xinghuo\allegro\push\model\erp\ErpGroupModel.class
com\xinghuo\allegro\manage\service\impl\AllegroLogServiceImpl.class
com\xinghuo\niceapi\ozon\category\OzonCategoryAPITest.class
com\xinghuo\fruugo\collect\service\impl\FruugoCollectSaleServiceImpl.class
com\xinghuo\coupang\offer\service\CoupangOfferService.class
com\xinghuo\allegro\push\entity\AllegroCategoryParameterEntity.class
com\xinghuo\fruugo\collect\dao\FruugoSellerMapper.class
com\xinghuo\niceapi\ozon\model\fbs\UnfulfilledPostingsRequest$Filter$FilterBuilder.class
com\xinghuo\niceapi\ozon\model\product\ProductDescriptionResponse.class
com\xinghuo\coupang\manage\model\category\CoupangCategoryVO.class
com\xinghuo\allegro\order\entity\OrderShipmentEntity.class
com\xinghuo\coupang\offer\dao\CoupangOfferItemMapper.class
com\xinghuo\niceapi\fpx\model\Fpx4OrderQueryResponse.class
com\xinghuo\allegro\sale\model\offer\AllegroOfferModel.class
com\xinghuo\allegro\push\model\ean\EanPagination.class
com\xinghuo\fruugo\data\dao\FruugoDataSellerMapper.class
com\xinghuo\allegro\manage\controller\TranslateController.class
com\xinghuo\niceapi\fpx\model\Fpx4OrderQueryResponse$ConsignmentInfo.class
com\baidu\translate\DecompressExample.class
com\xinghuo\allegro\sale\service\AllegroOfferEnService.class
com\xinghuo\niceapi\coupang\model\category\WingCategoryDto.class
com\xinghuo\ozon\msg\controller\OzonChatController.class
com\xinghuo\allegro\sale\service\StoreMoveService.class
com\xinghuo\allegro\collect\model\seller\CollectSellerModel.class
com\xinghuo\niceapi\ozon\model\product\ProductAttributeInfoRequest$Filter.class
com\xinghuo\fyndiq\data\model\FyndiqFruugoTemplateConfig$PriceStrategy.class
com\xinghuo\niceapi\ozon\model\product\ProductAttributeUpdateRequest$AttributeValue.class
com\xinghuo\niceapi\ozon\model\fbs\PackageLabelRequest.class
com\xinghuo\niceapi\ozon\model\product\ProductSubscriptionRequest.class
com\xinghuo\allegro\manage\service\AllegroService.class
com\xinghuo\ozon\manage\model\category\OzonCategoryPagination.class
com\xinghuo\allegro\order\model\order\UpdateOrderStatusForm.class
com\xinghuo\niceapi\ozon\model\fbs\ProductCancelRequest$CancelItem.class
com\xinghuo\fruugo\data\entity\FruugoDataSellerEntity.class
com\xinghuo\niceapi\yunexpress\ZtOpenapiSdkTests.class
com\xinghuo\ozon\manage\model\category\CategoryModel.class
com\xinghuo\allegro\order\model\declaration\ExpressDeclarationPagination.class
com\xinghuo\niceapi\ozon\model\returns\RfbsReturnDetailRequest$RfbsReturnDetailRequestBuilder.class
com\xinghuo\allegro\collect\service\CollectZeroOfferService.class
com\xinghuo\allegro\collect\model\collect\CollectTaskPagination.class
com\xinghuo\allegro\push\entity\AllegroPushLogEntity.class
com\xinghuo\amazon\collect\dao\AmazonListTaskMapper.class
com\xinghuo\allegro\sale\model\ActiveCodeResModel.class
com\xinghuo\niceapi\ozon\model\chat\ChatHistoryRequest.class
com\xinghuo\allegro\shelf\dao\AllegroShelfBatchSummaryMapper.class
com\xinghuo\ozon\manage\model\storeConfig\OzonStoreConfigModel.class
com\xinghuo\niceapi\allegro\message\MessageModel.class
com\xinghuo\admin\aop\MethodCountAspect.class
com\xinghuo\allegro\manage\service\SysExchangeRateService.class
com\xinghuo\fruugo\data\controller\FruugoDataProductController.class
com\xinghuo\allegro\shelf\model\log\AllegroShelfBatchSummaryPagination.class
com\xinghuo\fruugo\collect\service\impl\FruugoBoughtOfferServiceImpl.class
com\xinghuo\coupang\manage\service\impl\CoupangTemplateServiceImpl.class
com\xinghuo\allegro\manage\model\currency\RateModel.class
com\xinghuo\niceapi\ozon\model\product\ProductSubscriptionResponse.class
com\xinghuo\allegro\push\model\vioword\ViowordForm.class
com\xinghuo\fruugo\collect\controller\FruugoCollectSaleController.class
com\xinghuo\niceapi\ozon\model\category\CategoryTreeResponse$CategoryNode.class
com\xinghuo\fruugo\analysis\controller\FruugoSkuMonitorController.class
com\xinghuo\allegro\aiservice\controller\AiPushController.class
com\xinghuo\fruugo\data\model\FruugoProductDataForm.class
com\baidu\translate\ImageCompare.class
com\xinghuo\allegro\data\model\sale\AllegroSaleForm.class
com\xinghuo\niceapi\ozon\model\product\ProductInfoListResponse$ProductSource.class
com\xinghuo\fruugo\collect\model\FruugoBoughtOfferPagination.class
com\xinghuo\niceapi\allegro\message\MsgMessageAttachmentModel.class
com\xinghuo\dmj\shop\model\ShopBindingResponseModel.class
com\xinghuo\niceapi\ozon\model\product\ProductInfoListResponse$PriceIndexData.class
com\xinghuo\fruugo\collect\model\FruugoCollectSalePagination.class
com\xinghuo\niceapi\ozon\model\product\ProductImportBySkuResult.class
com\xinghuo\niceapi\ozon\model\price\DiscountedProductInfoResponse.class
com\xinghuo\fruugo\collect\entity\FruugoSellerEntity.class
com\xinghuo\fyndiq\data\controller\FyndiqDataProductController.class
com\xinghuo\fruugo\collect\entity\FruugoProductSkuEntity.class
com\xinghuo\ozon\offer\model\vo\OzonCategoryAttributeVO$DictionaryValue.class
com\xinghuo\allegro\order\service\impl\OrderDeliveryServiceImpl.class
com\xinghuo\allegro\collect\model\analysis\TaskStatusSummaryModel.class
com\xinghuo\allegro\push\controller\BrandController.class
com\xinghuo\niceapi\ozon\constants\OzonApiConstants$ProductVisibility.class
com\xinghuo\allegro\manage\entity\TranslateAccountEntity.class
com\xinghuo\allegro\order\model\profit\OrderProfitStatVO$OrderProfitStatVOBuilder.class
com\xinghuo\allegro\push\dao\AllegroCategoryParameterMapper.class
com\xinghuo\allegro\sale\entity\SyncTimeEntity.class
com\xinghuo\allegro\manage\controller\ExportTaskController.class
com\xinghuo\niceapi\allegro\message\MessageThreadModel.class
com\xinghuo\fruugo\data\service\FruugoDataProductService.class
com\xinghuo\tenant\model\RuleModelList.class
com\xinghuo\niceapi\ozon\model\fbs\UnfulfilledPostingsRequest$UnfulfilledPostingsRequestBuilder.class
com\xinghuo\allegro\sale\model\product\ProductModel$TecdocItem.class
com\xinghuo\allegro\manage\service\TranslateCacheService.class
com\xinghuo\niceapi\yunexpress\model\YtOrderDetailVo.class
com\xinghuo\allegro\push\model\erp\ErpProductUpdateModel.class
com\xinghuo\allegro\home\model\QuotationStatModel.class
com\xinghuo\allegro\order\model\authorize\ProductConfigSelectModel.class
com\xinghuo\fruugo\collect\model\FruugoCollectSaleModel.class
com\xinghuo\niceapi\fpx\model\Fpx4LabelResponse.class
com\xinghuo\allegro\collect\model\seller\CollectSellerForm.class
com\xinghuo\allegro\order\service\ExpressService.class
com\xinghuo\allegro\sale\service\SyncTaskService.class
com\xinghuo\allegro\collect\dao\CollectOfferMapper.class
com\xinghuo\amazon\collect\dao\AmazonPageTaskMapper.class
com\xinghuo\admin\aop\AsyncConfig.class
com\xinghuo\fyndiq\manage\model\category\FyndiqCategoryForm.class
com\xinghuo\fruugo\data\model\AllegroProductModel.class
com\xinghuo\niceapi\coupang\api\test\CoupangReturnShippingExample.class
com\xinghuo\fruugo\collect\dao\FruugoProductMapper.class
com\xinghuo\allegro\msg\model\message\MsgThreadModel.class
com\xinghuo\allegro\sale\model\offer\RelatedAllegroOfferModel.class
com\xinghuo\fruugo\analysis\model\FruugoMonitorVO$ProductStats.class
com\xinghuo\niceapi\ozon\model\fbs\UnfulfilledPostingsResponse$Product.class
com\xinghuo\allegro\manage\service\impl\AllegroBillTypeServiceImpl.class
com\xinghuo\niceapi\ozon\model\cancellation\ConditionalCancellationListResponse$CancellationDetail.class
com\xinghuo\niceapi\ozon\constants\OzonApiConstants$Returns.class
com\xinghuo\ozon\manage\dao\OzonCategoryMapper.class
com\xinghuo\allegro\order\service\RefundItemService.class
com\xinghuo\niceapi\coupang\model\product\CoupangProductCreateRequest$Content.class
com\xinghuo\allegro\manage\service\AllegroApiClientService.class
com\xinghuo\allegro\push\dao\AllegroCategoryMapper.class
com\xinghuo\allegro\manage\entity\AllegroStoreUserEntity.class
com\xinghuo\allegro\data\dao\AllegroDataOfferDao.class
com\xinghuo\allegro\shelf\entity\PushOfferLinkEntity.class
com\xinghuo\coupang\offer\model\CoupangOfferSumModel.class
com\xinghuo\niceapi\ozon\model\delivery\TrackingNumberRequest$TrackingNumberRequestBuilder.class
com\xinghuo\allegro\collect\model\collect\CollectOfferParsePagination.class
com\xinghuo\niceapi\coupang\api\CoupangProductApi.class
com\xinghuo\niceapi\ozon\model\cancellation\ConditionalCancellationResponse$Result.class
com\xinghuo\fruugo\analysis\model\FruugoAlertLogModel.class
com\xinghuo\allegro\msg\service\impl\MsgTemplateServiceImpl.class
com\xinghuo\allegro\msg\service\impl\MsgThreadServiceImpl.class
com\xinghuo\coupang\manage\controller\CoupangProductController.class
com\xinghuo\niceapi\ozon\fbs\OzonFBSAPI.class
com\xinghuo\visualdev\portal\model\EmailVO.class
com\xinghuo\allegro\util\AllegroConstant.class
com\xinghuo\collect\box\service\CollectBoxService.class
com\xinghuo\niceapi\fpx\model\create\ReturnInfo$ReturnInfoBuilder.class
com\xinghuo\niceapi\allegro\product\ProductResModel.class
com\baidu\translate\AllegroToFruggoConverter$AllegroProduct.class
com\xinghuo\allegro\collect\model\analysis\InProgressTaskModel.class
com\xinghuo\niceapi\ozon\model\product\ProductUnarchiveRequest.class
com\xinghuo\niceapi\ozon\model\fbs\UnfulfilledPostingsRequest$Filter.class
com\xinghuo\niceapi\allegro\message\DisputeModel.class
com\xinghuo\niceapi\ozon\model\fbs\UnfulfilledPostingsRequest$With$WithBuilder.class
com\xinghuo\analy\entity\AnalyOfferInfoEntity.class
com\xinghuo\niceapi\ozon\model\fbs\AwaitingDeliveryRequest$AwaitingDeliveryRequestBuilder.class
com\xinghuo\niceapi\yunexpress\model\CreatePackageResponse.class
com\xinghuo\allegro\msg\entity\MsgAttachmentEntity.class
com\xinghuo\allegro\home\model\MsgDeskStatModel$MsgDeskStatModelBuilder.class
com\xinghuo\allegro\collect\model\collect\CollectLinkModel.class
com\xinghuo\allegro\collect\model\collectOffer\SumForm.class
com\xinghuo\allegro\sale\service\SyncTaskService$TaskType.class
com\xinghuo\allegro\manage\model\store\StoreNoteForm.class
com\xinghuo\fruugo\analysis\dao\FruugoAlertLogMapper.class
com\xinghuo\niceapi\ozon\model\fbs\UnfulfilledPostingsResponse$TarifficationInfo.class
com\xinghuo\allegro\msg\model\dispute\MessagesStatusEnum.class
com\xinghuo\allegro\order\model\address\ExpressAddressSelectorModel.class
com\xinghuo\niceapi\ozon\model\promo\PromoProductsRequest$PromoProductsRequestBuilder.class
com\xinghuo\allegro\aiservice\dao\ServiceTaskMapper.class
com\xinghuo\allegro\order\service\impl\LabelServiceImpl.class
com\xinghuo\niceapi\ozon\model\product\ProductArchiveRequest.class
com\baidu\translate\HttpGet.class
com\xinghuo\allegro\manage\model\ApiClientModel.class
com\xinghuo\niceapi\ozon\model\product\ProductInfoListResponse$ProductStatus.class
com\xinghuo\allegro\sale\model\product\OfferParamModel$Value.class
com\xinghuo\allegro\sale\model\task\TaskItemForm.class
com\xinghuo\allegro\manage\model\store\AllegroStoreFeeModel.class
com\xinghuo\allegro\sale\dao\BillingEntryMapper.class
com\xinghuo\allegro\push\model\vioword\ViowordModel.class
com\xinghuo\fruugo\analysis\entity\FruugoMonitorEntity.class
com\xinghuo\tenant\util\ReaderUtil.class
com\xinghuo\collect\box\model\CollectBoxForm.class
com\xinghuo\fruugo\collect\model\FruugoProductPagination.class
com\xinghuo\fruugo\collect\service\FruugoProductSyncService.class
com\xinghuo\niceapi\ozon\model\product\ProductImportRequest$ComplexAttribute.class
com\xinghuo\allegro\stock\service\StockService.class
com\xinghuo\niceapi\allegro\sale\model\OfferEventResponse$OfferEvent.class
com\xinghuo\allegro\order\model\order\DeliveryModel.class
com\xinghuo\niceapi\ozon\model\product\ProductAttributeUpdateRequest.class
com\xinghuo\allegro\collect\dao\CollectConfigMapper.class
com\xinghuo\niceapi\coupang\model\category\CategoryMetaDTO$RequiredDocument.class
com\xinghuo\fruugo\data\model\FruugoSkuDataForm.class
com\xinghuo\niceapi\allegro\product\NewProductAPI.class
com\xinghuo\fruugo\manage\service\impl\FruugoShelfTemplateServiceImpl.class
com\xinghuo\allegro\order\service\ExpressAuthorizeService.class
com\xinghuo\allegro\sale\model\product\ProductModel$OfferRequirements.class
com\xinghuo\niceapi\ozon\model\fbs\UnfulfilledPostingsResponse$Posting.class
com\xinghuo\allegro\collect\service\CollectOfferService.class
com\xinghuo\fruugo\analysis\model\ProductDetailVO.class
com\xinghuo\niceapi\ozon\model\product\ProductInfoResponse$StockInfo.class
com\xinghuo\allegro\push\entity\ShelfTemplateEntity.class
com\xinghuo\dmj\shop\model\ShopBindingForm.class
com\xinghuo\allegro\order\dao\ExpressProductConfigMapper.class
com\xinghuo\allegro\order\model\express\ExpressModel.class
com\xinghuo\coupang\manage\service\impl\CoupangProductServiceImpl.class
com\xinghuo\allegro\push\service\impl\PushOfferServiceImpl.class
com\xinghuo\tenant\util\TenantDbService.class
com\xinghuo\niceapi\coupang\model\category\CategoryMetaDTO$NoticeCategoryDetailMeta.class
com\baidu\translate\util\Utf8mb4FilterRegex.class
com\xinghuo\allegro\collect\dao\CollectEventMapper.class
com\xinghuo\allegro\push\model\offer\PushOfferBatchForm.class
com\xinghuo\analy\entity\AnalyStoreInfoEntity.class
com\xinghuo\allegro\msg\model\message\MessageStatusEnum.class
com\xinghuo\allegro\home\entity\IndexDataEntity.class
com\xinghuo\allegro\push\service\ErpProductVioService.class
com\xinghuo\fruugo\analysis\model\AlertStatsModel$TypeStatItem.class
com\xinghuo\coupang\offer\entity\CoupangOfferEntity.class
com\xinghuo\allegro\home\model\TeamInfoModel$TeamInfoModelBuilder.class
com\xinghuo\allegro\order\dao\ExpressWaybillMapper.class
com\xinghuo\allegro\order\model\rule\ExpressRuleModel.class
com\xinghuo\niceapi\ozon\model\product\ProductRatingBySkuResponse$RatingCondition.class
com\xinghuo\ozon\manage\dao\OzonShippingFeeRulesMapper.class
com\xinghuo\niceapi\coupang\model\shipping\ReturnShippingCenterResponse$PlaceAddress.class
com\xinghuo\niceapi\ozon\model\cancellation\ConditionalCancellationListRequest$Filters.class
com\xinghuo\niceapi\fpx\Fpx4Client$7.class
com\xinghuo\allegro\collect\service\impl\CollectTaskServiceImpl.class
com\xinghuo\niceapi\ozon\model\returns\RfbsReturnCompensateRequest$RfbsReturnCompensateRequestBuilder.class
com\xinghuo\niceapi\fpx\model\Fpx4OrderQueryResponse$ConsignmentDetail.class
com\xinghuo\ozon\manage\model\StoreConfigModel.class
com\xinghuo\allegro\manage\model\store\StoreGroupModel$StoreSelectModel.class
com\xinghuo\allegro\push\model\shelfTemplate\ShelfTemplateDetailModel.class
com\xinghuo\niceapi\coupang\product\model\ProductListRequest$ProductListRequestBuilder.class
com\xinghuo\ozon\msg\model\OzonChatThreadPagination.class
com\xinghuo\allegro\msg\service\impl\MsgMessageServiceImpl.class
com\xinghuo\allegro\order\entity\ExpressProductEntity.class
com\xinghuo\niceapi\ozon\constants\OzonChatConstants$ChatType.class
com\xinghuo\ozon\manage\dao\OzonCollectOfferMapper.class
com\xinghuo\allegro\msg\model\dispute\DisputeMessageModel.class
com\xinghuo\niceapi\coupang\api\CoupangCategoryApi$2.class
com\xinghuo\ozon\manage\entity\OzonCategoryAttributeEntity.class
com\xinghuo\niceapi\ozon\model\price\ProductPriceUpdateResponse.class
com\xinghuo\niceapi\ozon\model\product\ProductPicturesResponse.class
com\xinghuo\ozon\manage\service\impl\OzonCategoryAttributeValueServiceImpl.class
com\xinghuo\niceapi\ozon\model\product\ProductLimitResponse$DailyLimit.class
com\xinghuo\allegro\sale\service\AllegroOfferEndEventService.class
com\xinghuo\fyndiq\data\dao\FyndiqDataProductMapper.class
com\xinghuo\allegro\push\service\impl\ProducerServiceImpl.class
com\xinghuo\coupang\manage\model\product\CoupangProductForm.class
com\xinghuo\allegro\collect\entity\CollectZeroOfferEntity.class
com\xinghuo\allegro\msg\model\rating\MsgRatingModel.class
com\xinghuo\niceapi\ozon\model\promo\PromoProductsResponse$PromoProduct.class
com\xinghuo\allegro\push\model\offer\CopySingleSellerForm.class
com\xinghuo\coupang\manage\controller\CoupangMetaController.class
com\xinghuo\niceapi\ozon\model\delivery\TrackingNumberRequest$TrackingNumberItem.class
com\xinghuo\fyndiq\data\service\FyndiqDataSkuService.class
com\xinghuo\niceapi\coupang\CoupangBaseApi.class
com\xinghuo\niceapi\coupang\model\category\CategoryResponse.class
com\xinghuo\niceapi\ozon\model\promo\PromoTaskDeclineRequest$PromoTaskDeclineRequestBuilder.class
com\xinghuo\fruugo\analysis\service\impl\FruugoSellerMonitorServiceImpl.class
com\xinghuo\fruugo\data\model\FruugoDataProductStatVO.class
com\xinghuo\allegro\push\model\erp\ErpProductStatusEnum.class
com\xinghuo\fruugo\analysis\model\StoreRankingVO.class
com\xinghuo\allegro\collect\model\collectOffer\CollectOfferModel.class
com\xinghuo\fruugo\analysis\controller\FruugoAnalysisController.class
com\xinghuo\allegro\collect\controller\CollectEventController.class
com\xinghuo\fruugo\collect\controller\FruugoSellerController.class
com\xinghuo\allegro\sale\service\AllegroOfferService.class
com\xinghuo\visualdev\portal\model\NoticeVO.class
com\xinghuo\allegro\order\model\order\OrderRemarkForm.class
com\xinghuo\fruugo\manage\model\shelfTemplate\FruugoShelfTemplateModel.class
com\xinghuo\allegro\msg\model\message\MessagePagination.class
com\xinghuo\ozon\msg\dao\OzonChatMessageMapper.class
com\xinghuo\allegro\shelf\entity\AllegroShelfBatchSummaryEntity.class
com\xinghuo\fyndiq\data\service\FyndiqExportFruugoService.class
com\xinghuo\allegro\sale\model\product\ParameterModel$RangeValue.class
com\xinghuo\amazon\collect\model\list\AmazonListTaskModel.class
com\xinghuo\niceapi\ozon\model\promo\HotSalesRequest$HotSalesRequestBuilder.class
com\xinghuo\allegro\collect\model\config\ExpressConfigModel.class
com\xinghuo\allegro\order\service\impl\OrderBuyerServiceImpl.class
com\xinghuo\niceapi\alibaba\AlibabaAPI.class
com\xinghuo\niceapi\ozon\constants\OzonApiConstants$Stock.class
com\xinghuo\amazon\test\AmazonHtmlParseTest$ProductInfo.class
com\xinghuo\ozon\manage\entity\OzonCategoryAttributeValueEntity.class
com\xinghuo\allegro\order\service\impl\ExpressProductConfigServiceImpl.class
com\xinghuo\allegro\sale\model\product\ProductModel$Parameter.class
com\xinghuo\allegro\order\entity\ExpressDeclarationEntity.class
com\xinghuo\fruugo\collect\entity\FruugoCollectSaleEntity.class
com\xinghuo\allegro\stock\model\StockForm.class
com\xinghuo\fruugo\analysis\service\FruugoSellerMonitorService.class
com\xinghuo\niceapi\allegro\sale\OfferPublicationAPI.class
com\xinghuo\allegro\data\model\seller\AllegroSellerStatsVO.class
com\xinghuo\allegro\push\model\brand\BrandModel.class
com\xinghuo\allegro\sale\controller\AllegroBillController.class
com\xinghuo\amazon\collect\entity\AmazonPageTaskEntity.class
com\xinghuo\niceapi\yunexpress\model\CreatePackageRequest$DeclarationInfo.class
com\xinghuo\fruugo\analysis\model\FruugoDataMonitorVO$ProductStats.class
com\xinghuo\niceapi\ozon\exception\OzonApiException.class
com\xinghuo\niceapi\ozon\model\promo\PromoProductsActivateRequest$PromoProduct.class
com\xinghuo\allegro\sale\service\impl\SyncTimeServiceImpl.class
com\xinghuo\fruugo\collect\model\FruugoProductSkuModel.class
com\xinghuo\niceapi\ozon\model\returns\RfbsReceiveReturnRequest.class
com\xinghuo\allegro\data\controller\AllegroDataSaleController.class
com\xinghuo\niceapi\allegro\message\ImageUtil.class
com\xinghuo\fruugo\manage\model\category\FruugoCategoryProductPagination.class
com\xinghuo\niceapi\coupang\model\product\ProductDetailResponse$ProductNotice.class
com\xinghuo\niceapi\ozon\model\fbs\PostingsRequest$LastChangedStatusDate.class
com\xinghuo\fruugo\collect\service\impl\FruugoCollectOfferServiceImpl.class
com\xinghuo\niceapi\fpx\model\create\DeliverTypeInfo$DeliverTypeInfoBuilder.class
com\xinghuo\niceapi\ozon\model\fbs\UnfulfilledPostingsResponse$Cancellation.class
com\xinghuo\niceapi\yanwenexpress\model\PackageRequest$ParcelInfo.class
com\xinghuo\niceapi\ozon\model\stock\UpdateStockRequest.class
com\xinghuo\allegro\sale\dao\SyncTaskItemMapper.class
com\baidu\translate\MD5.class
com\xinghuo\allegro\manage\service\impl\AllegroStoreServiceImpl.class
com\xinghuo\collect\box\controller\CollectBoxController$FruugoSkuInfo.class
com\xinghuo\niceapi\ozon\model\product\ProductRatingBySkuResponse$RatingGroup.class
com\xinghuo\allegro\sale\service\AllegroNewProductService.class
com\xinghuo\niceapi\ozon\model\fbs\PostingCancelReasonRequest.class
com\xinghuo\admin\util\JsonTest.class
com\xinghuo\allegro\push\model\offer\PushOfferActionEnum.class
com\xinghuo\allegro\sale\model\lts\LisModel.class
com\xinghuo\admin\util\PermissionAspectUtil.class
com\xinghuo\allegro\manage\service\impl\SysExchangeRateServiceImpl.class
com\xinghuo\allegro\sale\service\impl\AllegroOfferEndEventServiceImpl.class
com\xinghuo\niceapi\ozon\model\chat\ChatRequest.class
com\xinghuo\ozon\offer\service\impl\OzonOfferSyncServiceImpl.class
com\xinghuo\allegro\order\service\impl\OrderShipmentServiceImpl.class
com\xinghuo\allegro\home\model\StoreCheckModel.class
com\xinghuo\allegro\push\model\brand\BrandForm.class
com\xinghuo\allegro\manage\model\store\AllegroStoreFeeForm.class
com\xinghuo\allegro\collect\entity\CollectTaskEntity.class
com\xinghuo\niceapi\coupang\exception\CoupangApiException.class
com\xinghuo\niceapi\ozon\model\cancellation\ConditionalCancellationListRequest$ConditionalCancellationListRequestBuilder.class
com\baidu\translate\UniqueWordsExtractor.class
com\xinghuo\allegro\home\model\SalesStatModel.class
com\xinghuo\fruugo\analysis\service\FruugoSalesMonitorService.class
com\xinghuo\niceapi\coupang\api\CoupangCategoryMetaApi$1.class
com\xinghuo\allegro\push\controller\SellerController.class
com\xinghuo\allegro\data\service\AllegroSellerAnalysisService.class
com\xinghuo\allegro\order\model\profit\ProfitSummaryVO$ProfitSummaryVOBuilder.class
com\xinghuo\coupang\offer\controller\CoupangOfferController.class
com\xinghuo\coupang\offer\model\CoupangOfferForm.class
com\xinghuo\allegro\msg\model\rating\NewRatingForm.class
com\xinghuo\niceapi\ozon\model\product\ProductInfoListRequest.class
com\xinghuo\fruugo\manage\dao\FruugoStoreMapper.class
com\xinghuo\fruugo\analysis\service\impl\FruugoDataMonitorServiceImpl.class
com\xinghuo\ozon\manage\model\product\ShippingRuleUtil.class
com\xinghuo\niceapi\ozon\model\returns\RfbsReturnDetailResponse$Returns.class
com\xinghuo\niceapi\yunexpress\TokenManager.class
com\xinghuo\allegro\push\service\AllegroCategoryService.class
com\xinghuo\fruugo\collect\model\FruugoSellerForm.class
com\xinghuo\niceapi\yanwenexpress\model\PackageRequest$SenderInfo.class
com\xinghuo\fruugo\analysis\service\impl\FruugoMonitorServiceImpl.class
com\xinghuo\niceapi\ozon\model\product\ProductAttributeInfoResponse$ProductAttributeInfo.class
com\xinghuo\allegro\manage\service\impl\ExportTaskServiceImpl.class
com\xinghuo\niceapi\ozon\model\promo\HotSalesActivateRequest$HotSalesActivateRequestBuilder.class
com\xinghuo\admin\aop\PermissionOrgAspect.class
com\xinghuo\niceapi\coupang\model\product\ProductDetailResponse$ProductContent.class
com\xinghuo\fruugo\analysis\model\FruugoAlertLogPagination.class
com\xinghuo\allegro\order\service\OrderShipmentService.class
com\xinghuo\fruugo\manage\model\category\FruugoCategoryProductModel.class
com\xinghuo\niceapi\ozon\constants\OzonChatConstants$Endpoints.class
com\xinghuo\niceapi\allegro\order\model\ShipmentModel.class
com\xinghuo\ozon\manage\model\storeConfig\OzonStoreConfigPagination.class
com\xinghuo\allegro\msg\model\dispute\MsgDisputeDetailModel.class
com\xinghuo\allegro\push\dao\CollectVioWordMapper.class
com\xinghuo\niceapi\allegro\product\CategoryResponseModel.class
com\xinghuo\niceapi\allegro\sale\OfferTranslationAPI$OfferDescriptionTranslation.class
com\xinghuo\niceapi\ozon\model\stock\FBSStocksByWarehouseResponse.class
com\xinghuo\niceapi\fpx\Fpx4Client$2.class
com\xinghuo\fruugo\manage\model\category\FruugoCategoryModel.class
com\xinghuo\niceapi\ozon\product\OzonPriceAPI.class
com\xinghuo\fruugo\data\model\FruugoDataSellerVO.class
com\xinghuo\niceapi\ozon\model\product\ProductImportResponse.class
com\xinghuo\niceapi\allegro\account\AccountAPI.class
com\xinghuo\niceapi\coupang\api\CoupangWingApiClient.class
com\xinghuo\niceapi\yunexpress\model\YunexpressUtil.class
com\xinghuo\niceapi\ozon\model\fbs\PostingCancelReasonRequest$PostingCancelReasonRequestBuilder.class
com\xinghuo\fyndiq\data\service\FyndiqExportService.class
com\xinghuo\allegro\shelf\model\link\OfferLinkModel.class
com\xinghuo\allegro\sale\service\impl\AllegroNewProductServiceImpl.class
com\xinghuo\allegro\sale\model\task\TaskItemModel.class
com\xinghuo\niceapi\coupang\api\CoupangWingApiClient$1.class
com\xinghuo\allegro\home\model\VioStatModel$Metrics.class
com\xinghuo\niceapi\ozon\model\price\ProductPriceUpdateResponse$PriceUpdateError.class
com\xinghuo\allegro\collect\model\collect\CollectTaskModel.class
com\xinghuo\allegro\manage\service\AllegroStoreService.class
com\xinghuo\fruugo\analysis\controller\FruugoMonitorController.class
com\xinghuo\allegro\sale\service\impl\BillingEntryServiceImpl.class
com\xinghuo\allegro\order\model\yuntu\YanwenExpressAuthorizeForm.class
com\xinghuo\fruugo\collect\controller\FruugoClaimTaskController.class
com\xinghuo\niceapi\ozon\model\cancellation\ApproveCancellationRequest.class
com\xinghuo\fruugo\collect\controller\FruugoProductController.class
com\xinghuo\niceapi\allegro\message\RatingModel.class
com\xinghuo\fruugo\collect\model\FruugoPushOfferModel.class
com\xinghuo\allegro\order\entity\OrderItemEntity.class
com\xinghuo\niceapi\fpx\model\Fpx4LabelResponse$LabelUrlInfo.class
com\xinghuo\niceapi\ozon\model\product\ProductInfoListResponse$VisibilityDetails.class
com\xinghuo\ozon\manage\service\impl\OzonShippingFeeRulesServiceImpl.class
com\xinghuo\allegro\sale\model\task\TaskForm.class
com\xinghuo\niceapi\allegro\sale\OfferTranslationAPI.class
com\xinghuo\allegro\manage\service\impl\AllegroStoreUserServiceImpl.class
com\xinghuo\allegro\order\model\express\ExpressPagination.class
com\xinghuo\allegro\sale\model\product\ProductModel$AiCoCreatedContent.class
com\xinghuo\dmj\shop\controller\ShopBindingApiController.class
com\xinghuo\fruugo\analysis\controller\FruugoAlertLogController.class
com\xinghuo\allegro\push\dao\ErpProductMapper.class
com\xinghuo\allegro\push\dao\EanMapper.class
com\xinghuo\allegro\manage\service\translate\DeepLXTranslateAdapter.class
com\xinghuo\allegro\push\service\ShelfTemplateService.class
com\xinghuo\niceapi\fpx\Fpx4Client$3.class
com\xinghuo\allegro\order\model\order\OrderSumModel.class
com\xinghuo\fruugo\collect\entity\FruugoClaimTaskEntity.class
com\xinghuo\fyndiq\data\service\impl\FyndiqExportFruugoServiceImpl.class
com\xinghuo\allegro\order\controller\ExpressAddressController.class
com\xinghuo\allegro\data\service\AllegroProductService.class
com\xinghuo\admin\util\BaseServiceUtil$1.class
com\xinghuo\niceapi\ozon\model\returns\RfbsReturnDetailResponse.class
com\xinghuo\fruugo\manage\model\shelfTemplate\FruugoShelfTemplatePagination.class
com\xinghuo\niceapi\fpx\model\Fpx4LabelRequest.class
com\xinghuo\niceapi\fpx\Fpx4Client$6.class
com\baidu\translate\AllegroToFruggoConverter$Parameter.class
com\xinghuo\niceapi\ozon\model\fbs\ProductWeightChangeRequest.class
com\xinghuo\niceapi\yunexpress\model\CreatePackageRequest$Package.class
com\xinghuo\niceapi\ozon\model\product\ProductAttributeInfoResponse$ProductImage.class
com\xinghuo\allegro\msg\model\dispute\StatusEnum.class
com\xinghuo\allegro\home\model\MsgDeskStatModel$TodayStats.class
com\xinghuo\allegro\msg\service\impl\DisputeMessageServiceImpl.class
com\xinghuo\allegro\order\service\ExpressDeclarationService.class
com\xinghuo\coupang\manage\service\impl\CoupangCategoryServiceImpl.class
com\xinghuo\niceapi\ozon\model\fbs\UnfulfilledPostingsResponse$Barcodes.class
com\xinghuo\fruugo\manage\entity\FruugoShelfTemplateEntity.class
com\xinghuo\allegro\stock\model\StockPagination.class
com\xinghuo\niceapi\ozon\model\promo\PromoTaskResponse.class
com\xinghuo\niceapi\ozon\model\product\ProductAttributeUpdateResponse.class
com\xinghuo\niceapi\ozon\model\product\ProductInfoResponse$ProductStatus.class
com\xinghuo\fruugo\collect\controller\FruugoCollectOfferController.class
com\xinghuo\niceapi\ozon\model\cancellation\RejectCancellationRequest.class
com\xinghuo\allegro\sale\model\task\TaskResModel.class
com\xinghuo\niceapi\ozon\model\fbs\UnfulfilledPostingsResponse$PostingServices.class
com\xinghuo\allegro\sale\service\impl\AllegroNewProductEnServiceImpl.class
com\xinghuo\dmj\shop\service\ShopBindingService.class
com\xinghuo\allegro\aiservice\model\ServiceTaskModel.class
com\xinghuo\niceapi\allegro\AllegroBaseAPI.class
com\baidu\translate\AllegroToFruggoConverter$Description.class
com\xinghuo\niceapi\fpx\model\create\InsuranceInfo$InsuranceInfoBuilder.class
com\xinghuo\niceapi\fpx\model\Fpx4OrderQueryRequest.class
com\xinghuo\allegro\sale\entity\AllegroOfferEnEntity.class
com\xinghuo\niceapi\ozon\model\product\ProductInfoListResponse$ProductInfoListItem.class
com\xinghuo\allegro\data\model\seller\AllegroSellerModel.class
com\xinghuo\niceapi\ozon\model\promo\HotSalesRequest.class
com\baidu\translate\AllegroToFruggoConverter$Image.class
com\xinghuo\allegro\sale\model\product\ProductModel$TecdocSpecification.class
com\xinghuo\allegro\sale\dao\SyncTaskMapper.class
com\xinghuo\ozon\offer\service\OzonOfferSyncService.class
com\xinghuo\allegro\stock\service\impl\StockServiceImpl.class
com\xinghuo\allegro\manage\model\fourpx\AccessTokenResponse.class
com\xinghuo\allegro\manage\model\store\AllegroStoreSettingModel.class
com\xinghuo\ozon\manage\service\OzonDataMigrationService.class
com\xinghuo\allegro\collect\model\collectOffer\CollectSettingModel$CompanyMode.class
com\xinghuo\fruugo\collect\model\FruugoSellerStatsVO.class
com\xinghuo\niceapi\ozon\model\cancellation\ConditionalCancellationListRequest$Filters$FiltersBuilder.class
com\xinghuo\allegro\msg\model\message\MsgMessageModel.class
com\xinghuo\niceapi\ozon\model\product\ProductImportBySkuResponse$Result.class
com\xinghuo\allegro\data\entity\AllegroSellerEntity.class
com\xinghuo\admin\aop\PermissionAdminBase.class
com\xinghuo\allegro\order\dao\OrderProfitMapper.class
com\xinghuo\fruugo\collect\dao\FruugoBoughtOfferMapper.class
com\xinghuo\coupang\manage\model\storeConfig\CoupangStoreConfigModel.class
com\xinghuo\niceapi\ozon\model\product\ProductImportInfoResponse.class
com\xinghuo\coupang\offer\model\CoupangOfferModel.class
com\xinghuo\amazon\collect\service\AmazonListTaskService.class
com\xinghuo\allegro\push\model\ean\EanBatchForm.class
com\xinghuo\admin\XhAdminApplication.class
com\xinghuo\allegro\collect\controller\CollectAccountController.class
com\xinghuo\allegro\sale\model\offer\OfferStatusEnum.class
com\xinghuo\allegro\sale\entity\StoreMoveEntity.class
com\xinghuo\allegro\aiservice\service\ServiceTaskService.class
com\xinghuo\allegro\sale\dao\SyncTimeMapper.class
com\xinghuo\coupang\manage\model\storeConfig\CoupangStoreConfigPagination.class
com\xinghuo\allegro\manage\service\translate\TranslateAdapter.class
com\xinghuo\niceapi\coupang\model\shipping\ReturnShippingCenterResponse$Pagination.class
com\xinghuo\allegro\push\model\erp\ErpAllegroOfferModel.class
com\xinghuo\coupang\manage\model\category\CoupangCategoryMetaVO.class
com\xinghuo\allegro\manage\model\currency\CurrencyModel.class
com\xinghuo\allegro\manage\model\store\AllegroStoreDetailModel.class
com\xinghuo\coupang\manage\model\template\CoupangTemplateForm.class
com\xinghuo\ozon\manage\service\OzonCollectOfferService.class
com\xinghuo\niceapi\fpx\model\create\ParcelInfo.class
com\xinghuo\allegro\push\service\PushBatchService.class
com\xinghuo\niceapi\ozon\model\OzonBaseResponse$ErrorDetail.class
com\xinghuo\allegro\home\model\DetailStatModel$DetailStatModelBuilder.class
com\xinghuo\allegro\order\model\order\OrderExportModel.class
com\xinghuo\niceapi\fpx\model\Fpx4LogisticsProduct.class
com\xinghuo\niceapi\ozon\model\promo\PromoCandidatesRequest.class
com\xinghuo\niceapi\yanwenexpress\model\PackageResponse.class
com\xinghuo\allegro\home\service\impl\IndexDataServiceImpl.class
com\xinghuo\niceapi\ozon\model\product\ProductLimitResponse$TotalLimit.class
com\xinghuo\niceapi\ozon\model\product\ProductInfoListResponse$ProductError.class
com\xinghuo\fruugo\manage\model\category\FruugoCategorySkuModel.class
com\xinghuo\dmj\shop\controller\ShopBindingManageController.class
com\xinghuo\niceapi\ozon\delivery\OzonDeliveryAPI.class
com\xinghuo\allegro\manage\dao\TranslateUsageLogMapper.class
com\xinghuo\niceapi\ozon\model\product\UpdateProductOfferIdRequest.class
com\xinghuo\fyndiq\manage\controller\FyndiqCategoryController.class
com\xinghuo\niceapi\fpx\model\Fpx4OrderCancelRequest.class
com\xinghuo\allegro\manage\service\AllegroStoreUserService.class
com\xinghuo\fruugo\manage\service\impl\FruugoStoreServiceImpl.class
com\xinghuo\allegro\order\model\order\OrderErpStatusEnum.class
com\xinghuo\allegro\sale\model\offer\OfferActivateModel.class
com\xinghuo\niceapi\fpx\Fpx4Client$8.class
com\xinghuo\niceapi\ozon\model\returns\RfbsReturnsListRequest$Filter.class
com\xinghuo\niceapi\ozon\model\OzonBaseResponse.class
com\xinghuo\allegro\data\controller\AllegroDataSellerController.class
com\xinghuo\allegro\msg\controller\RatingController.class
com\xinghuo\allegro\order\dao\LabelMapper.class
com\xinghuo\allegro\data\controller\AllegroDataOfferController.class
com\xinghuo\allegro\collect\model\collectOffer\SumModel.class
com\xinghuo\allegro\manage\dao\AllegroApiClientMapper.class
com\xinghuo\niceapi\ozon\model\fbs\PostingsRequest$LastChangedStatusDate$LastChangedStatusDateBuilder.class
com\xinghuo\ozon\manage\dao\OzonCategoryAttributeMapper.class
com\xinghuo\niceapi\coupang\api\CoupangProductApi$1.class
com\xinghuo\ozon\offer\entity\OzonOfferEntity.class
com\xinghuo\niceapi\coupang\model\category\CategoryMetaDTO$CertificationMeta.class
com\xinghuo\fruugo\analysis\model\FruugoMonitorVO$CollectSaleStats.class
com\xinghuo\fruugo\analysis\model\StoreDetailVO.class
com\xinghuo\niceapi\fpx\model\Fpx4FreightRequest.class
com\xinghuo\niceapi\ozon\model\fbs\PostingsRequest$Filter$FilterBuilder.class
com\xinghuo\fruugo\data\dao\FruugoDataSkuMapper.class
com\xinghuo\allegro\sale\model\product\CategoryParameter$ParameterCondition.class
com\xinghuo\niceapi\ozon\model\product\ProductImportInfoRequest.class
com\xinghuo\allegro\collect\service\CollectProductService.class
com\xinghuo\allegro\home\model\VioStatModel$VioStatModelBuilder.class
com\xinghuo\allegro\push\model\offer\PushOfferStatusEnum.class
com\xinghuo\amazon\collect\model\page\AmazonPageTaskPagination.class
com\baidu\translate\AllegroToFruggoConverter$Section.class
com\xinghuo\collect\box\model\CollectForm.class
com\xinghuo\allegro\collect\entity\CollectEventEntity.class
com\xinghuo\allegro\manage\entity\TranslateCacheEntity.class
com\xinghuo\collect\box\dao\CollectBoxMapper.class
com\xinghuo\allegro\data\controller\AllegroSaleController.class
com\xinghuo\allegro\data\entity\AllegroDataOfferEntity.class
com\xinghuo\allegro\msg\model\dispute\DisputePagination.class
com\xinghuo\niceapi\ozon\model\delivery\DeliveryStatusResponse$DeliveryResult.class
com\xinghuo\niceapi\fpx\model\Fpx4ErrorInfo.class
com\xinghuo\allegro\home\model\MsgDeskStatModel.class
com\xinghuo\allegro\collect\entity\CollectOfferEntity.class
com\xinghuo\allegro\home\model\DetailStatModel.class
com\xinghuo\fruugo\collect\entity\FruugoCollectOfferEntity.class
com\xinghuo\niceapi\ozon\model\product\ProductImportInfoResponse$ImportInfoItem.class
com\xinghuo\fruugo\analysis\entity\FruugoAlertLogEntity.class
com\xinghuo\ozon\manage\entity\OzonStoreEntity.class
com\xinghuo\amazon\collect\controller\AmazonPageTaskController.class
com\xinghuo\ozon\manage\dao\OzonProductMapper.class
com\xinghuo\niceapi\ozon\constants\OzonApiConstants$Limits.class
com\xinghuo\allegro\home\dao\IndexDataMapper.class
com\xinghuo\niceapi\allegro\account\AllegroStoreModel$BaseMarketplace.class
com\xinghuo\allegro\msg\dao\DisputeMessageMapper.class
com\xinghuo\coupang\manage\controller\CoupangCategoryController.class
com\xinghuo\niceapi\ozon\model\cancellation\ConditionalCancellationRequest.class
com\xinghuo\niceapi\ozon\model\product\ProductListRequest$Filter.class
com\xinghuo\allegro\push\model\category\CategoryModel.class
com\xinghuo\niceapi\allegro\sale\OfferTranslationAPI$OfferTranslationResponse.class
com\xinghuo\ozon\msg\model\OzonChatThreadListVO.class
com\xinghuo\visualdev\portal\controller\DashboardController.class
com\xinghuo\ozon\offer\model\vo\OzonAttributeValueVO$Value.class
com\xinghuo\allegro\order\controller\ExpressController.class
com\xinghuo\coupang\manage\entity\CoupangCategoryMetaEntity.class
com\xinghuo\niceapi\ozon\model\returns\RfbsReturnsListRequest$DateRange$DateRangeBuilder.class
com\xinghuo\allegro\shelf\service\impl\PushOfferLinkServiceImpl.class
com\xinghuo\niceapi\ozon\model\fbs\ProductWeightChangeResponse.class
com\xinghuo\niceapi\coupang\CoupangBaseApi$CoupangApiException.class
com\xinghuo\fyndiq\data\service\FyndiqDataProductService.class
com\xinghuo\coupang\manage\dao\CoupangProductMapper.class
com\xinghuo\allegro\sale\model\lts\LisFeeSettingModel.class
com\xinghuo\collect\box\entity\CollectBoxEntity.class
com\xinghuo\niceapi\ozon\model\product\ProductImportBySkuResponse.class
com\xinghuo\fruugo\collect\controller\FruugoPushOfferController.class
com\xinghuo\allegro\order\model\authorize\AuthorizeProductGroupVO.class
com\xinghuo\allegro\manage\model\store\AllegroStoreOperationModel.class
com\xinghuo\niceapi\ozon\model\product\ProductRatingBySkuResponse$ImproveAttribute.class
com\xinghuo\allegro\sale\dao\AllegroNewProductMapper.class
com\xinghuo\allegro\msg\service\MsgAttachmentService.class
com\xinghuo\niceapi\ozon\constants\OzonApiConstants$Delivery.class
com\xinghuo\allegro\sale\controller\AllegroOfferController.class
com\xinghuo\niceapi\ozon\model\chat\ChatResponse$Message.class
com\xinghuo\niceapi\coupang\model\shipping\ShippingPlaceResponse.class
com\xinghuo\allegro\collect\model\collectOffer\CollectSettingModel$DuplicateMode.class
com\xinghuo\allegro\sale\dao\StoreMoveMapper.class
com\xinghuo\allegro\msg\model\message\MsgThreadDetailModel.class
com\xinghuo\niceapi\ozon\model\returns\RfbsReturnRejectRequest.class
com\xinghuo\niceapi\ozon\model\price\ProductPriceUpdateRequest$PriceItem.class
com\xinghuo\niceapi\ozon\model\delivery\DeliveryStatusResponse.class
com\xinghuo\niceapi\ozon\model\product\ProductInfoListResponse$ErrorTexts.class
com\xinghuo\niceapi\ozon\model\promo\PromoCandidatesResponse$Restriction.class
com\xinghuo\allegro\push\entity\ErpGroupEntity.class
com\xinghuo\allegro\manage\controller\AlibabaAuthController.class
com\xinghuo\fruugo\collect\service\impl\FruugoClaimTaskServiceImpl.class
com\xinghuo\allegro\push\controller\AllegroCategoryController.class
com\xinghuo\allegro\push\controller\PushOfferLinkController.class
com\xinghuo\niceapi\ozon\model\category\CategoryAttributeRequest.class
com\xinghuo\fruugo\image\service\impl\FruugoImageServiceImpl.class
com\xinghuo\niceapi\ozon\OzonBaseAPI.class
com\xinghuo\allegro\sale\model\offer\OfferBatchEditModel.class
com\xinghuo\amazon\test\AmazonHtmlParseTest.class
com\xinghuo\allegro\order\service\impl\ErpOrderServiceImpl.class
com\xinghuo\fruugo\analysis\model\FruugoSkuMonitorVO.class
com\xinghuo\niceapi\ozon\model\delivery\TrackingNumberResponse$TrackingResult.class
com\xinghuo\allegro\manage\model\store\AllegroStorePagination.class
com\xinghuo\allegro\collect\service\impl\CollectProductServiceImpl.class
com\xinghuo\niceapi\ozon\model\stock\UpdateStockRequest$StockUpdate.class
com\xinghuo\allegro\aiservice\dao\TitleVariantMapper.class
com\xinghuo\niceapi\ozon\model\fbs\UnfulfilledPostingsResponse$Address.class
com\xinghuo\allegro\push\service\CollectVioWordService.class
com\xinghuo\niceapi\ozon\model\promo\PromoCandidatesResponse.class
com\xinghuo\fruugo\collect\model\FruugoPushOfferBatchForm.class
com\xinghuo\ozon\msg\service\impl\OzonChatThreadServiceImpl.class
com\xinghuo\niceapi\ozon\model\product\ProductImportInfoResponse$Result.class
com\xinghuo\allegro\manage\model\currency\RatePagination.class
com\xinghuo\allegro\order\dao\ExpressMapper.class
com\xinghuo\niceapi\allegro\sale\OfferEventAPI.class
com\xinghuo\fruugo\collect\controller\FruugoSellerAnalysisController.class
com\xinghuo\allegro\manage\model\store\UserStoreSimpleModel.class
com\xinghuo\niceapi\ozon\model\product\ProductInfoResponse.class
com\xinghuo\niceapi\ozon\model\returns\RfbsReturnMoneyRequest$RfbsReturnMoneyRequestBuilder.class
com\xinghuo\allegro\collect\model\collect\CollectRemoveForm.class
com\xinghuo\allegro\order\dao\OrderShipmentMapper.class
com\xinghuo\allegro\sale\model\offer\OfferBatchEditModel$EditRow.class
com\xinghuo\ozon\msg\model\OzonChatThreadListModel.class
com\xinghuo\allegro\order\model\order\CheckLabelForm.class
com\xinghuo\allegro\manage\model\log\LogModel.class
com\xinghuo\fruugo\manage\service\impl\FruugoCategoryServiceImpl.class
com\xinghuo\allegro\manage\service\impl\TranslateCacheServiceImpl.class
com\xinghuo\niceapi\fpx\model\Fpx4FreightRequest$Fpx4FreightRequestBuilder.class
com\xinghuo\fruugo\data\service\impl\FruugoDataSkuServiceImpl.class
com\xinghuo\allegro\shelf\service\PushOfferLinkService.class
com\xinghuo\fruugo\analysis\dao\FruugoMonitorMapper.class
com\xinghuo\fruugo\analysis\model\AlertStatsModel.class
com\xinghuo\allegro\msg\model\UploadFileModel.class
com\xinghuo\niceapi\yunexpress\model\CreatePackageRequest$CustomsNumber.class
com\xinghuo\allegro\manage\model\store\AllegroStoreDetailForm.class
com\xinghuo\niceapi\ozon\model\product\ProductInfoResponse$Commission.class
com\xinghuo\niceapi\coupang\model\shipping\ReturnShippingCenterResponse.class
com\xinghuo\allegro\sale\model\product\CategoryParameter$RequiredIf.class
com\xinghuo\niceapi\ozon\model\product\UpdateProductOfferIdResponse$OfferIdError.class
com\xinghuo\dmj\shop\entity\ShopBindingEntity.class
com\xinghuo\ozon\manage\service\impl\OzonStoreServiceImpl.class
com\xinghuo\allegro\msg\dao\MsgMessageMapper.class
com\xinghuo\allegro\order\dao\ExpressProductMapper.class
com\xinghuo\niceapi\ozon\model\fbs\PackageLabelResponse.class
com\xinghuo\niceapi\ozon\model\promo\PromoProductsActivateRequest$PromoProduct$PromoProductBuilder.class
com\xinghuo\ozon\manage\entity\OzonCollectOfferEntity.class
com\xinghuo\niceapi\JsonXhUtil2.class
com\xinghuo\allegro\collect\model\collectOffer\CollectSettingModel.class
com\xinghuo\niceapi\coupang\model\product\ProductDetailResponse$ProductImage.class
com\xinghuo\niceapi\ozon\model\product\ProductRatingBySkuResponse.class
com\xinghuo\collect\box\model\CollectBoxItemModel.class
com\xinghuo\niceapi\yanwenexpress\PackageUtil.class
com\xinghuo\niceapi\ozon\model\product\ProductImportRequest$ProductItem.class
com\xinghuo\niceapi\ozon\model\returns\RfbsReturnsListResponse$Returns.class
com\xinghuo\niceapi\ozon\model\fbs\UnfulfilledPostingsResponse$ProductFinancial.class
com\xinghuo\niceapi\ozon\model\product\ProductInfoListResponse$StockInfo.class
com\xinghuo\allegro\manage\model\translate\TranslateException.class
com\xinghuo\fyndiq\data\entity\FyndiqDataSkuEntity.class
com\xinghuo\niceapi\ozon\model\returns\RfbsReturnsListRequest$Filter$FilterBuilder.class
com\xinghuo\fruugo\collect\model\FruugoBoughtOfferModel.class
com\xinghuo\niceapi\ozon\model\promo\PromoCandidatesResponse$PromoCandidate.class
com\xinghuo\fruugo\manage\dao\FruugoCategoryMapper.class
com\xinghuo\niceapi\ozon\model\product\ProductUnarchiveResponse.class
com\xinghuo\niceapi\ozon\model\product\RelatedSkuResponse$RelatedSkuError.class
com\xinghuo\fruugo\analysis\enums\AlertTypeEnum.class
com\xinghuo\niceapi\ozon\model\fbs\CancelReasonResponse.class
com\xinghuo\coupang\manage\dao\CoupangStoreConfigMapper.class
com\xinghuo\allegro\collect\model\collectOffer\CollectSettingModel$NonDeliverySeller.class
com\xinghuo\niceapi\allegro\account\AllegroStoreModel$Company.class
com\xinghuo\admin\aop\PermissionPositionAspect.class
com\xinghuo\coupang\manage\entity\CoupangStoreEntity.class
com\xinghuo\ozon\manage\dao\ImageAliMapper.class
com\xinghuo\niceapi\allegro\sale\OfferSyncAPI$ApiException.class
com\xinghuo\fruugo\collect\service\FruugoCollectSaleService.class
com\xinghuo\allegro\order\entity\ExpressAddressEntity.class
com\xinghuo\allegro\push\service\AllegroBrandService.class
com\xinghuo\niceapi\allegro\order\OrderAPI.class
com\xinghuo\allegro\order\controller\OrderProfitController.class
com\xinghuo\fruugo\analysis\util\WechatWorkUtil.class
com\xinghuo\ozon\offer\service\impl\OzonOfferServiceImpl.class
com\xinghuo\admin\aop\PermissionAdminAspect.class
com\xinghuo\ozon\manage\service\impl\OzonCollectOfferServiceImpl.class
com\xinghuo\ozon\manage\entity\OzonShippingFeeRulesEntity.class
com\xinghuo\fruugo\analysis\model\FruugoSellerMonitorVO.class
com\xinghuo\allegro\home\model\TeamInfoModel.class
com\xinghuo\allegro\manage\service\impl\AllegroStoreConfigServiceImpl.class
com\xinghuo\fruugo\analysis\model\FruugoSalesMonitorVO.class
com\xinghuo\niceapi\coupang\model\product\ProductInflowStatusResponse.class
com\xinghuo\niceapi\ozon\model\fbs\CancelPostingResponse.class
com\xinghuo\niceapi\coupang\model\product\ProductDetailResponse$ProductItem.class
com\xinghuo\fruugo\analysis\controller\FruugoSalesRecordController.class
com\xinghuo\coupang\manage\service\impl\CoupangStoreServiceImpl.class
com\xinghuo\allegro\shelf\entity\AllegroShelfLogEntity$AllegroShelfLogEntityBuilder.class
com\xinghuo\fyndiq\data\service\impl\FyndiqDataSkuServiceImpl.class
com\xinghuo\niceapi\coupang\api\CoupangShippingApi.class
com\xinghuo\niceapi\ozon\model\price\ProductPriceUpdateResponse$PriceUpdateResult.class
com\xinghuo\allegro\push\entity\ProducerEntity.class
com\xinghuo\niceapi\coupang\model\product\CoupangProductCreateRequest$RequiredDocument.class
com\xinghuo\allegro\order\dao\RefundMapper.class
com\xinghuo\tenant\TenantController.class
com\xinghuo\visualdev\portal\model\FlowTodo.class
com\xinghuo\niceapi\ozon\model\fbs\PostingsRequest.class
com\xinghuo\coupang\manage\service\CoupangStoreService.class
com\xinghuo\fruugo\collect\model\FruugoCollectOfferPagination.class
com\xinghuo\niceapi\allegro\sale\NewOfferAPI.class
com\xinghuo\niceapi\alibaba\ImageToBase64.class
com\xinghuo\dmj\shop\model\ShopBindingForm$ShopInfo.class
com\xinghuo\allegro\data\model\seller\AllegroSellerForm.class
com\xinghuo\allegro\push\model\erp\ErpProductModel.class
com\baidu\translate\TranslateFileWithMapping.class
com\xinghuo\allegro\home\controller\HomeController.class
com\xinghuo\allegro\push\service\impl\CollectVioWordServiceImpl.class
com\xinghuo\allegro\collect\service\impl\CollectOfferJsonServiceImpl.class
com\xinghuo\allegro\collect\entity\CollectProductEntity.class
com\xinghuo\allegro\order\model\declaration\ExpressDeclarationModel.class
com\xinghuo\dmj\shop\model\ShopBindingPagination.class
com\xinghuo\allegro\push\model\offer\OnlineDataCopyForm.class
com\xinghuo\niceapi\yunexpress\model\CreatePackageRequest.class
com\xinghuo\allegro\manage\service\translate\BaiduTranslateAdapter.class
com\xinghuo\allegro\msg\model\rating\RatingStatusNoteForm.class
com\xinghuo\niceapi\coupang\model\product\CoupangProductCreateRequest$Notice.class
com\xinghuo\niceapi\coupang\model\category\CoupangRegistrationType.class
com\xinghuo\fruugo\data\model\FruugoDataProductPagination.class
com\xinghuo\niceapi\ozon\model\fbs\CancelReasonResponse$CancelReason.class
com\xinghuo\allegro\manage\controller\AllegroStoreConfigController.class
com\xinghuo\fruugo\analysis\model\CategoryRankingVO.class
com\xinghuo\allegro\collect\model\collectOffer\CollectSettingModel$PriceMode.class
com\xinghuo\allegro\aiservice\service\impl\DescriptionVariantServiceImpl.class
com\xinghuo\allegro\push\dao\ErpProductVioMapper.class
com\xinghuo\allegro\push\entity\PushOfferEntity.class
com\xinghuo\niceapi\coupang\CoupangBaseApi$ErrorType.class
com\xinghuo\allegro\order\dao\OrderDeliveryMapper.class
com\xinghuo\allegro\order\model\authorize\ExpressProductConfigForm.class
com\xinghuo\fruugo\manage\model\category\FruugoCategoryForm.class
com\xinghuo\niceapi\allegro\account\AllegroStoreModel.class
com\xinghuo\allegro\manage\model\store\AllegroStoreBaseModel.class
com\xinghuo\niceapi\fpx\model\Fpx4LogisticsProductRequest$Fpx4LogisticsProductRequestBuilder.class
com\xinghuo\ozon\offer\entity\OzonListingInventory.class
com\xinghuo\niceapi\ozon\model\price\DiscountedProductInfoResponse$DiscountedItem.class
com\xinghuo\allegro\shelf\model\link\LinkEntityUtil.class
com\xinghuo\ozon\offer\model\vo\OzonAttributeValueVO.class
com\xinghuo\niceapi\ozon\model\product\ProductInfoRequest.class
com\xinghuo\niceapi\ozon\model\returns\RfbsReturnVerifyRequest.class
com\xinghuo\admin\aop\PermissionRoleAspect.class
com\xinghuo\fruugo\analysis\model\FruugoMonitorVO.class
com\xinghuo\fruugo\collect\service\FruugoBoughtOfferService.class
com\xinghuo\niceapi\ozon\model\product\ProductInfoListRequest$Filter.class
com\xinghuo\allegro\sale\service\impl\SyncTaskServiceImpl.class
com\xinghuo\collect\box\service\impl\CollectBoxItemServiceImpl.class
com\xinghuo\niceapi\coupang\model\product\CoupangProductCreateRequest$Attribute.class
com\xinghuo\allegro\order\entity\ExpressEntity.class
com\xinghuo\fruugo\util\FruugoUrlConverter.class
com\xinghuo\niceapi\fpx\Fpx4Client$9.class
com\xinghuo\niceapi\allegro\message\MessageAPI.class
com\xinghuo\niceapi\ozon\model\product\ProductLimitResponse.class
com\xinghuo\allegro\order\service\ExpressWaybillService.class
com\xinghuo\allegro\sale\entity\BillingEntryEntity.class
com\xinghuo\niceapi\coupang\model\shipping\ReturnShippingCenterResponse$ReturnShippingCenter.class
com\xinghuo\niceapi\ozon\model\promo\PromoRequest$PromoRequestBuilder.class
com\xinghuo\niceapi\fpx\model\Fpx4ApiResponse.class
com\xinghuo\allegro\order\entity\ExpressAuthorizeEntity.class
com\xinghuo\allegro\order\model\order\OrderItemModel.class
com\xinghuo\niceapi\allegro\order\BillingEntryAPI.class
com\xinghuo\allegro\sale\service\AllegroNewProductEnService.class
com\xinghuo\analy\service\AnalyStoreInfoService.class
com\xinghuo\analy\service\impl\AnalyStoreInfoServiceImpl.class
com\xinghuo\amazon\util\AmazonUtil.class
com\xinghuo\niceapi\ozon\model\product\ProductPictureImportResponse$PictureInfo.class
com\xinghuo\extenduser\controller\User2Controller.class
com\xinghuo\niceapi\allegro\account\RatingsAPI.class
com\xinghuo\ozon\offer\controller\OzonOfferController.class
com\xinghuo\allegro\manage\service\impl\TranslateServiceImpl.class
com\xinghuo\amazon\collect\model\list\AmazonListTaskContentForm.class
com\xinghuo\allegro\home\model\SalesStatModel$Metrics$MetricsBuilder.class
com\xinghuo\fyndiq\data\entity\FyndiqDataProductEntity.class
com\xinghuo\ozon\offer\model\OzonOfferPagination.class
com\xinghuo\ozon\manage\model\product\OzonProductPagination.class
com\xinghuo\allegro\collect\model\analysis\DailyCompletionModel.class
com\xinghuo\allegro\collect\dao\CollectZeroOfferMapper.class
com\xinghuo\fruugo\data\entity\FruugoDataProductEntity.class
com\xinghuo\allegro\collect\service\CollectOfferPLService.class
com\xinghuo\niceapi\coupang\model\product\ProductDetailResponse$ContentDetail.class
com\xinghuo\niceapi\ozon\model\cancellation\ConditionalCancellationResponse$State.class
com\xinghuo\allegro\order\controller\ExpressDeclarationController.class
com\xinghuo\fruugo\data\model\FruugoDataProductModel.class
com\xinghuo\allegro\push\service\AllegroPushLogService.class
com\xinghuo\ozon\msg\service\OzonChatThreadService.class
com\xinghuo\allegro\data\model\sale\AllegroSalePagination.class
com\xinghuo\niceapi\yanwenexpress\model\LabelResponse.class
com\xinghuo\ozon\manage\model\store\OzonStorePagination.class
com\xinghuo\niceapi\allegro\sale\model\BillingResponseModel.class
com\xinghuo\niceapi\fpx\Fpx4Util.class
com\xinghuo\niceapi\allegro\product\NewProductEnAPI.class
com\xinghuo\niceapi\coupang\model\shipping\ShippingPlaceResponse$ShippingPlace.class
com\xinghuo\allegro\data\controller\AllegroDataTaskController.class
com\xinghuo\ozon\msg\dao\OzonChatThreadMapper.class
com\xinghuo\niceapi\ozon\model\delivery\TrackingNumberResponse.class
com\xinghuo\allegro\util\SkuIdUtil.class
com\xinghuo\allegro\manage\entity\SysExchangeRateEntity.class
com\xinghuo\allegro\sale\model\product\ProductModel$SectionItem.class
com\xinghuo\coupang\manage\dao\CoupangCategoryMapper.class
com\xinghuo\niceapi\ozon\model\promo\PromoProductsResponse.class
com\xinghuo\allegro\order\service\ExpressProductService.class
com\xinghuo\niceapi\fpx\model\Fpx4OrderQueryResponse$ParcelConfirmInfo.class
com\xinghuo\niceapi\fpx\model\ResponseMsg.class
com\xinghuo\allegro\collect\controller\CollectConfigController.class
com\xinghuo\niceapi\ozon\model\fbs\CancelPostingRequest$CancelPostingRequestBuilder.class
com\xinghuo\niceapi\fpx\Fpx4Client$10.class
com\xinghuo\ozon\manage\dao\OzonCategoryAttributeValueMapper.class
com\xinghuo\allegro\order\controller\ErpOrderController.class
com\xinghuo\niceapi\ozon\model\product\ProductInfoResponse$PriceIndexData.class
com\xinghuo\niceapi\ozon\model\product\ProductPictureImportResponse.class
com\xinghuo\allegro\data\model\AllegroDataOfferModel.class
com\xinghuo\admin\TestMain.class
com\xinghuo\ozon\manage\dao\OzonStoreMapper.class
com\xinghuo\allegro\manage\model\task\TaskPagination.class
com\xinghuo\niceapi\allegro\product\CategoryAPI.class
com\xinghuo\niceapi\ozon\model\product\ProductInfoListResponse$StockItem.class
com\xinghuo\allegro\sale\model\product\ProductModel$CompatibilityList.class
com\xinghuo\fruugo\analysis\task\FruugoDataMonitorTask.class
com\xinghuo\fruugo\collect\service\impl\FruugoProductSkuServiceImpl.class
com\xinghuo\niceapi\ozon\model\fbs\PostingCancelReasonResponse$CancelReason.class
com\xinghuo\allegro\sale\model\event\AllegroOfferEndEventPagination.class
com\xinghuo\common\swagger\SwaggerConfig.class
com\xinghuo\niceapi\coupang\model\product\CoupangProductCreateRequest.class
com\xinghuo\niceapi\ozon\model\product\ProductRatingBySkuResponse$ProductRating.class
com\xinghuo\fruugo\analysis\enums\AlertModuleEnum.class
com\xinghuo\fruugo\manage\controller\FruugoCategoryController.class
com\xinghuo\allegro\order\model\profit\OrderProfitForm.class
com\xinghuo\niceapi\ozon\model\fbs\CountryListRequest.class
com\xinghuo\niceapi\ozon\model\cancellation\ConditionalCancellationResponse$CancellationState.class
com\xinghuo\niceapi\ozon\model\ErrorResponse$ErrorDetail.class
com\xinghuo\ozon\manage\service\OzonCategoryAttributeService.class
com\xinghuo\fruugo\collect\util\FruugoExcelExportUtil.class
com\xinghuo\niceapi\fpx\Fpx4Client$4.class
com\xinghuo\allegro\order\service\impl\ExpressWaybillServiceImpl.class
com\xinghuo\niceapi\coupang\model\category\WingCategoryDto$DisplayItemCategory.class
com\xinghuo\allegro\manage\service\translate\NiuTranslateAdapter.class
com\xinghuo\allegro\sale\model\offer\AllegroOfferSumModel.class
com\xinghuo\allegro\sale\model\billing\LastBalanceModel.class
com\xinghuo\fruugo\collect\model\FruugoCollectSaleForm.class
com\xinghuo\allegro\collect\controller\UpsController.class
com\xinghuo\fruugo\analysis\model\FruugoSaleDataForm.class
com\xinghuo\extenduser\service\impl\StoreUserServiceImpl.class
com\xinghuo\allegro\msg\model\template\MsgTemplateModel.class
com\xinghuo\allegro\push\model\offer\PushOfferModel.class
com\xinghuo\allegro\order\service\impl\ExpressAuthorizeServiceImpl.class
com\xinghuo\niceapi\allegro\message\DisputeAPI.class
com\xinghuo\niceapi\ozon\model\fbs\PostingsRequest$PostingsRequestBuilder.class
com\xinghuo\ozon\msg\model\OzonChatThreadVO.class
com\xinghuo\allegro\data\controller\AllegroSellerController.class
com\xinghuo\fruugo\manage\model\shelfTemplate\FruugoShelfTemplateDetailModel.class
com\xinghuo\niceapi\coupang\api\test\CoupangShippingApiExample.class
com\xinghuo\fruugo\collect\model\FruugoClaimTaskPagination.class
com\xinghuo\niceapi\ozon\model\product\ProductAttributeUpdateRequest$AttributeInfo.class
com\xinghuo\fruugo\analysis\service\FruugoProductMonitorService.class
com\xinghuo\niceapi\ozon\model\product\ProductAttributeInfoResponse.class
com\xinghuo\niceapi\ozon\constants\RFBSCancelReason.class
com\xinghuo\niceapi\ozon\model\chat\ChatResponse$FileInfo.class
com\xinghuo\allegro\msg\entity\MsgMessageEntity.class
com\xinghuo\allegro\util\UrlExtractor.class
com\xinghuo\niceapi\ozon\model\fbs\ProductCancelRequest$CancelItem$CancelItemBuilder.class
com\xinghuo\niceapi\coupang\model\product\CoupangProductCreateRequest$MaterialInfo.class
com\xinghuo\niceapi\ozon\model\cancellation\ConditionalCancellationResponse$CancellationInitiator.class
com\xinghuo\fruugo\manage\model\category\FruugoCategoryBatchForm.class
com\xinghuo\fruugo\collect\util\FruugoSkuInfoUtil.class
com\xinghuo\fruugo\data\model\FruugoProductDataForm$SkuUrl.class
com\xinghuo\allegro\push\model\offer\CopyMultiSellerForm.class
com\xinghuo\fruugo\collect\model\FruugoSellerPagination.class
com\xinghuo\allegro\msg\model\dispute\MsgDisputeModel.class
com\xinghuo\niceapi\ozon\model\returns\RfbsReturnDetailResponse$ReturnMethodType.class
com\xinghuo\niceapi\ozon\model\stock\UpdateStockResponse$StockUpdateResult.class
com\xinghuo\niceapi\fpx\model\Fpx4OrderQueryRequest$Fpx4OrderQueryRequestBuilder.class
com\xinghuo\fyndiq\data\dao\FyndiqDataSkuMapper.class
com\xinghuo\allegro\collect\dao\CollectOfferJsonMapper.class
com\xinghuo\allegro\push\dao\PushNewOfferMapper.class
com\xinghuo\niceapi\coupang\product\model\ProductListRequest.class
com\xinghuo\allegro\sale\model\product\ParameterModel.class
com\xinghuo\allegro\order\model\address\ExpressAddressModel.class
com\xinghuo\allegro\manage\dao\TranslateAccountMapper.class
com\xinghuo\fruugo\image\dao\FruugoImageMapper.class
com\xinghuo\niceapi\ozon\model\product\ProductDescriptionResponse$ProductDescription.class
com\xinghuo\allegro\collect\model\analysis\KpiModel.class
com\xinghuo\tenant\util\TestConnectionFactory.class
com\xinghuo\allegro\push\model\offer\PushOfferPagination.class
com\xinghuo\niceapi\fpx\Fpx4Client$1.class
com\xinghuo\allegro\sale\entity\SyncTaskEntity.class
com\xinghuo\allegro\order\dao\ErpOrderMapper.class
com\xinghuo\allegro\push\controller\PushController.class
com\xinghuo\fruugo\analysis\model\FruugoProductMonitorVO.class
com\xinghuo\niceapi\ozon\model\returns\RfbsReturnDetailResponse$RejectionReason.class
com\xinghuo\allegro\collect\entity\UpsEntity.class
com\xinghuo\allegro\manage\entity\ExportTaskEntity.class
com\xinghuo\allegro\manage\util\ExchangeUtil.class
com\baidu\translate\AllegroToFruggoConverter$Item.class
com\xinghuo\allegro\sale\model\product\ProductModel$Publication.class
com\xinghuo\allegro\shelf\entity\AllegroShelfLogEntity.class
com\xinghuo\dmj\shop\model\ShopBindingDetailModel.class
com\xinghuo\ozon\manage\controller\OzonCategoryController.class
com\xinghuo\ozon\manage\entity\ImageAliEntity.class
com\xinghuo\ozon\manage\model\product\ShippingRuleUtil$ShippingRule.class
com\xinghuo\visualdev\portal\model\MyFlowTodoVO.class
com\xinghuo\niceapi\ozon\model\fbs\CountryListResponse.class
com\xinghuo\allegro\data\service\AllegroDataTaskService.class
com\xinghuo\allegro\order\entity\OrderDeliveryEntity.class
com\xinghuo\allegro\push\model\brand\AllegroBrandPagination.class
com\xinghuo\allegro\data\model\AllegroDataTaskForm.class
com\xinghuo\fyndiq\manage\dao\FyndiqCategoryMapper.class
com\xinghuo\allegro\manage\service\TranslateService.class
com\xinghuo\niceapi\fpx\model\Fpx4LogisticsProductRequest.class
com\xinghuo\niceapi\allegro\sale\OfferTranslationAPI$DescriptionSection.class
com\xinghuo\coupang\manage\model\template\CoupangTemplatePagination.class
com\xinghuo\fruugo\manage\model\shelfTemplate\FruugoShelfTemplateSelectModel.class
com\xinghuo\fruugo\analysis\model\DataProcessStatsModel.class
com\xinghuo\niceapi\ozon\model\fbs\ProductWeightChangeRequest$ProductWeightChangeRequestBuilder.class
com\xinghuo\fruugo\data\dao\FruugoDataProductMapper.class
com\xinghuo\niceapi\ozon\model\category\CategoryTreeResponse.class
com\xinghuo\allegro\data\controller\AllegroProductController.class
com\xinghuo\niceapi\ozon\model\cancellation\RejectCancellationRequest$RejectCancellationRequestBuilder.class
com\xinghuo\allegro\push\controller\ErpGroupController.class
com\xinghuo\allegro\collect\controller\AllegroTaskAnalysisController.class
com\xinghuo\extenduser\service\StoreUserService.class
com\xinghuo\allegro\sale\controller\AllegroOfferEndEventController.class
com\baidu\translate\HttpGet$1.class
com\xinghuo\amazon\collect\entity\AmazonListTaskEntity.class
com\xinghuo\admin\filter\AuthFilter.class
com\xinghuo\niceapi\ozon\model\product\ProductInfoResponse$ProductInfo.class
com\xinghuo\allegro\collect\dao\CollectAccountMapper.class
com\xinghuo\niceapi\ozon\model\promo\HotSalesDeactivateRequest$HotSalesDeactivateRequestBuilder.class
com\xinghuo\niceapi\yunexpress\model\CreatePackageRequest$Receiver.class
com\xinghuo\niceapi\ozon\model\product\UpdateProductOfferIdResponse.class
com\xinghuo\niceapi\ozon\model\stock\FBSStocksByWarehouseRequest.class
com\xinghuo\allegro\manage\service\impl\StoreCheckServiceImpl.class
com\xinghuo\fruugo\collect\service\impl\FruugoPushOfferServiceImpl.class
com\xinghuo\niceapi\ozon\model\product\ProductInfoListResponse$DiscountedStocks.class
com\xinghuo\ozon\manage\service\impl\OzonCategoryServiceImpl.class
com\xinghuo\niceapi\ozon\model\fbs\PostingCancelReasonResponse$PostingCancelReason.class
com\xinghuo\niceapi\ozon\model\fbs\UnfulfilledPostingsResponse$Addressee.class
com\xinghuo\niceapi\coupang\model\product\CoupangProductCreateRequest$ProductImage.class
com\xinghuo\fruugo\data\entity\FruugoDataSkuEntity.class
com\xinghuo\niceapi\coupang\model\product\CoupangProductCreateRequest$Certification.class
com\xinghuo\niceapi\ozon\model\fbs\AwaitingDeliveryResponse.class
com\xinghuo\niceapi\yunexpress\model\CreatePackageRequest$ExtraService.class
com\xinghuo\niceapi\ozon\model\fbs\ProductCancelResponse.class
com\xinghuo\allegro\manage\model\TranslateModel.class
com\xinghuo\fyndiq\data\model\FyndiqDataProductPagination.class
com\xinghuo\allegro\sale\entity\AllegroNewProductEntity.class
com\xinghuo\niceapi\ozon\model\fbs\UnfulfilledPostingsResponse$AnalyticsData.class
com\xinghuo\ozon\msg\service\OzonChatMessageService.class
com\xinghuo\allegro\shelf\model\link\LinkStatusEnum.class
com\xinghuo\niceapi\ozon\model\promo\PromoResponse$Promo.class
com\xinghuo\allegro\push\model\offer\PushOfferNoteForm.class
com\xinghuo\niceapi\ozon\model\product\ProductDeleteResponse$DeleteStatus.class
com\xinghuo\allegro\msg\service\MsgMessageService.class
com\xinghuo\allegro\collect\service\impl\CollectOfferServiceImpl.class
com\xinghuo\allegro\aiservice\entity\TitleVariantEntity.class
com\xinghuo\niceapi\coupang\api\CoupangCategoryApi$1.class
com\xinghuo\niceapi\coupang\model\product\ProductDetailResponse$ProductCertification.class
com\xinghuo\allegro\shelf\dao\PushOfferLinkMapper.class
com\xinghuo\niceapi\ozon\model\fbs\PostingCancelReasonResponse.class
com\xinghuo\allegro\msg\service\MsgDisputeService.class
com\xinghuo\niceapi\allegro\sale\OfferTranslationAPI$OfferTranslation.class
com\xinghuo\ozon\manage\model\ShipRuleModel.class
com\xinghuo\admin\util\GatewayWhite.class
com\xinghuo\allegro\push\model\shelfTemplate\ShelfTemplateSelectModel.class
com\xinghuo\allegro\sale\service\BillingEntryService.class
com\xinghuo\allegro\sale\dao\AllegroNewProductEnMapper.class
com\xinghuo\fruugo\collect\util\FruugoImageUtil.class
com\xinghuo\niceapi\ozon\model\fbs\UnfulfilledPostingsResponse$Customer.class
com\xinghuo\niceapi\ozon\cancellation\OzonCancellationAPI.class
com\xinghuo\allegro\manage\dao\TranslateCacheMapper.class
com\xinghuo\allegro\sale\model\product\ProductModel$Category.class
com\xinghuo\coupang\offer\entity\CoupangOfferItemEntity.class
com\xinghuo\niceapi\ozon\model\product\ProductPictureImportRequest.class
com\xinghuo\allegro\order\model\yuntu\YuntuExpressAuthorizeForm.class
com\xinghuo\allegro\collect\model\collect\CollectOfferModel.class
com\xinghuo\allegro\manage\model\task\ExportTaskModel.class
com\xinghuo\allegro\order\model\order\UpdateExpressMethodForm.class
com\xinghuo\niceapi\ozon\model\fbs\UnfulfilledPostingsResponse$FinancialData.class
com\xinghuo\allegro\sale\service\impl\SyncTaskItemServiceImpl.class
com\xinghuo\allegro\collect\entity\CollectOfferJsonEntity.class
com\xinghuo\allegro\collect\service\impl\CollectConfigServiceImpl.class
com\xinghuo\allegro\order\entity\RefundItemEntity.class
com\xinghuo\coupang\manage\service\CoupangCategoryService.class
com\xinghuo\niceapi\ozon\model\product\ProductAttributeUpdateRequest$ProductAttributeItem.class
com\xinghuo\allegro\push\controller\ShelfTemplateController.class
com\xinghuo\niceapi\ozon\model\product\ProductInfoListResponse.class
com\xinghuo\niceapi\coupang\model\category\CategoryMetaDTO$NoticeCategoryMeta.class
com\xinghuo\niceapi\ozon\model\returns\RfbsReturnsListRequest$RfbsReturnsListRequestBuilder.class
com\xinghuo\allegro\aiservice\entity\ServiceTaskEntity.class
com\xinghuo\allegro\home\model\TaskPanelModel$TaskPanelModelBuilder.class
com\xinghuo\allegro\order\model\profit\OrderProfitStatVO.class
com\xinghuo\allegro\stock\entity\StockEntity.class
com\xinghuo\niceapi\ozon\model\product\ProductDeleteRequest.class
com\xinghuo\niceapi\ozon\model\promo\PromoTaskRequest.class
com\xinghuo\fruugo\analysis\service\FruugoMonitorService.class
com\xinghuo\fruugo\manage\model\store\FruugoStorePagination.class
com\xinghuo\fruugo\collect\entity\FruugoSitemapEntity.class
com\xinghuo\niceapi\ozon\model\product\ProductInfoListResponse$ProductStatusDetails.class
com\xinghuo\niceapi\coupang\model\product\CoupangProductCreateRequest$GlobalInfo.class
com\xinghuo\allegro\manage\dao\AllegroStoreMapper.class
com\xinghuo\niceapi\coupang\model\product\ProductDetailResponse$BundleInfo.class
com\xinghuo\allegro\manage\entity\AllegroStoreEntity.class
com\xinghuo\allegro\sale\model\product\CategoryParameter$Restrictions.class
com\xinghuo\fruugo\analysis\model\AlertStatsModel$ModuleStatItem.class
com\xinghuo\niceapi\coupang\model\product\CoupangProductCreateResponse$ResponseData.class
com\xinghuo\allegro\collect\model\product\CollectProductModel.class
com\xinghuo\allegro\order\service\LabelService.class
com\xinghuo\fruugo\analysis\service\FruugoSkuMonitorService.class
com\xinghuo\allegro\manage\model\storeConfig\AllegroStoreConfigModel.class
com\xinghuo\coupang\offer\model\CoupangOfferItemForm.class
com\xinghuo\niceapi\ozon\model\product\ProductInfoListResponse$Commission.class
com\xinghuo\allegro\push\model\erp\ErpProductBatchForm.class
com\xinghuo\coupang\manage\controller\CoupangStoreConfigController.class
com\xinghuo\allegro\order\dao\OrderItemMapper.class
com\xinghuo\niceapi\coupang\model\product\ProductInflowStatusResponse$DataInfo.class
com\xinghuo\allegro\collect\dao\CollectOfferPLMapper.class
com\xinghuo\fruugo\manage\model\store\StoreGroupModel$StoreSelectModel.class
com\xinghuo\allegro\data\service\impl\AllegroSellerServiceImpl.class
com\xinghuo\collect\box\model\CollectBoxItemForm.class
com\xinghuo\allegro\push\entity\PushNewOfferEntity.class
com\xinghuo\fruugo\collect\service\impl\FruugoSellerServiceImpl.class
com\xinghuo\ozon\offer\service\OzonOfferService.class
com\xinghuo\niceapi\ozon\model\chat\ChatListRequest$ChatListRequestBuilder.class
com\xinghuo\allegro\msg\model\message\StatusNoteForm.class
com\xinghuo\allegro\order\model\order\SumOrderModel.class
com\xinghuo\collect\box\model\CollectBoxModel.class
com\xinghuo\niceapi\coupang\CoupangBaseApi$CoupangRawResponse.class
com\xinghuo\allegro\home\model\VioStatModel.class
com\xinghuo\collect\box\service\CollectBoxItemService.class
com\xinghuo\niceapi\fpx\model\Fpx4BusinessException.class
com\xinghuo\niceapi\ozon\model\promo\PromoTaskResponse$PromoTask.class
com\xinghuo\allegro\push\dao\PushNameMapper.class
com\xinghuo\allegro\collect\model\product\CollectProductPagination.class
com\xinghuo\allegro\home\model\QuotationStatModel$QuotationStatModelBuilder.class
com\xinghuo\allegro\msg\service\MsgTemplateService.class
com\xinghuo\allegro\collect\controller\CollectTaskController.class
com\xinghuo\amazon\collect\service\AmazonPageTaskService.class
com\xinghuo\allegro\collect\dao\CollectTaskAnalysisMapper.class
com\xinghuo\fruugo\analysis\service\impl\FruugoAnalysisServiceImpl$DateRange.class
com\xinghuo\allegro\sale\model\product\ProductModel$Image.class
com\xinghuo\fruugo\manage\model\store\FruugoStoreForm.class
com\xinghuo\niceapi\ozon\model\chat\ChatHistoryRequest$ChatHistoryRequestBuilder.class
com\xinghuo\allegro\util\AesEncryptionUtil.class
com\xinghuo\allegro\order\service\RefundService.class
com\xinghuo\allegro\collect\entity\CollectConfigEntity.class
com\xinghuo\fruugo\manage\controller\FruugoShelfTemplateController.class
com\xinghuo\allegro\manage\dao\AllegroLogMapper.class
com\xinghuo\allegro\manage\model\storeConfig\AllegroStoreConfigPagination.class
com\xinghuo\allegro\sale\entity\SyncTaskItemEntity.class
com\xinghuo\dmj\shop\constant\ShopBindingErrorCode.class
com\xinghuo\fruugo\collect\service\FruugoExportService.class
com\xinghuo\allegro\manage\controller\AllegroStoreController.class
com\xinghuo\collect\box\entity\CollectBoxItemEntity.class
com\xinghuo\allegro\order\model\express\ExpressConstant.class
com\xinghuo\niceapi\coupang\model\product\CoupangProductCreateRequest$SkuInfo.class
com\xinghuo\niceapi\ozon\model\chat\ChatResponse.class
com\xinghuo\niceapi\yanwenexpress\model\PackageRequest$Product.class
com\xinghuo\allegro\collect\dao\UpsMapper.class
com\xinghuo\allegro\order\controller\ExpressRuleController.class
com\xinghuo\coupang\offer\service\impl\CoupangOfferServiceImpl.class
com\xinghuo\niceapi\allegro\sale\OfferPatchAPI.class
com\xinghuo\allegro\manage\service\StoreCheckService.class
com\xinghuo\allegro\collect\service\CollectOfferJsonService.class
com\xinghuo\allegro\order\dao\ExpressAuthorizeMapper.class
com\xinghuo\niceapi\ozon\model\product\RelatedSkuRequest.class
com\xinghuo\niceapi\ozon\model\ErrorResponse.class
com\xinghuo\allegro\push\service\impl\PushNewOfferServiceImpl.class
com\xinghuo\niceapi\fpx\util\ApiHttpClientUtils.class
com\xinghuo\niceapi\ozon\model\promo\PromoCandidatesRequest$PromoCandidatesRequestBuilder.class
com\xinghuo\allegro\home\model\VioStatModel$ProductStats$ProductStatsBuilder.class
com\xinghuo\allegro\manage\controller\FpxAuthController.class
com\xinghuo\allegro\data\entity\AllegroDataTaskEntity.class
com\xinghuo\allegro\collect\service\impl\CollectAccountServiceImpl.class
com\xinghuo\allegro\data\service\impl\AllegroDataOfferServiceImpl.class
com\xinghuo\allegro\order\model\express\UpdateProductStatusForm.class
com\xinghuo\allegro\push\controller\AllegroPushLogController.class
com\xinghuo\fruugo\collect\dao\FruugoCollectSaleMapper.class
com\xinghuo\allegro\collect\model\config\LisConfigModel.class
com\xinghuo\allegro\push\service\ProducerService.class
com\xinghuo\niceapi\fpx\model\ResponseMsg$Error.class
com\xinghuo\niceapi\yanwenexpress\model\PackageRequest$PoPStation.class
com\xinghuo\niceapi\ozon\model\product\RelatedSkuResponse.class
com\xinghuo\ozon\msg\model\OzonChatMessageForm.class
com\xinghuo\allegro\manage\entity\StoreCheckEntity.class
com\xinghuo\allegro\order\model\authorize\ExpressCompanyModel.class
com\xinghuo\allegro\sale\dao\AllegroOfferEnMapper.class
com\xinghuo\niceapi\ozon\model\product\ProductInfoListResponse$Price.class
com\xinghuo\dmj\shop\model\ShopBindingModel.class
com\xinghuo\allegro\home\model\VioStatModel$Metrics$MetricsBuilder.class
com\xinghuo\allegro\collect\service\CollectTaskAnalysisService.class
com\xinghuo\niceapi\coupang\model\product\ProductListResponse.class
com\xinghuo\coupang\manage\entity\CoupangTemplateEntity.class
com\xinghuo\allegro\push\controller\PushOfferController.class
com\xinghuo\niceapi\coupang\model\product\ProductDetailResponse.class
com\xinghuo\coupang\offer\model\CoupangOfferItemModel.class
com\xinghuo\fruugo\collect\model\FruugoProductClaimForm.class
com\xinghuo\niceapi\ozon\model\product\ProductRatingBySkuRequest.class
com\xinghuo\allegro\order\service\impl\ExpressProductServiceImpl.class
com\xinghuo\allegro\collect\model\OfferJsonUtil.class
com\xinghuo\amazon\test\AmazonUtilTest.class
com\xinghuo\fruugo\analysis\task\FruugoAlertTask.class
com\xinghuo\fruugo\collect\service\FruugoSitemapService.class
com\xinghuo\allegro\push\entity\PushBatchEntity.class
com\xinghuo\allegro\collect\model\collectOffer\CollectForm.class
com\xinghuo\niceapi\ozon\model\product\ProductImportBySkuRequest.class
com\xinghuo\coupang\offer\service\impl\CoupangOfferItemServiceImpl.class
com\xinghuo\niceapi\allegro\sale\model\OfferEventResponse$OfferEventExternal.class
com\xinghuo\fruugo\analysis\model\SellerRankingVO.class
com\xinghuo\allegro\order\service\impl\ExcelExportServiceImpl.class
com\xinghuo\niceapi\ozon\model\fbs\CountryListRequest$CountryListRequestBuilder.class
com\xinghuo\allegro\collect\service\impl\UpsServiceImpl.class
com\xinghuo\allegro\aiservice\dao\DescriptionVariantMapper.class
com\xinghuo\niceapi\ozon\model\product\UpdateProductOfferIdRequest$OfferIdUpdate.class
com\xinghuo\tenant\util\CreateDataUtil.class
com\xinghuo\niceapi\fpx\model\Fpx4OrderQueryResponse$1.class
com\xinghuo\niceapi\ozon\model\cancellation\ApproveCancellationRequest$ApproveCancellationRequestBuilder.class
com\xinghuo\allegro\push\model\vioword\ViowordPagination.class
com\xinghuo\fruugo\collect\service\FruugoProductSkuService.class
com\xinghuo\fruugo\analysis\service\FruugoDataMonitorService.class
com\xinghuo\allegro\order\entity\LabelEntity.class
com\xinghuo\niceapi\allegro\sale\model\OfferResponseModel.class
com\xinghuo\allegro\msg\service\DisputeMessageService.class
com\xinghuo\allegro\home\model\MsgDeskStatModel$TodayStats$TodayStatsBuilder.class
com\xinghuo\niceapi\allegro\sale\model\OfferEventResponse.class
com\xinghuo\fruugo\analysis\model\SellerDetailVO.class
com\xinghuo\ozon\offer\model\vo\OzonOfferDetailVO.class
com\xinghuo\allegro\order\dao\ExpressAddressMapper.class
com\xinghuo\niceapi\ozon\model\promo\PromoTaskApproveRequest.class
com\xinghuo\allegro\manage\entity\AllegroBillTypeEntity.class
com\xinghuo\ozon\msg\entity\OzonChatMessageEntity.class
com\xinghuo\niceapi\allegro\message\RatingAPI.class
com\xinghuo\fyndiq\data\controller\FyndiqDataProductController$ExportFunction.class
com\xinghuo\niceapi\ozon\model\product\ProductListResponse$ProductListItem$Quant.class
com\xinghuo\allegro\collect\model\collectOffer\CollectSettingModel$BlackSellerMode.class
com\xinghuo\allegro\aiservice\model\AiUtil.class
com\xinghuo\niceapi\yunexpress\model\CreatePackageRequest$Sender.class
com\baidu\translate\AllegroToFruggoConverter.class
com\xinghuo\allegro\collect\model\collectOffer\CollectSettingModel$ZeroSaleMode.class
com\xinghuo\niceapi\yunexpress\api\YunOrderAPI.class
com\xinghuo\ozon\msg\entity\OzonChatThreadEntity.class
com\xinghuo\fruugo\analysis\model\FruugoDataMonitorVO$SellerStats.class
com\xinghuo\fruugo\analysis\model\AlertStatsModel$TrendStatItem.class
com\xinghuo\allegro\push\service\impl\AllegroPushLogServiceImpl.class
com\xinghuo\niceapi\ozon\constants\OzonApiConstants$Headers.class
com\xinghuo\niceapi\fpx\model\create\DeclareProductInfo.class
com\xinghuo\niceapi\ozon\model\returns\RfbsReturnDetailResponse$Action.class
com\xinghuo\allegro\order\model\profit\OrderProfitStatPagination.class
com\xinghuo\fruugo\data\service\FruugoDataSellerService.class
com\xinghuo\coupang\offer\model\CoupangOfferPagination.class
com\xinghuo\allegro\manage\model\StoreConfigModel.class
com\xinghuo\niceapi\ozon\util\OzonJsonUtil.class
com\xinghuo\fruugo\data\model\AllegroProductClaimForm.class
com\xinghuo\niceapi\fpx\model\create\DeclareProductInfo$DeclareProductInfoBuilder.class
com\xinghuo\ozon\manage\service\OzonCategoryService.class
com\xinghuo\niceapi\fpx\model\create\ReturnInfo.class
com\xinghuo\allegro\home\model\SalesStatModel$SalesStatModelBuilder.class
com\xinghuo\niceapi\fpx\model\create\DeliverTypeInfo.class
com\xinghuo\allegro\push\entity\ErpProductVioEntity.class
com\xinghuo\allegro\home\model\TaskPanelModel.class
com\xinghuo\allegro\sale\controller\SyncTaskController.class
com\xinghuo\allegro\sale\model\event\AllegroOfferEndEventVO.class
com\xinghuo\niceapi\ozon\model\returns\RfbsReturnDetailResponse$Product.class
com\xinghuo\allegro\stock\model\StockModel.class
com\xinghuo\allegro\order\service\impl\ExpressServiceImpl.class
com\xinghuo\allegro\push\model\erpGroup\GroupModel.class
com\xinghuo\allegro\push\service\impl\AllegroCategoryServiceImpl.class
com\xinghuo\allegro\collect\entity\CollectAccountEntity.class
com\xinghuo\allegro\data\model\seller\AllegroSellerPagination.class
com\xinghuo\allegro\push\service\PushNewOfferService.class
com\xinghuo\collect\box\dao\CollectBoxItemMapper.class
com\xinghuo\ozon\manage\service\impl\OzonProductServiceImpl.class
com\xinghuo\allegro\collect\controller\CollectProductController.class
com\xinghuo\fruugo\collect\entity\FruugoBoughtOfferEntity.class
com\xinghuo\niceapi\coupang\model\CoupangResponse.class
com\xinghuo\fruugo\collect\entity\FruugoProductEntity.class
com\xinghuo\fyndiq\manage\service\impl\FyndiqCategoryServiceImpl.class
com\xinghuo\amazon\collect\model\page\AmazonPageTaskModel.class
com\xinghuo\allegro\sale\model\product\ProductModel$Options.class
com\xinghuo\niceapi\allegro\sale\OfferTranslationAPI$StandardizedDescription.class
com\xinghuo\niceapi\ozon\model\delivery\DeliveryStatusRequest$DeliveryStatusRequestBuilder.class
com\xinghuo\allegro\sale\controller\AllegroNewProductEnController.class
com\xinghuo\niceapi\fpx\model\AffterentParam.class
com\xinghuo\allegro\data\model\AllegroDataRemoveForm.class
com\xinghuo\niceapi\fpx\model\create\Fpx4OrderResponse.class
com\xinghuo\niceapi\fpx\model\Fpx4OrderCancelRequest$Fpx4OrderCancelRequestBuilder.class
com\xinghuo\amazon\collect\model\list\AmazonListTaskPagination.class
com\xinghuo\fruugo\collect\service\impl\FruugoExportServiceImpl.class
com\xinghuo\allegro\push\service\impl\AllegroBrandServiceImpl.class
com\xinghuo\niceapi\ozon\model\cancellation\ConditionalCancellationListResponse.class
com\xinghuo\allegro\sale\model\product\ProductModel$Path.class
com\xinghuo\analy\dao\AnalyStoreInfoMapper.class
com\xinghuo\niceapi\ozon\model\product\ProductInfoListResponse$Dimensions.class
com\xinghuo\allegro\manage\dao\SysExchangeRateMapper.class
com\xinghuo\ozon\manage\service\impl\OzonStoreConfigServiceImpl.class
com\xinghuo\allegro\sale\model\product\ProductModel$Section.class
com\xinghuo\allegro\aiservice\service\DescriptionVariantService.class
com\xinghuo\niceapi\ozon\model\chat\ChatResponse$Chat.class
com\xinghuo\allegro\order\service\impl\OrderItemServiceImpl.class
com\xinghuo\allegro\sale\model\lts\LisPagination.class
com\xinghuo\niceapi\ozon\model\product\ProductAttributeInfoResponse$ComplexAttribute.class
com\xinghuo\fruugo\collect\entity\FruugoPushOfferEntity.class
com\xinghuo\niceapi\ozon\model\product\ProductAttributeInfoResponse$PdfFile.class
com\xinghuo\niceapi\yunexpress\OpenapiHjServiceImpl.class
com\xinghuo\fruugo\manage\model\shelfTemplate\FruugoShelfTemplateForm.class
META-INF\spring-configuration-metadata.json
com\xinghuo\allegro\order\service\ErpOrderService.class
com\xinghuo\niceapi\ozon\model\fbs\CountryListResponse$Country.class
com\xinghuo\allegro\push\model\erp\ErpProductGroupBatchForm.class
com\xinghuo\admin\constant\PermissionConstant.class
com\xinghuo\ozon\offer\dao\OzonOfferMapper.class
com\xinghuo\allegro\manage\controller\AllegroAuthController.class
com\xinghuo\allegro\sale\service\impl\AllegroOfferServiceImpl.class
com\baidu\translate\AllegroToFruggoConverter$Category.class
com\xinghuo\allegro\sale\model\product\ProductModel$Item.class
com\xinghuo\niceapi\ozon\model\fbs\UnfulfilledPostingsResponse$Result.class
com\xinghuo\niceapi\ozon\chat\OzonChatAPI.class
com\xinghuo\allegro\manage\service\impl\AllegroApiClientServiceImpl.class
com\xinghuo\allegro\order\model\authorize\ExpressProductConfigModel.class
com\xinghuo\allegro\data\dao\AllegroProductMapper.class
com\xinghuo\allegro\collect\controller\CollectMemberOfferController.class
com\xinghuo\fruugo\collect\service\FruugoClaimTaskService.class
com\xinghuo\niceapi\ozon\model\delivery\PostingCutoffResponse.class
com\xinghuo\allegro\push\entity\EanEntity.class
com\xinghuo\niceapi\ozon\model\product\ProductImportBySkuRequest$ImportBySkuItem.class
com\xinghuo\coupang\manage\entity\CoupangProductEntity.class
com\xinghuo\niceapi\coupang\model\category\CategoryMetaDTO$AttributeMeta.class
com\xinghuo\niceapi\fpx\model\create\Fpx4OrderCreateRequest.class
com\xinghuo\niceapi\ozon\constants\OzonApiConstants$Language.class
com\xinghuo\niceapi\fpx\model\Fpx4FreightResponse.class
com\xinghuo\niceapi\ozon\model\fbs\ProductCancelRequest$ProductCancelRequestBuilder.class
com\xinghuo\niceapi\ozon\promo\OzonPromoAPI.class
com\xinghuo\allegro\push\dao\PushBatchMapper.class
com\xinghuo\fruugo\manage\service\FruugoShelfTemplateService.class
com\xinghuo\niceapi\ozon\constants\OzonApiConstants$Returns$Currency.class
com\xinghuo\allegro\shelf\dao\AllegroShelfLogMapper.class
com\xinghuo\niceapi\ozon\model\product\ProductSubscriptionResponse$SubscriptionInfo.class
com\xinghuo\allegro\collect\model\collectOffer\DuplicateForm.class
com\xinghuo\allegro\sale\model\product\CategoryParameter$DisplayedIf.class
com\xinghuo\fruugo\collect\model\FruugoSellerModel.class
com\xinghuo\niceapi\ozon\model\price\ProductPriceUpdateRequest$PriceItem$PriceItemBuilder.class
com\xinghuo\niceapi\allegro\product\ProductResEnModel.class
com\xinghuo\allegro\msg\entity\MsgDisputeEntity.class
com\xinghuo\allegro\order\model\order\ExportTaskModel.class
com\xinghuo\allegro\msg\dao\MsgThreadMapper.class
com\xinghuo\niceapi\allegro\WebHookAPI.class
com\xinghuo\allegro\msg\model\rating\RatingPagination.class
com\xinghuo\collect\box\model\CollectBoxQueryParams.class
com\xinghuo\allegro\msg\model\dispute\NewDisputeMessageForm.class
com\xinghuo\niceapi\ozon\model\returns\RfbsReturnCompensateRequest.class
com\xinghuo\allegro\push\model\erp\ErpProductPagination.class
com\xinghuo\niceapi\ozon\model\price\ProductPriceUpdateRequest.class
com\xinghuo\allegro\push\model\shelfTemplate\ShelfTemplateModel.class
com\xinghuo\niceapi\ozon\model\returns\RfbsReturnRejectRequest$RfbsReturnRejectRequestBuilder.class
com\xinghuo\allegro\order\service\OrderProfitService.class
com\xinghuo\ozon\offer\entity\OzonListing.class
com\xinghuo\allegro\data\entity\AllegroSaleEntity.class
com\xinghuo\fruugo\data\controller\FruugoDataController.class
com\xinghuo\niceapi\ozon\model\product\ProductAttributeInfoRequest.class
com\xinghuo\fyndiq\manage\service\FyndiqCategoryService.class
com\xinghuo\allegro\order\model\express\LogisticsStatusEnum.class
com\xinghuo\allegro\data\service\impl\AllegroSaleServiceImpl.class
com\xinghuo\allegro\push\model\erp\ErpProductFormModel.class
com\xinghuo\niceapi\allegro\sale\OfferSyncAPI.class
com\xinghuo\ozon\manage\service\OzonStoreConfigService.class
com\baidu\translate\CategoryParser.class
com\xinghuo\allegro\manage\model\store\StoreGroupModel.class
com\xinghuo\allegro\aiservice\service\impl\TitleVariantServiceImpl.class
com\xinghuo\allegro\push\model\erp\CollectOfferSimpleModel.class
com\xinghuo\niceapi\ozon\model\cancellation\ConditionalCancellationResponse$CancellationReason.class
com\xinghuo\allegro\msg\dao\MsgAttachmentMapper.class
com\xinghuo\allegro\order\model\profit\OrderProfitPagination.class
com\xinghuo\base\service\ExtendedBaseServiceImpl.class
com\xinghuo\allegro\order\service\impl\RefundServiceImpl.class
com\xinghuo\niceapi\yanwenexpress\YanwenExpressClient.class
com\baidu\translate\DownloadAndParseSitemaps.class
com\xinghuo\fruugo\analysis\controller\FruugoDataMonitorController.class
com\xinghuo\niceapi\ozon\constants\OzonChatConstants.class
com\xinghuo\niceapi\allegro\account\RatingModel.class
com\xinghuo\coupang\manage\model\template\CoupangTemplateModel.class
com\xinghuo\allegro\push\dao\AllegroPushLogMapper.class
com\xinghuo\allegro\collect\service\UpsService.class
com\xinghuo\allegro\order\service\ExpressProductConfigService.class
com\xinghuo\niceapi\coupang\api\CoupangCategoryApi.class
com\xinghuo\niceapi\ozon\model\cancellation\ConditionalCancellationListRequest$With$WithBuilder.class
com\xinghuo\allegro\order\entity\OrderBuyerEntity.class
com\xinghuo\allegro\collect\model\product\CollectProductForm.class
com\xinghuo\niceapi\ozon\model\promo\HotSalesDeactivateRequest.class
com\xinghuo\niceapi\ozon\model\returns\RfbsReturnDetailRequest.class
com\xinghuo\niceapi\coupang\model\product\ProductDetailResponse$ProductAttribute.class
com\xinghuo\allegro\msg\service\impl\MsgRatingServiceImpl.class
com\xinghuo\allegro\push\service\impl\ErpProductServiceImpl.class
com\xinghuo\niceapi\yanwenexpress\model\PackageRequest.class
com\xinghuo\allegro\order\service\impl\ExpressRuleServiceImpl.class
com\xinghuo\fruugo\collect\dao\FruugoSitemapMapper.class
com\xinghuo\allegro\aiservice\entity\DescriptionVariantEntity.class
com\xinghuo\allegro\order\model\profit\OrderProfitModel.class
com\xinghuo\common\export\service\BaseExportService.class
com\xinghuo\ozon\manage\service\OzonProductService.class
com\xinghuo\allegro\data\controller\AllegroSellerAnalysisController.class
com\xinghuo\niceapi\allegro\message\DisputeMessageModel.class
com\xinghuo\allegro\push\service\ErpProductService.class
com\xinghuo\niceapi\coupang\api\CoupangCategoryMetaApi.class
com\xinghuo\fruugo\analysis\model\FruugoMonitorVO$SellerStats.class
com\xinghuo\fyndiq\data\service\impl\FyndiqDataProductServiceImpl.class
com\xinghuo\amazon\collect\service\impl\AmazonListTaskServiceImpl.class
com\xinghuo\allegro\push\service\impl\EanServiceImpl.class
com\xinghuo\niceapi\ozon\model\promo\PromoProductsRequest.class
com\xinghuo\allegro\push\service\impl\ErpProductVioServiceImpl.class
com\xinghuo\fruugo\collect\service\FruugoProductService.class
com\xinghuo\niceapi\allegro\afterSales\AfterSalesAPI.class
com\xinghuo\ozon\manage\entity\OzonCategoryEntity.class
com\xinghuo\allegro\push\model\offer\PublishOfferForm.class
com\xinghuo\coupang\manage\service\CoupangProductService.class
com\xinghuo\allegro\data\service\AllegroSaleService.class
com\xinghuo\ozon\offer\model\OzonOfferUpdateDTO.class
com\xinghuo\niceapi\ozon\model\product\ProductInfoResponse$ItemError.class
com\xinghuo\niceapi\ozon\constants\OzonApiConstants$Endpoints.class
com\xinghuo\allegro\manage\entity\AllegroLogEntity.class
com\xinghuo\niceapi\ozon\constants\OzonApiConstants.class
com\xinghuo\allegro\msg\dao\MsgRatingMapper.class
com\xinghuo\fruugo\analysis\model\SalesOverviewVO.class
com\xinghuo\allegro\order\controller\LabelController.class
com\xinghuo\fruugo\data\controller\FruugoDataCleanupController.class
com\xinghuo\allegro\stock\dao\StockMapper.class
com\xinghuo\fruugo\manage\service\FruugoCategoryService.class
com\xinghuo\allegro\order\service\impl\OrderProfitServiceImpl.class
com\xinghuo\allegro\sale\entity\AllegroOfferEndEventEntity.class
com\xinghuo\fruugo\collect\model\FruugoSitemapModel.class
com\xinghuo\allegro\order\service\ExcelExportService.class
com\xinghuo\allegro\collect\model\collectOffer\CollectSettingModel$GoodRegionMode.class
com\xinghuo\niceapi\ozon\model\returns\RfbsReturnDetailResponse$ReturnReason.class
com\xinghuo\allegro\sale\dao\AllegroOfferMapper.class
com\xinghuo\niceapi\ozon\model\cancellation\ConditionalCancellationListResponse$Counters.class
com\xinghuo\fruugo\data\model\FruugoDataSkuModel.class
com\xinghuo\allegro\shelf\model\link\PushOfferLinkPagination.class
com\xinghuo\allegro\order\dao\ExpressRuleMapper.class
com\xinghuo\allegro\order\model\order\DeliveryForm.class
com\xinghuo\allegro\collect\service\impl\CollectOfferPLServiceImpl.class
com\xinghuo\allegro\push\entity\PushNameEntity.class
com\xinghuo\niceapi\ozon\model\returns\RfbsReturnMoneyRequest.class
com\xinghuo\allegro\msg\entity\MsgRatingEntity.class
com\xinghuo\coupang\manage\model\product\CoupangProductModel.class
com\xinghuo\allegro\push\controller\VioWordController.class
com\xinghuo\permission\model\user\vo\UserInfoVO.class
com\xinghuo\niceapi\ozon\model\promo\PromoTaskDeclineRequest.class
com\xinghuo\fyndiq\data\service\impl\FyndiqExportServiceImpl.class
com\xinghuo\allegro\order\service\impl\RefundItemServiceImpl.class
com\xinghuo\fyndiq\manage\model\category\FyndiqCategoryModel.class
com\xinghuo\allegro\collect\service\CollectConfigService.class
com\xinghuo\allegro\collect\model\collectOffer\CollectOfferPagination.class
com\xinghuo\coupang\manage\model\category\CoupangCategoryPagination.class
com\xinghuo\niceapi\ozon\model\fbs\PackageLabelRequest$PackageLabelRequestBuilder.class
com\xinghuo\allegro\order\service\impl\ExpressAddressServiceImpl.class
com\xinghuo\allegro\msg\controller\MsgMessageController.class
com\xinghuo\niceapi\coupang\model\category\CategoryMetaDTO.class
com\xinghuo\allegro\msg\model\rating\RatingStatusEnum.class
com\xinghuo\niceapi\ozon\model\product\ProductDeleteRequest$ProductToDelete.class
com\xinghuo\ozon\manage\model\category\CategoryForm.class
com\xinghuo\niceapi\ozon\model\promo\PromoProductsActivateRequest$PromoProductsActivateRequestBuilder.class
com\xinghuo\niceapi\allegro\order\model\RefundResponseModel.class
com\xinghuo\niceapi\ozon\model\promo\PromoRequest.class
com\xinghuo\ozon\offer\model\vo\OzonCategoryAttributeVO.class
com\xinghuo\coupang\manage\entity\CoupangCategoryEntity.class
com\xinghuo\allegro\order\service\ExpressAddressService.class
com\xinghuo\ozon\manage\controller\OzonProductController.class
com\xinghuo\allegro\manage\dao\AllegroBillTypeMapper.class
com\xinghuo\niceapi\ozon\model\product\ProductPicturesRequest.class
com\xinghuo\visualdev\portal\model\FlowTodoVO.class
com\xinghuo\allegro\order\dao\ExpressDeclarationMapper.class
com\xinghuo\allegro\sale\controller\AllegroImageController.class
com\xinghuo\allegro\sale\service\impl\SyncTaskServiceImpl$1.class
com\xinghuo\fruugo\analysis\service\FruugoAnalysisService.class
com\xinghuo\ozon\manage\entity\OzoonStoreConfigEntity.class
com\xinghuo\niceapi\ozon\model\price\UpdateProductDiscountRequest$UpdateProductDiscountRequestBuilder.class
com\xinghuo\ozon\manage\service\OzonShippingFeeRulesService.class
com\xinghuo\ozon\manage\service\impl\OzonCategoryAttributeServiceImpl.class
com\xinghuo\admin\XhAdminApplication$StartupTimePrinter.class
com\xinghuo\analy\dao\AnalyOfferInfoMapper.class
com\xinghuo\niceapi\ozon\product\OzonProductAPITest.class
com\xinghuo\niceapi\allegro\product\PushNameAPI.class
com\xinghuo\allegro\data\dao\AllegroSellerMapper.class
com\xinghuo\coupang\manage\model\product\CoupangProductPagination.class
com\xinghuo\coupang\manage\service\impl\CoupangStoreConfigServiceImpl.class
com\xinghuo\niceapi\ozon\model\fbs\AwaitingDeliveryRequest.class
com\xinghuo\allegro\data\service\impl\AllegroSellerAnalysisServiceImpl.class
com\xinghuo\allegro\msg\model\message\NewMessageForm.class
com\xinghuo\extenduser\dao\StoreUserMapper.class
com\xinghuo\niceapi\ozon\model\product\ProductImportRequest$ProductAttribute.class
com\xinghuo\allegro\data\service\impl\AllegroProductServiceImpl.class
com\xinghuo\niceapi\ozon\model\fbs\UnfulfilledPostingsResponse.class
com\xinghuo\allegro\collect\service\impl\CollectZeroOfferServiceImpl.class
com\xinghuo\ozon\manage\dao\OzoonStoreConfigMapper.class
com\xinghuo\allegro\order\model\order\OrderModel.class
com\xinghuo\ozon\manage\service\ImageAliService.class
com\xinghuo\coupang\manage\service\CoupangCategoryMetaService.class
com\xinghuo\allegro\order\model\order\OrderPagination.class
com\xinghuo\allegro\data\service\AllegroSellerService.class
com\xinghuo\niceapi\ozon\model\cancellation\ConditionalCancellationListRequest.class
com\xinghuo\niceapi\fpx\model\Fpx4FreightResponse$FreightSubItem.class
com\xinghuo\allegro\push\controller\PushNameController.class
com\xinghuo\fruugo\manage\model\category\FruugoCategoryPagination.class
com\xinghuo\niceapi\ozon\model\fbs\PostingsRequest$With.class
com\xinghuo\allegro\collect\model\collect\WySyncForm.class
com\xinghuo\niceapi\ozon\model\promo\PromoResponse.class
com\xinghuo\allegro\collect\entity\ReportCollectOfferEntity.class
com\xinghuo\allegro\push\model\ean\EanForm.class
com\xinghuo\allegro\manage\service\impl\AllegroAuthServiceImpl.class
com\xinghuo\allegro\msg\entity\DisputeMessageEntity.class
com\xinghuo\niceapi\ozon\model\delivery\PostingCutoffRequest$PostingCutoffRequestBuilder.class
com\xinghuo\allegro\push\model\category\CategoryForm.class
com\xinghuo\allegro\collect\model\config\CollectConfigModel.class
com\xinghuo\allegro\order\model\express\ExpressFeeSettingModel$ExpressFeeSettingModelBuilder.class
com\xinghuo\allegro\manage\entity\TranslateUsageLogEntity.class
com\xinghuo\allegro\manage\model\store\AllegroStoreManageModel.class
com\xinghuo\niceapi\ozon\model\promo\PromoProductsActivateRequest.class
com\xinghuo\niceapi\ozon\model\fbs\UnfulfilledPostingsResponse$Requirements.class
com\xinghuo\allegro\sale\model\product\AllegroProductPagination.class
com\xinghuo\niceapi\yunexpress\model\CreatePackageRequest$OrderNumbers.class
com\xinghuo\allegro\sale\service\impl\AllegroOfferEnServiceImpl.class
com\xinghuo\allegro\order\model\order\WaybillResponse.class
com\xinghuo\niceapi\ozon\model\returns\RfbsReturnsListResponse$Product.class
com\xinghuo\fruugo\analysis\controller\FruugoSalesMonitorController.class
com\xinghuo\extenduser\entity\StoreUserEntity.class
com\xinghuo\fruugo\analysis\model\SalesRecordVO.class
com\xinghuo\allegro\push\model\erp\VioStatusEnum.class
com\xinghuo\niceapi\ozon\model\fbs\PostingsRequest$Filter.class
com\xinghuo\allegro\msg\model\rating\StoreRatingModel.class
com\xinghuo\niceapi\ozon\product\OzonStockAPI.class
com\xinghuo\allegro\collect\controller\CollectFhTaskController.class
com\xinghuo\niceapi\coupang\model\product\ProductDetailResponse$RequiredDocument.class
com\xinghuo\fruugo\collect\service\impl\FruugoProductServiceImpl.class
com\xinghuo\allegro\push\entity\ErpProductEntity.class
com\xinghuo\fruugo\manage\entity\FruugoCategoryEntity.class
com\xinghuo\niceapi\ozon\model\returns\RfbsReceiveReturnRequest$RfbsReceiveReturnRequestBuilder.class
com\xinghuo\niceapi\coupang\model\product\ProductListResponse$ProductInfo.class
com\xinghuo\allegro\manage\dao\AllegroStoreConfigMapper.class
com\xinghuo\allegro\shelf\model\log\AllegroShelfLogPagination.class
com\xinghuo\fruugo\analysis\model\DataProcessStatsModel$TotalStats.class
com\xinghuo\allegro\push\service\PushNameService.class
com\xinghuo\niceapi\ozon\model\fbs\PostingsResponse$Result.class
com\xinghuo\amazon\util\AmazonConstant.class
com\xinghuo\allegro\push\controller\EanController.class
com\xinghuo\niceapi\ozon\model\promo\PromoProductsDeactivateRequest.class
com\xinghuo\allegro\collect\model\collectOffer\CollectSettingModel$RaiseMode.class
com\xinghuo\niceapi\yanwenexpress\model\PackageRequest$ReceiverInfo.class
com\xinghuo\niceapi\ozon\model\returns\RfbsReturnsListResponse.class
com\xinghuo\allegro\aiservice\model\DescriptionModel.class
com\xinghuo\collect\box\controller\CollectBoxController.class
com\xinghuo\collect\box\service\impl\CollectBoxServiceImpl.class
com\xinghuo\niceapi\ozon\model\product\ProductListResponse$ProductListItem.class
com\xinghuo\coupang\offer\model\CoupangOfferDetailModel.class
com\xinghuo\allegro\order\model\authorize\ExpressAuthorizeForm.class
com\xinghuo\fruugo\collect\service\impl\FruugoSitemapServiceImpl.class
com\xinghuo\niceapi\coupang\model\shipping\ShippingPlaceResponse$RemoteInfo.class
com\xinghuo\allegro\sale\entity\AllegroOfferEntity.class
com\xinghuo\fruugo\manage\entity\FruugoStoreEntity.class
com\xinghuo\allegro\collect\service\CollectEventService.class
com\xinghuo\fruugo\collect\controller\FruugoSitemapController.class
com\xinghuo\niceapi\allegro\afterSales\StoreConfigModel.class
com\xinghuo\allegro\sale\entity\AllegroNewProductEnEntity.class
com\xinghuo\fruugo\analysis\service\impl\FruugoAnalysisServiceImpl.class
com\xinghuo\tenant\model\RuleModel.class
com\xinghuo\fruugo\data\model\FruugoDataSellerPagination.class
com\xinghuo\niceapi\ozon\model\returns\RfbsReturnsListRequest.class
com\xinghuo\niceapi\allegro\order\RefundAPI.class
com\xinghuo\niceapi\fpx\model\create\LogisticsServiceInfo.class
com\xinghuo\niceapi\ozon\model\fbs\ProductWeightChangeRequest$ProductWeightItem$ProductWeightItemBuilder.class
com\xinghuo\allegro\home\model\SalesStatModel$Metrics.class
com\xinghuo\fruugo\collect\dao\FruugoClaimTaskMapper.class
com\xinghuo\allegro\push\service\impl\PushBatchServiceImpl.class
com\xinghuo\allegro\collect\service\CollectTaskService.class
com\xinghuo\niceapi\ozon\model\returns\RfbsReturnDetailResponse$State.class
com\xinghuo\niceapi\ozon\model\promo\PromoTaskApproveRequest$PromoTaskApproveRequestBuilder.class
com\xinghuo\tenant\util\JdbcUtil.class
com\xinghuo\allegro\order\service\impl\ExpressDeclarationServiceImpl.class
com\xinghuo\allegro\push\dao\ProducerMapper.class
com\xinghuo\allegro\data\service\AllegroDataOfferService.class
com\xinghuo\allegro\push\model\erpGroup\ErpGroupPagination.class
com\xinghuo\fruugo\data\model\AllegroProductFormModel.class
com\xinghuo\niceapi\ozon\returns\OzonReturnsAPI.class
com\xinghuo\niceapi\ozon\model\category\CategoryAttributeRequest$CategoryAttributeRequestBuilder.class
com\xinghuo\allegro\order\model\express\ExpressFeeSettingModel.class
com\xinghuo\fyndiq\data\model\FyndiqFruugoTemplateConfig$ExportOptions.class
com\xinghuo\allegro\sale\model\product\CategoryParameter$Options.class
com\xinghuo\allegro\push\service\impl\PushNameServiceImpl.class
com\xinghuo\allegro\collect\dao\CollectProductMapper.class
com\xinghuo\allegro\manage\service\ExportTaskService.class
com\xinghuo\allegro\home\service\IndexDataService.class
com\xinghuo\fruugo\analysis\model\ProductRankingVO.class
com\baidu\translate\TestBASE64.class
com\xinghuo\allegro\manage\service\impl\AllegroServiceImpl.class
com\xinghuo\allegro\sale\model\product\CategoryParameter.class
com\xinghuo\fruugo\analysis\service\impl\FruugoSkuMonitorServiceImpl.class
com\xinghuo\fruugo\collect\service\FruugoSellerService.class
com\xinghuo\allegro\order\controller\PDFController.class
com\xinghuo\niceapi\ozon\model\product\RelatedSkuResponse$RelatedSkuInfo.class
com\xinghuo\allegro\push\model\offer\SaleDateSdkCopyForm.class
com\xinghuo\allegro\push\service\AllegroCategoryParameterService.class
com\xinghuo\fruugo\collect\service\impl\FruugoProductSyncServiceImpl.class
com\baidu\translate\TransApi.class
com\xinghuo\allegro\shelf\service\AllegroShelfBatchSummaryService.class
com\xinghuo\niceapi\fpx\model\create\ParcelInfo$ParcelInfoBuilder.class
com\xinghuo\fruugo\analysis\service\impl\FruugoSalesMonitorServiceImpl.class
com\xinghuo\niceapi\ozon\model\cancellation\ConditionalCancellationListRequest$With.class
com\xinghuo\niceapi\ozon\model\category\CategoryAttributeResponse.class
com\xinghuo\niceapi\ozon\model\product\ProductListResponse.class
com\xinghuo\ozon\offer\dao\OzonOfferStockMapper.class
com\xinghuo\allegro\push\controller\ErpProductController.class
com\xinghuo\ozon\offer\entity\OzonOfferStockEntity.class
com\xinghuo\fruugo\analysis\task\FruugoMonitorTask.class
com\xinghuo\fruugo\data\model\AllegroProductPagination.class
com\xinghuo\niceapi\ozon\model\cancellation\ConditionalCancellationResponse.class
com\xinghuo\niceapi\ozon\model\delivery\DeliveryStatusRequest.class
com\xinghuo\allegro\push\controller\MenuController.class
com\xinghuo\fruugo\data\model\FruugoDataSellerForm.class
com\xinghuo\fruugo\analysis\service\FruugoAlertService.class
com\xinghuo\fruugo\collect\service\FruugoPushOfferService.class
com\xinghuo\niceapi\coupang\model\product\CoupangProductCreateResponse.class
com\xinghuo\allegro\push\model\shelfTemplate\ShelfTemplatePagination.class
com\xinghuo\niceapi\allegro\afterSales\GprsAPI.class
com\xinghuo\fruugo\manage\controller\FruugoStoreController.class
com\xinghuo\niceapi\ozon\model\product\ProductImportRequest.class
com\xinghuo\niceapi\ozon\model\fbs\UnfulfilledPostingsResponse$DeliveryMethod.class
com\xinghuo\allegro\msg\model\rating\MsgRatingDetailModel.class
com\xinghuo\niceapi\ozon\model\product\ProductInfoResponse$VisibilityDetails.class
com\xinghuo\niceapi\fpx\model\create\AddressInfo$AddressInfoBuilder.class
com\xinghuo\allegro\push\dao\ShelfTemplateMapper.class
com\xinghuo\ozon\manage\controller\OzonStoreConfigController.class
com\xinghuo\niceapi\ozon\model\product\ProductAttributeInfoResponse$Value.class
com\xinghuo\coupang\manage\dao\CoupangCategoryMetaMapper.class
com\xinghuo\ozon\msg\service\impl\OzonChatMessageServiceImpl.class
com\xinghuo\niceapi\ozon\model\price\UpdateProductDiscountResponse.class
com\xinghuo\ozon\manage\model\product\OzonProductModel.class
com\xinghuo\allegro\order\entity\ErpOrderEntity.class
com\xinghuo\niceapi\fpx\model\AffterentParam$AffterentParamBuilder.class
com\xinghuo\niceapi\fpx\model\create\Fpx4OrderCreateRequest$Fpx4OrderCreateRequestBuilder.class
com\xinghuo\niceapi\ozon\model\product\ProductAttributeInfoResponse$AttributeValue.class
com\xinghuo\fruugo\collect\model\FruugoPushOfferBatchQueryForm.class
