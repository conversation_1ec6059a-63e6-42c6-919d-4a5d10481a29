package com.xinghuo.allegro.collect.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@TableName("zz_collect_event")
@Data
public class CollectEventEntity {

    @TableId("f_id")
    private String id;

    private String eventType;

    private String eventDesc;

    private Integer eventStatus;

    private Date createTime;

    private Date updateTime;
}
