package com.xinghuo.allegro.collect.service;

import com.xinghuo.allegro.collect.entity.CollectEventEntity;
import com.xinghuo.common.base.service.BaseService;

/**
 * Ean 服务
 *
 * <AUTHOR>
 */
public interface CollectEventService extends BaseService<CollectEventEntity> {

    int checkCollectOfferVio();

    int checkNewProductVio(String tenantId);

    int checkOfferVio();

    int checkErpProductVio(String tenantId);


    void todaySummary(boolean isSummary);

}