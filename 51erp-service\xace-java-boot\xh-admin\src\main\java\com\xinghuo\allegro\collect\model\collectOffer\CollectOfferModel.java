package com.xinghuo.allegro.collect.model.collectOffer;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 采集数据Model
 * <AUTHOR>
 * 2024-10-20
 */
@Data
public class CollectOfferModel {

    @Schema(description = "唯一标识ID")
    private String id;

    @Schema(description = "报价ID")
    private String offerId;

    @Schema(description = "SKU ID")
    private Integer skuId;

    @Schema(description = "报价链接")
    private String offerLink;

    @Schema(description = "报价名称")
    private String offerName;

    @Schema(description = "图片URL")
    private String imageUrl;

    @Schema(description = "价格")
    private BigDecimal price;

    @Schema(description = "运费")
    private BigDecimal shipFee;

    @Schema(description = "总价")
    private BigDecimal totalPrice;

    @Schema(description = "购买数量")
    private Integer buyersQuantity;

    @Schema(description = "分类ID")
    private String categoryId;

    @Schema(description = "分类路径")
    private String categoryPath;

    @Schema(description = "卖家ID")
    private String sellerId;

    @Schema(description = "卖家链接")
    private String sellerListUrl;

    @Schema(description = "产品ID")
    private String productId;

    @Schema(description = "新产品ID")
    private String newProductId;

    @Schema(description = "发货国家")
    private String dispatchCountry;

    @Schema(description = "配送时间")
    private String deliveryTime;

    @Schema(description = "配送次数")
    private Integer deliveryCount;

    @Schema(description = "国家")
    private String country;

    @Schema(description = "创建用户")
    private String creatorUserId;

    @Schema(description = "创建时间")
    private Date creatorTime;





}
