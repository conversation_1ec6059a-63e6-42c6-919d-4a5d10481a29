package com.xinghuo.allegro.data.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xinghuo.allegro.data.dao.AllegroSellerAnalysisMapper;
import com.xinghuo.allegro.data.entity.AllegroSellerEntity;
import com.xinghuo.allegro.data.model.seller.AllegroSellerAnalysisVO;
import com.xinghuo.allegro.data.service.AllegroSellerAnalysisService;
import com.xinghuo.allegro.data.service.AllegroSellerService;
import com.xinghuo.common.util.core.DateXhUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * Allegro卖家分析服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class AllegroSellerAnalysisServiceImpl implements AllegroSellerAnalysisService {

    @Resource
    private AllegroSellerService allegroSellerService;

    @Resource
    private AllegroSellerAnalysisMapper allegroSellerAnalysisMapper;

    @Override
    public AllegroSellerAnalysisVO getSellerStats() {
        AllegroSellerAnalysisVO stats = new AllegroSellerAnalysisVO();

        // 获取总卖家数
        LambdaQueryWrapper<AllegroSellerEntity> totalQuery = new LambdaQueryWrapper<>();
        long totalSellers = allegroSellerService.count(totalQuery);
        stats.setTotalSellers(totalSellers);

        // 获取有效卖家数
        LambdaQueryWrapper<AllegroSellerEntity> activeQuery = new LambdaQueryWrapper<>();
        activeQuery.eq(AllegroSellerEntity::getStatus, 1);
        long activeSellers = allegroSellerService.count(activeQuery);
        stats.setActiveSellers(activeSellers);

        // 获取有效高级卖家数（在线数据大于500的卖家）
        LambdaQueryWrapper<AllegroSellerEntity> premiumQuery = new LambdaQueryWrapper<>();
        premiumQuery.gt(AllegroSellerEntity::getTotalNum, 500);
        premiumQuery.eq(AllegroSellerEntity::getStatus, 1);
        long premiumSellers = allegroSellerService.count(premiumQuery);
        stats.setPremiumSellers(premiumSellers);

        // 获取普通卖家数（在线数据小于等于500的卖家）
        LambdaQueryWrapper<AllegroSellerEntity> normalQuery = new LambdaQueryWrapper<>();
        normalQuery.le(AllegroSellerEntity::getTotalNum, 500);
        normalQuery.eq(AllegroSellerEntity::getStatus, 1);
        long normalSellers = allegroSellerService.count(normalQuery);
        stats.setNormalSellers(normalSellers);

        // 获取黑名单卖家数
        LambdaQueryWrapper<AllegroSellerEntity> blockedQuery = new LambdaQueryWrapper<>();
        blockedQuery.eq(AllegroSellerEntity::getSellType, "BLOCKED");
        long blockedSellers = allegroSellerService.count(blockedQuery);
        stats.setBlockedSellers(blockedSellers);

        // 获取今日新增卖家数
        Date today = DateXhUtil.beginOfDay(new Date());
        LambdaQueryWrapper<AllegroSellerEntity> todayQuery = new LambdaQueryWrapper<>();
        todayQuery.ge(AllegroSellerEntity::getCreatorTime, today);
        long todayNewSellers = allegroSellerService.count(todayQuery);
        stats.setTodayNewSellers(todayNewSellers);

        // 获取昨日新增卖家数
        Date yesterday = DateXhUtil.offsetDay(today, -1);
        Date beforeYesterday = DateXhUtil.offsetDay(today, -2);
        LambdaQueryWrapper<AllegroSellerEntity> yesterdayQuery = new LambdaQueryWrapper<>();
        yesterdayQuery.ge(AllegroSellerEntity::getCreatorTime, yesterday)
                .lt(AllegroSellerEntity::getCreatorTime, today);
        long yesterdayNewSellers = allegroSellerService.count(yesterdayQuery);
        stats.setYesterdayNewSellers(yesterdayNewSellers);

        // 获取本周新增卖家数
        Date weekStart = DateXhUtil.beginOfWeek(new Date());
        LambdaQueryWrapper<AllegroSellerEntity> weekQuery = new LambdaQueryWrapper<>();
        weekQuery.ge(AllegroSellerEntity::getCreatorTime, weekStart);
        long weekNewSellers = allegroSellerService.count(weekQuery);
        stats.setWeekNewSellers(weekNewSellers);

        // 获取本月新增卖家数
        Date monthStart = DateXhUtil.beginOfMonth(new Date());
        LambdaQueryWrapper<AllegroSellerEntity> monthQuery = new LambdaQueryWrapper<>();
        monthQuery.ge(AllegroSellerEntity::getCreatorTime, monthStart);
        long monthNewSellers = allegroSellerService.count(monthQuery);
        stats.setMonthNewSellers(monthNewSellers);

        // 获取今日黑名单卖家数
        LambdaQueryWrapper<AllegroSellerEntity> todayBlockedQuery = new LambdaQueryWrapper<>();
        todayBlockedQuery.eq(AllegroSellerEntity::getSellType, "BLOCKED")
                .ge(AllegroSellerEntity::getEndDate, today);
        long todayBlockedSellers = allegroSellerService.count(todayBlockedQuery);
        stats.setTodayBlockedSellers(todayBlockedSellers);

        // 获取昨日黑名单卖家数
        LambdaQueryWrapper<AllegroSellerEntity> yesterdayBlockedQuery = new LambdaQueryWrapper<>();
        yesterdayBlockedQuery.eq(AllegroSellerEntity::getSellType, "BLOCKED")
                .ge(AllegroSellerEntity::getEndDate, yesterday)
                .lt(AllegroSellerEntity::getEndDate, today);
        long yesterdayBlockedSellers = allegroSellerService.count(yesterdayBlockedQuery);
        stats.setYesterdayBlockedSellers(yesterdayBlockedSellers);

        // 获取本周黑名单卖家数
        LambdaQueryWrapper<AllegroSellerEntity> weekBlockedQuery = new LambdaQueryWrapper<>();
        weekBlockedQuery.eq(AllegroSellerEntity::getSellType, "BLOCKED")
                .ge(AllegroSellerEntity::getEndDate, weekStart);
        long weekBlockedSellers = allegroSellerService.count(weekBlockedQuery);
        stats.setWeekBlockedSellers(weekBlockedSellers);

        // 获取本月黑名单卖家数
        LambdaQueryWrapper<AllegroSellerEntity> monthBlockedQuery = new LambdaQueryWrapper<>();
        monthBlockedQuery.eq(AllegroSellerEntity::getSellType, "BLOCKED")
                .ge(AllegroSellerEntity::getEndDate, monthStart);
        long monthBlockedSellers = allegroSellerService.count(monthBlockedQuery);
        stats.setMonthBlockedSellers(monthBlockedSellers);

        // 计算卖家增长率
        Date lastMonth = DateXhUtil.offsetMonth(monthStart, -1);
        LambdaQueryWrapper<AllegroSellerEntity> lastMonthQuery = new LambdaQueryWrapper<>();
        lastMonthQuery.ge(AllegroSellerEntity::getCreatorTime, lastMonth)
                .lt(AllegroSellerEntity::getCreatorTime, monthStart);
        long lastMonthNewSellers = allegroSellerService.count(lastMonthQuery);
        double sellerGrowthRate = lastMonthNewSellers > 0 ? (double) monthNewSellers / lastMonthNewSellers - 1 : 0;
        stats.setSellerGrowthRate(sellerGrowthRate);

        // 计算高级卖家占比
        double premiumSellerRate = totalSellers > 0 ? (double) premiumSellers / totalSellers : 0;
        stats.setPremiumSellerRate(premiumSellerRate);

        // 计算黑名单卖家占比
        double blockedSellerRate = totalSellers > 0 ? (double) blockedSellers / totalSellers : 0;
        stats.setBlockedSellerRate(blockedSellerRate);

        return stats;
    }

    @Override
    public Map<String, Object> getSellerTypeStats() {
        Map<String, Object> result = new HashMap<>();

        // 获取卖家类型分布数据
        List<Map<String, Object>> typeStats = allegroSellerAnalysisMapper.getSellerTypeStats();

        // 处理数据
        List<String> types = new ArrayList<>();
        List<Integer> counts = new ArrayList<>();

        for (Map<String, Object> stat : typeStats) {
            String type = (String) stat.get("type");
            Integer count = ((Number) stat.get("count")).intValue();

            types.add(type);
            counts.add(count);
        }

        result.put("types", types);
        result.put("counts", counts);
        result.put("data", typeStats);

        return result;
    }

    @Override
    public Map<String, Object> getSellerStatusStats() {
        Map<String, Object> result = new HashMap<>();

        // 获取卖家状态分布数据
        List<Map<String, Object>> statusStats = allegroSellerAnalysisMapper.getSellerStatusStats();

        // 处理数据
        List<String> statuses = new ArrayList<>();
        List<Integer> counts = new ArrayList<>();

        for (Map<String, Object> stat : statusStats) {
            Integer status = ((Number) stat.get("status")).intValue();
            Integer count = ((Number) stat.get("count")).intValue();

            String statusText = status == 1 ? "启用" : "禁用";
            statuses.add(statusText);
            counts.add(count);
        }

        result.put("statuses", statuses);
        result.put("counts", counts);
        result.put("data", statusStats);

        return result;
    }

    @Override
    public List<Map<String, Object>> getNewSellersTrend(Integer days) {
        return allegroSellerAnalysisMapper.getNewSellersTrend(days);
    }

    @Override
    public List<Map<String, Object>> getBlockedSellersTrend(Integer days) {
        return allegroSellerAnalysisMapper.getBlockedSellersTrend(days);
    }

    @Override
    public List<Map<String, Object>> getMonthlyStats(Date startDate, Date endDate) {
        return allegroSellerAnalysisMapper.getMonthlyStats(startDate, endDate);
    }

    @Override
    public Map<String, Object> getPremiumSellersStats() {
        Map<String, Object> result = new HashMap<>();

        // 获取高级卖家统计数据
        Map<String, Object> stats = allegroSellerAnalysisMapper.getPremiumSellersStats();

        // 处理数据
        int totalSellers = ((Number) stats.get("totalSellers")).intValue();
        int premiumSellers = ((Number) stats.get("premiumSellers")).intValue();
        int normalSellers = ((Number) stats.get("normalSellers")).intValue();

        double premiumRate = totalSellers > 0 ? (double) premiumSellers / totalSellers : 0;

        result.put("totalSellers", totalSellers);
        result.put("premiumSellers", premiumSellers);
        result.put("normalSellers", normalSellers);
        result.put("premiumRate", premiumRate);

        return result;
    }

    @Override
    public Map<String, Object> getChinaSellerStats() {
        Map<String, Object> result = new HashMap<>();

        // 获取中国卖家统计数据
        Map<String, Object> stats = allegroSellerAnalysisMapper.getChinaSellerStats();

        // 处理数据
        int totalSellers = ((Number) stats.get("totalSellers")).intValue();
        int activeSellers = ((Number) stats.get("activeSellers")).intValue();
        int premiumSellers = ((Number) stats.get("premiumSellers")).intValue();
        int newSellers = ((Number) stats.get("newSellers")).intValue();

        // 计算比率
        double activeRate = totalSellers > 0 ? (double) activeSellers / totalSellers : 0;
        double premiumRate = totalSellers > 0 ? (double) premiumSellers / totalSellers : 0;

        // 计算增长率（这里使用模拟数据，实际项目中应该从数据库获取上个月的数据进行比较）
        // 假设上个月中国卖家总数为 totalSellers - newSellers
        double growthRate = (totalSellers - newSellers) > 0 ?
                (double) newSellers / (totalSellers - newSellers) : 0;

        result.put("totalSellers", totalSellers);
        result.put("activeSellers", activeSellers);
        result.put("premiumSellers", premiumSellers);
        result.put("newSellers", newSellers);
        result.put("activeRate", activeRate);
        result.put("premiumRate", premiumRate);
        result.put("growthRate", growthRate);

        return result;
    }

    @Override
    public List<Map<String, Object>> getChinaSellersTrend(Integer days) {
        // 获取中国卖家趋势数据
        return allegroSellerAnalysisMapper.getChinaSellersTrend(days);
    }

    @Override
    public List<Map<String, Object>> getHotSellers(Integer days, Integer limit) {
        return allegroSellerAnalysisMapper.getHotSellers(days, limit);
    }
}
