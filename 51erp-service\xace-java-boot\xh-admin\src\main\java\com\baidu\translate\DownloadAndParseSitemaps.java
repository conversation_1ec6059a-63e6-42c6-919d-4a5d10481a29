package com.baidu.translate;

import org.apache.commons.io.IOUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.net.URL;
import java.util.zip.GZIPInputStream;

public class DownloadAndParseSitemaps {

    public static void main(String[] args) {
        String sitemapUrl = "https://www.fruugo.co.uk/sitemap.xml";
        String outputDir = "d:/sitemaps";

        try {
            // Create output directory if it doesn't exist
            File dir = new File(outputDir);
            if (!dir.exists()) {
                dir.mkdirs();
            }

            // Parse the main sitemap XML
            Document doc = Jsoup.connect(sitemapUrl).get();
            Elements sitemaps = doc.select("sitemap loc");

            for (Element sitemap : sitemaps) {
                String sitemapPartUrl = sitemap.text();
                System.out.println("Downloading: " + sitemapPartUrl);
                downloadAndExtractSitemap(sitemapPartUrl, outputDir);
            }

            System.out.println("All sitemaps downloaded and extracted successfully.");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static void downloadAndExtractSitemap(String sitemapUrl, String outputDir) {
        try (InputStream in = new URL(sitemapUrl).openStream();
             GZIPInputStream gis = new GZIPInputStream(in);
             FileOutputStream fos = new FileOutputStream(new File(outputDir, new File(sitemapUrl).getName().replace(".gz", "")))) {

            IOUtils.copy(gis, fos);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
