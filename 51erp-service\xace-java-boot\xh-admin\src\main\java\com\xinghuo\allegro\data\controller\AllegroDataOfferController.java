package com.xinghuo.allegro.data.controller;

import com.xinghuo.allegro.data.entity.AllegroDataOfferEntity;
import com.xinghuo.allegro.data.model.AllegroDataLinkModel;
import com.xinghuo.allegro.data.model.AllegroDataOfferModel;
import com.xinghuo.allegro.data.model.AllegroDataOfferParseForm;
import com.xinghuo.allegro.data.model.AllegroDataRemoveForm;
import com.xinghuo.allegro.data.service.AllegroDataOfferService;
import com.xinghuo.allegro.data.service.AllegroDataTaskService;
import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.common.util.core.StrXhUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Allegro 英文采集商品控制器
 *
 * <AUTHOR>
 * @date 2024-11-05
 */
@Slf4j
@RestController
@Tag(name = "Allegro 英文采集", description = "Allegro 英文采集商品管理")
@RequestMapping("/api/allegro/data/collect")
public class AllegroDataOfferController {

    @Resource
    private AllegroDataOfferService allegroDataOfferService;

    @Resource
    private AllegroDataTaskService allegroDataTaskService;

    /**
     * 批量收集产品信息--目录采集
     *
     * @param list 包含待收集的产品信息的列表
     * @return 表示操作结果的ActionResult对象，包含成功接收的数据条数
     */
    @Operation(summary = "批量收集英文数据")
    @PutMapping("/batch")
    public ActionResult<String> batchCollect(@RequestBody List<AllegroDataOfferModel> list) {
        List<AllegroDataOfferEntity> entityList = new ArrayList<>();
        String taskType = "S";

        for (AllegroDataOfferModel model : list) {
            AllegroDataOfferEntity entity = BeanCopierUtils.copy(model, AllegroDataOfferEntity.class);
            entity.setListClientId(model.getClientId());

            // 如果是卖家采集
            if ("S".equalsIgnoreCase(model.getTaskType())) {
                entity.setSellerId(model.getCategoryId());
                entity.setCategoryId(null);
            }
            taskType = model.getTaskType();
            entityList.add(entity);
        }

        allegroDataOfferService.saveBatch(taskType, entityList);

        if (!list.isEmpty()) {
            AllegroDataOfferModel model = list.get(0);
            if (StrXhUtil.isNotBlank(model.getTaskId())) {
                allegroDataTaskService.updateCollectTask(model.getTaskId());
            }
        }

        return ActionResult.success("成功接收英文数据条数：" + entityList.size());
    }

    /**
     * 获取待处理的产品链接列表
     *
     * @return 表示操作结果的ActionResult对象，包含待处理的产品链接列表
     */
    @Operation(summary = "获取后台待处理英文数据清单")
    @GetMapping("/waitGets")
    public ActionResult<List<AllegroDataLinkModel>> waitGets() {
        List<AllegroDataOfferEntity> entityList = allegroDataOfferService.waitGets();
        List<AllegroDataLinkModel> linkModels = BeanCopierUtils.copyList(entityList, AllegroDataLinkModel.class);
        return ActionResult.success(linkModels);
    }

    /**
     * 前端提交详情页的json数据
     * 注意：images、description、parameter 直接保存到 AllegroDataOfferEntity 字段中
     *
     * @param offerParseForm 解析表单
     */
    @Operation(summary = "前端提交英文详情页的json数据")
    @PutMapping("/putOfferJson")
    @Transactional
    public ActionResult<String> putOfferJson(@RequestBody AllegroDataOfferParseForm offerParseForm) {
        log.debug("前端提交英文详情页的json数据 offerId:{}", offerParseForm.getCpId());

        // 获取商品实体
        AllegroDataOfferEntity offerEntity = allegroDataOfferService.getById(offerParseForm.getCpId());
        if (offerEntity == null) {
            log.warn("获取英文报价详情：数据不存在，忽略。采集offerId:" + offerParseForm.getCpId());
            return ActionResult.fail("数据不存在，忽略");
        }

        try {
            // 从JSON中提取并直接保存 images、description、parameters
            if (offerParseForm.getOfferJson() != null) {
                // 提取 images
                if (offerParseForm.getOfferJson().has("images")) {
                    offerEntity.setImages(offerParseForm.getOfferJson().get("images").toString());
                }

                // 提取 description
                if (offerParseForm.getOfferJson().has("description")) {
                    offerEntity.setDescription(offerParseForm.getOfferJson().get("description").asText());
                }

                // 提取 parameters
                if (offerParseForm.getOfferJson().has("parameters")) {
                    offerEntity.setParameters(offerParseForm.getOfferJson().get("parameters").toString());
                }
            }

            // 更新基础信息
            offerEntity.setDetailClientId(offerParseForm.getClientId());
            offerEntity.setRequestStatus(2); // 已完成
            offerEntity.setReqFinishTime(new Date());
            offerEntity.setLastUpdatedAt(new Date());

            // 处理商品数据
            allegroDataOfferService.dealData(offerEntity);

            log.info("成功保存英文JSON数据！offerId: {}", offerEntity.getOfferId());
            return ActionResult.success("成功保存英文JSON数据！offerId: " + offerEntity.getOfferId());

        } catch (Exception e) {
            log.error("处理英文JSON数据失败，offerId: {}", offerParseForm.getCpId(), e);
            return ActionResult.fail("处理英文JSON数据失败: " + e.getMessage());
        }
    }

    /**
     * 404或者销售结束删除offer
     */
    @Operation(summary = "404或者销售结束删除英文offer")
    @PutMapping("/remove")
    public ActionResult<String> putOfferRemove(@RequestBody AllegroDataRemoveForm form) {
        log.warn("404或者销售结束直接删除英文数据。报404的数据 cpId:{}", form.getCpId());

        AllegroDataOfferEntity offerEntity = allegroDataOfferService.getById(form.getCpId());
        if (offerEntity != null) {
            if (StrXhUtil.isBlank(form.getReason())) {
                form.setReason("404错误");
            }

            offerEntity.setNote(form.getReason() + ";" + StrXhUtil.blankToDefault(offerEntity.getNote(), ""));
            offerEntity.setDetailClientId(form.getClientId());
            offerEntity.setLastUpdatedAt(new Date());

            allegroDataOfferService.updateById(offerEntity);
            allegroDataOfferService.removeById(form.getCpId());

            return ActionResult.success("404错误或者销售结束，成功删除英文数据！" + form.getCpId());
        }

        return ActionResult.fail("未找到英文数据！" + form.getCpId());
    }

    /**
     * 新卖家英文数据采集
     * 处理新卖家的英文数据采集，支持不同的任务类型
     * 根据 offerId 判断数据是否已存在，存在则跳过，否则插入
     */
    @Operation(summary = "新卖家英文数据采集")
    @PutMapping("/newSellerCollect/{taskType}")
    public ActionResult<String> newSellerCollect(@PathVariable("taskType") String taskType,
                                                 @RequestBody List<AllegroDataOfferModel> list) {

        if (list == null || list.isEmpty()) {
            return ActionResult.success("数据为空，无需处理");
        }

        List<AllegroDataOfferEntity> entityList = new ArrayList<>();
        int originalCount = list.size();

            // 产品列表英文数据：直接处理所有数据
        for (AllegroDataOfferModel model : list) {
            AllegroDataOfferEntity entity = BeanCopierUtils.copy(model, AllegroDataOfferEntity.class);
            entity.setOfferId(model.getOfferId());
            entity.setListClientId(model.getClientId());
            entityList.add(entity);
        }


        // 批量保存数据（内部会进行 offerId 重复检查）
        if (!entityList.isEmpty()) {
            allegroDataOfferService.saveBatch(taskType, entityList);
            log.info("英文数据采集完成，任务类型: {}, 原始数量: {}, 处理数量: {}",
                    taskType, originalCount, entityList.size());
        } else {
            log.info("无有效数据需要处理，任务类型: {}, 原始数量: {}", taskType, originalCount);
        }

        return ActionResult.success(String.format("成功处理英文数据采集，任务类型: %s, 原始数量: %d, 处理数量: %d",
                taskType, originalCount, entityList.size()));
    }
}
