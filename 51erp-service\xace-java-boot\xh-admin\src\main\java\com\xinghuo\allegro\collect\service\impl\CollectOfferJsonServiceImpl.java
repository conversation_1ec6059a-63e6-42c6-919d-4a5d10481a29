package com.xinghuo.allegro.collect.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xinghuo.allegro.collect.dao.CollectOfferJsonMapper;
import com.xinghuo.allegro.collect.entity.CollectOfferJsonEntity;
import com.xinghuo.allegro.collect.model.OfferJsonUtil;
import com.xinghuo.allegro.collect.service.CollectOfferJsonService;
import com.xinghuo.common.annotation.NoDataSourceBind;
import com.xinghuo.common.base.service.BaseServiceImpl;
import com.xinghuo.common.database.util.NotTenantPluginHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

@Slf4j
@Service
public class CollectOfferJsonServiceImpl extends BaseServiceImpl<CollectOfferJsonMapper, CollectOfferJsonEntity> implements CollectOfferJsonService {



    @Override
    public void syncTmpData(){


        boolean hasMoreData = true;
        Integer currentPage = 0;
        ExecutorService executorService = Executors.newFixedThreadPool(20);
        while (hasMoreData) {
            log.info("TMP待检查数据,当前页面数：" + (currentPage++));
            List<CollectOfferJsonEntity> list =dealDataList();
            if (list.isEmpty()) {
                hasMoreData = false;
            } else {
                List<Future<Boolean>> futures = new ArrayList<>();

                for (CollectOfferJsonEntity entity : list) {

                    futures.add(executorService.submit(() -> {
                        try {
                            entity.setOfferJson(OfferJsonUtil.minOfferJson(entity.getOfferJson()));
                            entity.setDealStatus(1);
                            super.updateById(entity);
                            return true;
                        } catch (Exception e) {
                            log.error("Error processing product with id", e);
                        }
                        return false;
                    }));
                }
                // 等待所有任务完成并检查更新状态
                for (Future<Boolean> future : futures) {
                    try {
                        future.get();
                    } catch (Exception e) {
                        log.error("Error waiting for product update", e);
                    }
                }
            }
        }

    }

    @Override
    public List<CollectOfferJsonEntity> dealDataList() {
        // 创建查询条件，指定查询0状态的采集优惠信息，并按照购买数量降序排列，限制查询一条数据。
        QueryWrapper<CollectOfferJsonEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(CollectOfferJsonEntity::getDealStatus, 0)
                .last("limit 4000");
        return this.list(queryWrapper);
    }
        @Override
        @NoDataSourceBind
        public   CollectOfferJsonEntity  getInfo(String id){
            NotTenantPluginHolder.setNotSwitchAlwaysFlag();
            try {
                QueryWrapper<CollectOfferJsonEntity> queryWrapper = new QueryWrapper<>();
                queryWrapper.lambda()
                        .eq(CollectOfferJsonEntity::getId, id)
                        .last("limit 1");
                return this.getOne(queryWrapper);
            }finally {
                NotTenantPluginHolder.clearNotSwitchAlwaysFlag();
            }
        }

}
