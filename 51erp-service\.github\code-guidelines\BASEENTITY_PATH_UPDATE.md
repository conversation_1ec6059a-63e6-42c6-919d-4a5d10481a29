# BaseEntity 路径更新说明

## 更新概述

本次更新修正了代码规范文档中 BaseEntity 相关类的导入路径，确保开发人员使用正确的包路径。

## 主要更新内容

### 1. 修正 BaseEntityV2 导入路径

**正确的导入路径**：
```java
import com.xinghuo.common.base.entity.BaseEntityV2;
```

**错误的导入路径**（已修正）：
```java
import com.xinghuo.common.base.BaseEntityV2;  // ❌ 错误
```

### 2. 更新的文档

#### 2.1 Entity 层规范 (06_ENTITY_GUIDELINES.md)
- 添加了正确的 BaseEntityV2 导入路径说明
- 更新了示例代码中的导入语句
- 添加了其他基类的使用说明和导入路径

#### 2.2 后端架构指南 (10_BACKEND_ARCHITECTURE.md)
- 更新了实体层示例代码的导入路径
- 确保示例代码的完整性和正确性

### 3. 项目中的基类体系

项目中提供了多种基类，适用于不同场景：

#### 3.1 BaseEntityV2 系列（推荐）
```java
import com.xinghuo.common.base.entity.BaseEntityV2;

// 继承示例
public class ProductEntity extends BaseEntityV2.CUBaseEntityV2<String> {
    // 实体字段...
}
```

**变体说明**：
- `IBaseEntityV2`: 包含ID
- `TBaseEntityV2`: 包含ID + 租户ID
- `CBaseEntityV2`: 包含创建信息
- `CUBaseEntityV2`: 包含创建+更新信息（推荐默认使用）
- `CUDBaseEntityV2`: 包含创建+更新+删除信息

#### 3.2 AbstractBaseEntity 系列
```java
import com.xinghuo.common.base.entity.AbstractBaseEntity;

// 使用示例
public class MessageEntity extends AbstractBaseEntity.AbstractTBaseEntity<String> {
    // 实体字段...
}
```

#### 3.3 BaseExtendEntity 系列
```java
import com.xinghuo.common.base.entity.BaseExtendEntity;

// 使用示例
public class GroupEntity extends BaseExtendEntity.BaseExtendSortEntity<String> {
    // 实体字段...
}
```

#### 3.4 BaseEntity（传统基类）
```java
import com.xinghuo.common.base.entity.BaseEntity;

// 使用示例
public class SystemEntity extends BaseEntity<String> {
    // 实体字段...
}
```

## 选择建议

### 新项目/新模块
- **优先使用**: `BaseEntityV2.CUBaseEntityV2<String>`
- **原因**: 提供完整的基础字段（ID、租户ID、创建和更新信息）

### 现有模块
- **保持一致**: 与现有代码使用相同的基类
- **原因**: 避免混用不同基类导致的不一致

### 特殊需求
- **按需选择**: 根据具体字段需求选择合适的基类
- **考虑因素**: 是否需要租户隔离、是否需要删除标记等

## 实际应用示例

### 完整的实体类示例
```java
package com.xinghuo.product.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.BaseEntityV2;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 产品实体
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("product_info")
public class ProductEntity extends BaseEntityV2.CUBaseEntityV2<String> {

    /**
     * 产品名称
     */
    @TableField("product_name")
    private String productName;

    /**
     * 产品价格
     */
    @TableField("price")
    private BigDecimal price;

    /**
     * 产品状态
     */
    @TableField("status")
    private Integer status;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    @TableLogic
    private Boolean isDeleted;
}
```

## 注意事项

1. **导入路径**: 确保使用正确的包路径 `com.xinghuo.common.base.entity.*`
2. **基类选择**: 新项目优先使用 BaseEntityV2 系列
3. **字段重复**: 避免在实体中重复定义基类已提供的字段
4. **注解使用**: 继承时必须使用 `@EqualsAndHashCode(callSuper = true)`
5. **泛型参数**: 明确指定主键类型，如 `<String>` 或 `<Long>`

## 更新日期

- **更新时间**: 2024-01-XX
- **更新内容**: 修正 BaseEntity 导入路径，完善基类使用说明
- **影响范围**: 所有使用 BaseEntity 的实体类开发
