package com.xinghuo.allegro.collect.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.xinghuo.allegro.collect.dao.CollectOfferMapper;
import com.xinghuo.allegro.collect.entity.CollectOfferEntity;
import com.xinghuo.allegro.collect.entity.CollectOfferJsonEntity;
import com.xinghuo.allegro.collect.entity.CollectZeroOfferEntity;
import com.xinghuo.allegro.collect.model.OfferJsonUtil;
import com.xinghuo.allegro.collect.model.collect.WySyncForm;
import com.xinghuo.allegro.collect.model.collectOffer.CollectForm;
import com.xinghuo.allegro.collect.model.collectOffer.CollectOfferPagination;
import com.xinghuo.allegro.collect.model.collectOffer.CollectSettingModel;
import com.xinghuo.allegro.collect.model.collectOffer.SumModel;
import com.xinghuo.allegro.collect.service.CollectOfferJsonService;
import com.xinghuo.allegro.collect.service.CollectOfferService;
import com.xinghuo.allegro.data.service.AllegroSaleService;
import com.xinghuo.allegro.collect.service.CollectZeroOfferService;
import com.xinghuo.allegro.manage.entity.AllegroStoreConfigEntity;
import com.xinghuo.allegro.manage.entity.AllegroStoreEntity;
import com.xinghuo.allegro.manage.service.AllegroLogService;
import com.xinghuo.allegro.manage.service.AllegroStoreConfigService;
import com.xinghuo.allegro.manage.service.AllegroStoreService;
import com.xinghuo.allegro.push.entity.AllegroCategoryEntity;
import com.xinghuo.allegro.push.entity.ErpProductEntity;
import com.xinghuo.allegro.push.service.AllegroCategoryService;
import com.xinghuo.allegro.push.service.CollectVioWordService;
import com.xinghuo.allegro.push.service.ErpProductService;
import com.xinghuo.allegro.sale.entity.AllegroOfferEntity;
import com.xinghuo.allegro.sale.service.AllegroOfferService;
import com.xinghuo.allegro.util.AllegroConstant;
import com.xinghuo.allegro.util.UrlExtractor;
import com.xinghuo.common.base.UserInfo;
import com.xinghuo.common.base.service.BaseServiceImpl;
import com.xinghuo.common.util.UserProvider;
import com.xinghuo.common.util.core.RandomUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.common.util.json.JsonXhUtil;
import com.xinghuo.niceapi.allegro.sale.NewOfferAPI;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.xmlbeans.impl.xb.xsdschema.Public;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.locks.ReentrantLock;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 报价录入，必须对数据进行侵权词管理
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class CollectOfferServiceImpl extends BaseServiceImpl<CollectOfferMapper, CollectOfferEntity> implements CollectOfferService {

    @Resource
    private CollectVioWordService collectVioWordService;
    @Resource
    private CollectOfferJsonService collectOfferJsonService;
    @Resource
    private CollectZeroOfferService collectZeroOfferService;
    @Resource
    private AllegroSaleService allegroSaleService;
    @Resource
    private ErpProductService erpProductService;
    @Resource
    private AllegroLogService allegroLogService;
    @Resource
    private UserProvider userProvider;
    @Resource
    private AllegroCategoryService allegroCategoryService;

    @Resource
    private AllegroStoreService allegroStoreService;

    @Resource
    private AllegroOfferService allegroOfferService;

    @Resource
    private AllegroStoreConfigService allegroStoreConfigService;

    private final ReentrantLock lock = new ReentrantLock();

    @Override
    public List<CollectOfferEntity> getList(CollectOfferPagination pagination) {
        QueryWrapper<CollectOfferEntity> queryWrapper = new QueryWrapper<>();
        extracted(pagination, queryWrapper);
        if (StrXhUtil.isBlank(pagination.getListOrder())) {
            pagination.setSidx("creatorTime");
            pagination.setSort("desc");
            sort(queryWrapper, pagination, new CollectOfferEntity());
        } else {
            //queryWrapper  设置 buy desc
            String[] desc = pagination.getListOrder().split("\\|");
            if (desc.length > 1 && desc[1].equalsIgnoreCase("desc")) {
                queryWrapper.orderByDesc(desc[0]);
            } else {
                queryWrapper.orderByAsc(desc[0]);
            }
        }

        Page<CollectOfferEntity> page = new Page<>(pagination.getCurrentPage(), pagination.getPageSize());
        IPage<CollectOfferEntity> userIpage = this.page(page, queryWrapper);
        return pagination.setDataList(userIpage.getRecords(), userIpage.getTotal());
    }



    @Override
    public List<SumModel> getSumList(String beginDate, String endDate) {
    QueryWrapper<CollectOfferEntity> queryWrapper = new QueryWrapper<>();
    queryWrapper
        .select("F_CreatorUserId as userName", "count(0) as count") // 选择需要的字段
        .groupBy("F_CreatorUserId"); // 按F_CreatorUserId分组
    if (StrXhUtil.isNotBlank(beginDate)) {
        queryWrapper.ge("F_CreatorTime", beginDate);
    }
    if (StrXhUtil.isNotBlank(endDate)) {
        queryWrapper.le("F_CreatorTime", endDate);
    }

     // 使用selectList方法来获取SumModel列表
        return this.baseMapper.selectMaps(queryWrapper).stream()
            .map(result -> {
                SumModel sumModel = new SumModel();
                sumModel.setUserName((String) result.get("userName"));
                sumModel.setCount(Integer.valueOf(result.get("count").toString()) );
                return sumModel;
            })
            .collect(Collectors.toList());
}

    private void extracted(CollectOfferPagination pagination, QueryWrapper<CollectOfferEntity> queryWrapper) {

        if (pagination.getDateType() != null && pagination.getStartTime() != null) {
            queryWrapper.ge(pagination.getDateType(), pagination.getStartTime());
        }
        if (pagination.getDateType() != null && pagination.getEndTime() != null) {
            queryWrapper.le(pagination.getDateType(), pagination.getEndTime());
        }
        if (pagination.getSellerId() != null) {
            queryWrapper.lambda().eq(CollectOfferEntity::getSellerId, pagination.getSellerId());
        }

        if (StrXhUtil.isNotBlank(pagination.getSearchKey())) {
            String[] searchFields = pagination.getSearchType().split("\\|");
            String field = searchFields[0];
            String searchType = searchFields[1];
            if (searchType.equalsIgnoreCase("EQ")) {
                queryWrapper.eq(field, pagination.getSearchKey().trim());
            } else if (searchType.equalsIgnoreCase("LIKERIGHT")) {
                queryWrapper.likeRight(field, pagination.getSearchKey().trim());
            } else if (searchType.equalsIgnoreCase("LIKE")) {
                queryWrapper.like(field, pagination.getSearchKey().trim());
            }
        }
    }


    /**
     * 批量保存采集到的Offer信息。
     * 此方法接收一个Offer信息实体列表，对每个实体进行处理，包括验证URL格式、提取offerId、检查是否存在侵权词汇，
     * 并根据处理结果决定是保存新的实体还是更新已存在的实体。
     * 保存规则：总价大于50，低于500， 售卖数量<50 才进行入库保存。
     *
     * @param list 采集到的Offer信息实体列表。
     */
    @Override
    public void saveBatch(String taskType, List<CollectOfferEntity> list) {
        // 获取有效侵权的词汇列表
        Set<String> vioWords = collectVioWordService.getValidList(null);
        for (CollectOfferEntity entity : list) {
            // 验证Offer链接是否符合特定格式，不符合则忽略该Offer信息
            // 判断 是否存在同样的link ，如果有就更新销量  根据link 获取
            if (StrXhUtil.isBlank(entity.getOfferLink()) || !(entity.getOfferLink().startsWith("https://allegro.pl/oferta/"))) {
                log.warn("链接采集：忽略；提供的Offer_url不符合要求，链接：" + entity.getOfferLink());
                continue;
            }

            // 从链接中提取offerId
            String offerId = UrlExtractor.extractOfferId(entity.getOfferLink());
            // 检查offerId是否为数字格式，不是则忽略该Offer
            // 使用正则表达式检查offerId是否只包含数字
            if (offerId != null && Pattern.matches("\\d+", offerId)) {
                //正常采集
            } else {
                log.warn("目录链接采集：忽略；无效offerId,  链接：" + entity.getOfferLink());
                continue;
            }

            //如果不是产品采集，则对链接进行判断，如果库里面已经存在，则跳过
            if (!StrXhUtil.equalsIgnoreCase("P", taskType)) {
                CollectZeroOfferEntity existEntity = collectZeroOfferService.getById(offerId);
                if (existEntity != null) {
                    log.warn("目录链接采集：存在同样数据，数据忽略。对应数据offer_id:" + offerId);
                    continue;
                }
            }


            if (taskType.equals("P") && entity.getBuyersQuantity() > 50) {
                log.warn("卖家链接采集：忽略；购买数超过50，疑似独家产品， 链接：" + entity.getOfferLink());
                continue;
            }

            if (!taskType.equals("P")) {

                if (entity.getBuyersQuantity() > 0) {
                    entity.setPriority(2);
                    entity.setBuyersQuantity(0);
                }
            }


            // 检查OfferName中是否包含侵权词,
            boolean containsVioWord = vioWords.stream()
                    .anyMatch(vioWord -> entity.getOfferName().toLowerCase().contains(vioWord.toLowerCase()));
            // 根据检查结果设置侵权状态
            if (containsVioWord && entity.getBuyersQuantity() == 0) {
                log.warn("目录链接采集：存在侵权词,零销量，忽略， 链接：" + entity.getOfferLink());
                continue;
            } else {
                // 如果不包含侵权词或销量不为零，则设置vioStatus为false，并重置requestStatus
                entity.setRequestStatus(AllegroConstant.REQUEST_STATUS_INIT);
            }

            if (entity.getTotalPrice().compareTo(new BigDecimal(20)) <= 0 || entity.getTotalPrice().compareTo(new BigDecimal(500)) > 0) {
                log.warn("链接采集：采集的产品价格不在20-500区间：忽略；价格:{}，链接：{}", entity.getTotalPrice(), entity.getOfferLink());
                continue;
            }

            //处理imageUrl
            if (StrXhUtil.isNotBlank(entity.getImageUrl())) {
                entity.setImageUrl(OfferJsonUtil.contructImagerUrl(entity.getImageUrl()));
            }

            // 设置offerId，并根据该id检查数据库中是否已存在该实体
            entity.setOfferId(offerId);
            CollectOfferEntity existEntity = getByOfferId(offerId);
            //后台采集的优先级，越高代表优先采集
            if (entity.getBuyersQuantity() != null && entity.getBuyersQuantity() > 0) {
                entity.setPriority(2);
            }
            // 如果实体不存在，则保存新的实体；如果已存在，则更新实体信息
            if (existEntity == null) {
                if (StrXhUtil.equalsIgnoreCase("P", taskType) && entity.getBuyersQuantity() != null && entity.getBuyersQuantity() > 0) {
                    allegroSaleService.saveFromOffer(entity);
                }
                entity.setCreatorTime(new Date());
                this.save(entity);

            } else {
                if (StrXhUtil.equalsIgnoreCase("P", taskType) && entity.getBuyersQuantity() != null && entity.getBuyersQuantity() > 0) {
                    allegroSaleService.saveFromExistOffer(entity, existEntity);
                }
                log.debug(entity.getOfferLink() + "数据存在，更新中");
                // 更新实体的各个字段
                if (existEntity.getTotalPrice().compareTo(entity.getTotalPrice()) != 0) {
                    log.info("ID:{} 采集的价格发生变化，总价：原：{}，新：{}", existEntity.getId(), existEntity.getTotalPrice(), entity.getTotalPrice());
                    allegroLogService.saveLog("COLLECT_OFFER", existEntity.getId(), "总价：原：" + existEntity.getTotalPrice() + "，新：" + entity.getTotalPrice());
                    // 获取ERP 中的产品价格
                    ErpProductEntity erpProductEntity = erpProductService.getByCpId(existEntity.getId(),null);
                    if (erpProductEntity != null) {
                        BigDecimal oldTotalPrice = erpProductEntity.getTotalPrice();

                        if (oldTotalPrice.compareTo(entity.getTotalPrice()) < 0) {

                            erpProductEntity.setTotalPrice(entity.getTotalPrice());
                            allegroLogService.saveLog("ERP_PRODUCT", "" + erpProductEntity.getSkuId(), "价格发生变化，原：" + oldTotalPrice + "，新：" + entity.getTotalPrice());
                            erpProductEntity.setUpdateTime(new Date());
                            erpProductService.updateById(erpProductEntity);
                        }



                    }

                    //根据productId 更新产品的价格  20250130
                    ErpProductEntity erpProductEntity2 = erpProductService.getByProductId(existEntity.getProductId(),existEntity.getTenantId());
                    if (erpProductEntity2 != null) {
                        boolean updateFlag = false;
                        BigDecimal collectMinPrice =  erpProductEntity2.getCollectMinPrice();
                        if(collectMinPrice.compareTo(entity.getTotalPrice())>0) {
                            updateFlag = true;
                            erpProductEntity2.setCollectMinPrice(entity.getTotalPrice());
                        }
                        BigDecimal collectMaxPrice =  erpProductEntity2.getCollectMaxPrice();
                        if(collectMaxPrice.compareTo(entity.getTotalPrice())<0) {
                            updateFlag = true;
                            erpProductEntity2.setCollectMaxPrice(entity.getTotalPrice());
                        }
                        if(updateFlag){
                            erpProductEntity2.setUpdateTime(new Date());
                            erpProductService.updateById(erpProductEntity2);
                        }
                    }


                }
                existEntity.setPrice(entity.getPrice());
                if (StrXhUtil.equalsIgnoreCase("P", taskType)) {
                    //20241009--只有卖家列表才能更新 数据，其他分类列表目前汇总的是总数，数据不准
                    existEntity.setBuyersQuantity(entity.getBuyersQuantity());
                }
                existEntity.setTotalPrice(entity.getTotalPrice());
                existEntity.setShipFee(entity.getShipFee());
                existEntity.setPriority(entity.getPriority());
                existEntity.setLastModifyTime(new Date());
                this.updateById(existEntity);
            }
        }
    }

    /**
     * 获取待处理的Offer信息列表。
     * <p>
     * 本方法用于查询数据库中尚未处理（vioStatus和requestStatus均为0）的采集优惠信息，
     * 并将这些信息的请求状态更新为已请求（requestStatus设为1），同时增加请求次数（reqNum+1），
     * 并记录请求时间。查询结果按照购买数量降序排列，并限制只返回一条数据。
     *
     * @return 返回待处理的采集优惠信息列表。
     */
    @Override
//    @Transactional
    public List<CollectOfferEntity> waitGets() {
        //如果锁已被另一个线程持有，当前线程将进入等待状态，直到锁被释放。
        List<CollectOfferEntity> list = new ArrayList<>();

        lock.lock();
        try {
            // 数据必须是36小时之前生成的
            //         Date yesterdayNow = DateUtil.yesterday();
            Date yesterdayNow = DateUtil.offsetHour(new Date(), -24);
            // 创建查询条件，指定查询状态的采集优惠信息，并按照购买数量降序排列，限制查询一条数据。
            QueryWrapper<CollectOfferEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda()
                    .select(CollectOfferEntity::getId, CollectOfferEntity::getOfferLink)
                    .eq(CollectOfferEntity::getRequestStatus, AllegroConstant.REQUEST_STATUS_INIT)
//                    .eq(CollectOfferEntity::getBuyersQuantity, 0)
//                    .eq(CollectOfferEntity::getRequestStatus, -3)
                    .le(CollectOfferEntity::getCreatorTime, yesterdayNow)
                    .orderByDesc(CollectOfferEntity::getPriority)
                    .orderByDesc(CollectOfferEntity::getCreatorTime)
                    .last("limit 1 ");

            // 根据查询条件获取采集优惠信息列表
            list = this.list(queryWrapper);

            // 遍历列表中的每个采集优惠信息，更新其请求状态和请求次数，并保存更新。
            list.forEach(entity -> {
                UpdateWrapper<CollectOfferEntity> updateWrapper = new UpdateWrapper<>();
                updateWrapper.lambda().eq(CollectOfferEntity::getId, entity.getId())
//                        .setSql("req_num = req_num + 1")
                        .set(CollectOfferEntity::getRequestStatus, AllegroConstant.REQUEST_STATUS_PROCESSING)
                        .set(CollectOfferEntity::getRequestTime, new java.util.Date());
                this.update(updateWrapper);

            });

        } finally {
            lock.unlock();
        }
        return list;
    }


    @Override
    public CollectOfferEntity waitGet() {
        //如果锁已被另一个线程持有，当前线程将进入等待状态，直到锁被释放。
        lock.lock();
        try {
            // 数据必须是24小时之前生成的
            Date yesterdayNow = DateUtil.yesterday();
            // 创建查询条件，指定查询状态的采集优惠信息，并按照购买数量降序排列，限制查询一条数据。
            QueryWrapper<CollectOfferEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda()
                    .select(CollectOfferEntity::getId, CollectOfferEntity::getOfferLink)
                    .eq(CollectOfferEntity::getRequestStatus, AllegroConstant.REQUEST_STATUS_INIT)
//                    .eq(CollectOfferEntity::getRequestStatus, -3)
                    .le(CollectOfferEntity::getCreatorTime, yesterdayNow)
                    .orderByDesc(CollectOfferEntity::getPriority)
                    .orderByDesc(CollectOfferEntity::getCreatorTime)
                    .last("limit 1");

            // 根据查询条件获取采集优惠信息列表
            CollectOfferEntity entity = this.getOne(queryWrapper);
            if (entity != null) {
                entity.setRequestStatus(AllegroConstant.REQUEST_STATUS_PROCESSING);
                entity.setRequestTime(new java.util.Date());
                this.updateById(entity);
            }
            return entity;

        } finally {
            lock.unlock();
        }
    }

    /**
     * 根据Offer_ID获取链接实体信息。
     */
    @Override
    public CollectOfferEntity getByOfferId(String offerId) {
        QueryWrapper<CollectOfferEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(CollectOfferEntity::getOfferId, offerId);
        return this.getOne(queryWrapper);
    }

    @Override
    public int updateDealStatusClear() {
        return this.baseMapper.updateDealStatusClear();
    }


    @Override
    public List<CollectOfferEntity> dealDataList() {
        QueryWrapper<CollectOfferEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
//                .select(CollectOfferEntity::getId, CollectOfferEntity::getOfferLink)
                .eq(CollectOfferEntity::getDealStatus, 0)
                .last("limit 1000");
        return this.list(queryWrapper);
    }


    @Override
    public boolean checkNewSellerIdExists(String sellerId) {
        QueryWrapper<CollectOfferEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .select(CollectOfferEntity::getId)
                .eq(CollectOfferEntity::getRequestStatus, 0)
                .eq(CollectOfferEntity::getSellerId, sellerId)
                .last("limit 1000");
        return this.list(queryWrapper).size() > 0;
    }

    @Override
    public List<CollectOfferEntity> getListBySkuId(Integer skuId) {
        QueryWrapper<CollectOfferEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(CollectOfferEntity::getSkuId, skuId);
        return this.list(queryWrapper);
    }


    @Override
    public List<CollectOfferEntity> getListBySkuIdSet(Set<Integer> skuIdSet) {
        if (skuIdSet.isEmpty())
            return new ArrayList<>();

        QueryWrapper<CollectOfferEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .in(CollectOfferEntity::getSkuId, skuIdSet);
        return this.list(queryWrapper);
    }

    @Override
    public List<CollectOfferEntity> blockedSellerOfferList(String tenantId) {
        QueryWrapper<CollectOfferEntity> queryWrapper = new QueryWrapper<>();
        String blockUrl ="select seller_id from zz_allegro_seller where sell_type='BLOCKED'";
        if(tenantId!=null){
            blockUrl = blockUrl+" and f_tenantId='"+tenantId+"'";
            queryWrapper.lambda().eq(CollectOfferEntity::getTenantId, tenantId);
        }
        queryWrapper.inSql("seller_id", blockUrl)  // 使用内层查询的结果集
                .isNotNull("sku_id")                // 确保sku_id不为null
                .select("DISTINCT sku_id", "seller_id"); // 选择DISTINCT字段

        return this.list(queryWrapper);
    }


    @Override
    public List<CollectOfferEntity> noDetailDealList() {
        QueryWrapper<CollectOfferEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(CollectOfferEntity::getRequestStatus, 2).eq(CollectOfferEntity::getDetailDealStatus, 0).last("limit 100");
        return this.list(queryWrapper);
    }

    public List<CollectOfferEntity> noDetailDeal2List() {
        QueryWrapper<CollectOfferEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(CollectOfferEntity::getRequestStatus, 2).eq(CollectOfferEntity::getPriority, 7).eq(CollectOfferEntity::getDetailDealStatus, 0).last("limit 500");
        return this.list(queryWrapper);
    }


    public void dealData(CollectOfferJsonEntity offerJsonEntity, CollectOfferEntity collectOfferEntity) {
        log.info("开始处理数据 collectOffer=>erpProduct,ID:{},价格：{}", collectOfferEntity.getId(), collectOfferEntity.getTotalPrice());

        //
        BigDecimal totalPrice = collectOfferEntity.getTotalPrice();


        // 20240623 只有中国或者美国发货,中国香港发货，英国发货  的数据才能构造推送产品的数据  中国，美国，香港，英国 Szwajcaria-瑞士  Niemcy-德国 Ukraina-乌克兰
        String[] countries = {"Chiny", "Stany Zjednoczone", "SRA Hongkong (Chiny)", "Wielka Brytania", "Szwajcaria", "Niemcy", "Ukraina"};
        if (totalPrice.compareTo(new BigDecimal(20)) >= 0 && totalPrice.compareTo(new BigDecimal(500)) <= 0) {
            boolean isChinaOrUS = Arrays.asList(countries).contains(collectOfferEntity.getDispatchCountry());
            if (StrXhUtil.isBlank(collectOfferEntity.getDispatchCountry()) && collectOfferEntity.getDeliveryCount() <= 2) {
                isChinaOrUS = true;
            }

            if (isChinaOrUS) {
                ErpProductEntity erpProductEntity = erpProductService.getByProductId(collectOfferEntity.getProductId(),null);
                if (erpProductEntity != null) {
                    collectOfferEntity.setSkuId(erpProductEntity.getSkuId());
                    if (StrXhUtil.isBlank(erpProductEntity.getFromCpId())) {
                        erpProductEntity.setFromCpId(collectOfferEntity.getId());
                        erpProductEntity.setFromSellerId(collectOfferEntity.getSellerId());
                        erpProductEntity.setFromOfferId(collectOfferEntity.getOfferId());
                        erpProductEntity.setTotalPrice(collectOfferEntity.getTotalPrice());
                        erpProductEntity.setUpdateTime(new Date());
                        erpProductService.updateById(erpProductEntity);
                    }
                } else {
                    JsonNode offerJsonNode = JsonXhUtil.parseObject(offerJsonEntity.getOfferJson()).get("offer");
                    erpProductEntity = erpProductService.processAndSaveProductDetails(collectOfferEntity, offerJsonNode,true,null,null);
                    if (erpProductEntity == null) {
                        log.warn("通过详情请求构造erpProduct失败！ 产品ID为空，返回.offerID:" + collectOfferEntity.getOfferId());
                        collectOfferEntity.setRequestStatus(AllegroConstant.REQUEST_STATUS_ERPFAIL);
                        collectOfferEntity.setNote(collectOfferEntity.getNote() + "，通过详情请求构造erpProduct失败！");
                        this.updateById(collectOfferEntity);
                    } else {
                        collectOfferEntity.setSkuId(erpProductEntity.getSkuId());
                        if (!(collectOfferEntity.getProductId().equalsIgnoreCase(erpProductEntity.getProductId()))) {
                            collectOfferEntity.setNewProductId(erpProductEntity.getProductId());
                        }
                    }

                }
                // 如果价格提高了,ERP_PRODUCT 中的价格也要跟着提高
                if (erpProductEntity != null && StrXhUtil.isNotBlank(erpProductEntity.getFromCpId()) && erpProductEntity.getFromCpId().equals(collectOfferEntity.getId()) && erpProductEntity.getTotalPrice().compareTo(collectOfferEntity.getTotalPrice()) < 0) {
                    allegroLogService.saveLog("ERP_PRODUCT", "" + erpProductEntity.getSkuId(), "价格调整，原价格：" + erpProductEntity.getTotalPrice() + "，现价格：" + collectOfferEntity.getTotalPrice());
                    erpProductEntity.setTotalPrice(collectOfferEntity.getTotalPrice());
                    //同步写入日志记录
                    erpProductService.updateById(erpProductEntity);
                }
                if (collectOfferEntity.getSkuId() != null) {
                    allegroSaleService.updateSkuId(collectOfferEntity.getOfferId(), collectOfferEntity.getSkuId());
                }
                this.updateById(collectOfferEntity);


            }
        }
    }


    /**
     * 烽火处理数据  20250206   去掉 中国区域判断。
     * @param offerJsonEntity
     * @param collectOfferEntity
     */
    public void dealDataFh(CollectOfferJsonEntity offerJsonEntity, CollectOfferEntity collectOfferEntity) {
        log.info("开始处理数据 collectOffer=>erpProduct,ID:{},价格：{}", collectOfferEntity.getId(), collectOfferEntity.getTotalPrice());

        //
        BigDecimal totalPrice = collectOfferEntity.getTotalPrice();


        // 20240623 只有中国或者美国发货,中国香港发货，英国发货  的数据才能构造推送产品的数据  中国，美国，香港，英国
        if (totalPrice.compareTo(new BigDecimal(70)) >= 0 && totalPrice.compareTo(new BigDecimal(200)) <= 0) {



                ErpProductEntity erpProductEntity = erpProductService.getByProductId(collectOfferEntity.getProductId(),"8");
                if (erpProductEntity != null) {
                    collectOfferEntity.setSkuId(erpProductEntity.getSkuId());
                    if (StrXhUtil.isBlank(erpProductEntity.getFromCpId())) {
                        erpProductEntity.setFromCpId(collectOfferEntity.getId());
                        erpProductEntity.setFromSellerId(collectOfferEntity.getSellerId());
                        erpProductEntity.setFromOfferId(collectOfferEntity.getOfferId());
                        erpProductEntity.setTotalPrice(collectOfferEntity.getTotalPrice());
                        erpProductEntity.setUpdateTime(new Date());
                        erpProductService.updateById(erpProductEntity);
                    }
                } else {
                    JsonNode offerJsonNode = JsonXhUtil.parseObject(offerJsonEntity.getOfferJson()).get("offer");
                    WySyncForm wySyncForm = new WySyncForm();
                    wySyncForm.setTenantId("8");
                    erpProductEntity = erpProductService.processAndSaveProductDetails(collectOfferEntity, offerJsonNode,true,null,wySyncForm);
                    if (erpProductEntity == null) {
                        log.warn("通过详情请求构造erpProduct失败！ 产品ID为空，返回.offerID:" + collectOfferEntity.getOfferId());
                        collectOfferEntity.setRequestStatus(AllegroConstant.REQUEST_STATUS_ERPFAIL);
                        collectOfferEntity.setNote(collectOfferEntity.getNote() + "，通过详情请求构造erpProduct失败！");
                        this.updateById(collectOfferEntity);
                    } else {
                        collectOfferEntity.setSkuId(erpProductEntity.getSkuId());
                        if (!(collectOfferEntity.getProductId().equalsIgnoreCase(erpProductEntity.getProductId()))) {
                            collectOfferEntity.setNewProductId(erpProductEntity.getProductId());
                        }
                    }

                }
                // 如果价格提高了,ERP_PRODUCT 中的价格也要跟着提高
                if (erpProductEntity != null && StrXhUtil.isNotBlank(erpProductEntity.getFromCpId()) && erpProductEntity.getFromCpId().equals(collectOfferEntity.getId()) && erpProductEntity.getTotalPrice().compareTo(collectOfferEntity.getTotalPrice()) < 0) {
                    allegroLogService.saveLog("ERP_PRODUCT", "" + erpProductEntity.getSkuId(), "价格调整，原价格：" + erpProductEntity.getTotalPrice() + "，现价格：" + collectOfferEntity.getTotalPrice());
                    erpProductEntity.setTotalPrice(collectOfferEntity.getTotalPrice());
                    //同步写入日志记录
                    erpProductService.updateById(erpProductEntity);
                }
                if (collectOfferEntity.getSkuId() != null) {
                    allegroSaleService.updateSkuId(collectOfferEntity.getOfferId(), collectOfferEntity.getSkuId());
                }
                this.updateById(collectOfferEntity);



        }
    }

    @Override
//    @Async
    public void dealDataOld(CollectOfferJsonEntity offerJsonEntity, CollectOfferEntity collectOfferEntity) {
        log.info("开始处理数据 collectOffer=>erpProduct,ID:{},价格：{}", collectOfferEntity.getId(), collectOfferEntity.getTotalPrice());
        //数据保存 入 seller 和productParse表
        JsonNode offerJsonNode = JsonXhUtil.parseObject(offerJsonEntity.getOfferJson()).get("offer");
//        syncSellerData(offerJsonNode);

        JsonNode productJsonNode = offerJsonNode.get("product");
        String productId = productJsonNode.path("id").asText(null);
        String categoryId = offerJsonNode.path("category").path("id").asText(null);
        BigDecimal price = BigDecimal.ZERO;
        BigDecimal shipFee = BigDecimal.ZERO;
        int buyersQuantity = 0;

        //20240811 判断首页图片
        if (StrXhUtil.isBlank(collectOfferEntity.getImageUrl())) {
            if (offerJsonNode.has("images")) {
                ArrayNode imagesNode = (ArrayNode) offerJsonNode.get("images");
                if (!imagesNode.isEmpty()) {
                    String imageUrl = imagesNode.get(0).path("url").asText();
                    if (StrXhUtil.isNotBlank(imageUrl)) {
                        collectOfferEntity.setImageUrl(OfferJsonUtil.contructImagerUrl(imageUrl));
                    }
                }
            }
        }
        if (offerJsonNode.path("sellingMode").path("buyNow").path("price").path("sale").has("amount")) {
            price = new BigDecimal(offerJsonNode.path("sellingMode").path("buyNow").path("price").path("sale").path("amount").asText());
        }

        if (offerJsonNode.path("shipping").path("delivery").path("cheapestMethod").path("price").has("amount")) {
            shipFee = new BigDecimal(offerJsonNode.path("shipping").path("delivery").path("cheapestMethod").path("price").path("amount").asText());
        }

        if (offerJsonNode.has("popularity") && offerJsonNode.path("popularity").has("buyersQuantity")) {
            buyersQuantity = offerJsonNode.path("popularity").path("buyersQuantity").asInt();
        }

        String country = "";
        if (offerJsonNode.get("shipping").has("delivery") && offerJsonNode.get("shipping").get("delivery").has("from")) {
            country = offerJsonNode.path("shipping").path("delivery").path("from").path("country").asText();
        }

        BigDecimal totalPrice = price.add(shipFee);
        collectOfferEntity.setOfferName(offerJsonNode.path("name").asText());
        collectOfferEntity.setCountry(country);
//        collectOfferEntity.setSellerId(offerJsonNode.path("seller").path("id").asText());
        collectOfferEntity.setProductId(productId);
        collectOfferEntity.setNewProductId(productId);
        collectOfferEntity.setCategoryId(categoryId);
        collectOfferEntity.setPrice(price);
        collectOfferEntity.setShipFee(shipFee);
        collectOfferEntity.setTotalPrice(totalPrice);
        collectOfferEntity.setBuyersQuantity(buyersQuantity);
        collectOfferEntity.setReqFinishTime(new Date());
        collectOfferEntity.setDetailDealStatus(Boolean.TRUE);
        collectOfferEntity.setParseStatus(AllegroConstant.PARSE_STATUS_VALID);
        // 20240629 处理新的发货方式处理
        JsonNode develiveryJsonNode = offerJsonNode.get("delivery");
        ArrayNode summaryArrayNode = (ArrayNode) develiveryJsonNode.get("summary");
        //从summary 获取
        for (JsonNode item : summaryArrayNode) {
            JsonNode analyticTagNode = item.get("analyticTag");
            if (analyticTagNode != null && "CountryOfDispatch".equals(analyticTagNode.asText())) {
                JsonNode valueNode = item.get("value");
                if (valueNode != null) {
                    JsonNode textNode = valueNode.get("text");
                    if (textNode != null) {
                        String countryOfDispatch = textNode.asText();
                        collectOfferEntity.setDispatchCountry(countryOfDispatch);
                        System.out.println("Country of Dispatch: " + countryOfDispatch);
                    }
                }
            }
            if (analyticTagNode != null && "DeliveryTime".equals(analyticTagNode.asText())) {
                JsonNode valueNode = item.get("name");
                if (valueNode != null) {
                    JsonNode textNode = valueNode.get("text");
                    if (textNode != null) {
                        String deliveryTime = textNode.asText();
                        collectOfferEntity.setDeliveryTime(deliveryTime);
                        System.out.println("Country of Dispatch: " + deliveryTime);
                    }
                }
            }
        }

        ArrayNode groupNode = (ArrayNode) offerJsonNode.get("shipping").get("delivery").get("groups");
        int deliveryCount = 0;
        for (JsonNode group : groupNode) {
            if ("PL".equalsIgnoreCase(group.get("countryCode").asText())) {
                ArrayNode deliveryMethodsNode = (ArrayNode) group.get("deliveryMethods");
                deliveryCount += deliveryMethodsNode.size();

            }
        }
        collectOfferEntity.setDeliveryCount(deliveryCount);
        collectOfferEntity.setDetailDealStatus(true);
        collectOfferEntity.setLastModifyTime(new Date());
        this.updateById(collectOfferEntity);

        if (collectOfferEntity.getBuyersQuantity() == 0) {
            //如果是零销量的数据，不入采集库
            return;
        }


        // 20240623 只有中国或者美国发货,中国香港发货，英国发货  的数据才能构造推送产品的数据  中国，美国，香港，英国
        String[] countries = {"Chiny", "Stany Zjednoczone", "SRA Hongkong (Chiny)", "Wielka Brytania"};
        if (totalPrice.compareTo(new BigDecimal(20)) >= 0 && totalPrice.compareTo(new BigDecimal(500)) <= 0) {
            boolean isChinaOrUS = Arrays.asList(countries).contains(collectOfferEntity.getDispatchCountry());
            if (StrXhUtil.isBlank(collectOfferEntity.getDispatchCountry()) && collectOfferEntity.getDeliveryCount() <= 2) {
                isChinaOrUS = true;
            }

            if (isChinaOrUS) {
                ErpProductEntity erpProductEntity = erpProductService.getByProductId(productId,null);
                if (erpProductEntity != null) {
                    collectOfferEntity.setSkuId(erpProductEntity.getSkuId());
                    if (StrXhUtil.isBlank(erpProductEntity.getFromCpId())) {
                        erpProductEntity.setFromCpId(collectOfferEntity.getId());
                        erpProductEntity.setFromSellerId(collectOfferEntity.getSellerId());
                        erpProductEntity.setFromOfferId(collectOfferEntity.getOfferId());
                        erpProductEntity.setTotalPrice(collectOfferEntity.getTotalPrice());
                        erpProductEntity.setUpdateTime(new Date());
                        erpProductService.updateById(erpProductEntity);
                    }
                } else {
                    erpProductEntity = erpProductService.processAndSaveProductDetails(collectOfferEntity, offerJsonNode,true,null,null);
                    if (erpProductEntity == null) {
                        log.warn("通过详情请求构造erpProduct失败！ 产品ID为空，返回");
                        collectOfferEntity.setRequestStatus(AllegroConstant.REQUEST_STATUS_ERPFAIL);
                        collectOfferEntity.setNote(collectOfferEntity.getNote() + "，通过详情请求构造erpProduct失败！");
                        this.updateById(collectOfferEntity);
                    } else {
                        collectOfferEntity.setSkuId(erpProductEntity.getSkuId());
                        if (!productId.equalsIgnoreCase(erpProductEntity.getProductId())) {
                            collectOfferEntity.setNewProductId(erpProductEntity.getProductId());
                        }
                    }

                }
                // 如果价格提高了,ERP_PRODUCT 中的价格也要跟着提高
                if (erpProductEntity != null && StrXhUtil.isNotBlank(erpProductEntity.getFromCpId()) && erpProductEntity.getFromCpId().equals(collectOfferEntity.getId()) && erpProductEntity.getTotalPrice().compareTo(collectOfferEntity.getTotalPrice()) < 0) {
                    allegroLogService.saveLog("ERP_PRODUCT", "" + erpProductEntity.getSkuId(), "价格调整，原价格：" + erpProductEntity.getTotalPrice() + "，现价格：" + collectOfferEntity.getTotalPrice());
                    erpProductEntity.setTotalPrice(collectOfferEntity.getTotalPrice());
                    //同步写入日志记录
                    erpProductService.updateById(erpProductEntity);
                }
                if (collectOfferEntity.getSkuId() != null) {
                    allegroSaleService.updateSkuId(collectOfferEntity.getOfferId(), collectOfferEntity.getSkuId());
                }
                this.updateById(collectOfferEntity);


            }
        }
    }


    public CollectOfferEntity getByOfferNameAndImageUrl(String offerName, String imageUrl, String excludeId) {
        QueryWrapper<CollectOfferEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(CollectOfferEntity::getOfferName, offerName);
        queryWrapper.lambda().eq(CollectOfferEntity::getImageUrl, imageUrl);
        queryWrapper.lambda().ne(CollectOfferEntity::getId, excludeId);
        queryWrapper.lambda().last("limit 1");
        return this.getOne(queryWrapper);
    }


    /**
     * 会员版处理
     * @param offerForm
     */
    @Override
//    @Async
    public void dealDataMember(CollectForm offerForm, CollectSettingModel settingModel,String tenantId ) {

        CollectOfferEntity collectOfferEntity = refactor(offerForm);
        BigDecimal manualTotalPrice = null;

        if(settingModel!=null && settingModel.getRaiseMode().isEnabled()){

            if(collectOfferEntity.getTotalPrice().compareTo(settingModel.getRaiseMode().getLessPrice())<=0){
                manualTotalPrice = settingModel.getRaiseMode().getTargetPrice();
            }
        }



        AllegroCategoryEntity allegroCategoryEntity = allegroCategoryService.getSimpleInfoByCategoryId(collectOfferEntity.getCategoryId(),tenantId);
        if(allegroCategoryEntity != null){
            collectOfferEntity.setCategoryPath(allegroCategoryEntity.getName()+"("+allegroCategoryEntity.getPath() +")");
        }
        UserInfo userInfo = userProvider.get();
        collectOfferEntity.setCreatorUserId(userInfo.getUserName());
        collectOfferEntity.setLastModifyTime(new Date());
        collectOfferEntity.setId(RandomUtil.snowId());
        collectOfferEntity.setTenantId(userInfo.getTenantId());
        this.save(collectOfferEntity);

        CollectOfferJsonEntity collectOfferJsonEntity = new CollectOfferJsonEntity();
        collectOfferJsonEntity.setId(collectOfferEntity.getId());
        collectOfferJsonEntity.setOfferJson(offerForm.getOfferJson().toString());
        collectOfferJsonEntity.setDealStatus(1);
        collectOfferJsonEntity.setCreateTime(new Date());
        collectOfferJsonEntity.setTenantId(userInfo.getTenantId());
        collectOfferJsonService.save(collectOfferJsonEntity);
        ErpProductEntity erpProductEntity = erpProductService.getByProductId(collectOfferEntity.getProductId(),tenantId);
        if (erpProductEntity != null) {
            collectOfferEntity.setSkuId(erpProductEntity.getSkuId());
            if (StrXhUtil.isBlank(erpProductEntity.getFromCpId())) {
                erpProductEntity.setFromCpId(collectOfferEntity.getId());
                erpProductEntity.setFromSellerId(collectOfferEntity.getSellerId());
                erpProductEntity.setFromOfferId(collectOfferEntity.getOfferId());
                erpProductEntity.setTotalPrice(collectOfferEntity.getTotalPrice());
                erpProductEntity.setUpdateTime(new Date());
                erpProductService.updateById(erpProductEntity);
            }
        } else {
            erpProductEntity = erpProductService.processAndSaveProductDetails(collectOfferEntity, offerForm.getOfferJson().get("offer"),false,manualTotalPrice,null);
            if (erpProductEntity == null) {
                log.warn("通过详情请求构造erpProduct失败！ 产品ID为空，返回");
                this.updateById(collectOfferEntity);
            } else {
                collectOfferEntity.setSkuId(erpProductEntity.getSkuId());
                if (!collectOfferEntity.getProductId().equalsIgnoreCase(erpProductEntity.getProductId())) {
                    collectOfferEntity.setNewProductId(erpProductEntity.getProductId());
                }
            }

        }
        super.updateById(collectOfferEntity);
        this.updateBuyersQuantity(collectOfferEntity.getSkuId());
    }

    public CollectOfferEntity refactor(CollectForm offerForm) {
        CollectOfferEntity collectOfferEntity = new CollectOfferEntity();
        collectOfferEntity.setOfferLink(UrlExtractor.extractOfferLink(offerForm.getOfferLink()));
        //数据保存 入 seller 和productParse表
        JsonNode offerJsonNode = JsonXhUtil.parseObject(offerForm.getOfferJson()).get("offer");

        JsonNode productJsonNode = offerJsonNode.get("product");
        String productId = productJsonNode.path("id").asText(null);
        String categoryId = offerJsonNode.path("category").path("id").asText(null);
        BigDecimal price = BigDecimal.ZERO;
        BigDecimal shipFee = BigDecimal.ZERO;
        int buyersQuantity = 0;


        //20240811 判断首页图片
        if (StrXhUtil.isBlank(collectOfferEntity.getImageUrl())) {
            if (offerJsonNode.has("images")) {
                ArrayNode imagesNode = (ArrayNode) offerJsonNode.get("images");
                if (!imagesNode.isEmpty()) {
                    String imageUrl = imagesNode.get(0).path("url").asText();
                    if (StrXhUtil.isNotBlank(imageUrl)) {
                        collectOfferEntity.setImageUrl(OfferJsonUtil.contructImagerUrl(imageUrl));
                    }
                }
            }
        }
        if (offerJsonNode.path("sellingMode").path("buyNow").path("price").path("sale").has("amount")) {
            price = new BigDecimal(offerJsonNode.path("sellingMode").path("buyNow").path("price").path("sale").path("amount").asText());
        }

        if (offerJsonNode.path("shipping").path("delivery").path("cheapestMethod").path("price").has("amount")) {
            shipFee = new BigDecimal(offerJsonNode.path("shipping").path("delivery").path("cheapestMethod").path("price").path("amount").asText());
        }

        if (offerJsonNode.has("popularity") && offerJsonNode.path("popularity").has("buyersQuantity")) {
            buyersQuantity = offerJsonNode.path("popularity").path("buyersQuantity").asInt();
        }
        if (offerJsonNode.has("id")) {
            collectOfferEntity.setOfferId(offerJsonNode.path("id").asText());
        }

        String country = "";
        if (offerJsonNode.get("shipping").has("delivery") && offerJsonNode.get("shipping").get("delivery").has("from")) {
            country = offerJsonNode.path("shipping").path("delivery").path("from").path("country").asText();
        }

        BigDecimal totalPrice = price.add(shipFee);
        collectOfferEntity.setOfferName(offerJsonNode.path("name").asText());
        collectOfferEntity.setCountry(country);
        collectOfferEntity.setSellerId(offerJsonNode.path("seller").path("id").asText());
        collectOfferEntity.setProductId(productId);
        collectOfferEntity.setNewProductId(productId);
        collectOfferEntity.setCategoryId(categoryId);
        collectOfferEntity.setPrice(price);
        collectOfferEntity.setShipFee(shipFee);
        collectOfferEntity.setTotalPrice(totalPrice);
        collectOfferEntity.setBuyersQuantity(buyersQuantity);
        log.info("会员开始处理数据 collectMemberOffer到erpProduct,ID:{},价格：{}", collectOfferEntity.getOfferId(), totalPrice);

        // 20240629 处理新的发货方式处理
        JsonNode develiveryJsonNode = offerJsonNode.get("delivery");
        ArrayNode summaryArrayNode = (ArrayNode) develiveryJsonNode.get("summary");
        //从summary 获取
        for (JsonNode item : summaryArrayNode) {
            JsonNode analyticTagNode = item.get("analyticTag");
            if (analyticTagNode != null && "CountryOfDispatch".equals(analyticTagNode.asText())) {
                JsonNode valueNode = item.get("value");
                if (valueNode != null) {
                    JsonNode textNode = valueNode.get("text");
                    if (textNode != null) {
                        String countryOfDispatch = textNode.asText();
                        collectOfferEntity.setDispatchCountry(countryOfDispatch);
                        System.out.println("Country of Dispatch: " + countryOfDispatch);
                    }
                }
            }
            if (analyticTagNode != null && "DeliveryTime".equals(analyticTagNode.asText())) {
                JsonNode valueNode = item.get("name");
                if (valueNode != null) {
                    JsonNode textNode = valueNode.get("text");
                    if (textNode != null) {
                        String deliveryTime = textNode.asText();
                        collectOfferEntity.setDeliveryTime(deliveryTime);
                        System.out.println("Country of Dispatch: " + deliveryTime);
                    }
                }
            }
        }

        ArrayNode groupNode = (ArrayNode) offerJsonNode.get("shipping").get("delivery").get("groups");
        int deliveryCount = 0;
        for (JsonNode group : groupNode) {
            if ("PL".equalsIgnoreCase(group.get("countryCode").asText())) {
                ArrayNode deliveryMethodsNode = (ArrayNode) group.get("deliveryMethods");
                deliveryCount += deliveryMethodsNode.size();

            }
        }
        collectOfferEntity.setDeliveryCount(deliveryCount);
        return collectOfferEntity;
    }




    /**
     * 会员版处理
     * @param offerForm
     */
//    @Async
    public ErpProductEntity dealDataMemberWy(CollectForm offerForm,JsonNode productNode,String ship_rating_id,WySyncForm form) {

        CollectOfferEntity collectOfferEntity = refactorWy(offerForm,productNode,ship_rating_id, form);
        BigDecimal manualTotalPrice = null;

        AllegroCategoryEntity allegroCategoryEntity = allegroCategoryService.getInfoByCategoryId(collectOfferEntity.getCategoryId());
        if(allegroCategoryEntity != null){
            collectOfferEntity.setCategoryPath(allegroCategoryEntity.getName()+"("+allegroCategoryEntity.getPath() +")");
        }
        UserInfo userInfo = userProvider.get();
        collectOfferEntity.setCreatorUserId(userInfo.getUserName());
        collectOfferEntity.setLastModifyTime(new Date());
        collectOfferEntity.setId(RandomUtil.snowId());
        collectOfferEntity.setTenantId(form.getTenantId());
        collectOfferEntity.setCreatorUserId(form.getGroupName());
        this.save(collectOfferEntity);

        CollectOfferJsonEntity collectOfferJsonEntity = new CollectOfferJsonEntity();
        collectOfferJsonEntity.setId(collectOfferEntity.getId());
        collectOfferJsonEntity.setOfferJson(offerForm.getOfferJson().toString());
        collectOfferJsonEntity.setDealStatus(1);
        collectOfferJsonEntity.setTenantId(form.getTenantId());

        collectOfferJsonEntity.setCreateTime(new Date());
        collectOfferJsonService.save(collectOfferJsonEntity);
        ErpProductEntity erpProductEntity = erpProductService.getByProductId(collectOfferEntity.getProductId(),null);
        if (erpProductEntity != null) {
            collectOfferEntity.setSkuId(erpProductEntity.getSkuId());
            if (StrXhUtil.isBlank(erpProductEntity.getFromCpId())) {
                erpProductEntity.setFromCpId(collectOfferEntity.getId());
                erpProductEntity.setFromSellerId(collectOfferEntity.getSellerId());
                erpProductEntity.setFromOfferId(collectOfferEntity.getOfferId());
                erpProductEntity.setTotalPrice(collectOfferEntity.getTotalPrice());
                erpProductEntity.setUpdateTime(new Date());
                erpProductService.updateById(erpProductEntity);
            }
        } else {
            erpProductEntity = erpProductService.processAndSaveProductDetails(collectOfferEntity, offerForm.getOfferJson().get("offer"),false,manualTotalPrice,form);
            if (erpProductEntity == null) {
                log.warn("通过详情请求构造erpProduct失败！ 产品ID为空，返回");
                this.updateById(collectOfferEntity);
            } else {
                collectOfferEntity.setSkuId(erpProductEntity.getSkuId());
                if (!collectOfferEntity.getProductId().equalsIgnoreCase(erpProductEntity.getProductId())) {
                    collectOfferEntity.setNewProductId(erpProductEntity.getProductId());
                }
            }

        }
        super.updateById(collectOfferEntity);
        return erpProductEntity;
    }

    public CollectOfferEntity refactorWy(CollectForm offerForm,JsonNode productNode,String ship_rating_id,WySyncForm form) {
        CollectOfferEntity collectOfferEntity = new CollectOfferEntity();
        collectOfferEntity.setOfferLink(UrlExtractor.extractOfferLink(offerForm.getOfferLink()));
        //数据保存 入 seller 和productParse表
        JsonNode offerJsonNode = JsonXhUtil.parseObject(offerForm.getOfferJson()).get("offer");

        JsonNode productJsonNode = offerJsonNode.get("product");
        String productId = productJsonNode.path("id").asText(null);
        String categoryId = offerJsonNode.path("category").path("id").asText(null);
        BigDecimal price = BigDecimal.ZERO;
        BigDecimal shipFee = form.getShipfee();
        if(form.getIsMultifee()){
            //启用多店铺配置，根据店铺配置的运费计算
           if(StrXhUtil.isNotBlank(ship_rating_id)) {
               AllegroStoreConfigEntity storeConfigEntity = allegroStoreConfigService.getByTypeId(form.getSellerId(), ship_rating_id);
               if (StrXhUtil.isNotBlank(storeConfigEntity.getAfterSalesTypeId())) {
                   shipFee = new BigDecimal(storeConfigEntity.getAfterSalesTypeName());
                   log.info("店铺多运费:{}：{}", ship_rating_id,shipFee);
               }
           }else{
               log.warn("{} 没有找到运费配置{}，使用默认运费：{}", form.getSellerId(),ship_rating_id, shipFee);
           }

        }
        int buyersQuantity = 0;


        //20240811 判断首页图片
        if (StrXhUtil.isBlank(collectOfferEntity.getImageUrl())) {
            if (offerJsonNode.has("images")) {
                ArrayNode imagesNode = (ArrayNode) offerJsonNode.get("images");
                if (!imagesNode.isEmpty()) {
                    String imageUrl = imagesNode.get(0).path("url").asText();
                    if (StrXhUtil.isNotBlank(imageUrl)) {
                        collectOfferEntity.setImageUrl(OfferJsonUtil.contructImagerUrl(imageUrl));
                    }
                }
            }
        }

        collectOfferEntity.setOfferId(productNode.path("id").asText());
        collectOfferEntity.setProductId(productNode.path("productSet").get(0).path("product").path("id").asText());
        collectOfferEntity.setOfferName(productNode.path("name").asText());
        collectOfferEntity.setShipFee(shipFee);
        price = new BigDecimal(productNode.path("sellingMode").path("price").path("amount").asText());
        log.info("sellingMode:{}",productNode.path("sellingMode"));
        log.info("price:{}",productNode.path("sellingMode").path("price"));
        log.info("amount:{}",productNode.path("sellingMode").path("price").path("amount").asText());
        BigDecimal totalPrice = price.add(collectOfferEntity.getShipFee());
        collectOfferEntity.setOfferName(offerJsonNode.path("name").asText());
        collectOfferEntity.setCountry("CN");
        collectOfferEntity.setSellerId(offerJsonNode.path("seller").path("id").asText());
        collectOfferEntity.setProductId(productId);
        collectOfferEntity.setNewProductId(productId);
        collectOfferEntity.setCategoryId(categoryId);
        collectOfferEntity.setPrice(price);
        collectOfferEntity.setTotalPrice(totalPrice);
        collectOfferEntity.setBuyersQuantity(buyersQuantity);
        log.info("会员开始处理数据 collectMemberOffer到erpProduct,ID:{},价格：{}", collectOfferEntity.getOfferId(), totalPrice);
        collectOfferEntity.setDeliveryCount(1);
        return collectOfferEntity;
    }

    public List<CollectOfferEntity> getProductsBySellerId(String sellerId, Integer page, Integer limit){
        CollectOfferPagination pagination = new CollectOfferPagination();
        pagination.setPageSize(limit);
        pagination.setCurrentPage(page);
        pagination.setSellerId(sellerId);

        return getList(pagination);
    }

    public long countBySellerId(String sellerId){
        QueryWrapper<CollectOfferEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(CollectOfferEntity::getSellerId,sellerId);
        return this.count(queryWrapper);
    }



    public void contractData(String sellerId){

        String str = """
                {
                    "name": "POJEMNIK NA ZWIERZĘCE ODCHODY TORBA NA ŚMIECI ( )",
                    "productSet": [
                        {
                            "product": {
                                "id": "e9d68443-d43f-4eb4-8867-a9f21d5b86e7",
                                "publication": {
                                    "status": "LISTED"
                                },
                                "parameters": [
                                    {
                                        "id": "13288",
                                        "name": "Marka",
                                        "values": [
                                            "bez marki"
                                        ],
                                        "valuesIds": [
                                            "13288_1728921"
                                        ],
                                        "rangeValue": null
                                    },
                                    {
                                        "id": "209138",
                                        "name": "Liczba sztuk w zestawie",
                                        "values": [
                                            "1"
                                        ],
                                        "valuesIds": null,
                                        "rangeValue": null
                                    },
                                    {
                                        "id": "224017",
                                        "name": "Kod producenta",
                                        "values": [
                                            "8595148"
                                        ],
                                        "valuesIds": null,
                                        "rangeValue": null
                                    },
                                    {
                                        "id": "209134",
                                        "name": "Rodzaj",
                                        "values": [
                                            "chusteczki nawilżane"
                                        ],
                                        "valuesIds": [
                                            "209134_1742307"
                                        ],
                                        "rangeValue": null
                                    },
                                    {
                                        "id": "17448",
                                        "name": "Waga produktu z opakowaniem jednostkowym",
                                        "values": [
                                            "0.217"
                                        ],
                                        "valuesIds": null,
                                        "rangeValue": null
                                    },
                                    {
                                        "id": "225693",
                                        "name": "EAN (GTIN)",
                                        "values": [
                                            "4894973076591"
                                        ],
                                        "valuesIds": null,
                                        "rangeValue": null
                                    }
                                ]
                            },
                            "quantity": {
                                "value": 1
                            },
                            "responsiblePerson": {
                                "id": "49b39923-6e31-4fe1-9381-298063dadc9f"
                            },
                            "responsibleProducer": {
                                "id": "87ab4841-66eb-4d4c-94f2-78553fbc0510"
                            },
                            "safetyInformation": {
                                "type": "NO_SAFETY_INFORMATION"
                            }
                        }
                    ],
                    "parameters": [
                        {
                            "id": "11323",
                            "name": "Stan",
                            "values": [
                                "Nowy"
                            ],
                            "valuesIds": [
                                "11323_1"
                            ],
                            "rangeValue": null
                        }
                    ],
                    "images": [
                        "https://a.allegroimg.com/original/113c1e/ddc9ce104f5591df5ea6877da61e",
                        "https://a.allegroimg.com/original/1143cc/1d09d1e6419895f3447697997322",
                        "https://a.allegroimg.com/original/11301b/81a8de2d45c6bac1c95af43edca5",
                        "https://a.allegroimg.com/original/11df1a/311324334c5189143afb055b845f",
                        "https://a.allegroimg.com/original/116b1b/af3dcc754109812ee7906f5ae068",
                        "https://a.allegroimg.com/original/11e9aa/175e049d4c1b9b97830445c62b6d",
                        "https://a.allegroimg.com/original/110a2c/7b01ebb34b2cba9c7e495a88b4bb",
                        "https://a.allegroimg.com/original/11cdb8/d881ed20463590a573393bc87e1b",
                        "https://a.allegroimg.com/original/11c5d4/6e2d0668497f9f591188cbc50e73",
                        "https://a.allegroimg.com/original/1134ff/84f3ae67499e93aefa30b297203b",
                        "https://a.allegroimg.com/original/1127bf/9817e35f403ca97296ec999c867b",
                        "https://a.allegroimg.com/original/1199ad/261076a74fbcbf9fa4db93e5328c",
                        "https://a.allegroimg.com/original/110433/7a5b5847441687c300df74d14f6b",
                        "https://a.allegroimg.com/original/11d462/36b0c50747e6ba1c4d69372a83d2",
                        "https://a.allegroimg.com/original/11eff1/a6c9dca14f0eabec38d038c1a656",
                        "https://a.allegroimg.com/original/11bbb3/67d5fdb34f7a9a18d69404b67317"
                    ],
                    "afterSalesServices": {
                        "impliedWarranty": {
                            "id": "f2091097-3734-49e2-b178-f65f855edc54"
                        },
                        "returnPolicy": {
                            "id": "cec2f9b7-1907-4028-84b7-13ca3eb6b8b5"
                        },
                        "warranty": {
                            "id": "081050ab-4786-4454-bec6-0178a6b3d146"
                        }
                    },
                    "payments": {
                        "invoice": "NO_INVOICE"
                    },
                    "sellingMode": {
                        "format": "BUY_NOW",
                        "price": {
                            "amount": "15.89",
                            "currency": "PLN"
                        },
                        "startingPrice": null,
                        "minimalPrice": null
                    },
                    "stock": {
                        "available": 355,
                        "unit": "UNIT"
                    },
                    "location": {
                        "countryCode": "HK",
                        "province": null,
                        "city": "xijiulong",
                        "postCode": "999077"
                    },
                    "delivery": {
                        "shippingRates": {
                            "id": "33ad487b-9730-49a6-99c3-a78f3561b157"
                        },
                        "handlingTime": "PT96H",
                        "additionalInfo": null,
                        "shipmentDate": null
                    },
                    "description": {
                        "sections": [
                            {
                                "items": [
                                    {
                                        "type": "TEXT",
                                        "content": "<h2>✅DOZOWNIK WOREK NA ODCHODY</h2><h2>✅Torba na odchody zwierząt</h2><h2>✅UCHWYT NA Psie Kupy</h2><h2>✅Uchwyt na worek na odchody</h2><h2>✅TORBEK NA ODPADY ZWIERZĘTOWE</h2>"
                                    },
                                    {
                                        "type": "IMAGE",
                                        "url": "https://a.allegroimg.com/original/113c1e/ddc9ce104f5591df5ea6877da61e"
                                    }
                                ]
                            },
                            {
                                "items": [
                                    {
                                        "type": "IMAGE",
                                        "url": "https://a.allegroimg.com/original/11bbb3/67d5fdb34f7a9a18d69404b67317"
                                    },
                                    {
                                        "type": "TEXT",
                                        "content": "<h1>⭐DOZOWNIK NA WORKI NA ODCHODY</h1><p>✅Biodegradowalna torba na psie odchody, bardzo przyjazna dla środowiska.</p><p>✅Lekka waga i duża pojemność, wygodna do noszenia na spacerze na świeżym powietrzu.</p><p>✅Skrobia kukurydziana nie powoduje zanieczyszczeń i może ulec rozkładowi, stając się nawozem glebowym.</p><p>✅ Dbaj o środowisko, w którym przebywają zwierzęta domowe, i sprzątaj ich odchody</p><p>✅Klasyfikacja i rozmieszczenie śmieci to dobra pomoc w życiu.</p>"
                                    }
                                ]
                            },
                            {
                                "items": [
                                    {
                                        "type": "TEXT",
                                        "content": "<h1>⭐LISTA PAKOWANIA</h1><p>✅6 x worków na śmieci</p>"
                                    },
                                    {
                                        "type": "IMAGE",
                                        "url": "https://a.allegroimg.com/original/11c5d4/6e2d0668497f9f591188cbc50e73"
                                    }
                                ]
                            },
                            {
                                "items": [
                                    {
                                        "type": "IMAGE",
                                        "url": "https://a.allegroimg.com/original/11eff1/a6c9dca14f0eabec38d038c1a656"
                                    },
                                    {
                                        "type": "TEXT",
                                        "content": "<h1>⭐TORBA NA KUPY ZWIERZĄT</h1><p>✅Jeśli szukasz worka na śmieci dla swojego zwierzaka</p><p>✅wtedy Twoje poszukiwania dobiegły końca! Ponieważ nasz produkt spełnia wszystkie Twoje wymagania, jest wykonany z biodegradowalnego materiału, co czyni go przyjaznym dla środowiska i chroniącym środowisko. Trwała i mocna nośność.</p>"
                                    }
                                ]
                            },
                            {
                                "items": [
                                    {
                                        "type": "TEXT",
                                        "content": "<h1>⭐UCHWYT NA PSIE KUPY</h1><p>✅Kolor: zielony.</p><p>✅Materiał: skrobia kukurydziana.</p><p>✅Rozmiar: 6,00 x 4,00 x 4,00 cm / 2,36 x 1,57 x 1,57 cala.</p><p>✅Biodegradowalna torba na psie odchody, bardzo przyjazna dla środowiska.</p><p>✅Lekka waga i duża pojemność, wygodna do noszenia na spacerze na świeżym powietrzu.</p><p>✅Skrobia kukurydziana nie powoduje zanieczyszczeń i może ulec rozkładowi, stając się nawozem glebowym.</p><p>✅ Dbaj o środowisko, w którym przebywają zwierzęta domowe, i sprzątaj ich odchody</p><p>✅Klasyfikacja i rozmieszczenie śmieci to dobra pomoc w życiu.</p>"
                                    },
                                    {
                                        "type": "IMAGE",
                                        "url": "https://a.allegroimg.com/original/1134ff/84f3ae67499e93aefa30b297203b"
                                    }
                                ]
                            },
                            {
                                "items": [
                                    {
                                        "type": "IMAGE",
                                        "url": "https://a.allegroimg.com/original/110433/7a5b5847441687c300df74d14f6b"
                                    },
                                    {
                                        "type": "IMAGE",
                                        "url": "https://a.allegroimg.com/original/11cdb8/d881ed20463590a573393bc87e1b"
                                    }
                                ]
                            }
                        ]
                    },
                    "external": {
                        "id": "966c018327e418154814a754c38a69f2"
                    },
                    "category": {
                        "id": "257435"
                    },
                    "taxSettings": null,
                    "sizeTable": null,
                    "discounts": {
                        "wholesalePriceList": null
                    },
                    "contact": null,
                    "fundraisingCampaign": null,
                    "messageToSellerSettings": null,
                    "attachments": [],
                    "b2b": null,
                    "additionalServices": null,
                    "compatibilityList": null,
                    "additionalMarketplaces": {
                        "allegro-sk": {
                            "sellingMode": {
                                "price": {
                                    "amount": "3.66",
                                    "currency": "EUR"
                                }
                            },
                            "publication": {
                                "state": "REFUSED",
                                "refusalReasons": [
                                    {
                                        "code": "VQR010_SAURON_LEVEL_LOW",
                                        "userMessage": "sales quality level too low",
                                        "parameters": {}
                                    }
                                ]
                            }
                        },
                        "allegro-cz": {
                            "sellingMode": {
                                "price": {
                                    "amount": "93.00",
                                    "currency": "CZK"
                                }
                            },
                            "publication": {
                                "state": "REFUSED",
                                "refusalReasons": [
                                    {
                                        "code": "VQR010_SAURON_LEVEL_LOW",
                                        "userMessage": "sales quality level too low",
                                        "parameters": {}
                                    }
                                ]
                            }
                        },
                        "allegro-hu": {
                            "sellingMode": {
                                "price": {
                                    "amount": "1505.00",
                                    "currency": "HUF"
                                }
                            },
                            "publication": {
                                "state": "REFUSED",
                                "refusalReasons": [
                                    {
                                        "code": "VQR010_SAURON_LEVEL_LOW",
                                        "userMessage": "sales quality level too low",
                                        "parameters": {}
                                    }
                                ]
                            }
                        },
                        "allegro-business-cz": {
                            "sellingMode": {
                                "price": {
                                    "amount": "93.00",
                                    "currency": "CZK"
                                }
                            },
                            "publication": {
                                "state": "NOT_REQUESTED",
                                "refusalReasons": []
                            }
                        }
                    },
                    "warnings": [],
                    "id": "***********",
                    "language": "pl-PL",
                    "publication": {
                        "status": "ENDED",
                        "duration": null,
                        "endedBy": "ADMIN",
                        "endingAt": null,
                        "startingAt": null,
                        "republish": false,
                        "marketplaces": {
                            "base": {
                                "id": "allegro-pl"
                            },
                            "additional": [
                                {
                                    "id": "allegro-sk"
                                },
                                {
                                    "id": "allegro-cz"
                                },
                                {
                                    "id": "allegro-hu"
                                }
                            ]
                        }
                    },
                    "validation": {
                        "errors": [],
                        "warnings": [],
                        "validatedAt": "2024-11-23T19:12:23.623Z"
                    },
                    "createdAt": "2024-11-23T19:12:23.000Z",
                    "updatedAt": "2024-11-29T10:50:26.056Z"
                }     
                
                """;

        JsonNode productJsonNode = JsonXhUtil.parseObject(str);
        ObjectNode offerJsonNode = JsonXhUtil.createObjectNode();
        ObjectNode offerNode = JsonXhUtil.createObjectNode();
        ObjectNode categoryNode = JsonXhUtil.createObjectNode();
        ObjectNode descriptionsNode = JsonXhUtil.createObjectNode();
        ObjectNode standardizedNode = JsonXhUtil.createObjectNode();




        categoryNode.put("id",productJsonNode.path("category").path("id").asText());
        offerNode.put("category",categoryNode);
        offerNode.put("product",productJsonNode.path("productSet").get(0).path("product")); ;

        standardizedNode.put("sections", productJsonNode.path("description").path("sections"));
        descriptionsNode.set("standardized",standardizedNode);
        offerNode.put("name",productJsonNode.path("name").asText());

        offerNode.put("name",productJsonNode.path("name").asText());
        offerNode.put("descriptions",descriptionsNode);
        offerNode.put("images",convertImage((ArrayNode) productJsonNode.path("images")));
        offerNode.put("parameters", convertParameter((ArrayNode) productJsonNode.path("parameters")));
        offerJsonNode.set("offer",offerNode);

        log.info("offerNode:{}",offerJsonNode.toString());


        offerJsonNode.set("offer",offerNode);


        CollectForm collectForm = new CollectForm();
        collectForm.setOfferJson(offerJsonNode);
        collectForm.setOfferLink("https://allegro.pl/offera/"+productJsonNode.path("id").asText());

//        dealDataMemberWy(collectForm,productJsonNode,null);

    }



    public void contractData2(AllegroStoreEntity storeEntity, String offerId,String ship_rating_id, WySyncForm form){
        log.info("contractData2:{},租户：{}",userProvider.get().getUserName(),userProvider.get().getTenantId());
        JsonNode productJsonNode = NewOfferAPI.getOffer(storeEntity,offerId);
        //如果获取不到product id ，这一条数据则抛弃
        if( productJsonNode.path("productSet").get(0).path("product").path("id").asText().isEmpty()){
            log.warn("获取offer失败：{}",offerId);
            allegroOfferService.failOfferProductId(offerId);
            return ;
        }

        ObjectNode offerJsonNode = JsonXhUtil.createObjectNode();
        ObjectNode offerNode = JsonXhUtil.createObjectNode();
        ObjectNode categoryNode = JsonXhUtil.createObjectNode();
        ObjectNode descriptionsNode = JsonXhUtil.createObjectNode();
        ObjectNode standardizedNode = JsonXhUtil.createObjectNode();
        categoryNode.put("id",productJsonNode.path("category").path("id").asText());
        offerNode.put("category",categoryNode);
        offerNode.put("product",productJsonNode.path("productSet").get(0).path("product")); ;

        standardizedNode.put("sections", productJsonNode.path("description").path("sections"));
        descriptionsNode.set("standardized",standardizedNode);
        offerNode.put("name",productJsonNode.path("name").asText());

        offerNode.put("name",productJsonNode.path("name").asText());
        offerNode.put("descriptions",descriptionsNode);
        offerNode.put("images",convertImage((ArrayNode) productJsonNode.path("images")));
        offerNode.put("parameters", convertParameter((ArrayNode) productJsonNode.path("parameters")));
        offerJsonNode.set("offer",offerNode);

//        log.info("offerNode:{}",offerJsonNode.toString());


        offerJsonNode.set("offer",offerNode);


        CollectForm collectForm = new CollectForm();
        collectForm.setOfferJson(offerJsonNode);
        collectForm.setOfferLink("https://allegro.pl/oferta/"+productJsonNode.path("id").asText());

        ErpProductEntity erpProductEntity =   dealDataMemberWy(collectForm,productJsonNode,ship_rating_id,form);
        allegroOfferService.updateOfferProductId(offerId,erpProductEntity.getProductId(),erpProductEntity.getSkuId());


    }


    private JsonNode convertParameter(ArrayNode parameterNode){

        if(parameterNode==null) {
            return null;
        }
        ArrayNode newParametersArray = JsonXhUtil.createArrayNode();

        for (JsonNode parameter : parameterNode) {
            ObjectNode newParameter = JsonXhUtil.createObjectNode();
            newParameter.put("id", parameter.get("id").asText());
            newParameter.put("name", parameter.get("name").asText());

            ArrayNode valuesArray = JsonXhUtil.createArrayNode();
            JsonNode values = parameter.get("values");
            JsonNode valuesIds = parameter.get("valuesIds");

            for (int i = 0; i < values.size(); i++) {
                ObjectNode valueObject = JsonXhUtil.createObjectNode();
                if(valuesIds!=null && valuesIds.size()>i){
                    valueObject.put("id", valuesIds.get(i).asText());
                }
                valueObject.put("url", (String) null);
                valueObject.put("valueLabel", values.get(i).asText());
                valuesArray.add(valueObject);
            }

            newParameter.set("values", valuesArray);
            newParametersArray.add(newParameter);
        }
        return newParametersArray;

    }


    private JsonNode convertImage(ArrayNode imageNode){

        if(imageNode==null) {
            return null;
        }
        ArrayNode newImagesArray = JsonXhUtil.createArrayNode();
        for (JsonNode imageUrl : imageNode) {
            ObjectNode imageObject = JsonXhUtil.createObjectNode();
            imageObject.put("url", imageUrl.asText());
            newImagesArray.add(imageObject);
        }
        return newImagesArray;

    }

    @Async
    @Override
    public void syncWyData(WySyncForm form){
        List<AllegroOfferEntity> offerList = allegroOfferService.movelistBySellerId(form.getSellerId());
//        Integer size = Math.min(offerList.size(),100);
//        offerList = offerList.subList(0,size);
        AllegroStoreEntity storeEntity = allegroStoreService.getInfoBySellerId(form.getSellerId());
        ExecutorService executorService = Executors.newFixedThreadPool(10);
        try {
            List<Future<?>> futures = offerList.stream().map(offerEntity -> executorService.submit(() -> {
                try {
                    log.info("正在转换：sellerId:{},offerId:{}",storeEntity.getStoreName(),offerEntity.getId());
                    contractData2(storeEntity,offerEntity.getId(),offerEntity.getShippingRatesId(), form);
                } catch (Exception e) {
                    log.error("转换Offer失败：{}", e);
                }
            })).collect(Collectors.toList());

            for (Future<?> future : futures) {
                try {
                    // 设置每个任务的超时时间为60秒
                    future.get(60, TimeUnit.SECONDS);
                } catch (TimeoutException e) {
                    log.warn("任务超时：", e);
                    // 取消超时的任务
                    future.cancel(true);
                } catch (InterruptedException | ExecutionException e) {
                    log.error("执行任务时发生错误: ", e);
//                    Thread.currentThread().interrupt();
                }
            }
        }finally {
            executorService.shutdown();
        }
    }


    @Override
   public Long todayDelete(){
        return this.getBaseMapper().todayDelete();
    }


    @Override
    public Long todayCollect(){
        return this.getBaseMapper().todayCollect();
    }


    @Override
    public Long yesterdayCollect(){
        return this.getBaseMapper().yesterdayCollect();
    }


     @Override
    public void updateBuyersQuantity( Integer skuId){
        if(skuId!=null){
            this.getBaseMapper().updateBuyersQuantity(skuId);
        }
     }
}
