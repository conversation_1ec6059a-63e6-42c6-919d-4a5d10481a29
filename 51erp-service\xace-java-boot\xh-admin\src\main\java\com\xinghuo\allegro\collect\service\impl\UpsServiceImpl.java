package com.xinghuo.allegro.collect.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xinghuo.allegro.collect.dao.UpsMapper;
import com.xinghuo.allegro.collect.entity.UpsEntity;
import com.xinghuo.allegro.collect.service.UpsService;
import com.xinghuo.common.base.service.BaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 自动检索事务，继承自BaseServiceImpl，提供特定的EAN编码管理功能。
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class UpsServiceImpl extends BaseServiceImpl<UpsMapper, UpsEntity> implements UpsService {


    @Override
    public UpsEntity waitGets( ) {
        // 创建查询条件，指定查询状态的采集优惠信息，并按照购买数量降序排列，限制查询一条数据。
        QueryWrapper<UpsEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
//                .select(CollectTaskEntity::getId, CollectTaskEntity::getLink)
                .eq(UpsEntity::getDealStatus, 0);

        queryWrapper.lambda().last("limit 1");

        // 根据查询条件获取采集优惠信息列表
        UpsEntity entity = this.getOne(queryWrapper);

        // 遍历列表中的每个采集优惠信息，更新其请求状态和请求次数，并保存更新。
        if(entity!=null) {
            entity.setDealStatus(1);
            this.updateById(entity);
        }
        else{


        }
        return entity;
    }

}
