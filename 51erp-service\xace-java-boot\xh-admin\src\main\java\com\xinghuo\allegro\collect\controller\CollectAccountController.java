package com.xinghuo.allegro.collect.controller;


import com.xinghuo.allegro.collect.entity.CollectAccountEntity;
import com.xinghuo.allegro.collect.service.CollectAccountService;
import com.xinghuo.allegro.util.AesEncryptionUtil;
import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.util.core.DateXhUtil;
import com.xinghuo.common.util.sys.IpUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

/**
 * 产品采集-激活账户 Controller
 * 暂时不需要
 *
 * <AUTHOR>
 * 2024-06-08
 */
@Slf4j
@RestController
@Tag(name = "产品采集-激活账户", description = "产品采集-激活账户")
@RequestMapping("/api/allegro/collect/account")
public class CollectAccountController {

    @Autowired
    private CollectAccountService collectAccountService;


    /**
     * 校验激活码
     */
    @Operation(summary = "校验激活码")
    @GetMapping("/checkActive")
    public ActionResult<String> checkActive(String activeCode, String uniqueId) {
        CollectAccountEntity entity = collectAccountService.getByActiveCode(activeCode);
        if (entity == null) {
            return ActionResult.fail("激活码不存在，请重新激活！");
        }
        if (entity.getUsedStatus() == 0) {
            return ActionResult.fail("激活码未激活！");
        }
        if (entity.getExpireDate().getTime() < System.currentTimeMillis()) {
            log.warn("激活码已过期！" + entity.getExpireDate());
            return ActionResult.fail("激活码已过期！");
        }
        if (!entity.getActiveUniqueId().equalsIgnoreCase(uniqueId)) {
            return ActionResult.fail("激活码和绑定设备不匹配，请重新激活！");
        }

        entity.setLastConnectIp(IpUtil.getIpAddr() + "(" + IpUtil.getIpCity(IpUtil.getIpAddr()) + ")");
        entity.setLastConnectTime(new Date());
        collectAccountService.updateById(entity);
        return ActionResult.success(AesEncryptionUtil.simpleEncrypt(DateXhUtil.formatDate(entity.getExpireDate())));
    }


    @Operation(summary = "绑定")
    @PostMapping("/bind")
    public ActionResult<String> bind(String activeCode, String uniqueId) {
        CollectAccountEntity entity = collectAccountService.getByActiveCode(activeCode);
        if (entity == null) {
            return ActionResult.fail("激活码无效！");
        }
        if (entity.getUsedStatus() == 1) {
            return ActionResult.fail("激活码已使用！");
        }
        if (entity.getExpireDate().getTime() < System.currentTimeMillis()) {
            log.warn("激活码已过期！" + entity.getExpireDate());

            return ActionResult.fail("激活码已过期！");
        }
        entity.setActiveTime(new Date());
        entity.setActiveFromIp(IpUtil.getIpAddr());
        entity.setUsedStatus(1);
        entity.setActiveUniqueId(uniqueId);
        collectAccountService.updateById(entity);
        return ActionResult.success(AesEncryptionUtil.simpleEncrypt(DateXhUtil.formatDate(entity.getExpireDate())));
    }
}
