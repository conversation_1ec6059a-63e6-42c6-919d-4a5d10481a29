package com.xinghuo.allegro.manage.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.BaseEntityV2;
import lombok.Data;

import java.math.BigDecimal;

@Data
@TableName("zz_sys_exchange_rate")
public class SysExchangeRateEntity  extends BaseEntityV2.CUBaseEntityV2 {

    @TableField("name")
    private String name;

    @TableField("currency")
    private String currency;

    @TableField("rate")
    private BigDecimal rate;

    @TableField("note")
    private String note;




}
