package com.xinghuo.allegro.data.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xinghuo.allegro.collect.model.collect.CollectTaskPagination;
import com.xinghuo.allegro.data.dao.AllegroDataTaskMapper;
import com.xinghuo.allegro.data.entity.AllegroDataTaskEntity;
import com.xinghuo.allegro.data.service.AllegroDataTaskService;
import com.xinghuo.common.base.service.BaseServiceImpl;
import com.xinghuo.common.util.core.StrXhUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * Allegro 英文采集任务服务实现
 *
 * <AUTHOR>
 * @date 2024-11-05
 */
@Slf4j
@Service
public class AllegroDataTaskServiceImpl extends BaseServiceImpl<AllegroDataTaskMapper, AllegroDataTaskEntity> implements AllegroDataTaskService {

    @Override
    public List<AllegroDataTaskEntity> getList(CollectTaskPagination pagination) {
        LambdaQueryWrapper<AllegroDataTaskEntity> queryWrapper = new LambdaQueryWrapper<>();

        // 任务类型过滤
        if (StrXhUtil.isNotBlank(pagination.getTaskType())) {
            queryWrapper.eq(AllegroDataTaskEntity::getTaskType, pagination.getTaskType());
        }

        // 平台过滤
        if (StrXhUtil.isNotBlank(pagination.getPlatform())) {
            queryWrapper.eq(AllegroDataTaskEntity::getPlatform, pagination.getPlatform());
        }

        // 状态过滤
        if (pagination.getStatus() != null) {
            queryWrapper.eq(AllegroDataTaskEntity::getStatus, pagination.getStatus());
        }

        // 卖家ID过滤
        if (StrXhUtil.isNotBlank(pagination.getSellerId())) {
            queryWrapper.eq(AllegroDataTaskEntity::getSellerId, pagination.getSellerId());
        }

        // 按创建时间倒序排列
        queryWrapper.orderByDesc(AllegroDataTaskEntity::getCreatedAt);

        // 分页查询
        Page<AllegroDataTaskEntity> page = new Page<>(pagination.getCurrentPage(), pagination.getPageSize());
        Page<AllegroDataTaskEntity> result = this.page(page, queryWrapper);

        return result.getRecords();
    }

    @Override
    public AllegroDataTaskEntity waitGets(String clientId, String taskType, String platform) {
        LambdaQueryWrapper<AllegroDataTaskEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AllegroDataTaskEntity::getStatus, 0) // 未处理状态
                .eq(AllegroDataTaskEntity::getPlatform, platform)
                .orderByDesc(AllegroDataTaskEntity::getPriority) // 按优先级排序
                .orderByAsc(AllegroDataTaskEntity::getCreatedAt) // 按创建时间排序
                .last("LIMIT 1");

        if (StrXhUtil.isNotBlank(taskType)) {
            queryWrapper.eq(AllegroDataTaskEntity::getTaskType, taskType);
        }

        AllegroDataTaskEntity task = this.getOne(queryWrapper);

        if (task != null) {
            // 更新任务状态为处理中
            task.setStatus(1);
            task.setClientId(clientId);
            task.setRequestTime(new Date());
            task.setLastUpdatedAt(new Date());
            this.updateById(task);

            log.info("分配任务给客户端: taskId={}, clientId={}, taskType={}",
                    task.getId(), clientId, task.getTaskType());
        }

        return task;
    }

    @Override
    public int updateCollectTask(String taskId) {
        if (StrXhUtil.isBlank(taskId)) {
            return 0;
        }

        AllegroDataTaskEntity task = this.getById(taskId);
        if (task != null) {
            task.setStatus(2); // 已完成状态
            task.setFinishTime(new Date());
            task.setLastUpdatedAt(new Date());
            boolean result = this.updateById(task);

            log.info("更新任务状态为已完成: taskId={}, result={}", taskId, result);
            return result ? 1 : 0;
        }

        log.warn("未找到任务: taskId={}", taskId);
        return 0;
    }

    @Override
    public boolean existTask(String productId) {
        if (StrXhUtil.isBlank(productId)) {
            return false;
        }

        LambdaQueryWrapper<AllegroDataTaskEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AllegroDataTaskEntity::getPath, productId)
                .in(AllegroDataTaskEntity::getStatus, 0, 1); // 未处理或处理中状态

        long count = this.count(queryWrapper);
        return count > 0;
    }

    @Override
    public long getPendingTaskCount(String taskType) {
        LambdaQueryWrapper<AllegroDataTaskEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AllegroDataTaskEntity::getStatus, 0); // 未处理状态

        if (StrXhUtil.isNotBlank(taskType)) {
            queryWrapper.eq(AllegroDataTaskEntity::getTaskType, taskType);
        }

        return this.count(queryWrapper);
    }
}
