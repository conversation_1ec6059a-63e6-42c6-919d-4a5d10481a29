package com.baidu.translate;

import java.io.FileInputStream;
import java.security.MessageDigest;

public class ImageCompare {

    public static String getImageHash(String imagePath) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            FileInputStream fis = new FileInputStream(imagePath);

            byte[] dataBytes = new byte[1024];

            int nread = 0;

            while ((nread = fis.read(dataBytes)) != -1) {
                md.update(dataBytes, 0, nread);
            };

            byte[] mdbytes = md.digest();

            StringBuffer sb = new StringBuffer();
            for (int i = 0; i < mdbytes.length; i++) {
                sb.append(Integer.toString((mdbytes[i] & 0xff) + 0x100, 16).substring(1));
            }

            fis.close();
            return sb.toString();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static void main(String[] args) {
        String imagePath1 = "g:/1.jpg";
        String imagePath2 = "g:/2.jpg";

        String hash1 = getImageHash(imagePath1);
        String hash2 = getImageHash(imagePath2);

        if (hash1.equals(hash2)) {
            System.out.println("俩张图片是同一张");
        } else {
            System.out.println("俩张图片不是同一张");
        }
    }
}