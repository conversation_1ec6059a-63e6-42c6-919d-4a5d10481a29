package com.xinghuo.allegro.manage.controller;


import com.xinghuo.allegro.manage.entity.AllegroStoreEntity;
import com.xinghuo.allegro.manage.model.store.*;
import com.xinghuo.allegro.manage.service.AllegroStoreService;
import com.xinghuo.allegro.shelf.service.PushOfferLinkService;
import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.constant.MsgCode;
import com.xinghuo.common.exception.DataException;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.common.util.core.DateXhUtil;
import com.xinghuo.extenduser.entity.StoreUserEntity;
import com.xinghuo.extenduser.service.StoreUserService;
import com.xinghuo.permission.entity.UserEntity;
import com.xinghuo.permission.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Allegro店铺管理
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@Tag(name = "Allegro店铺管理", description = "Allegro店铺管理")
@RequestMapping("/api/allegro/store")
public class AllegroStoreController {

    @Resource
    private AllegroStoreService allegroStoreService;

    @Resource
    private UserService userService;

    @Resource
    private StoreUserService storeUserService;

    @Resource
    private PushOfferLinkService pushOfferLinkService;

    /**
     * 列表
     */
    @Operation(summary = "店铺管理列表")
    @PostMapping("/manageList")
    public ActionResult<PageListVO<AllegroStoreManageModel>> manageList(@RequestBody AllegroStorePagination pagination) {
        List<AllegroStoreEntity> list = allegroStoreService.getList(pagination);
        List<AllegroStoreManageModel> listVO = BeanCopierUtils.copyList(list, AllegroStoreManageModel.class);
        List<String> sellerIdList = list.stream().map(AllegroStoreEntity::getSellerId).collect(Collectors.toList());
        List<StoreUserEntity> storeUserList;
        if(!sellerIdList.isEmpty()){
            storeUserList = storeUserService.getUserIdListBySellerId(sellerIdList);
        } else {
            storeUserList = new ArrayList<>();
        }

        Map<String,Integer> waintingCountMap;
        if(pagination.getIsValid()!=null && pagination.getIsValid()){
            waintingCountMap = pushOfferLinkService.getPushStatusCount(sellerIdList);
        } else {
            waintingCountMap = new HashMap<>();
        }

        List<UserEntity> alluserList = userService.list();
        //需要过滤掉无效用户的数据
        alluserList.removeIf(user -> user.getEnabledMark() == 0 || user.getIsAdministrator() == 1);
        //组装成 id,realname的键值对
        Map<String, String> allUserMap = alluserList.stream()
                .collect(Collectors.toMap(UserEntity::getId, UserEntity::getRealName));
        //显示店铺授权的用户
        listVO.forEach(item -> {
            item.setUserName(allUserMap.get(item.getUserId()));

            if(pagination.getIsValid()!=null &&pagination.getIsValid()){
                item.setWaitLinkCount(waintingCountMap.getOrDefault(item.getSellerId(),0));
            }

            if(item.getSyncFlag() && item.getExpireDate().getTime() < System.currentTimeMillis() ) {
                item.setTokenStatus(Boolean.FALSE);
            }else
            {
                item.setTokenStatus(Boolean.TRUE);
            }

            List<String> userIdList = storeUserList.stream().filter(storeUser -> storeUser.getSellerId().equals(item.getSellerId()))
                    .map(StoreUserEntity::getUserId).toList();
            //根据allUserMap 组装用户姓名，用逗号隔开
            String userName = userIdList.stream().map(userId -> {
                return allUserMap.get(userId);
            }).collect(Collectors.joining(","));
            item.setAuthorizedUserNames(userName);

            //如果店铺已经挂店，则用挂店时间减去创建日期，否则用当前日期减去创建日期
            if(item.getEndTime()!=null) {
                item.setLiveDays( DateXhUtil.betweenDay(item.getCreatorTime(), item.getEndTime(),false)+1);
            } else {
                item.setLiveDays(DateXhUtil.betweenDay(item.getCreatorTime(), DateXhUtil.date(),false)+1);
            }

            item.setDayOrders(BigDecimal.valueOf(item.getOrders()) .divide(BigDecimal.valueOf(item.getLiveDays()), 2, RoundingMode.HALF_UP));


        });
        PaginationVO page = BeanCopierUtils.copy(pagination, PaginationVO.class);
        return ActionResult.page(listVO, page);
    }


    @Operation(summary = "店铺运营管理列表")
    @PostMapping("/operationList")
    public ActionResult<PageListVO<AllegroStoreOperationModel>> operationList(@RequestBody AllegroStorePagination pagination) {
        List<AllegroStoreEntity> list = allegroStoreService.getList(pagination);
        List<AllegroStoreOperationModel> listVO = BeanCopierUtils.copyList(list, AllegroStoreOperationModel.class);
        List<UserEntity> alluserList = userService.list();
        //需要过滤掉无效用户的数据
        alluserList.removeIf(user -> user.getEnabledMark() == 0 || user.getIsAdministrator() == 1);
        //组装成 id,realname的键值对
        Map<String, String> allUserMap = alluserList.stream()
                .collect(Collectors.toMap(UserEntity::getId, UserEntity::getRealName));
        //显示店铺授权的用户
        listVO.forEach(item -> {
            item.setUserName(allUserMap.get(item.getUserId()));
        });
        PaginationVO page = BeanCopierUtils.copy(pagination, PaginationVO.class);
        return ActionResult.page(listVO, page);
    }

    @Operation(summary = "店铺财务管理列表")
    @PostMapping("/feeList")
    public ActionResult<PageListVO<AllegroStoreFeeModel>> feeList(@RequestBody AllegroStorePagination pagination) {
        List<AllegroStoreEntity> list = allegroStoreService.getList(pagination);
        List<AllegroStoreFeeModel> listVO = BeanCopierUtils.copyList(list, AllegroStoreFeeModel.class);
        List<UserEntity> alluserList = userService.list();
        //需要过滤掉无效用户的数据
        alluserList.removeIf(user -> user.getEnabledMark() == 0 || user.getIsAdministrator() == 1);
        //组装成 id,realname的键值对
        Map<String, String> allUserMap = alluserList.stream()
                .collect(Collectors.toMap(UserEntity::getId, UserEntity::getRealName));
        listVO.forEach(item -> {
            item.setUserName(allUserMap.get(item.getUserId()));
        });
        PaginationVO page = BeanCopierUtils.copy(pagination, PaginationVO.class);
        return ActionResult.page(listVO, page);
    }


    /**
     * 获取详情(编辑页)--转换数据
     */
    @Operation(summary = "店铺-详情")
    @GetMapping("/{id}")
    @Parameters({
            @Parameter(name = "id", description = "主键", required = true)
    })
    public ActionResult<AllegroStoreDetailModel> info(@PathVariable("id") String id) {
        AllegroStoreEntity entity = allegroStoreService.getInfo(id);
        if (entity == null) {
            return ActionResult.fail("店铺不存在");
        }

        AllegroStoreDetailModel infoVo = BeanCopierUtils.copy(entity, AllegroStoreDetailModel.class);
        UserEntity userEntity = userService.getById(entity.getUserId());
        if (userEntity != null) {
            infoVo.setUserName(userEntity.getRealName());
        }
        //20241005 增加 店铺用户列表 和店铺
        List<String> userIdList = storeUserService.getUserIdListBySellerId(entity.getSellerId());
        List<UserEntity> alluserList = userService.list();
        //需要过滤掉无效用户的数据
        alluserList.removeIf(user -> user.getEnabledMark() == 0 || user.getIsAdministrator() == 1);
        List<UserStoreSimpleModel> userList = BeanCopierUtils.copyList(alluserList, UserStoreSimpleModel.class);
        infoVo.setSelectedUserIdList(userIdList);
        infoVo.setAllUserList(userList);
        return ActionResult.success(infoVo);
    }


    @Operation(summary = "选择店铺")
    @GetMapping("/storeSelect")
    public ActionResult<List<StoreGroupModel>> storeSelect(@RequestParam(required = false, defaultValue = "false") boolean isValid) {
        List<StoreGroupModel> list = allegroStoreService.storeSelect(isValid);

        return ActionResult.success(list);
    }


    /**
     * 编辑
     */
    @PutMapping("/{id}")
    @Parameters({
            @Parameter(name = "id", description = "主键", required = true)
    })
    @Operation(summary = "店铺-更新")
    public ActionResult update(@PathVariable("id") String id, @RequestBody @Valid AllegroStoreDetailForm form) throws DataException {
        AllegroStoreEntity entity = allegroStoreService.getInfo(id);
        if (entity != null) {
            entity.setStoreName(form.getStoreName());
            entity.setStoreType(form.getStoreType());
            entity.setGroupName(form.getGroupName());
            entity.setSyncFlag(form.getSyncFlag());
            entity.setEndType(form.getEndType());
            entity.setEndTime(form.getEndTime());
            entity.setEndReason(form.getEndReason());
            entity.setAdvExpireDate(form.getAdvExpireDate());
            entity.setNote(form.getNote());
            entity.setUserId(form.getUserId());
            allegroStoreService.update(id, entity);
            //20241005更新店铺数据
            if (form.getSelectedUserIdList() != null) {
                storeUserService.saveStoreUserWithUserIdList(form.getSelectedUserIdList(), entity.getSellerId());
            }

            return ActionResult.success(MsgCode.SU004.get());
        }
        return ActionResult.success(MsgCode.FA002.get());
    }

    @PutMapping("/feeUpdate/{id}")
    @Parameters({
            @Parameter(name = "id", description = "主键", required = true)
    })
    @Operation(summary = "店铺-费用更新")
    public ActionResult feeUpdate(@PathVariable("id") String id, @RequestBody @Valid AllegroStoreFeeForm form) throws DataException {
        AllegroStoreEntity entity = allegroStoreService.getInfo(id);
        if (entity != null) {
            entity.setId(id);
            entity.setNote(form.getNote());
            entity.setPlatformRate(form.getPlatformRate());
            entity.setOcid(form.getOcid());
            entity.setTransferRate(form.getTransferRate());
            entity.setTransferRateType(form.getTransferRateType());
            entity.setTransferRateMethod(form.getTransferRateMethod());
            entity.setIossType(form.getIossType());
            entity.setIossNo(form.getIossNo());
            entity.setVatRate(form.getVatRate());
            entity.setVatType(form.getVatType());
            entity.setVatNo(form.getVatNo());
            entity.setNorwayNumber(form.getNorwayNumber());
            allegroStoreService.update(id, entity);
            return ActionResult.success(MsgCode.SU004.get());
        }
        return ActionResult.success(MsgCode.FA002.get());
    }

    @PutMapping("/buckVaxUpdate")
    @Operation(summary = "店铺-批量税号设置")
    public ActionResult buckVaxUpdate(@RequestBody @Valid List<AllegroStoreFeeForm> formlist) throws DataException {
        for (AllegroStoreFeeForm form : formlist) {
            AllegroStoreEntity entity = allegroStoreService.getInfo(form.getId());
            if (entity != null) {
                entity.setIossType(form.getIossType());
                entity.setIossNo(form.getIossNo());
                entity.setVatNo(form.getVatNo());
                entity.setNorwayNumber(form.getNorwayNumber());
                allegroStoreService.update(form.getId(), entity);
            }
        }

        return ActionResult.success(MsgCode.SU004.get());
    }

    @PutMapping("/buckFeeUpdate")
    @Operation(summary = "店铺-批量费用设置")
    public ActionResult buckFeeUpdate(@RequestBody @Valid BuckFeeForm form) throws DataException {
        int result = allegroStoreService.buckFeeUpdate(form);
        return ActionResult.success("成功设置" + result + "条数据");
    }


    @PutMapping("/updateNote")
    @Operation(summary = "店铺-备注保存")
    public ActionResult updateNote(@RequestBody @Valid StoreNoteForm form) throws DataException {
        int result = allegroStoreService.updateNote(form);
        return ActionResult.success("成功保存" + result + "条数据");
    }


    @DeleteMapping("/deleteSellData/{id}")
    @Parameters({
            @Parameter(name = "id", description = "主键", required = true)
    })
    @Operation(summary = "删除店铺数据")
    public ActionResult deleteSellData
            (@PathVariable("id") String id) throws DataException {
        AllegroStoreEntity entity = allegroStoreService.getInfo(id);
        if (entity != null) {
            allegroStoreService.deleteSellerData(entity.getSellerId());
            return ActionResult.success(MsgCode.SU003.get());
        }
        return ActionResult.success(MsgCode.FA002.get());
    }



    @PutMapping("/updateAutoShelf")
    @Operation(summary = "更新店铺自动上架数据")
    public ActionResult updateAutoShelf(@RequestBody @Valid StoreShelfForm form) throws DataException {
        form.validate();
        AllegroStoreEntity entity = allegroStoreService.getInfo(form.getId());
        if(entity != null) {
            entity.setIsAutoShelf(form.getIsAutoShelf());
            entity.setNextShelfTime(form.getNextShelfTime());
            entity.setPeriodDays(form.getPeriodDays());
            allegroStoreService.update(entity.getId(), entity);
            return ActionResult.success(MsgCode.SU004.get());
        }

        return ActionResult.fail(MsgCode.FA002.get());
    }
}
