package com.baidu.translate;

            import cn.hutool.core.io.FileUtil;
            import org.jsoup.Jsoup;
            import org.jsoup.nodes.Document;
            import org.jsoup.nodes.Element;
            import org.jsoup.select.Elements;

            import java.io.BufferedWriter;
            import java.io.File;
            import java.io.FileWriter;
            import java.io.IOException;
            import java.util.ArrayList;
            import java.util.List;
            import java.util.regex.Matcher;
            import java.util.regex.Pattern;

            public class CategoryParser {
                private static final String OUTPUT_FILE = "d:/1.txt";

                public static void parseCategories(Document doc) throws IOException {
                    try (BufferedWriter writer = new BufferedWriter(new FileWriter(OUTPUT_FILE))) {
                        Elements items = doc.select("li.site-map-item");
                        System.out.println("Found " + items.size() + " category items");

                        for (Element item : items) {
                            Element link = item.select("a").first();
                            if (link == null) continue;

                            String href = link.attr("href");
                            String id = extractId(href);
                            String name = link.text().trim();

                            // Skip items without ID
                            if (id.isEmpty()) continue;

                            // Find parent ID
                            String parentId = findParentId(item);

                            // Build hierarchical path
                            String path = buildPath(item);

                            // Write line with tab separation
                            writer.write(String.format("%s\t%s\t%s\t%s\t%s%n",
                                    id,
                                    name,
                                    parentId,
                                    path,href));
                        }
                        System.out.println("Category data written to " + OUTPUT_FILE);
                    }
                }

                private static String extractId(String href) {
                    if (href == null || href.isEmpty()) return "";
                    Pattern pattern = Pattern.compile("/a-(\\d+)");
                    Matcher matcher = pattern.matcher(href);
                    return matcher.find() ? matcher.group(1) : "";
                }

                private static String findParentId(Element item) {
                    // Find the closest parent ul.site-map-sub-list
                    Element parentUl = item.closest("ul.site-map-sub-list");
                    if (parentUl != null) {
                        // Find the li.site-map-item that contains this ul
                        Element parentLi = parentUl.parent();
                        while (parentLi != null && !parentLi.is("li.site-map-item")) {
                            parentLi = parentLi.parent();
                        }

                        if (parentLi != null) {
                            Element parentLink = parentLi.select("a").first();
                            if (parentLink != null) {
                                return extractId(parentLink.attr("href"));
                            }
                        }
                    }
                    return "0";
                }

                private static String buildPath(Element item) {
                    List<String> pathParts = new ArrayList<>();

                    // Get the current category name
                    Element link = item.select("a").first();
                    if (link != null) {
                        String categoryName = link.text().trim();
                        pathParts.add(categoryName);
                    }

                    // Traverse up the DOM to build the path
                    Element current = item;
                    while (true) {
                        Element parentUl = current.closest("ul.site-map-sub-list");
                        if (parentUl == null) break;

                        // Find the parent li.site-map-item
                        Element parentLi = parentUl.parent();
                        while (parentLi != null && !parentLi.is("li.site-map-item")) {
                            parentLi = parentLi.parent();
                        }

                        if (parentLi == null) break;

                        Element parentLink = parentLi.select("a").first();
                        if (parentLink != null) {
                            String parentName = parentLink.text().trim();
                            pathParts.add(0, parentName); // Add to beginning of list
                        }

                        current = parentLi;
                    }

                    return String.join(" > ", pathParts);
                }

                public static void main(String[] args) {
                    try {
                        File file = new File("G:\\v2\\51erp\\51erp-service\\xace-java-boot\\xh-admin\\doc\\fruugo.sitemap.html");
                        String html = FileUtil.readString(file, "UTF-8");
                        Document doc = Jsoup.parse(html);
                        parseCategories(doc);
                        System.out.println("Successfully parsed sitemap categories");
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }