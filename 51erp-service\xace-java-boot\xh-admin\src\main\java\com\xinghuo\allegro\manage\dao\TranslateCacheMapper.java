package com.xinghuo.allegro.manage.dao;


import com.xinghuo.allegro.manage.entity.TranslateCacheEntity;
import com.xinghuo.common.base.dao.XHBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

@Mapper
public interface TranslateCacheMapper extends XHBaseMapper<TranslateCacheEntity> {

    @Update("UPDATE zz_translate_cache SET hit_count = hit_count + 1, last_used_at = NOW() WHERE f_id = #{id}")
    int incrementHitCount(@Param("id") String id);
}