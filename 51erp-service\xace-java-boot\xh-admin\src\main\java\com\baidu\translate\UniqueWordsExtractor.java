package com.baidu.translate;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.util.HashSet;
import java.util.Set;

public class UniqueWordsExtractor {

    public static void main(String[] args) {
        String inputFilePath = "d:/2.txt"; // 输入文件路径
        String outputFilePath = "d:/1.txt"; // 输出文件路径

        // 用于存储唯一单词的集合
        Set<String> uniqueWords = new HashSet<>();

        // 读取文件并提取单词
        try (BufferedReader reader = new BufferedReader(new FileReader(inputFilePath))) {
            String line;
            while ((line = reader.readLine()) != null) {
                String[] words = line.split(" > ");
                for (String word : words) {
                    uniqueWords.add(word.trim());
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

        // 将结果写入输出文件
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(outputFilePath))) {
            for (String word : uniqueWords) {
                writer.write(word);
                writer.newLine();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

        System.out.println("Unique words have been extracted and written to " + outputFilePath);
    }
}
