package com.xinghuo.allegro.collect.controller;

import com.xinghuo.allegro.collect.entity.CollectTaskEntity;
import com.xinghuo.allegro.collect.model.collect.CollectTaskForm;
import com.xinghuo.allegro.collect.model.collect.CollectTaskModel;
import com.xinghuo.allegro.collect.model.collect.CollectTaskPagination;
import com.xinghuo.allegro.data.service.AllegroSellerService;
import com.xinghuo.allegro.collect.service.CollectTaskService;
import com.xinghuo.allegro.util.AllegroConstant;
import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.constant.MsgCode;
import com.xinghuo.common.exception.DataException;
import com.xinghuo.common.util.UserProvider;
import com.xinghuo.common.util.core.BeanCopierUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * 产品采集Controller，负责处理产品采集相关的请求。
 * 接受客户端采集的数据上传。
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@Tag(name = "采集任务", description = "采集任务")
@RequestMapping("/api/allegro/task")
public class CollectTaskController {

    @Resource
    private CollectTaskService collectTaskService;

    @Resource
    private AllegroSellerService allegroSellerService;

    @Resource
    private UserProvider userProvider;

    /**
     * 采集任务列表
     */
    @Operation(summary = "采集任务列表")
    @PostMapping("/getList")
    public ActionResult list(@RequestBody CollectTaskPagination pagination) {
        List<CollectTaskEntity> list = collectTaskService.getList(pagination);
        List<CollectTaskModel> listVO = BeanCopierUtils.copyList(list, CollectTaskModel.class);
        PaginationVO page = BeanCopierUtils.copy(pagination, PaginationVO.class);
        return ActionResult.page(listVO, page);
    }

    /**
     * 获取采集任务详情
     */
    @Operation(summary = "采集任务-详情")
    @GetMapping("/{id}")
    @Parameters({
            @Parameter(name = "id", description = "主键", required = true)
    })
    public ActionResult info(@PathVariable("id") String id) {
        CollectTaskEntity entity = collectTaskService.getById(id);
        CollectTaskModel infoVo = BeanCopierUtils.copy(entity, CollectTaskModel.class);
        return ActionResult.success(infoVo);
    }

    /**
     * 编辑采集任务
     */
    @PutMapping("/{id}")
    @Parameters({
            @Parameter(name = "id", description = "主键", required = true)
    })
    @Operation(summary = "采集任务-更新")
    public ActionResult update(@PathVariable("id") String id, @RequestBody @Valid CollectTaskForm taskForm) throws DataException {
        CollectTaskEntity entity = collectTaskService.getById(id);
        if (entity != null) {
            entity = BeanCopierUtils.copy(taskForm, CollectTaskEntity.class);
            entity.setId(id);
            entity.setLastUpdatedBy(userProvider.get().getUserName());
            entity.setLastUpdatedAt(new Date());
            collectTaskService.updateById(entity);
            return ActionResult.success(MsgCode.SU004.get());
        }
        return ActionResult.success(MsgCode.FA002.get());
    }

    /**
     * 新增采集任务
     */
    @Operation(summary = "采集任务-新增")
    @PostMapping
    public ActionResult create(@RequestBody @Valid CollectTaskForm taskForm) {
        CollectTaskEntity entity = BeanCopierUtils.copy(taskForm, CollectTaskEntity.class);
        entity.setCreatedBy(userProvider.get().getUserName());
        entity.setCreatedAt(new Date());
        entity.setLastUpdatedBy(userProvider.get().getUserName());
        entity.setLastUpdatedAt(new Date());
        collectTaskService.save(entity);
        return ActionResult.success("新建成功");
    }

    /**
     * 删除采集任务
     */
    @DeleteMapping("/{id}")
    @Parameters({
            @Parameter(name = "id", description = "主键", required = true)
    })
    @Operation(summary = "采集任务删除")
    public ActionResult delete(@PathVariable("id") String id) throws DataException {
        CollectTaskEntity entity = collectTaskService.getById(id);
        if (entity != null) {
            collectTaskService.removeById(id);
            return ActionResult.success(MsgCode.SU003.get());
        }
        return ActionResult.success(MsgCode.FA002.get());
    }

    /**
     * 获取待处理的产品链接列表。
     *
     * @return 表示操作结果的ActionResult对象，包含待处理的产品链接列表。
     */
    @Operation(summary = "获取后台待处理数据清单")
    @GetMapping("/waitGets")
    public ActionResult<CollectTaskModel> waitGets(String clientId, String taskType) {
        CollectTaskEntity entity  = collectTaskService.waitGets(clientId,taskType,"ALLEGRO");
        CollectTaskModel collectLinkModel = BeanCopierUtils.copy(entity, CollectTaskModel.class);
        return ActionResult.success(collectLinkModel);
    }

    /**
     * 接收并处理前端提交的商品信息解析请求。
     * 该方法主要负责解析前端上传的JSON数据，提取其中的商家和产品信息，
     * 并更新相应的数据库记录。
     *
     * @param taskForm 包含待解析的商品JSON数据和相关ID的信息表单。
     * @return 成功保存的提示信息。
     */
    @Operation(summary = "前端提交详情页的json数据")
    @PutMapping("/putTaskJson")
    public ActionResult<String> putLinkJson(@RequestBody CollectTaskForm taskForm) {
        log.debug("前端提交详情页的json数据 taskId:{}", taskForm.getId());
        //满足条件的数据，设置已采集。
        CollectTaskEntity collectTaskEntity = collectTaskService.getById(taskForm.getId());
        if (collectTaskEntity != null) {
            collectTaskEntity.setStatus(AllegroConstant.REQUEST_STATUS_FINISH);
            collectTaskEntity.setTotalNum(taskForm.getTotalNum());
            collectTaskEntity.setTotalSalesNum(taskForm.getTotalSalesNum());
            collectTaskEntity.setFinishTime(new Date());
            collectTaskEntity.setSumCount(taskForm.getSumCount());
            collectTaskService.updateById(collectTaskEntity);

            if("S".equals(collectTaskEntity.getTaskType())){
                allegroSellerService.updateSellerOfferCount(taskForm);
            }
        }
        else {
            log.warn("任务数据在系统中不存在已存在，忽略。ID:" + taskForm.getId());
        }
        return ActionResult.success("成功保存！" + taskForm.getId());
    }
}
