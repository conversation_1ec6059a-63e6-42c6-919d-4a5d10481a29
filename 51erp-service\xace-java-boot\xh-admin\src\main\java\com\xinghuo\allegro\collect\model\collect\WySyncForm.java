package com.xinghuo.allegro.collect.model.collect;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;

import java.math.BigDecimal;

@lombok.Data
public class WySyncForm {

    @Schema(description = "卖家ID")
    @NotNull(message = "卖家ID不能为空")
    private String sellerId;

    @Schema(description = "分组名称")
    @NotNull(message = "分组名称不能为空")
    private String groupName;

    private String groupId;

    @Schema(description = "店铺运费")
    @NotNull(message = "原始店铺运费不能为空")
    private BigDecimal shipfee;

    @Schema(description = "是否多运费")
    private Boolean isMultifee;

    @Schema(description = "租户ID")
    private String tenantId;
}
