[2025-06-15 17:50:00.657] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20000} executed. select count(*) as total
from zz_fruugo_bought_offer
where (f_created_at >= TIMESTAMP '2025-06-15 17:20:00.036')
[2025-06-15 17:50:00.907] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20001} executed. insert into zz_fruugo_alert_log (id, alert_type, alert_module, alert_content, alert_time
	, status, notify_type, ip, created_at, updated_at)
values ('703991272129476229', 3, 'collect_sale', '最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行', TIMESTAMP '2025-06-15 17:50:00.674'
	, 0, 1, '***********', TIMESTAMP '2025-06-15 17:50:00.674', TIMESTAMP '2025-06-15 17:50:00.674')
[2025-06-15 17:50:00.943] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20002} executed. select count(*) as total
from zz_fruugo_seller
where (next_collect_time < TIMESTAMP '2025-06-15 15:50:00.908'
	and country = 'CN')
[2025-06-15 17:50:00.984] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20003} executed. select count(*) as total
from zz_fruugo_data_product
where (process_status = 0)
[2025-06-15 17:50:01.059] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20004} executed. insert into zz_fruugo_alert_log (id, alert_type, alert_module, alert_content, alert_time
	, status, notify_type, ip, created_at, updated_at)
values ('703991272905422469', 5, 'product', '产品数据堆积严重，当前有 30988 条待处理产品数据，超过阈值(10000)，请检查产品处理系统', TIMESTAMP '2025-06-15 17:50:00.986'
	, 0, 1, '***********', TIMESTAMP '2025-06-15 17:50:00.986', TIMESTAMP '2025-06-15 17:50:00.986')
[2025-06-15 17:50:01.077] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20005} executed. select count(*) as total
from zz_fruugo_data_sku
where (process_status = 0)
[2025-06-15 17:55:00.133] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10007, pstmt-20006} executed. select count(*) as total
from zz_fruugo_bought_offer
where (f_created_at >= TIMESTAMP '2025-06-15 17:25:00.009')
[2025-06-15 17:55:00.218] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10007, pstmt-20007} executed. select count(*) as total
from zz_fruugo_seller
where (next_collect_time < TIMESTAMP '2025-06-15 15:55:00.134'
	and country = 'CN')
[2025-06-15 17:55:18.893] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10007, pstmt-20008} executed. select count(*) as total
from zz_fruugo_data_product
where (process_status = 0)
[2025-06-15 17:55:18.943] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10007, pstmt-20009} executed. select count(*) as total
from zz_fruugo_data_sku
where (process_status = 0)
[2025-06-15 18:00:00.060] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20010} executed. select id, stat_date, collect_offer_total, collect_offer_pending, collect_offer_processed
	, collect_offer_new, collect_sale_total, collect_sale_new, seller_total, seller_new
	, product_total, product_new, sku_total, sku_new, f_created_at as createdAt
	, f_last_updated_at as lastUpdatedAt
from zz_fruugo_monitor
where (stat_date = '2025-06-15')
[2025-06-15 18:00:02.530] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20011} executed. SELECT COUNT( * ) AS total FROM zz_fruugo_collect_offer
[2025-06-15 18:00:03.604] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20012} executed. select count(*) as total
from zz_fruugo_collect_offer
where (request_status = 0)
[2025-06-15 18:00:08.491] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20013} executed. select count(*) as total
from zz_fruugo_collect_offer
where (request_status = 2)
[2025-06-15 18:00:23.639] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20014} executed. select count(*) as total
from zz_fruugo_collect_offer
where (f_created_at between TIMESTAMP '2025-06-15 00:00:00.000' and TIMESTAMP '2025-06-15 23:59:59.999')
[2025-06-15 18:00:56.532] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20015} executed. SELECT COUNT( * ) AS total FROM zz_fruugo_collect_sale
[2025-06-15 18:00:56.551] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20016} executed. select count(*) as total
from zz_fruugo_collect_sale
where (f_created_at between TIMESTAMP '2025-06-15 00:00:00.000' and TIMESTAMP '2025-06-15 23:59:59.999')
[2025-06-15 18:00:56.942] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20017} executed. SELECT COUNT( * ) AS total FROM zz_fruugo_seller
[2025-06-15 18:00:56.962] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20018} executed. select count(*) as total
from zz_fruugo_seller
where (f_created_at between TIMESTAMP '2025-06-15 00:00:00.000' and TIMESTAMP '2025-06-15 23:59:59.999')
[2025-06-15 18:01:07.119] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20019} executed. SELECT COUNT( * ) AS total FROM zz_fruugo_product
[2025-06-15 18:01:27.849] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20020} executed. select count(*) as total
from zz_fruugo_product
where (f_created_at between TIMESTAMP '2025-06-15 00:00:00.000' and TIMESTAMP '2025-06-15 23:59:59.999')
[2025-06-15 18:02:42.152] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20021} executed. SELECT COUNT( * ) AS total FROM zz_fruugo_product_sku
[2025-06-15 18:04:15.663] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20022} executed. select count(*) as total
from zz_fruugo_product_sku
where (f_created_at between TIMESTAMP '2025-06-15 00:00:00.000' and TIMESTAMP '2025-06-15 23:59:59.999')
[2025-06-15 18:04:15.687] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20023} executed. select id, stat_date, collect_offer_total, collect_offer_pending, collect_offer_processed
	, collect_offer_new, collect_sale_total, collect_sale_new, seller_total, seller_new
	, product_total, product_new, sku_total, sku_new, f_created_at as createdAt
	, f_last_updated_at as lastUpdatedAt
from zz_fruugo_monitor
where id = '703721994465583173'
[2025-06-15 18:04:15.732] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20024} executed. update zz_fruugo_monitor
set stat_date = TIMESTAMP '2025-06-15 00:00:00.000', collect_offer_total = 581232, collect_offer_pending = 120267, collect_offer_processed = 371647, collect_offer_new = 0, collect_sale_total = 8180610, collect_sale_new = 0, seller_total = 2960, seller_new = 0, product_total = 1032438, product_new = 0, sku_total = 2684538, sku_new = 0, f_created_at = TIMESTAMP '2025-06-15 00:00:00.000', f_last_updated_at = TIMESTAMP '2025-06-15 18:04:15.665'
where id = '703721994465583173'
[2025-06-15 18:04:15.748] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20025} executed. select id, stat_date, collect_offer_total, collect_offer_pending, collect_offer_processed
	, collect_offer_new, collect_sale_total, collect_sale_new, seller_total, seller_new
	, product_total, product_new, sku_total, sku_new, f_created_at as createdAt
	, f_last_updated_at as lastUpdatedAt
from zz_fruugo_monitor
where (stat_date = '2025-06-15')
[2025-06-15 18:04:17.788] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20026} executed. SELECT COUNT( * ) AS total FROM zz_fruugo_collect_offer
[2025-06-15 18:04:17.945] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20027} executed. select count(*) as total
from zz_fruugo_collect_offer
where (request_status = 0)
[2025-06-15 18:04:18.345] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20028} executed. select count(*) as total
from zz_fruugo_collect_offer
where (request_status = 2)
[2025-06-15 18:04:20.211] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20029} executed. select count(*) as total
from zz_fruugo_collect_offer
where (f_created_at between TIMESTAMP '2025-06-15 00:00:00.000' and TIMESTAMP '2025-06-15 23:59:59.999')
[2025-06-15 18:04:45.618] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20030} executed. SELECT COUNT( * ) AS total FROM zz_fruugo_collect_sale
[2025-06-15 18:04:45.632] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20031} executed. select count(*) as total
from zz_fruugo_collect_sale
where (f_created_at between TIMESTAMP '2025-06-15 00:00:00.000' and TIMESTAMP '2025-06-15 23:59:59.999')
[2025-06-15 18:04:45.715] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20032} executed. SELECT COUNT( * ) AS total FROM zz_fruugo_seller
[2025-06-15 18:04:45.734] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20033} executed. select count(*) as total
from zz_fruugo_seller
where (f_created_at between TIMESTAMP '2025-06-15 00:00:00.000' and TIMESTAMP '2025-06-15 23:59:59.999')
[2025-06-15 18:04:54.032] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20034} executed. SELECT COUNT( * ) AS total FROM zz_fruugo_product
[2025-06-15 18:04:57.491] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20035} executed. select count(*) as total
from zz_fruugo_product
where (f_created_at between TIMESTAMP '2025-06-15 00:00:00.000' and TIMESTAMP '2025-06-15 23:59:59.999')
[2025-06-15 18:05:58.000] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20036} executed. SELECT COUNT( * ) AS total FROM zz_fruugo_product_sku
[2025-06-15 18:07:13.068] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20037} executed. select count(*) as total
from zz_fruugo_product_sku
where (f_created_at between TIMESTAMP '2025-06-15 00:00:00.000' and TIMESTAMP '2025-06-15 23:59:59.999')
[2025-06-15 18:07:13.099] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20038} executed. select id, stat_date, collect_offer_total, collect_offer_pending, collect_offer_processed
	, collect_offer_new, collect_sale_total, collect_sale_new, seller_total, seller_new
	, product_total, product_new, sku_total, sku_new, f_created_at as createdAt
	, f_last_updated_at as lastUpdatedAt
from zz_fruugo_monitor
where id = '703721994465583173'
[2025-06-15 18:07:13.125] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20039} executed. update zz_fruugo_monitor
set stat_date = TIMESTAMP '2025-06-15 00:00:00.000', collect_offer_total = 581232, collect_offer_pending = 120267, collect_offer_processed = 371647, collect_offer_new = 0, collect_sale_total = 8180610, collect_sale_new = 0, seller_total = 2960, seller_new = 0, product_total = 1032438, product_new = 0, sku_total = 2684538, sku_new = 0, f_created_at = TIMESTAMP '2025-06-15 00:00:00.000', f_last_updated_at = TIMESTAMP '2025-06-15 18:07:13.069'
where id = '703721994465583173'
[2025-06-15 18:07:13.212] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20040} executed. select count(*) as total
from zz_fruugo_bought_offer
where (f_created_at >= TIMESTAMP '2025-06-15 17:37:13.126')
[2025-06-15 18:07:13.230] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20041} executed. select count(*) as total
from zz_fruugo_seller
where (next_collect_time < TIMESTAMP '2025-06-15 16:07:13.213'
	and country = 'CN')
[2025-06-15 18:07:20.475] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20042} executed. select count(*) as total
from zz_fruugo_data_product
where (process_status = 0)
[2025-06-15 18:07:20.488] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20043} executed. select count(*) as total
from zz_fruugo_data_sku
where (process_status = 0)
[2025-06-15 18:09:52.804] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10005, pstmt-20000} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:10:00.321] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10005, pstmt-20001} executed. select count(*) as total
from zz_fruugo_bought_offer
where (f_created_at >= TIMESTAMP '2025-06-15 17:40:00.019')
[2025-06-15 18:10:00.541] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10005, pstmt-20002} executed. insert into zz_fruugo_alert_log (id, alert_type, alert_module, alert_content, alert_time
	, status, notify_type, ip, created_at, updated_at)
values ('703996303851435717', 3, 'collect_sale', '最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行', TIMESTAMP '2025-06-15 18:10:00.323'
	, 0, 1, '***********', TIMESTAMP '2025-06-15 18:10:00.323', TIMESTAMP '2025-06-15 18:10:00.323')
[2025-06-15 18:10:00.560] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10005, pstmt-20003} executed. select count(*) as total
from zz_fruugo_seller
where (next_collect_time < TIMESTAMP '2025-06-15 16:10:00.543'
	and country = 'CN')
[2025-06-15 18:10:00.584] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10005, pstmt-20004} executed. select count(*) as total
from zz_fruugo_data_product
where (process_status = 0)
[2025-06-15 18:10:00.657] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10005, pstmt-20005} executed. insert into zz_fruugo_alert_log (id, alert_type, alert_module, alert_content, alert_time
	, status, notify_type, ip, created_at, updated_at)
values ('703996304388306629', 5, 'product', '产品数据堆积严重，当前有 30985 条待处理产品数据，超过阈值(10000)，请检查产品处理系统', TIMESTAMP '2025-06-15 18:10:00.585'
	, 0, 1, '***********', TIMESTAMP '2025-06-15 18:10:00.585', TIMESTAMP '2025-06-15 18:10:00.585')
[2025-06-15 18:10:00.670] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10005, pstmt-20006} executed. select count(*) as total
from zz_fruugo_data_sku
where (process_status = 0)
[2025-06-15 18:10:01.916] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10005, pstmt-20007} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:10:11.895] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10005, pstmt-20008} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:10:21.911] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10005, pstmt-20009} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:10:31.895] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10005, pstmt-20010} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:10:41.906] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10005, pstmt-20011} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:10:51.900] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20012} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:11:01.908] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20013} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:11:11.906] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20014} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:11:21.898] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20015} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:11:31.905] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20016} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:11:41.901] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20017} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:11:52.047] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10006, pstmt-20018} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:12:01.895] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10006, pstmt-20019} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:12:10.094] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10006, pstmt-20020} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:12:20.094] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10006, pstmt-20021} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:12:24.560] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10006, pstmt-20022} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:12:30.107] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10006, pstmt-20023} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:12:40.116] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10006, pstmt-20024} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:12:50.122] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10005, pstmt-20025} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:12:59.390] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10005, pstmt-20026} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:13:00.093] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10005, pstmt-20027} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:13:10.129] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10005, pstmt-20028} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:13:20.103] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10005, pstmt-20029} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:13:30.103] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10005, pstmt-20030} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:13:30.134] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10005, pstmt-20031} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A274332011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 99999, status = 1, max_pages_to_crawl = 1, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 16:25:33.000', updated_at = TIMESTAMP '2025-06-15 18:13:30.106'
where id = 4112
[2025-06-15 18:13:40.103] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10005, pstmt-20032} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:13:40.127] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10005, pstmt-20033} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A2528084011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 2528084011, status = 1, max_pages_to_crawl = 400, crawled_pages = 4, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:13:40.104', source_category_path = 'Home & Kitchen(家庭和厨房)/Event & Party Supplies(活动和派对用品)/Children''s Party Supplies(儿童派对用品)/Centerpieces(核心)'
where id = 1
[2025-06-15 18:13:50.105] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10007, pstmt-20034} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:13:50.130] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10007, pstmt-20035} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A274321011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 274321011, status = 1, max_pages_to_crawl = 400, crawled_pages = 9, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:13:50.106', source_category_path = 'Home & Kitchen(家庭和厨房)/Event & Party Supplies(活动和派对用品)/Children''s Party Supplies(儿童派对用品)/Decorations(装饰品)/Balloons(气球)'
where id = 2
[2025-06-15 18:13:59.380] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10007, pstmt-20036} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:13:59.402] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10007, pstmt-20037} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A2489384011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 2489384011, status = 1, max_pages_to_crawl = 400, crawled_pages = 6, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:13:59.380', source_category_path = 'Home & Kitchen(家庭和厨房)/Event & Party Supplies(活动和派对用品)/Children''s Party Supplies(儿童派对用品)/Decorations(装饰品)/Banners(横幅)'
where id = 3
[2025-06-15 18:14:00.099] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10007, pstmt-20038} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:14:00.122] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10007, pstmt-20039} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A10844431011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 10844431011, status = 1, max_pages_to_crawl = 400, crawled_pages = 6, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:14:00.099', source_category_path = 'Home & Kitchen/Event & Party Supplies/Children''s Party Supplies/Decorations/Cake & Cupcake Toppers/Non-Edible Cake Toppers'
where id = 4
[2025-06-15 18:14:10.108] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10007, pstmt-20040} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:14:10.131] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10007, pstmt-20041} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A10844432011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 10844432011, status = 1, max_pages_to_crawl = 400, crawled_pages = 2, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:14:10.109', source_category_path = 'Home & Kitchen/Event & Party Supplies/Children''s Party Supplies/Decorations/Cake & Cupcake Toppers/Non-Edible Cupcake Toppers'
where id = 5
[2025-06-15 18:14:20.094] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10007, pstmt-20042} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:14:20.118] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10007, pstmt-20043} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A723472011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 723472011, status = 1, max_pages_to_crawl = 400, crawled_pages = 5, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:14:20.095', source_category_path = 'Home & Kitchen(家庭和厨房)/Event & Party Supplies(活动和派对用品)/Children''s Party Supplies(儿童派对用品)/Decorations(装饰品)/Streamers(拖缆)'
where id = 6
[2025-06-15 18:14:28.246] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10007, pstmt-20044} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:14:28.270] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10007, pstmt-20045} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A274325011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 274325011, status = 1, max_pages_to_crawl = 400, crawled_pages = 8, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:14:28.248', source_category_path = 'Home & Kitchen(家庭和厨房)/Event & Party Supplies(活动和派对用品)/Children''s Party Supplies(儿童派对用品)/Headwear(头饰)'
where id = 7
[2025-06-15 18:14:30.146] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10007, pstmt-20046} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:14:30.175] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10007, pstmt-20047} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A2528081011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 2528081011, status = 1, max_pages_to_crawl = 400, crawled_pages = 3, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:14:30.148', source_category_path = 'Home & Kitchen(家庭和厨房)/Event & Party Supplies(活动和派对用品)/Children''s Party Supplies(儿童派对用品)/Invitations(邀请)'
where id = 8
[2025-06-15 18:14:40.104] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10007, pstmt-20048} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:14:40.128] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10007, pstmt-20049} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A166042011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 166042011, status = 1, max_pages_to_crawl = 400, crawled_pages = 5, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:14:40.105', source_category_path = 'Home & Kitchen(家庭和厨房)/Event & Party Supplies(活动和派对用品)/Children''s Party Supplies(儿童派对用品)/Noisemakers(噪音制造者)'
where id = 9
[2025-06-15 18:14:50.093] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10006, pstmt-20050} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:14:50.120] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10006, pstmt-20051} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A23569992011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 23569992011, status = 1, max_pages_to_crawl = 400, crawled_pages = 6, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:14:50.094', source_category_path = 'Home & Kitchen(家庭和厨房)/Event & Party Supplies(活动和派对用品)/Children''s Party Supplies(儿童派对用品)/Party Favors(派对恩惠)/Multi-Item Party Favor Packs(多件派对优惠包)'
where id = 10
[2025-06-15 18:15:00.086] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10006, pstmt-20052} executed. select count(*) as total
from zz_fruugo_bought_offer
where (f_created_at >= TIMESTAMP '2025-06-15 17:45:00.006')
[2025-06-15 18:15:00.118] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10006, pstmt-20053} executed. select count(*) as total
from zz_fruugo_seller
where (next_collect_time < TIMESTAMP '2025-06-15 16:15:00.087'
	and country = 'CN')
[2025-06-15 18:15:00.159] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10006, pstmt-20054} executed. select count(*) as total
from zz_fruugo_data_product
where (process_status = 0)
[2025-06-15 18:15:00.165] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10007, pstmt-20055} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:15:00.172] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10006, pstmt-20056} executed. select count(*) as total
from zz_fruugo_data_sku
where (process_status = 0)
[2025-06-15 18:15:00.191] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10007, pstmt-20057} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A274331011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 274331011, status = 1, max_pages_to_crawl = 400, crawled_pages = 4, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:15:00.166', source_category_path = 'Home & Kitchen(家庭和厨房)/Event & Party Supplies(活动和派对用品)/Children''s Party Supplies(儿童派对用品)/Party Tableware(派对餐具)/Cups(杯子)'
where id = 11
[2025-06-15 18:15:10.115] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10007, pstmt-20058} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:15:10.142] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10007, pstmt-20059} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A274332011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 274332011, status = 1, max_pages_to_crawl = 400, crawled_pages = 6, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:15:10.116', source_category_path = 'Home & Kitchen(家庭和厨房)/Event & Party Supplies(活动和派对用品)/Children''s Party Supplies(儿童派对用品)/Party Tableware(派对餐具)/Napkins(餐巾)'
where id = 12
[2025-06-15 18:15:20.103] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10007, pstmt-20060} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:15:20.126] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10007, pstmt-20061} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A274333011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 274333011, status = 1, max_pages_to_crawl = 400, crawled_pages = 8, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:15:20.104', source_category_path = 'Home & Kitchen(家庭和厨房)/Event & Party Supplies(活动和派对用品)/Children''s Party Supplies(儿童派对用品)/Party Tableware(派对餐具)/Plates(板)'
where id = 13
[2025-06-15 18:15:30.126] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10007, pstmt-20062} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:15:30.152] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10007, pstmt-20063} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A274329011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 274329011, status = 1, max_pages_to_crawl = 400, crawled_pages = 2, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:15:30.128', source_category_path = 'Home & Kitchen/Event & Party Supplies/Children''s Party Supplies/Piñatas'
where id = 14
[2025-06-15 18:15:40.091] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10007, pstmt-20064} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:15:40.113] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10007, pstmt-20065} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A2528083011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 2528083011, status = 1, max_pages_to_crawl = 400, crawled_pages = 6, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:15:40.092', source_category_path = 'Home & Kitchen(家庭和厨房)/Event & Party Supplies(活动和派对用品)/Children''s Party Supplies(儿童派对用品)/Tablecovers(桌布)'
where id = 15
[2025-06-15 18:15:50.105] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20066} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:15:50.135] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20067} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A17928788011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 17928788011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:15:50.107', source_category_path = 'Handmade Products(手工产品)/Sports & Outdoors(运动与户外)/Camping & Hiking(露营和徒步旅行)/Water Bottles & Accessories(水瓶和配件)'
where id = 16
[2025-06-15 18:16:00.103] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20068} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:16:00.152] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20069} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A14351409011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 14351409011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:16:00.106', source_category_path = 'Handmade Products(手工产品)/Sports & Outdoors(运动与户外)/Cycling(骑自行车)/Cycling Accessories(自行车配件)'
where id = 17
[2025-06-15 18:16:10.100] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20070} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:16:10.124] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20071} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A17928772011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 17928772011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:16:10.101', source_category_path = 'Handmade Products(手工产品)/Sports & Outdoors(运动与户外)/Hunting & Shooting(狩猎和射击)/Archery(射箭)/Arrows & Accessories(箭头和附件)'
where id = 18
[2025-06-15 18:16:20.100] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20072} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:16:20.127] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20073} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A17928778011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 17928778011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:16:20.101', source_category_path = 'Handmade Products(手工产品)/Sports & Outdoors(运动与户外)/Hunting & Shooting(狩猎和射击)/Gun Accessories(枪支附件)/Gun & Ammunition Storage(枪支弹药储存)'
where id = 19
[2025-06-15 18:16:30.089] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20074} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:16:30.122] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20075} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A17928788011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 17928788011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:16:30.090', source_category_path = 'Handmade Products(手工产品)/Sports & Outdoors(运动与户外)/Sports & Fitness(运动与健身)/Accessories(附件)/Water Bottles & Accessories(水瓶和配件)'
where id = 20
[2025-06-15 18:16:40.113] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20076} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:16:40.141] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20077} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A17928795011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 17928795011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:16:40.115', source_category_path = 'Handmade Products(手工产品)/Sports & Outdoors(运动与户外)/Sports & Fitness(运动与健身)/Golf(高尔夫)/Balls & Accessories(球和配件)'
where id = 21
[2025-06-15 18:16:50.106] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20078} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:16:50.138] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20079} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A15970507011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 15970507011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:16:50.107', source_category_path = 'Handmade Products(手工产品)/Beauty & Grooming(美容与美容)/Tools & Accessories(工具和附件)/Storage & Organization(存储和组织)/Makeup Organizers(化妆组织者)'
where id = 22
[2025-06-15 18:17:00.106] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20080} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:17:00.144] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20081} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3732451%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3732451, status = 1, max_pages_to_crawl = 400, crawled_pages = 1, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:17:00.107', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Air Mattresses & Accessories(空气床垫和配件)/Air Mattresses(空气床垫)'
where id = 23
[2025-06-15 18:17:10.103] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20082} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:17:10.128] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20083} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3732481%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3732481, status = 1, max_pages_to_crawl = 400, crawled_pages = 3, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:17:10.104', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Air Mattresses & Accessories(空气床垫和配件)/Pumps(泵)'
where id = 24
[2025-06-15 18:17:20.093] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20084} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:17:20.119] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20085} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3732201%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3732201, status = 1, max_pages_to_crawl = 400, crawled_pages = 3, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:17:20.095', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Bed Canopies & Drapes(床篷和窗帘)'
where id = 25
[2025-06-15 18:17:30.112] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20086} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:17:30.163] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20087} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A10671043011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 10671043011, status = 1, max_pages_to_crawl = 400, crawled_pages = 2, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:17:30.128', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Bed Pillows & Positioners(枕头和定位器)/Bed Pillows(枕头)'
where id = 26
[2025-06-15 18:17:40.098] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20088} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:17:40.135] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20089} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3731991%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3731991, status = 1, max_pages_to_crawl = 400, crawled_pages = 2, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:17:40.099', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Bed Pillows & Positioners(枕头和定位器)/Body Pillows(身体枕头)'
where id = 27
[2025-06-15 18:17:50.098] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20090} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:17:50.122] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20091} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3732061%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3732061, status = 1, max_pages_to_crawl = 400, crawled_pages = 1, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:17:50.099', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Bed Pillows & Positioners(枕头和定位器)/Leg Positioner Pillows(腿部定位器枕头)'
where id = 28
[2025-06-15 18:18:00.100] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20092} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:18:00.135] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20093} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3732051%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3732051, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:18:00.101', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Bed Pillows & Positioners(枕头和定位器)/Lumbar Pillows(腰枕)'
where id = 29
[2025-06-15 18:18:10.105] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20094} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:18:10.137] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20095} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3732111%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3732111, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:18:10.106', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Bed Pillows & Positioners(枕头和定位器)/Neck & Cervical Pillows(颈枕)'
where id = 30
[2025-06-15 18:18:20.104] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20096} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:18:20.127] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20097} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3732021%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3732021, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:18:20.105', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Bed Pillows & Positioners(枕头和定位器)/Reading & Bed Rest Pillows(阅读和床上休息枕头)'
where id = 31
[2025-06-15 18:18:30.098] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20098} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:18:30.120] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20099} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3732121%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3732121, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:18:30.099', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Bed Pillows & Positioners(枕头和定位器)/Travel Pillows(旅行枕头)'
where id = 32
[2025-06-15 18:18:40.125] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20100} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:18:40.150] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20101} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3775471%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3775471, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:18:40.126', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Bed Pillows & Positioners(枕头和定位器)/Wedges & Body Positioners(楔子和身体定位器)'
where id = 33
[2025-06-15 18:18:50.103] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20102} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:18:50.128] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20103} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A16175638011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 16175638011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:18:50.104', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Bed Runners & Scarves(床跑者和围巾)'
where id = 34
[2025-06-15 18:19:00.103] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20104} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:19:00.136] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20105} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3732211%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3732211, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:19:00.104', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Bed Skirts(床裙)'
where id = 35
[2025-06-15 18:19:10.091] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20106} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:19:10.122] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20107} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3732271%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3732271, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:19:10.091', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Bedding Accessories(床上用品配件)/Bed Risers(床板)'
where id = 36
[2025-06-15 18:19:20.093] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20108} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:19:20.117] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20109} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3732311%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3732311, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:19:20.093', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Bedding Accessories(床上用品配件)/Bed Skirt Pins(床裙别针)'
where id = 37
[2025-06-15 18:19:30.110] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20110} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:19:30.142] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20111} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A21579650011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 21579650011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:19:30.111', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Air Fresheners(空气清新剂)/Charcoal Bags(炭袋)'
where id = 38
[2025-06-15 18:19:40.109] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20112} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:19:40.133] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20113} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A17874218011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 17874218011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:19:40.111', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Bedding Accessories(床上用品配件)/Comforter Clips(被子夹)'
where id = 39
[2025-06-15 18:19:50.124] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20114} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:19:50.149] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20115} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A14087331%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 14087331, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:19:50.125', source_category_path = 'Home & Kitchen/Home Décor Products/Artificial Plants & Flowers/Artificial Flowers'
where id = 40
[2025-06-15 18:20:00.084] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20116} executed. select count(*) as total
from zz_fruugo_bought_offer
where (f_created_at >= TIMESTAMP '2025-06-15 17:50:00.002')
[2025-06-15 18:20:00.097] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20117} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:20:00.105] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20118} executed. select count(*) as total
from zz_fruugo_seller
where (next_collect_time < TIMESTAMP '2025-06-15 16:20:00.085'
	and country = 'CN')
[2025-06-15 18:20:00.144] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20119} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3732241%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3732241, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:20:00.098', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Bedding Accessories(床上用品配件)/Sheet Fasteners(薄板紧固件)'
where id = 41
[2025-06-15 18:20:00.232] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20120} executed. select count(*) as total
from zz_fruugo_data_product
where (process_status = 0)
[2025-06-15 18:20:00.264] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20121} executed. select count(*) as total
from zz_fruugo_data_sku
where (process_status = 0)
[2025-06-15 18:20:10.100] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20122} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:20:10.124] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20123} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A14087391%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 14087391, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:20:10.101', source_category_path = 'Home & Kitchen/Home Décor Products/Artificial Plants & Flowers/Artificial Fruit'
where id = 42
[2025-06-15 18:20:20.110] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20124} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:20:20.140] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20125} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3731671%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3731671, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:20:20.112', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bath Linen Sets(沐浴亚麻套装)'
where id = 43
[2025-06-15 18:20:30.116] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20126} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:20:30.153] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20127} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11433413011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11433413011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:20:30.117', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Artwork(艺术品)/Drawings(图纸)'
where id = 44
[2025-06-15 18:20:40.107] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20128} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:20:40.151] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20129} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A15356221%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 15356221, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:20:40.108', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Air Fresheners(空气清新剂)/Spray(喷雾)'
where id = 45
[2025-06-15 18:20:50.113] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20130} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:20:50.152] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20131} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A17833726011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 17833726011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:20:50.114', source_category_path = 'Home & Kitchen/Home Décor Products/Artificial Plants & Flowers/Artificial Plants & Greenery'
where id = 46
[2025-06-15 18:21:00.104] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20132} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:21:00.148] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20133} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A17874217011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 17874217011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:21:00.105', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Bedding Accessories(床上用品配件)/Twin Bed Bridges(双床桥)'
where id = 47
[2025-06-15 18:21:10.121] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20134} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:21:10.145] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20135} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A1063242%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 1063242, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:21:10.121', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bath Rugs(浴毯)'
where id = 48
[2025-06-15 18:21:20.101] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20136} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:21:20.124] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20137} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11433414011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11433414011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:21:20.102', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Artwork(艺术品)/Mixed Media(混合介质)'
where id = 49
[2025-06-15 18:21:30.102] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20138} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:21:30.128] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20139} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11433415011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11433415011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:21:30.102', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Artwork(艺术品)/Paintings(绘画)'
where id = 50
[2025-06-15 18:21:40.313] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20140} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:21:40.341] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20141} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A85975011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 85975011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:21:40.314', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Brushes(刷子)/Toilet Brushes & Holders(马桶刷和支架)'
where id = 51
[2025-06-15 18:21:50.102] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20142} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:21:50.125] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20143} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A14087361%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 14087361, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:21:50.103', source_category_path = 'Home & Kitchen/Home Décor Products/Artificial Plants & Flowers/Artificial Shrubs & Topiaries/Artificial Shrubs'
where id = 52
[2025-06-15 18:22:00.143] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20144} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:22:00.189] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20145} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A10671068011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 10671068011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:22:00.144', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Bedding Sets & Collections(床上用品套装和系列)/Daybed Sets(日床套装)'
where id = 53
[2025-06-15 18:22:10.099] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20146} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:22:10.131] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20147} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3731911%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3731911, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:22:10.100', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Bathroom Accessory Sets(浴室配件套装)'
where id = 54
[2025-06-15 18:22:20.099] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20148} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:22:20.127] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20149} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11433416011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11433416011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:22:20.100', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Artwork(艺术品)/Photographs(照片)'
where id = 55
[2025-06-15 18:22:30.149] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20150} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:22:30.176] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20151} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A14087371%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 14087371, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:22:30.151', source_category_path = 'Home & Kitchen/Home Décor Products/Artificial Plants & Flowers/Artificial Shrubs & Topiaries/Artificial Topiaries'
where id = 56
[2025-06-15 18:22:40.125] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20152} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:22:40.156] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20153} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11433417011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11433417011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:22:40.127', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Artwork(艺术品)/Posters(海报)'
where id = 57
[2025-06-15 18:22:50.109] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20154} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:22:50.142] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20155} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A2245498011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 2245498011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:22:50.110', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Dusting(除尘)/Feather Dusters(羽毛掸子)'
where id = 58
[2025-06-15 18:23:00.098] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20156} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:23:00.128] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20157} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A14087381%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 14087381, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:23:00.100', source_category_path = 'Home & Kitchen/Home Décor Products/Artificial Plants & Flowers/Artificial Trees'
where id = 59
[2025-06-15 18:23:10.103] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20158} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:23:10.136] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20159} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3733641%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3733641, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:23:10.105', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Accent Furniture(口音家具)/End Tables(结束表)'
where id = 60
[2025-06-15 18:23:20.099] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20160} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:23:20.122] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20161} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A10671046011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 10671046011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:23:20.099', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Bedspreads, Coverlets & Sets(床罩、床罩和套装)/Bedspread & Coverlet Sets(床罩和床罩套装)'
where id = 61
[2025-06-15 18:23:30.102] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20162} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:23:30.126] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20163} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3785121%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3785121, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:23:30.102', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Bathroom Mirrors(浴室镜子)/Makeup Mirrors(化妆镜)'
where id = 62
[2025-06-15 18:23:40.103] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20164} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:23:40.131] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20165} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11433418011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11433418011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:23:40.104', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Artwork(艺术品)/Prints(打印)'
where id = 63
[2025-06-15 18:23:50.097] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20166} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:23:50.122] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20167} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A14087401%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 14087401, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:23:50.099', source_category_path = 'Home & Kitchen/Home Décor Products/Artificial Plants & Flowers/Artificial Vegetables'
where id = 64
[2025-06-15 18:24:00.108] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20168} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:24:00.147] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20169} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3733841%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3733841, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:24:00.109', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Accent Furniture(口音家具)/Glass Display Cabinets(玻璃展示柜)'
where id = 65
[2025-06-15 18:24:10.110] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20170} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:24:10.145] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20171} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3732161%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3732161, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:24:10.112', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Bedspreads, Coverlets & Sets(床罩、床罩和套装)/Bedspreads & Coverlets(床罩和床罩)'
where id = 66
[2025-06-15 18:24:20.105] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20172} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:24:20.131] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20173} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3785131%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3785131, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:24:20.106', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Bathroom Mirrors(浴室镜子)/Shower Mirrors(淋浴镜)'
where id = 67
[2025-06-15 18:24:24.909] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20174} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:24:24.934] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20175} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11433419011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11433419011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:24:24.911', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Artwork(艺术品)/Wall Stickers(墙贴)'
where id = 68
[2025-06-15 18:24:34.913] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20176} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:24:34.940] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20177} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A15356141%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 15356141, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:24:34.914', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Household Cleaners(家用清洁工)/All-Purpose Cleaners(通用清洁剂)'
where id = 69
[2025-06-15 18:24:44.929] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20178} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:24:44.952] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20179} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A16183931011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 16183931011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:24:44.930', source_category_path = 'Home & Kitchen/Home Décor Products/Artificial Plants & Flowers/Decorative Swags'
where id = 70
[2025-06-15 18:24:54.915] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20180} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:24:54.939] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20181} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3733741%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3733741, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:24:54.915', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Accent Furniture(口音家具)/Ladder Shelves(梯子搁板)'
where id = 71
[2025-06-15 18:25:00.177] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20182} executed. select count(*) as total
from zz_fruugo_bought_offer
where (f_created_at >= TIMESTAMP '2025-06-15 17:55:00.003')
[2025-06-15 18:25:00.286] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20183} executed. select count(*) as total
from zz_fruugo_seller
where (next_collect_time < TIMESTAMP '2025-06-15 16:25:00.178'
	and country = 'CN')
[2025-06-15 18:25:00.316] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20184} executed. select count(*) as total
from zz_fruugo_data_product
where (process_status = 0)
[2025-06-15 18:25:00.329] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20185} executed. select count(*) as total
from zz_fruugo_data_sku
where (process_status = 0)
[2025-06-15 18:25:04.910] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20186} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:25:04.935] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20187} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A17738953011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 17738953011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:25:04.910', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Bathroom Mirrors(浴室镜子)/Wall-Mounted Vanity Mirrors(壁挂式化妆镜)'
where id = 72
[2025-06-15 18:25:14.929] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20188} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:25:14.960] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20189} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3734221%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3734221, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:25:14.929', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Accent Furniture(口音家具)/Nesting Tables(嵌套表)'
where id = 73
[2025-06-15 18:25:24.930] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20190} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:25:24.960] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20191} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3732181%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3732181, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:25:24.931', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Blankets & Throws(毯子和投掷)/Bed Blankets(床毯)'
where id = 74
[2025-06-15 18:25:34.912] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20192} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:25:34.934] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20193} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A15524334011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 15524334011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:25:34.913', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Household Cleaners(家用清洁工)/Bathroom Cleaners(浴室清洁剂)/Lime & Rust Removers(石灰除锈剂)'
where id = 75
[2025-06-15 18:25:44.932] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20194} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:25:44.956] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20195} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3254639011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3254639011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:25:44.932', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Accent Furniture(口音家具)/Ottomans(奥斯曼人)'
where id = 76
[2025-06-15 18:25:54.924] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20196} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:25:54.951] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20197} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A85961011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 85961011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:25:54.925', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Bathtub Accessories(浴缸配件)/Bathtub Appliques(浴缸贴花)'
where id = 77
[2025-06-15 18:26:04.935] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20198} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:26:04.959] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20199} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11433428011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11433428011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:26:04.935', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bath Linen(浴巾)/Bath Mats(浴垫)'
where id = 78
[2025-06-15 18:26:14.921] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20200} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:26:14.942] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20201} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3734381%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3734381, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:26:14.921', source_category_path = 'Home & Kitchen/Home Décor Products/Candles & Holders/Accessories/Accessory Sets'
where id = 79
[2025-06-15 18:26:24.926] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20202} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:26:24.952] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20203} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A16353441%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 16353441, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:26:24.926', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Accent Furniture(口音家具)/Quilt Stands(被子架)'
where id = 80
[2025-06-15 18:26:34.931] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20204} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:26:34.952] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20205} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A85963011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 85963011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:26:34.931', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Bathtub Accessories(浴缸配件)/Bathtub Mats(浴缸垫)'
where id = 81
[2025-06-15 18:26:44.912] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20206} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:26:44.938] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20207} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11433422011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11433422011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:26:44.912', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bath Linen(浴巾)/Bath Towels(浴巾)'
where id = 82
[2025-06-15 18:26:54.911] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20208} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:26:54.934] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20209} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A19294221011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 19294221011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:26:54.912', source_category_path = 'Home & Kitchen/Home Décor Products/Candles & Holders/Accessories/Candle Shades'
where id = 83
[2025-06-15 18:27:04.917] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20210} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:27:04.942] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20211} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3734261%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3734261, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:27:04.917', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Accent Furniture(口音家具)/Room Dividers(房间隔断)'
where id = 84
[2025-06-15 18:27:14.913] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20212} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:27:14.936] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20213} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A15524335011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 15524335011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:27:14.913', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Household Cleaners(家用清洁工)/Bathroom Cleaners(浴室清洁剂)/Mold & Mildew Removers(霉菌去除剂)'
where id = 85
[2025-06-15 18:27:24.928] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20214} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:27:24.951] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20215} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A85966011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 85966011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:27:24.928', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Bathtub Accessories(浴缸配件)/Bathtub Trays(浴缸托盘)'
where id = 86
[2025-06-15 18:27:34.922] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20216} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:27:34.947] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20217} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11433426011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11433426011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:27:34.922', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bath Linen(浴巾)/Hand Towels(手巾)'
where id = 87
[2025-06-15 18:27:44.910] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20218} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:27:44.942] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20219} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A19294222011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 19294222011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:27:44.911', source_category_path = 'Home & Kitchen/Home Décor Products/Candles & Holders/Accessories/Candle Sleeves'
where id = 88
[2025-06-15 18:27:54.927] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20220} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:27:54.951] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20221} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3733651%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3733651, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:27:54.927', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Accent Furniture(口音家具)/Sofa & Console Tables(沙发和控制台桌)'
where id = 89
[2025-06-15 18:28:04.924] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20222} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:28:04.946] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20223} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A15524333011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 15524333011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:28:04.925', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Household Cleaners(家用清洁工)/Bathroom Cleaners(浴室清洁剂)/Multipurpose Bathroom Cleaners(多功能浴室清洁剂)'
where id = 90
[2025-06-15 18:28:14.913] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20224} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:28:14.936] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20225} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A344741011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 344741011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:28:14.914', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Bathtub Accessories(浴缸配件)/Drain Stoppers(排水限位器)'
where id = 91
[2025-06-15 18:28:24.908] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20226} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:28:24.931] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20227} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11433427011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11433427011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:28:24.909', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bath Linen(浴巾)/Washcloths(毛巾)'
where id = 92
[2025-06-15 18:28:34.939] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20228} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:28:34.960] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20229} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A14058581%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 14058581, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:28:34.939', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Blankets & Throws(毯子和投掷)/Throws(投掷)'
where id = 93
[2025-06-15 18:28:44.910] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20230} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:28:44.934] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20231} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3734341%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3734341, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:28:44.911', source_category_path = 'Home & Kitchen/Home Décor Products/Candles & Holders/Accessories/Candlesnuffers'
where id = 94
[2025-06-15 18:28:54.932] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20232} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:28:54.961] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20233} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A681151011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 681151011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:28:54.932', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Accent Furniture(口音家具)/Storage Cabinets(储物柜)'
where id = 95
[2025-06-15 18:29:04.922] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20234} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:29:04.946] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20235} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A15524336011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 15524336011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:29:04.922', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Household Cleaners(家用清洁工)/Bathroom Cleaners(浴室清洁剂)/Soap Scum Removers(肥皂浮渣清除剂)'
where id = 96
[2025-06-15 18:29:14.907] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20236} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:29:14.927] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20237} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A672238011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 672238011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:29:14.907', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Bathtub Accessories(浴缸配件)/Splash Guards(防溅板)'
where id = 97
[2025-06-15 18:29:24.924] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20238} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:29:24.944] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20239} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A17874220011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 17874220011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:29:24.925', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Blankets & Throws(毯子和投掷)/Wearable Blankets(可穿戴毛毯)'
where id = 98
[2025-06-15 18:29:34.916] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20240} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:29:34.938] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20241} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3734351%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3734351, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:29:34.916', source_category_path = 'Home & Kitchen/Home Décor Products/Candles & Holders/Accessories/Replacement Wicks & Trimmers'
where id = 99
[2025-06-15 18:29:44.914] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20242} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:29:44.941] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20243} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A15524337011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 15524337011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:29:44.916', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Household Cleaners(家用清洁工)/Bathroom Cleaners(浴室清洁剂)/Toilet Cleaners(厕所清洁剂)'
where id = 100
[2025-06-15 18:29:54.985] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20244} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:29:55.007] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20245} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A6810213011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 6810213011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:29:54.985', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Fixed Showerheads(固定喷头)'
where id = 101
[2025-06-15 18:30:00.136] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20246} executed. select count(*) as total
from zz_fruugo_bought_offer
where (f_created_at >= TIMESTAMP '2025-06-15 18:00:00.004')
[2025-06-15 18:30:00.196] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20247} executed. select count(*) as total
from zz_fruugo_seller
where (next_collect_time < TIMESTAMP '2025-06-15 16:30:00.137'
	and country = 'CN')
[2025-06-15 18:30:03.209] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20248} executed. select count(*) as total
from zz_fruugo_data_product
where (process_status = 0)
[2025-06-15 18:30:03.266] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20249} executed. select count(*) as total
from zz_fruugo_data_sku
where (process_status = 0)
[2025-06-15 18:30:03.285] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20250} executed. select id, stat_date, collect_offer_total, collect_offer_pending, collect_offer_processed
	, collect_offer_new, collect_sale_total, collect_sale_new, seller_total, seller_new
	, product_total, product_new, sku_total, sku_new, f_created_at as createdAt
	, f_last_updated_at as lastUpdatedAt
from zz_fruugo_monitor
where (stat_date = '2025-06-15')
[2025-06-15 18:30:03.653] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20251} executed. SELECT COUNT( * ) AS total FROM zz_fruugo_collect_offer
[2025-06-15 18:30:03.805] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20252} executed. select count(*) as total
from zz_fruugo_collect_offer
where (request_status = 0)
[2025-06-15 18:30:04.432] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20253} executed. select count(*) as total
from zz_fruugo_collect_offer
where (request_status = 2)
[2025-06-15 18:30:04.927] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20255} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:30:04.954] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20256} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11433431011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11433431011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:30:04.928', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Bathroom Cups(浴室杯子)'
where id = 102
[2025-06-15 18:30:05.796] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20254} executed. select count(*) as total
from zz_fruugo_collect_offer
where (f_created_at between TIMESTAMP '2025-06-15 00:00:00.000' and TIMESTAMP '2025-06-15 23:59:59.999')
[2025-06-15 18:30:15.008] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20258} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:30:15.035] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20259} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A17874221011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 17874221011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:30:15.009', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Blankets & Throws(毯子和投掷)/Weighted Blankets(加重毛毯)'
where id = 103
[2025-06-15 18:30:24.912] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20260} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:30:24.941] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20261} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3734371%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3734371, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:30:24.913', source_category_path = 'Home & Kitchen/Home Décor Products/Candles & Holders/Accessories/Travel Tins'
where id = 104
[2025-06-15 18:30:28.842] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20257} executed. SELECT COUNT( * ) AS total FROM zz_fruugo_collect_sale
[2025-06-15 18:30:28.858] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20262} executed. select count(*) as total
from zz_fruugo_collect_sale
where (f_created_at between TIMESTAMP '2025-06-15 00:00:00.000' and TIMESTAMP '2025-06-15 23:59:59.999')
[2025-06-15 18:30:28.917] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20263} executed. SELECT COUNT( * ) AS total FROM zz_fruugo_seller
[2025-06-15 18:30:28.941] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20264} executed. select count(*) as total
from zz_fruugo_seller
where (f_created_at between TIMESTAMP '2025-06-15 00:00:00.000' and TIMESTAMP '2025-06-15 23:59:59.999')
[2025-06-15 18:30:34.872] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20265} executed. SELECT COUNT( * ) AS total FROM zz_fruugo_product
[2025-06-15 18:30:34.920] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20267} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:30:34.951] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20268} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A684877011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 684877011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:30:34.921', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Accent Furniture(口音家具)/Storage Trunks & Chests(储物箱和箱子)/Storage Chests(储物箱)'
where id = 105
[2025-06-15 18:30:38.178] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20266} executed. select count(*) as total
from zz_fruugo_product
where (f_created_at between TIMESTAMP '2025-06-15 00:00:00.000' and TIMESTAMP '2025-06-15 23:59:59.999')
[2025-06-15 18:30:44.918] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20270} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:30:44.946] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20271} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A344739011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 344739011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:30:44.918', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Hand Dryers(干手器)'
where id = 106
[2025-06-15 18:30:54.915] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20272} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:30:54.936] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20273} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11433430011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11433430011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:30:54.915', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Bathroom Garbage Cans(浴室垃圾桶)'
where id = 107
[2025-06-15 18:31:04.913] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20274} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:31:04.936] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20275} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A14163030011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 14163030011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:31:04.913', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Household Cleaners(家用清洁工)/Carpet Cleaners(地毯清洁剂)/Carpet Machine Detergents(地毯机械洗涤剂)'
where id = 108
[2025-06-15 18:31:14.911] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20276} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:31:14.932] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20277} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3734271%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3734271, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:31:14.911', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Accent Furniture(口音家具)/Storage Trunks & Chests(储物箱和箱子)/Storage Trunks(储物行李箱)'
where id = 109
[2025-06-15 18:31:24.929] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20278} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:31:25.019] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20279} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A6810212011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 6810212011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:31:24.929', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Handheld Showers(手持淋浴)'
where id = 110
[2025-06-15 18:31:34.919] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20280} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:31:34.941] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20281} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11433434011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11433434011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:31:34.920', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Shower Curtains(浴帘)'
where id = 111
[2025-06-15 18:31:44.909] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20282} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:31:44.930] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20283} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A14053321%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 14053321, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:31:44.909', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Comforters & Sets(被子和套装)/Comforter Sets(被子套装)'
where id = 112
[2025-06-15 18:31:54.527] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20269} executed. SELECT COUNT( * ) AS total FROM zz_fruugo_product_sku
[2025-06-15 18:31:54.925] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20285} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:31:54.973] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20286} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3734681%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3734681, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:31:54.926', source_category_path = 'Home & Kitchen/Home Décor Products/Candles & Holders/Candleholders/Bowl Candleholders'
where id = 113
[2025-06-15 18:32:04.917] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20287} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:32:04.942] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20288} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A14163029011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 14163029011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:32:04.917', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Household Cleaners(家用清洁工)/Carpet Cleaners(地毯清洁剂)/Carpet Spot Cleaning Sprays(地毯斑点清洁喷雾剂)'
where id = 114
[2025-06-15 18:32:14.926] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20289} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:32:14.951] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20290} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11433435011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11433435011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:32:14.927', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Towel Racks(毛巾架)'
where id = 115
[2025-06-15 18:32:24.923] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20291} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:32:24.954] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20292} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A1199128%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 1199128, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:32:24.924', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Comforters & Sets(被子和套装)/Comforters(被子)'
where id = 116
[2025-06-15 18:32:34.922] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20293} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:32:34.945] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20294} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3734631%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3734631, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:32:34.923', source_category_path = 'Home & Kitchen/Home Décor Products/Candles & Holders/Candleholders/Candelabras'
where id = 117
[2025-06-15 18:32:44.906] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20295} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:32:44.938] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20296} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A13749741%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 13749741, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:32:44.907', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Holders & Dispensers(支架和分配器)/Bathroom Trays(浴室托盘)'
where id = 118
[2025-06-15 18:32:54.919] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20297} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:32:54.942] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20298} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3743601%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3743601, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:32:54.919', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Household Cleaners(家用清洁工)/Carpet Cleaners(地毯清洁剂)/Carpet Stain Precleaners(地毯污渍预清洁)'
where id = 119
[2025-06-15 18:33:04.003] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20284} executed. select count(*) as total
from zz_fruugo_product_sku
where (f_created_at between TIMESTAMP '2025-06-15 00:00:00.000' and TIMESTAMP '2025-06-15 23:59:59.999')
[2025-06-15 18:33:04.022] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20299} executed. select id, stat_date, collect_offer_total, collect_offer_pending, collect_offer_processed
	, collect_offer_new, collect_sale_total, collect_sale_new, seller_total, seller_new
	, product_total, product_new, sku_total, sku_new, f_created_at as createdAt
	, f_last_updated_at as lastUpdatedAt
from zz_fruugo_monitor
where id = '703721994465583173'
[2025-06-15 18:33:04.047] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20300} executed. update zz_fruugo_monitor
set stat_date = TIMESTAMP '2025-06-15 00:00:00.000', collect_offer_total = 581232, collect_offer_pending = 120267, collect_offer_processed = 371647, collect_offer_new = 0, collect_sale_total = 8180610, collect_sale_new = 0, seller_total = 2960, seller_new = 0, product_total = 1032438, product_new = 0, sku_total = 2684538, sku_new = 0, f_created_at = TIMESTAMP '2025-06-15 00:00:00.000', f_last_updated_at = TIMESTAMP '2025-06-15 18:33:04.003'
where id = '703721994465583173'
[2025-06-15 18:33:04.058] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20301} executed. select id, stat_date, collect_offer_total, collect_offer_pending, collect_offer_processed
	, collect_offer_new, collect_sale_total, collect_sale_new, seller_total, seller_new
	, product_total, product_new, sku_total, sku_new, f_created_at as createdAt
	, f_last_updated_at as lastUpdatedAt
from zz_fruugo_monitor
where (stat_date = '2025-06-15')
[2025-06-15 18:33:04.930] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20303} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:33:04.971] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20304} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3734621%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3734621, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:33:04.930', source_category_path = 'Home & Kitchen/Home Décor Products/Candles & Holders/Candleholders/Candle Chandeliers'
where id = 120
[2025-06-15 18:33:06.572] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20302} executed. SELECT COUNT( * ) AS total FROM zz_fruugo_collect_offer
[2025-06-15 18:33:06.745] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20305} executed. select count(*) as total
from zz_fruugo_collect_offer
where (request_status = 0)
[2025-06-15 18:33:07.309] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20306} executed. select count(*) as total
from zz_fruugo_collect_offer
where (request_status = 2)
[2025-06-15 18:33:09.763] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20307} executed. select count(*) as total
from zz_fruugo_collect_offer
where (f_created_at between TIMESTAMP '2025-06-15 00:00:00.000' and TIMESTAMP '2025-06-15 23:59:59.999')
[2025-06-15 18:33:14.915] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20309} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:33:14.982] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20310} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3743581%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3743581, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:33:14.916', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Household Cleaners(家用清洁工)/Carpet Deodorizers(地毯除臭剂)'
where id = 121
[2025-06-15 18:33:24.922] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20311} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:33:24.949] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20312} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3785121%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3785121, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:33:24.923', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Bathroom Furniture(浴室家具)/Bathroom Mirrors(浴室镜子)/Makeup Mirrors(化妆镜)'
where id = 122
[2025-06-15 18:33:34.911] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20313} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:33:34.935] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20314} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A13749751%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 13749751, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:33:34.911', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Holders & Dispensers(支架和分配器)/Canisters(罐)'
where id = 123
[2025-06-15 18:33:38.335] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20308} executed. SELECT COUNT( * ) AS total FROM zz_fruugo_collect_sale
[2025-06-15 18:33:38.361] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20315} executed. select count(*) as total
from zz_fruugo_collect_sale
where (f_created_at between TIMESTAMP '2025-06-15 00:00:00.000' and TIMESTAMP '2025-06-15 23:59:59.999')
[2025-06-15 18:33:38.574] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20316} executed. SELECT COUNT( * ) AS total FROM zz_fruugo_seller
[2025-06-15 18:33:38.596] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20317} executed. select count(*) as total
from zz_fruugo_seller
where (f_created_at between TIMESTAMP '2025-06-15 00:00:00.000' and TIMESTAMP '2025-06-15 23:59:59.999')
[2025-06-15 18:33:44.915] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20319} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:33:44.984] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20320} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11433432011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11433432011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:33:44.915', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Fixtures(浴室固定装置)/Medicine Cabinets(药柜)'
where id = 124
[2025-06-15 18:33:49.287] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20318} executed. SELECT COUNT( * ) AS total FROM zz_fruugo_product
[2025-06-15 18:33:54.927] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20322} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:33:54.966] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20323} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A10671060011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 10671060011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:33:54.928', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Decorative Pillows, Inserts & Covers(装饰枕头、插件和盖子)/Floor Pillows & Cushions(地板枕头和靠垫)'
where id = 125
[2025-06-15 18:33:55.350] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20321} executed. select count(*) as total
from zz_fruugo_product
where (f_created_at between TIMESTAMP '2025-06-15 00:00:00.000' and TIMESTAMP '2025-06-15 23:59:59.999')
[2025-06-15 18:34:04.922] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20325} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:34:04.951] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20326} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3734711%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3734711, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:34:04.922', source_category_path = 'Home & Kitchen/Home Décor Products/Candles & Holders/Candleholders/Candle Lamps'
where id = 126
[2025-06-15 18:34:14.924] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20327} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:34:14.964] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20328} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3785131%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3785131, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:34:14.928', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Bathroom Furniture(浴室家具)/Bathroom Mirrors(浴室镜子)/Shower Mirrors(淋浴镜)'
where id = 127
[2025-06-15 18:34:24.915] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20329} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:34:24.938] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20330} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A13749771%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 13749771, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:34:24.915', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Holders & Dispensers(支架和分配器)/Cup Holders(杯架)'
where id = 128
[2025-06-15 18:34:34.916] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20331} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:34:34.946] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20332} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11433433011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11433433011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:34:34.918', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Fixtures(浴室固定装置)/Shelves(货架)'
where id = 129
[2025-06-15 18:34:44.922] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20333} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:34:44.952] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20334} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3732331%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3732331, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:34:44.922', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Decorative Pillows, Inserts & Covers(装饰枕头、插件和盖子)/Pillow Inserts(枕头插件)'
where id = 130
[2025-06-15 18:34:54.919] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20335} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:34:54.942] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20336} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3734691%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3734691, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:34:54.920', source_category_path = 'Home & Kitchen/Home Décor Products/Candles & Holders/Candleholders/Candle Sconces'
where id = 131
[2025-06-15 18:35:04.932] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20337} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:35:04.978] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20338} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A17738953011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 17738953011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:35:04.932', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Bathroom Furniture(浴室家具)/Bathroom Mirrors(浴室镜子)/Wall-Mounted Vanity Mirrors(壁挂式化妆镜)'
where id = 132
[2025-06-15 18:35:14.952] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20339} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:35:14.996] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20340} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11433471011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11433471011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:35:14.953', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Fixtures(浴室固定装置)/Vanities(虚荣)'
where id = 133
[2025-06-15 18:35:16.599] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20324} executed. SELECT COUNT( * ) AS total FROM zz_fruugo_product_sku
[2025-06-15 18:35:24.925] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20342} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:35:24.948] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20343} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A16183925011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 16183925011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:35:24.926', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Decorative Pillows, Inserts & Covers(装饰枕头、插件和盖子)/Poufs(磅)'
where id = 134
[2025-06-15 18:35:34.910] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20344} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:35:35.121] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20345} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3734291%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3734291, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:35:34.911', source_category_path = 'Home & Kitchen/Home Décor Products/Candles & Holders/Candleholders/Candleholder Sets'
where id = 135
[2025-06-15 18:35:44.917] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20346} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:35:44.939] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20347} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A85975011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 85975011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:35:44.917', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Household Cleaners(家用清洁工)/Cleaning Tools(清洁工具)/Brushes(刷子)/Toilet Brushes & Holders(马桶刷和支架)'
where id = 136
[2025-06-15 18:35:54.911] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20348} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:35:54.992] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20349} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3732921%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3732921, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:35:54.911', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Bathroom Furniture(浴室家具)/Bathroom Sets(浴室套装)'
where id = 137
[2025-06-15 18:36:04.916] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20350} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:36:04.940] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20351} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A13749761%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 13749761, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:36:04.917', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Holders & Dispensers(支架和分配器)/Dispensers(分配器)/Countertop Soap Dispensers(台面肥皂分配器)'
where id = 138
[2025-06-15 18:36:14.933] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20352} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:36:14.958] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20353} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3732341%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3732341, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:36:14.935', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Decorative Pillows, Inserts & Covers(装饰枕头、插件和盖子)/Throw Pillow Covers(扔枕头套)'
where id = 139
[2025-06-15 18:36:24.957] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20354} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:36:24.983] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20355} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3734611%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3734611, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:36:24.958', source_category_path = 'Home & Kitchen/Home Décor Products/Candles & Holders/Candleholders/Candlestick Holders'
where id = 140
[2025-06-15 18:36:34.929] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20356} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:36:34.965] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20357} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A344744011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 344744011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:36:34.930', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Holders & Dispensers(支架和分配器)/Dispensers(分配器)/Lotion Dispensers(乳液分配器)'
where id = 141
[2025-06-15 18:36:44.919] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20358} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:36:44.948] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20359} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A335116011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 335116011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:36:44.919', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Bathroom Furniture(浴室家具)/Bathroom Shelves(浴室搁板)'
where id = 142
[2025-06-15 18:36:54.918] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20360} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:36:54.946] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20361} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A15524361011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 15524361011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:36:54.918', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Household Cleaners(家用清洁工)/Cleaning Tools(清洁工具)/Cleaning Cloths(清洁布)'
where id = 143
[2025-06-15 18:37:04.914] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20362} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:37:04.940] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20363} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11433449011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11433449011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:37:04.915', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Bed Pillows(枕头)'
where id = 144
[2025-06-15 18:37:14.546] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20341} executed. select count(*) as total
from zz_fruugo_product_sku
where (f_created_at between TIMESTAMP '2025-06-15 00:00:00.000' and TIMESTAMP '2025-06-15 23:59:59.999')
[2025-06-15 18:37:14.566] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20364} executed. select id, stat_date, collect_offer_total, collect_offer_pending, collect_offer_processed
	, collect_offer_new, collect_sale_total, collect_sale_new, seller_total, seller_new
	, product_total, product_new, sku_total, sku_new, f_created_at as createdAt
	, f_last_updated_at as lastUpdatedAt
from zz_fruugo_monitor
where id = '703721994465583173'
[2025-06-15 18:37:14.603] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20365} executed. update zz_fruugo_monitor
set stat_date = TIMESTAMP '2025-06-15 00:00:00.000', collect_offer_total = 581232, collect_offer_pending = 120267, collect_offer_processed = 371647, collect_offer_new = 0, collect_sale_total = 8180610, collect_sale_new = 0, seller_total = 2960, seller_new = 0, product_total = 1032438, product_new = 0, sku_total = 2684538, sku_new = 0, f_created_at = TIMESTAMP '2025-06-15 00:00:00.000', f_last_updated_at = TIMESTAMP '2025-06-15 18:37:14.547'
where id = '703721994465583173'
[2025-06-15 18:37:14.921] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20367} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:37:14.943] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20368} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3732321%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3732321, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:37:14.921', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Decorative Pillows, Inserts & Covers(装饰枕头、插件和盖子)/Throw Pillows(扔枕头)'
where id = 145
[2025-06-15 18:37:15.035] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20366} executed. select count(*) as total
from zz_fruugo_bought_offer
where (f_created_at >= TIMESTAMP '2025-06-15 18:07:14.605')
[2025-06-15 18:37:15.074] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20369} executed. select count(*) as total
from zz_fruugo_seller
where (next_collect_time < TIMESTAMP '2025-06-15 16:37:15.036'
	and country = 'CN')
[2025-06-15 18:37:17.618] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20370} executed. select count(*) as total
from zz_fruugo_data_product
where (process_status = 0)
[2025-06-15 18:37:17.630] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20371} executed. select count(*) as total
from zz_fruugo_data_sku
where (process_status = 0)
[2025-06-15 18:37:24.933] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20372} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:37:24.964] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20373} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3734671%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3734671, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:37:24.934', source_category_path = 'Home & Kitchen/Home Décor Products/Candles & Holders/Candleholders/Decorative Candle Lanterns'
where id = 146
[2025-06-15 18:37:34.927] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20374} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:37:34.954] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20375} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A13749791%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 13749791, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:37:34.927', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Holders & Dispensers(支架和分配器)/Dispensers(分配器)/Shower Dispensers(淋浴分配器)'
where id = 147
[2025-06-15 18:37:44.922] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20376} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:37:44.945] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20377} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A16187581011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 16187581011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:37:44.922', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Bathroom Furniture(浴室家具)/Over-the-Toilet Storage(厕所储物)'
where id = 148
[2025-06-15 18:37:54.930] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10019, pstmt-20378} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:37:54.957] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10019, pstmt-20379} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11433438011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11433438011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:37:54.931', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Bedspreads & Coverlets(床罩和床罩)'
where id = 149
[2025-06-15 18:38:04.919] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10019, pstmt-20380} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:38:04.945] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10019, pstmt-20381} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3734731%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3734731, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:38:04.919', source_category_path = 'Home & Kitchen/Home Décor Products/Candles & Holders/Candleholders/Hurricane Candleholders'
where id = 150
[2025-06-15 18:38:14.908] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10019, pstmt-20382} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:38:14.978] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10019, pstmt-20383} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A13749801%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 13749801, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:38:14.908', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Holders & Dispensers(支架和分配器)/Soap Dishes(肥皂碟)'
where id = 151
[2025-06-15 18:38:24.938] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10019, pstmt-20384} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:38:24.970] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10019, pstmt-20385} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A2245498011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 2245498011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:38:24.940', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Household Cleaners(家用清洁工)/Cleaning Tools(清洁工具)/Dusting(除尘)/Feather Dusters(羽毛掸子)'
where id = 152
[2025-06-15 18:38:34.920] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10019, pstmt-20386} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:38:34.955] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10019, pstmt-20387} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A14053331%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 14053331, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:38:34.922', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Duvet Covers & Sets(羽绒被套和套装)/Duvet Cover Sets(羽绒被套)'
where id = 153
[2025-06-15 18:38:45.088] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10019, pstmt-20388} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:38:45.113] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10019, pstmt-20389} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A16961058011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 16961058011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:38:45.089', source_category_path = 'Home & Kitchen/Home Décor Products/Candles & Holders/Candleholders/Menorahs'
where id = 154
[2025-06-15 18:38:54.917] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20390} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:38:54.945] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20391} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A13749811%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 13749811, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:38:54.918', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Holders & Dispensers(支架和分配器)/Tissue Holders(组织支架)'
where id = 155
[2025-06-15 18:39:04.911] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20392} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:39:04.935] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20393} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A15342901%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 15342901, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:39:04.911', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Household Cleaners(家用清洁工)/Cleaning Tools(清洁工具)/Gloves(手套)'
where id = 156
[2025-06-15 18:39:14.921] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20394} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:39:14.956] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20395} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A335107011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 335107011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:39:14.922', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Bathroom Furniture(浴室家具)/Towel Holders(毛巾架)/Towel Bars(毛巾杆)'
where id = 157
[2025-06-15 18:39:20.047] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20396} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:39:20.076] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20397} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11433443011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11433443011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:39:20.049', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Blankets & Quilts(毯子和被子)/Quilts(被子)'
where id = 158
[2025-06-15 18:39:30.037] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20398} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:39:30.063] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20399} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A1199134%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 1199134, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:39:30.038', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Duvet Covers & Sets(羽绒被套和套装)/Duvet Covers(羽绒被盖)'
where id = 159
[2025-06-15 18:39:33.015] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20400} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where id = 'submitForm'
[2025-06-15 18:39:33.275] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20401} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where id = 'submitForm'
[2025-06-15 18:39:40.044] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20402} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:39:40.068] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20403} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3734651%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3734651, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:39:40.045', source_category_path = 'Home & Kitchen/Home Décor Products/Candles & Holders/Candleholders/Tea Light Holders'
where id = 160
[2025-06-15 18:39:48.052] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20404} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where id = 'submitForm'
[2025-06-15 18:39:50.050] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20405} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:39:50.075] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20406} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A16350361%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 16350361, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:39:50.051', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Holders & Dispensers(支架和分配器)/Toilet Paper Holders(卫生纸架)'
where id = 161
[2025-06-15 18:40:00.061] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20408} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:40:00.139] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20409} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A16350721%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 16350721, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:40:00.061', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Bathroom Furniture(浴室家具)/Towel Holders(毛巾架)/Towel Racks(毛巾架)'
where id = 162
[2025-06-15 18:40:00.168] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10020, pstmt-20407} executed. select count(*) as total
from zz_fruugo_bought_offer
where (f_created_at >= TIMESTAMP '2025-06-15 18:10:00.004')
[2025-06-15 18:40:00.236] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10020, pstmt-20410} executed. select count(*) as total
from zz_fruugo_seller
where (next_collect_time < TIMESTAMP '2025-06-15 16:40:00.168'
	and country = 'CN')
[2025-06-15 18:40:01.301] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10020, pstmt-20411} executed. select count(*) as total
from zz_fruugo_data_product
where (process_status = 0)
[2025-06-15 18:40:01.396] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10020, pstmt-20412} executed. insert into zz_fruugo_alert_log (id, alert_type, alert_module, alert_content, alert_time
	, status, notify_type, ip, created_at, updated_at)
values ('704003857138628293', 5, 'product', '产品数据堆积严重，当前有 31012 条待处理产品数据，超过阈值(10000)，请检查产品处理系统', TIMESTAMP '2025-06-15 18:40:01.301'
	, 0, 1, '***********', TIMESTAMP '2025-06-15 18:40:01.301', TIMESTAMP '2025-06-15 18:40:01.301')
[2025-06-15 18:40:01.422] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10020, pstmt-20413} executed. select count(*) as total
from zz_fruugo_data_sku
where (process_status = 0)
[2025-06-15 18:40:10.050] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10020, pstmt-20414} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:40:10.092] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10020, pstmt-20415} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11433444011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11433444011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:40:10.050', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Blankets & Quilts(毯子和被子)/Throw Blankets(扔毯子)'
where id = 163
[2025-06-15 18:40:20.056] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10020, pstmt-20416} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:40:20.085] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10020, pstmt-20417} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A10671048011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 10671048011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:40:20.056', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Duvets & Down Comforters(羽绒被和羽绒被)'
where id = 164
[2025-06-15 18:40:30.061] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10020, pstmt-20418} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:40:30.092] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10020, pstmt-20419} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A695476011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 695476011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:40:30.062', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Holders & Dispensers(支架和分配器)/Toilet Paper Storage Containers(卫生纸存储容器)'
where id = 165
[2025-06-15 18:40:40.044] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10020, pstmt-20420} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:40:40.070] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10020, pstmt-20421} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A335111011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 335111011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:40:40.044', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Bathroom Furniture(浴室家具)/Towel Holders(毛巾架)/Towel Rings(毛巾环)'
where id = 166
[2025-06-15 18:40:50.055] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10020, pstmt-20422} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:40:50.078] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10020, pstmt-20423} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11433445011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11433445011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:40:50.056', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Blankets & Quilts(毯子和被子)/Wearable Blankets(可穿戴毛毯)'
where id = 167
[2025-06-15 18:42:00.464] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20000} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:42:00.511] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20001} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A16250371%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 16250371, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:42:00.471', source_category_path = 'Home & Kitchen/Home Décor Products/Candles & Holders/Candles/Aromatherapy Candles'
where id = 168
[2025-06-15 18:42:10.057] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20002} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:42:10.082] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20003} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A13749821%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 13749821, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:42:10.057', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Holders & Dispensers(支架和分配器)/Toothbrush Holders(牙刷架)'
where id = 169
[2025-06-15 18:42:20.063] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20004} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:42:20.097] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20005} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A14253971%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 14253971, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:42:20.065', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Household Cleaners(家用清洁工)/Cleaning Tools(清洁工具)/Mopping(拖地)/Accessories(附件)/Mop Handles(拖把手柄)'
where id = 170
[2025-06-15 18:42:30.084] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20006} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:42:30.115] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20007} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3734301%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3734301, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:42:30.085', source_category_path = 'Home & Kitchen/Home Décor Products/Candles & Holders/Candles/Candle Sets'
where id = 171
[2025-06-15 18:42:30.453] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20008} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:42:30.482] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20009} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A695492011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 695492011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:42:30.455', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Household Cleaners(家用清洁工)/Cleaning Tools(清洁工具)/Mopping(拖地)/Accessories(附件)/Refill Sponges(重新填充海绵)'
where id = 172
[2025-06-15 18:42:40.448] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20010} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:42:40.475] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20011} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A344745011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 344745011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:42:40.449', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Holders & Dispensers(支架和分配器)/Tumblers(不倒翁)'
where id = 173
[2025-06-15 18:45:00.594] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10003, pstmt-20013} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:45:00.600] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20012} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:45:00.605] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10002, pstmt-20014} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:45:04.009] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10003, pstmt-20016} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:45:04.009] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10002, pstmt-20018} executed. select count(*) as total
from zz_fruugo_bought_offer
where (f_created_at >= TIMESTAMP '2025-06-15 18:15:00.579')
[2025-06-15 18:45:04.011] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20017} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:45:04.011] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10001, pstmt-20015} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:45:04.030] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10002, pstmt-20019} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11433447011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11433447011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:45:00.600', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Duvets & Duvet Covers(羽绒被和羽绒被套)/Duvet Covers(羽绒被盖)'
where id = 174
[2025-06-15 18:45:04.032] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10003, pstmt-20020} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11433447011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11433447011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:45:00.604', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Duvets & Duvet Covers(羽绒被和羽绒被套)/Duvet Covers(羽绒被盖)'
where id = 174
[2025-06-15 18:45:04.034] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20021} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11433447011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11433447011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:45:00.612', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Duvets & Duvet Covers(羽绒被和羽绒被套)/Duvet Covers(羽绒被盖)'
where id = 174
[2025-06-15 18:45:04.039] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10001, pstmt-20022} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11433447011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11433447011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:45:04.010', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Duvets & Duvet Covers(羽绒被和羽绒被套)/Duvet Covers(羽绒被盖)'
where id = 174
[2025-06-15 18:45:04.061] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10001, pstmt-20026} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:45:04.063] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10003, pstmt-20024} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11433447011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11433447011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:45:04.011', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Duvets & Duvet Covers(羽绒被和羽绒被套)/Duvet Covers(羽绒被盖)'
where id = 174
[2025-06-15 18:45:04.063] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10002, pstmt-20023} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11433447011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11433447011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:45:04.011', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Duvets & Duvet Covers(羽绒被和羽绒被套)/Duvet Covers(羽绒被盖)'
where id = 174
[2025-06-15 18:45:04.084] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20025} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:45:04.089] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10003, pstmt-20028} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:45:04.094] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10001, pstmt-20027} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:45:04.094] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10002, pstmt-20029} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3733271%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3733271, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:45:04.062', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Bedroom Furniture(卧室家具)/Bedroom Armoires(卧室衣橱)'
where id = 175
[2025-06-15 18:45:04.122] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20030} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:45:04.122] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10003, pstmt-20031} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:45:04.122] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10002, pstmt-20033} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3733271%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3733271, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:45:04.090', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Bedroom Furniture(卧室家具)/Bedroom Armoires(卧室衣橱)'
where id = 175
[2025-06-15 18:45:04.124] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10005, pstmt-20032} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3733271%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3733271, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:45:04.084', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Bedroom Furniture(卧室家具)/Bedroom Armoires(卧室衣橱)'
where id = 175
[2025-06-15 18:45:04.134] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10001, pstmt-20034} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3733271%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3733271, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:45:04.094', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Bedroom Furniture(卧室家具)/Bedroom Armoires(卧室衣橱)'
where id = 175
[2025-06-15 18:45:04.142] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10002, pstmt-20036} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A17874222011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 17874222011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:45:04.122', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Kids'' Bedding(儿童床上用品)/Air Mattresses(空气床垫)'
where id = 176
[2025-06-15 18:45:04.145] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10003, pstmt-20035} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A17874222011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 17874222011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:45:04.122', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Kids'' Bedding(儿童床上用品)/Air Mattresses(空气床垫)'
where id = 176
[2025-06-15 18:45:04.152] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20037} executed. insert into zz_fruugo_alert_log (id, alert_type, alert_module, alert_content, alert_time
	, status, notify_type, ip, created_at, updated_at)
values ('704005127106770693', 3, 'collect_sale', '最近30分钟内没有新的JustBought数据，请检查JustBought采集系统是否正常运行', TIMESTAMP '2025-06-15 18:45:04.012'
	, 0, 1, '***********', TIMESTAMP '2025-06-15 18:45:04.012', TIMESTAMP '2025-06-15 18:45:04.012')
[2025-06-15 18:45:04.166] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10003, pstmt-20038} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:45:04.170] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20039} executed. select count(*) as total
from zz_fruugo_seller
where (next_collect_time < TIMESTAMP '2025-06-15 16:45:04.152'
	and country = 'CN')
[2025-06-15 18:45:04.206] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20041} executed. select count(*) as total
from zz_fruugo_data_product
where (process_status = 0)
[2025-06-15 18:45:04.209] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10003, pstmt-20040} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3734491%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3734491, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:45:04.167', source_category_path = 'Home & Kitchen/Home Décor Products/Candles & Holders/Candles/Floating Candles'
where id = 177
[2025-06-15 18:45:04.235] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10003, pstmt-20042} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:45:04.255] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10006, pstmt-20043} executed. insert into zz_fruugo_alert_log (id, alert_type, alert_module, alert_content, alert_time
	, status, notify_type, ip, created_at, updated_at)
values ('704005127622670085', 5, 'product', '产品数据堆积严重，当前有 30986 条待处理产品数据，超过阈值(10000)，请检查产品处理系统', TIMESTAMP '2025-06-15 18:45:04.207'
	, 0, 1, '***********', TIMESTAMP '2025-06-15 18:45:04.207', TIMESTAMP '2025-06-15 18:45:04.207')
[2025-06-15 18:45:04.257] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10003, pstmt-20044} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A695491011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 695491011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:45:04.235', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Household Cleaners(家用清洁工)/Cleaning Tools(清洁工具)/Mopping(拖地)/Accessories(附件)/Replacement Heads(更换头)'
where id = 178
[2025-06-15 18:45:04.267] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10006, pstmt-20045} executed. select count(*) as total
from zz_fruugo_data_sku
where (process_status = 0)
[2025-06-15 18:45:10.460] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10006, pstmt-20046} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:45:10.482] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10006, pstmt-20047} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A13749831%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 13749831, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:45:10.460', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Holders & Dispensers(支架和分配器)/Vanity Trays(梳妆台托盘)'
where id = 179
[2025-06-15 18:45:20.455] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10006, pstmt-20048} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:45:20.488] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10006, pstmt-20049} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3732931%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3732931, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:45:20.456', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Bedroom Furniture(卧室家具)/Bedroom Sets(卧室套装)'
where id = 180
[2025-06-15 18:49:32.485] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10006, pstmt-20050} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where id = 7
[2025-06-15 18:49:32.494] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10003, pstmt-20051} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:49:32.512] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20052} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:49:32.516] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10002, pstmt-20054} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:49:32.516] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10006, pstmt-20053} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:49:32.518] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10003, pstmt-20055} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:49:32.522] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10001, pstmt-20056} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:49:32.534] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10005, pstmt-20058} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11433448011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11433448011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:49:32.512', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Duvets & Duvet Covers(羽绒被和羽绒被套)/Duvets(羽绒被)'
where id = 181
[2025-06-15 18:49:32.536] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10002, pstmt-20060} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11433448011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11433448011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:49:32.516', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Duvets & Duvet Covers(羽绒被和羽绒被套)/Duvets(羽绒被)'
where id = 181
[2025-06-15 18:49:32.536] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10006, pstmt-20059} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11433448011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11433448011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:49:32.516', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Duvets & Duvet Covers(羽绒被和羽绒被套)/Duvets(羽绒被)'
where id = 181
[2025-06-15 18:49:32.536] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20057} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11433448011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11433448011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:49:32.495', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Duvets & Duvet Covers(羽绒被和羽绒被套)/Duvets(羽绒被)'
where id = 181
[2025-06-15 18:49:32.547] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10003, pstmt-20061} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11433448011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11433448011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:49:32.518', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Duvets & Duvet Covers(羽绒被和羽绒被套)/Duvets(羽绒被)'
where id = 181
[2025-06-15 18:49:32.547] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10001, pstmt-20062} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11433448011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11433448011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:49:32.523', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Duvets & Duvet Covers(羽绒被和羽绒被套)/Duvets(羽绒被)'
where id = 181
[2025-06-15 18:49:32.582] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10006, pstmt-20065} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:49:32.613] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10002, pstmt-20067} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:49:32.613] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10001, pstmt-20063} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:49:32.613] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20066} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:49:32.613] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10005, pstmt-20068} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:49:32.613] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10003, pstmt-20064} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:49:32.638] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10006, pstmt-20069} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A17874223011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 17874223011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:49:32.584', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Kids'' Bedding(儿童床上用品)/Bed Skirts(床裙)'
where id = 182
[2025-06-15 18:49:32.676] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10006, pstmt-20075} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:49:32.676] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10001, pstmt-20070} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A17874223011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 17874223011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:49:32.613', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Kids'' Bedding(儿童床上用品)/Bed Skirts(床裙)'
where id = 182
[2025-06-15 18:49:32.676] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10005, pstmt-20072} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A17874223011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 17874223011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:49:32.613', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Kids'' Bedding(儿童床上用品)/Bed Skirts(床裙)'
where id = 182
[2025-06-15 18:49:32.677] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10003, pstmt-20074} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A17874223011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 17874223011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:49:32.613', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Kids'' Bedding(儿童床上用品)/Bed Skirts(床裙)'
where id = 182
[2025-06-15 18:49:32.677] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10002, pstmt-20071} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A17874223011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 17874223011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:49:32.613', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Kids'' Bedding(儿童床上用品)/Bed Skirts(床裙)'
where id = 182
[2025-06-15 18:49:32.705] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10003, pstmt-20076} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3734481%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3734481, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:49:32.677', source_category_path = 'Home & Kitchen/Home Décor Products/Candles & Holders/Candles/Jar Candles'
where id = 183
[2025-06-15 18:49:32.705] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10002, pstmt-20077} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:49:32.708] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10006, pstmt-20078} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:49:32.716] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10005, pstmt-20080} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:49:32.716] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10001, pstmt-20079} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:49:32.727] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10003, pstmt-20083} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:49:32.729] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10006, pstmt-20082} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3734481%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3734481, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:49:32.709', source_category_path = 'Home & Kitchen/Home Décor Products/Candles & Holders/Candles/Jar Candles'
where id = 183
[2025-06-15 18:49:32.729] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10002, pstmt-20081} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3734481%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3734481, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:49:32.706', source_category_path = 'Home & Kitchen/Home Décor Products/Candles & Holders/Candles/Jar Candles'
where id = 183
[2025-06-15 18:49:32.742] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10001, pstmt-20084} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A23537025011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 23537025011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:49:32.717', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Household Cleaners(家用清洁工)/Cleaning Tools(清洁工具)/Mopping(拖地)/Accessories(附件)/Replacement Mop Pads(更换拖把垫)'
where id = 184
[2025-06-15 18:49:32.742] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10005, pstmt-20085} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A23537025011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 23537025011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:49:32.717', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Household Cleaners(家用清洁工)/Cleaning Tools(清洁工具)/Mopping(拖地)/Accessories(附件)/Replacement Mop Pads(更换拖把垫)'
where id = 184
[2025-06-15 18:49:32.751] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10003, pstmt-20086} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A23537025011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 23537025011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:49:32.727', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Household Cleaners(家用清洁工)/Cleaning Tools(清洁工具)/Mopping(拖地)/Accessories(附件)/Replacement Mop Pads(更换拖把垫)'
where id = 184
[2025-06-15 18:49:32.756] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10002, pstmt-20088} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:49:32.756] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10006, pstmt-20087} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:49:32.767] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10005, pstmt-20090} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:49:32.770] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10001, pstmt-20089} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:49:32.780] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10006, pstmt-20092} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A13861693011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 13861693011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:49:32.757', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Makeup Organizers(化妆组织者)'
where id = 185
[2025-06-15 18:49:32.780] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10003, pstmt-20091} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:49:32.780] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10002, pstmt-20093} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A13861693011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 13861693011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:49:32.757', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Makeup Organizers(化妆组织者)'
where id = 185
[2025-06-15 18:49:32.790] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10005, pstmt-20094} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A13861693011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 13861693011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:49:32.768', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Makeup Organizers(化妆组织者)'
where id = 185
[2025-06-15 18:49:32.795] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10001, pstmt-20095} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A13861693011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 13861693011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:49:32.770', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Makeup Organizers(化妆组织者)'
where id = 185
[2025-06-15 18:49:32.802] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10003, pstmt-20096} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A13861693011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 13861693011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:49:32.781', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Makeup Organizers(化妆组织者)'
where id = 185
[2025-06-15 18:49:32.802] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10002, pstmt-20097} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:49:32.826] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10002, pstmt-20098} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3734431%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3734431, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:49:32.802', source_category_path = 'Home & Kitchen/Home Décor Products/Candles & Holders/Candles/Pillars'
where id = 186
[2025-06-15 18:49:32.846] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10002, pstmt-20099} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:49:32.867] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10002, pstmt-20100} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A2245509011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 2245509011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:49:32.847', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Household Cleaners(家用清洁工)/Cleaning Tools(清洁工具)/Mopping(拖地)/Buckets(水桶)'
where id = 187
[2025-06-15 18:49:32.876] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20073} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A17874223011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 17874223011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:49:32.613', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Kids'' Bedding(儿童床上用品)/Bed Skirts(床裙)'
where id = 182
[2025-06-15 18:49:40.446] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20101} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:49:40.471] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20102} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3743901%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3743901, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:49:40.447', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Medicine Cabinets(药柜)'
where id = 188
[2025-06-15 18:49:50.458] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20103} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:49:50.486] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20104} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11426914011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11426914011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:49:50.459', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Nursery Bedding(托儿所床上用品)/Bedding Sets(床上用品套装)'
where id = 189
[2025-06-15 18:50:00.198] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20105} executed. select count(*) as total
from zz_fruugo_bought_offer
where (f_created_at >= TIMESTAMP '2025-06-15 18:20:00.012')
[2025-06-15 18:50:00.222] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20106} executed. select count(*) as total
from zz_fruugo_seller
where (next_collect_time < TIMESTAMP '2025-06-15 16:50:00.198'
	and country = 'CN')
[2025-06-15 18:50:00.652] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20107} executed. select count(*) as total
from zz_fruugo_data_product
where (process_status = 0)
[2025-06-15 18:50:00.672] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20108} executed. select count(*) as total
from zz_fruugo_data_sku
where (process_status = 0)
[2025-06-15 18:50:00.715] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20109} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:50:00.755] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20110} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A23537016011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 23537016011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:50:00.716', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Household Cleaners(家用清洁工)/Cleaning Tools(清洁工具)/Mopping(拖地)/Mops & Bucket Sets(拖把和铲斗套装)'
where id = 190
[2025-06-15 18:50:10.452] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20111} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:50:10.483] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20112} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A10671052011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 10671052011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:50:10.454', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Kids'' Bedding(儿童床上用品)/Bedding Sets & Collections(床上用品套装和系列)/Bedspread & Coverlet Sets(床罩和床罩套装)'
where id = 191
[2025-06-15 18:50:20.452] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20113} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:50:20.483] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20114} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A16187581011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 16187581011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:50:20.453', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Over the Toilet Storage(在厕所储物处)'
where id = 192
[2025-06-15 18:50:30.444] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20115} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:50:30.485] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20116} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A17873919011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 17873919011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:50:30.444', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Bedroom Furniture(卧室家具)/Beds, Frames & Bases(床、框架和底座)/Bases & Foundations(基地和基础)/Adjustable Bases(可调底座)'
where id = 193
[2025-06-15 18:50:40.455] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20117} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:50:40.488] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20118} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11426909011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11426909011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:50:40.456', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Nursery Bedding(托儿所床上用品)/Blankets & Swaddling(毯子和襁褓)'
where id = 194
[2025-06-15 18:50:50.463] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20119} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:50:50.495] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20120} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3734541%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3734541, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:50:50.464', source_category_path = 'Home & Kitchen/Home Décor Products/Candles & Holders/Candles/Specialty Candles/Birthday Candles'
where id = 195
[2025-06-15 18:51:00.456] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20121} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:51:00.487] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20122} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3744271%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3744271, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:51:00.456', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Robe & Towel Hooks(长袍和毛巾钩)'
where id = 196
[2025-06-15 18:51:10.453] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20123} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:51:10.480] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20124} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A15659400011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 15659400011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:51:10.454', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Bedroom Furniture(卧室家具)/Beds, Frames & Bases(床、框架和底座)/Bases & Foundations(基地和基础)/Bed Slats(床板)'
where id = 197
[2025-06-15 18:51:20.461] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20125} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:51:20.494] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20126} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11426912011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11426912011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:51:20.462', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Nursery Bedding(托儿所床上用品)/Cradle Bedding(摇篮床上用品)'
where id = 198
[2025-06-15 18:51:30.457] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20127} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:51:30.488] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20128} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A23537023011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 23537023011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:51:30.457', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Household Cleaners(家用清洁工)/Cleaning Tools(清洁工具)/Mopping(拖地)/Wet & Dry Mops(干湿拖把)/Dust Mops(防尘拖把)'
where id = 199
[2025-06-15 18:51:40.875] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20129} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:51:40.900] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20130} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3734561%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3734561, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:51:40.876', source_category_path = 'Home & Kitchen/Home Décor Products/Candles & Holders/Candles/Specialty Candles/Devotional Candles'
where id = 200
[2025-06-15 18:51:50.447] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20131} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:51:50.473] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20132} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A17874233011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 17874233011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:51:50.448', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Kids'' Bedding(儿童床上用品)/Blankets & Throws(毯子和投掷)/Bed Blankets(床毯)'
where id = 201
[2025-06-15 18:52:00.447] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20133} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:52:00.503] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20134} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3733071%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3733071, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:52:00.448', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Bedroom Furniture(卧室家具)/Beds, Frames & Bases(床、框架和底座)/Bases & Foundations(基地和基础)/Box Springs(盒式弹簧)'
where id = 202
[2025-06-15 18:52:10.454] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20135} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:52:10.481] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20136} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11426913011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11426913011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:52:10.455', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Nursery Bedding(托儿所床上用品)/Crib Bedding(婴儿床床上用品)'
where id = 203
[2025-06-15 18:52:20.456] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20137} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:52:20.497] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20138} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A23537024011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 23537024011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:52:20.457', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Household Cleaners(家用清洁工)/Cleaning Tools(清洁工具)/Mopping(拖地)/Wet & Dry Mops(干湿拖把)/Sponge Mops(海绵拖把)'
where id = 204
[2025-06-15 18:52:30.454] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20139} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:52:30.482] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20140} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3734551%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3734551, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:52:30.455', source_category_path = 'Home & Kitchen/Home Décor Products/Candles & Holders/Candles/Specialty Candles/Hanukkah Candles'
where id = 205
[2025-06-15 18:52:40.706] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20141} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:52:40.748] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20142} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A344746011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 344746011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:52:40.707', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Scales(秤)/Digital(数字)'
where id = 206
[2025-06-15 18:52:50.452] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20143} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:52:50.487] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20144} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A17388409011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 17388409011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:52:50.453', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Bedroom Furniture(卧室家具)/Beds, Frames & Bases(床、框架和底座)/Bases & Foundations(基地和基础)/Bunkie Boards(双层板)'
where id = 207
[2025-06-15 18:53:00.455] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20145} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:53:00.491] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20146} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A23537022011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 23537022011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:53:00.456', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Household Cleaners(家用清洁工)/Cleaning Tools(清洁工具)/Mopping(拖地)/Wet & Dry Mops(干湿拖把)/String Mops(拖把)'
where id = 208
[2025-06-15 18:53:10.478] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20147} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:53:10.510] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20148} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3734571%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3734571, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:53:10.478', source_category_path = 'Home & Kitchen/Home Décor Products/Candles & Holders/Candles/Specialty Candles/Novelty Candles'
where id = 209
[2025-06-15 18:53:20.464] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20149} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:53:20.495] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20150} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A17874234011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 17874234011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:53:20.464', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Kids'' Bedding(儿童床上用品)/Blankets & Throws(毯子和投掷)/Throws(投掷)'
where id = 210
[2025-06-15 18:53:30.445] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20151} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:53:30.474] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20152} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A344747011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 344747011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:53:30.446', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Scales(秤)/Mechanical(机械)'
where id = 211
[2025-06-15 18:53:40.453] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20153} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:53:40.482] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20154} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3248801011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3248801011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:53:40.454', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Bedroom Furniture(卧室家具)/Beds, Frames & Bases(床、框架和底座)/Bed Frames(床架)'
where id = 212
[2025-06-15 18:53:50.454] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20155} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:53:50.498] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20156} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11426908011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11426908011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:53:50.455', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Nursery Bedding(托儿所床上用品)/Pillows, Covers & Protectors(枕头、盖子和保护器)/Back & Body Supports(背部和身体支撑)'
where id = 213
[2025-06-15 18:54:00.464] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20157} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:54:00.507] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20158} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A17419390011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 17419390011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:54:00.465', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Household Cleaners(家用清洁工)/Cleaning Tools(清洁工具)/Scouring Pads & Sticks(擦洗垫和棍棒)'
where id = 214
[2025-06-15 18:54:11.636] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20159} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:54:11.677] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20160} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A17874235011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 17874235011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:54:11.637', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Kids'' Bedding(儿童床上用品)/Blankets & Throws(毯子和投掷)/Wearable Blankets(可穿戴毛毯)'
where id = 215
[2025-06-15 18:54:20.458] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20161} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:54:20.491] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20162} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3734581%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3734581, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:54:20.459', source_category_path = 'Home & Kitchen/Home Décor Products/Candles & Holders/Candles/Specialty Candles/Seasonal Celebration Candles'
where id = 216
[2025-06-15 18:54:30.446] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20163} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:54:30.482] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10008, pstmt-20164} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3248804011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3248804011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:54:30.447', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Bedroom Furniture(卧室家具)/Beds, Frames & Bases(床、框架和底座)/Beds(床)'
where id = 217
[2025-06-15 18:54:40.456] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20165} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:54:40.488] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20166} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11426916011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11426916011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:54:40.457', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Nursery Bedding(托儿所床上用品)/Pillows, Covers & Protectors(枕头、盖子和保护器)/Pillow Protectors(枕头保护器)'
where id = 218
[2025-06-15 18:54:50.458] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20167} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:54:50.485] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20168} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A15754811%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 15754811, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:54:50.459', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Household Cleaners(家用清洁工)/Cleaning Tools(清洁工具)/Sponges(海绵)'
where id = 219
[2025-06-15 18:55:00.246] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20169} executed. select count(*) as total
from zz_fruugo_bought_offer
where (f_created_at >= TIMESTAMP '2025-06-15 18:25:00.008')
[2025-06-15 18:55:00.296] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20170} executed. select count(*) as total
from zz_fruugo_seller
where (next_collect_time < TIMESTAMP '2025-06-15 16:55:00.247'
	and country = 'CN')
[2025-06-15 18:55:00.477] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20172} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:55:00.531] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20173} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A17874236011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 17874236011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:55:00.477', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Kids'' Bedding(儿童床上用品)/Blankets & Throws(毯子和投掷)/Weighted Blankets(加重毛毯)'
where id = 220
[2025-06-15 18:55:10.982] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20174} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:55:11.012] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20175} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A2299642011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 2299642011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:55:10.983', source_category_path = 'Home & Kitchen/Home Décor Products/Candles & Holders/Candles/Specialty Candles/Unity Candles'
where id = 221
[2025-06-15 18:55:19.878] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20171} executed. select count(*) as total
from zz_fruugo_data_product
where (process_status = 0)
[2025-06-15 18:55:19.914] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20176} executed. select count(*) as total
from zz_fruugo_data_sku
where (process_status = 0)
[2025-06-15 18:55:20.490] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20177} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:55:20.520] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20178} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A85969011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 85969011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:55:20.490', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Shower Accessories(淋浴配件)/Shower Caddies(淋浴球童)'
where id = 222
[2025-06-15 18:55:30.450] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20179} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:55:30.475] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20180} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11426917011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11426917011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:55:30.451', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Nursery Bedding(托儿所床上用品)/Pillows, Covers & Protectors(枕头、盖子和保护器)/Pillowcases(枕套)'
where id = 223
[2025-06-15 18:55:40.468] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20181} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:55:40.500] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20182} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A2245500011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 2245500011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:55:40.469', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Household Cleaners(家用清洁工)/Cleaning Tools(清洁工具)/Squeegees(挤压)'
where id = 224
[2025-06-15 18:55:50.465] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20183} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:55:50.503] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20184} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A16927403011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 16927403011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:55:50.466', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Kids'' Bedding(儿童床上用品)/Canopies(檐篷)'
where id = 225
[2025-06-15 18:56:00.469] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20185} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:56:00.501] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20186} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3734401%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3734401, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:56:00.471', source_category_path = 'Home & Kitchen/Home Décor Products/Candles & Holders/Candles/Taper Candles'
where id = 226
[2025-06-15 18:56:10.462] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20187} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:56:10.509] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20188} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A1065910%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 1065910, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:56:10.464', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Shower Accessories(淋浴配件)/Shower Radios(淋浴收音机)'
where id = 227
[2025-06-15 18:56:20.460] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20189} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:56:20.495] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20190} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11426915011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11426915011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:56:20.460', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Nursery Bedding(托儿所床上用品)/Pillows, Covers & Protectors(枕头、盖子和保护器)/Pillows(枕头)'
where id = 228
[2025-06-15 18:56:30.452] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20191} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:56:30.481] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10009, pstmt-20192} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3248803011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3248803011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:56:30.454', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Bedroom Furniture(卧室家具)/Beds, Frames & Bases(床、框架和底座)/Headboards & Footboards(床头板和脚踏板)/Footboards(脚踏板)'
where id = 229
[2025-06-15 18:56:40.456] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20193} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:56:40.501] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20194} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3734421%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3734421, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:56:40.457', source_category_path = 'Home & Kitchen/Home Décor Products/Candles & Holders/Candles/Tea Lights'
where id = 230
[2025-06-15 18:56:50.454] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20195} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:56:50.488] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20196} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A19201454011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 19201454011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:56:50.456', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Shower Accessories(淋浴配件)/Shower Squeegees(淋浴刮板)'
where id = 231
[2025-06-15 18:57:00.467] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20197} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:57:00.494] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20198} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11426918011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11426918011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:57:00.468', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Nursery Bedding(托儿所床上用品)/Playard Bedding(Playard床上用品)'
where id = 232
[2025-06-15 18:57:10.454] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20199} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:57:10.496] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20200} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3248802011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3248802011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:57:10.455', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Bedroom Furniture(卧室家具)/Beds, Frames & Bases(床、框架和底座)/Headboards & Footboards(床头板和脚踏板)/Headboards(床头板)'
where id = 233
[2025-06-15 18:57:20.445] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20201} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:57:20.480] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20202} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3734411%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3734411, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:57:20.446', source_category_path = 'Home & Kitchen/Home Décor Products/Candles & Holders/Candles/Votive Candles'
where id = 234
[2025-06-15 18:57:30.479] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20203} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:57:30.513] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20204} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A10671054011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 10671054011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:57:30.481', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Kids'' Bedding(儿童床上用品)/Comforters & Sets(被子和套装)/Comforter Sets(被子套装)'
where id = 235
[2025-06-15 18:57:40.456] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20205} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:57:40.482] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20206} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A19201455011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 19201455011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:57:40.456', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Shower Accessories(淋浴配件)/Shower Stall Mats(淋浴间垫)'
where id = 236
[2025-06-15 18:57:50.457] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20207} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:57:50.482] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20208} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11426919011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11426919011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:57:50.458', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Nursery Bedding(托儿所床上用品)/Quilts & Bed Covers(被子和床罩)'
where id = 237
[2025-06-15 18:58:00.464] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20209} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:58:00.504] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20210} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A14253831%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 14253831, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:58:00.465', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Household Cleaners(家用清洁工)/Cleaning Tools(清洁工具)/Sweeping(清扫)/Brooms(扫帚)/Angle Brooms(角扫帚)'
where id = 238
[2025-06-15 18:58:10.462] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20211} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:58:10.489] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20212} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3733261%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3733261, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:58:10.464', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Bedroom Furniture(卧室家具)/Dressers(梳妆台)'
where id = 239
[2025-06-15 18:58:20.467] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20213} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:58:20.505] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20214} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A362534011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 362534011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:58:20.468', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Kids'' Bedding(儿童床上用品)/Comforters & Sets(被子和套装)/Comforters(被子)'
where id = 240
[2025-06-15 18:58:30.455] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20215} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:58:30.502] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10004, pstmt-20216} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A14253841%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 14253841, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:58:30.456', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Household Cleaners(家用清洁工)/Cleaning Tools(清洁工具)/Sweeping(清扫)/Brooms(扫帚)/Hand Brooms(手扫帚)'
where id = 241
[2025-06-15 18:58:40.459] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20217} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:58:40.498] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20218} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3734911%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3734911, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:58:40.459', source_category_path = 'Home & Kitchen/Home Décor Products/Clocks/Alarm Clocks'
where id = 242
[2025-06-15 18:58:50.454] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20219} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:58:50.491] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20220} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11433451011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11433451011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:58:50.455', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Sheets & Pillowcases(床单和枕套)/Pillowcases(枕套)'
where id = 243
[2025-06-15 18:59:00.453] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20221} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:59:00.484] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20222} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3733071%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3733071, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:59:00.454', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Bedroom Furniture(卧室家具)/Mattresses & Box Springs(床垫和盒式弹簧)/Box Springs(盒式弹簧)'
where id = 244
[2025-06-15 18:59:10.444] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20223} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:59:10.479] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20224} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A14253851%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 14253851, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:59:10.444', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Household Cleaners(家用清洁工)/Cleaning Tools(清洁工具)/Sweeping(清扫)/Brooms(扫帚)/Push Brooms(推扫帚)'
where id = 245
[2025-06-15 18:59:20.472] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20225} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:59:20.511] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20226} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3734891%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3734891, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:59:20.472', source_category_path = 'Home & Kitchen/Home Décor Products/Clocks/Desk & Shelf Clocks'
where id = 246
[2025-06-15 18:59:30.452] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20227} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:59:30.490] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20228} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A17874227011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 17874227011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:59:30.452', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Kids'' Bedding(儿童床上用品)/Decorative Pillows & Covers(装饰枕头和覆盖物)/Body Pillows(身体枕头)'
where id = 247
[2025-06-15 18:59:40.458] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20229} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:59:40.482] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20230} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A11433452011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 11433452011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:59:40.459', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Sheets & Pillowcases(床单和枕套)/Sheets(床单)'
where id = 248
[2025-06-15 18:59:50.478] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20231} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 18:59:50.530] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20232} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3732971%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3732971, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 18:59:50.478', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Bedroom Furniture(卧室家具)/Mattresses & Box Springs(床垫和盒式弹簧)/Mattress & Box Spring Sets(床垫和盒式弹簧套装)'
where id = 249
[2025-06-15 19:00:00.422] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20233} executed. select count(*) as total
from zz_fruugo_bought_offer
where (f_created_at >= TIMESTAMP '2025-06-15 18:30:00.000')
[2025-06-15 19:00:00.445] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20235} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:00:00.477] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20236} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A13749841%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 13749841, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:00:00.445', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Shower Curtains, Hooks & Liners(浴帘、挂钩和衬垫)/Shower Curtain Hangers(浴帘衣架)/Decorative Shower Curtain Hooks(装饰浴帘挂钩)'
where id = 250
[2025-06-15 19:00:00.477] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20234} executed. select count(*) as total
from zz_fruugo_seller
where (next_collect_time < TIMESTAMP '2025-06-15 17:00:00.422'
	and country = 'CN')
[2025-06-15 19:00:10.476] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20238} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:00:10.541] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20239} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3734881%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3734881, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:00:10.476', source_category_path = 'Home & Kitchen/Home Décor Products/Clocks/Floor & Grandfather Clocks'
where id = 251
[2025-06-15 19:00:20.466] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20240} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:00:20.515] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20241} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A17874228011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 17874228011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:00:20.466', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Kids'' Bedding(儿童床上用品)/Decorative Pillows & Covers(装饰枕头和覆盖物)/Floor Pillows & Cushions(地板枕头和靠垫)'
where id = 252
[2025-06-15 19:00:30.454] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20242} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:00:30.501] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10011, pstmt-20243} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3278164011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3278164011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:00:30.455', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Household Cleaners(家用清洁工)/Cleaning Tools(清洁工具)/Sweeping(清扫)/Parts & Accessories(零件和附件)/Broom Handles(扫帚手柄)'
where id = 253
[2025-06-15 19:00:31.862] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20237} executed. select count(*) as total
from zz_fruugo_data_product
where (process_status = 0)
[2025-06-15 19:00:31.884] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20244} executed. select count(*) as total
from zz_fruugo_data_sku
where (process_status = 0)
[2025-06-15 19:00:31.906] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20245} executed. select id, stat_date, collect_offer_total, collect_offer_pending, collect_offer_processed
	, collect_offer_new, collect_sale_total, collect_sale_new, seller_total, seller_new
	, product_total, product_new, sku_total, sku_new, f_created_at as createdAt
	, f_last_updated_at as lastUpdatedAt
from zz_fruugo_monitor
where (stat_date = '2025-06-15')
[2025-06-15 19:00:37.602] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20246} executed. SELECT COUNT( * ) AS total FROM zz_fruugo_collect_offer
[2025-06-15 19:00:38.961] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20247} executed. select count(*) as total
from zz_fruugo_collect_offer
where (request_status = 0)
[2025-06-15 19:00:40.466] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20249} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:00:40.489] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20250} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A8521807011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 8521807011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:00:40.467', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Bedroom Furniture(卧室家具)/Mattresses & Box Springs(床垫和盒式弹簧)/Mattress Toppers(床垫)'
where id = 254
[2025-06-15 19:00:42.294] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20248} executed. select count(*) as total
from zz_fruugo_collect_offer
where (request_status = 2)
[2025-06-15 19:00:44.404] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20251} executed. select count(*) as total
from zz_fruugo_collect_offer
where (f_created_at between TIMESTAMP '2025-06-15 00:00:00.000' and TIMESTAMP '2025-06-15 23:59:59.999')
[2025-06-15 19:00:50.458] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20253} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:00:50.481] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20254} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A13749861%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 13749861, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:00:50.458', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Shower Curtains, Hooks & Liners(浴帘、挂钩和衬垫)/Shower Curtain Hangers(浴帘衣架)/Shower Curtain Rings(浴帘环)'
where id = 255
[2025-06-15 19:01:00.454] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20255} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:01:00.482] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20256} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3259035011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3259035011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:01:00.454', source_category_path = 'Home & Kitchen/Home Décor Products/Clocks/Mantel Clocks'
where id = 256
[2025-06-15 19:01:10.486] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20257} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:01:10.511] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20258} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3278165011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3278165011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:01:10.487', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Household Cleaners(家用清洁工)/Cleaning Tools(清洁工具)/Sweeping(清扫)/Parts & Accessories(零件和附件)/Broom Heads(扫帚头)'
where id = 257
[2025-06-15 19:01:20.441] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20259} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:01:20.470] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20260} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3732981%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3732981, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:01:20.442', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Bedroom Furniture(卧室家具)/Mattresses & Box Springs(床垫和盒式弹簧)/Mattresses(床垫)'
where id = 258
[2025-06-15 19:01:24.269] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20252} executed. SELECT COUNT( * ) AS total FROM zz_fruugo_collect_sale
[2025-06-15 19:01:24.287] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20261} executed. select count(*) as total
from zz_fruugo_collect_sale
where (f_created_at between TIMESTAMP '2025-06-15 00:00:00.000' and TIMESTAMP '2025-06-15 23:59:59.999')
[2025-06-15 19:01:24.300] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20262} executed. SELECT COUNT( * ) AS total FROM zz_fruugo_seller
[2025-06-15 19:01:24.322] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20263} executed. select count(*) as total
from zz_fruugo_seller
where (f_created_at between TIMESTAMP '2025-06-15 00:00:00.000' and TIMESTAMP '2025-06-15 23:59:59.999')
[2025-06-15 19:01:30.454] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20265} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:01:30.482] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20266} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A672239011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 672239011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:01:30.455', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Shower Curtains, Hooks & Liners(浴帘、挂钩和衬垫)/Shower Curtain Hangers(浴帘衣架)/Shower Curtain Roller Balls(浴帘滚球)'
where id = 259
[2025-06-15 19:01:33.305] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20264} executed. SELECT COUNT( * ) AS total FROM zz_fruugo_product
[2025-06-15 19:01:40.464] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20268} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:01:40.512] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20269} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A17874229011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 17874229011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:01:40.465', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Kids'' Bedding(儿童床上用品)/Decorative Pillows & Covers(装饰枕头和覆盖物)/Reading & Bed Rest Pillows(阅读和床上休息枕头)'
where id = 260
[2025-06-15 19:01:50.452] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20270} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:01:50.475] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20271} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A21588789011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 21588789011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:01:50.452', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Household Cleaners(家用清洁工)/Cleaning Tools(清洁工具)/Sweeping(清扫)/Parts & Accessories(零件和附件)/Dustpans(防尘罩)'
where id = 261
[2025-06-15 19:02:00.458] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20272} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:02:00.499] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20273} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A8521806011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 8521806011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:02:00.458', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Bedroom Furniture(卧室家具)/Mattresses & Box Springs(床垫和盒式弹簧)/Waterbed Mattresses(水床床垫)'
where id = 262
[2025-06-15 19:02:01.569] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20267} executed. select count(*) as total
from zz_fruugo_product
where (f_created_at between TIMESTAMP '2025-06-15 00:00:00.000' and TIMESTAMP '2025-06-15 23:59:59.999')
[2025-06-15 19:02:10.456] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20275} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:02:10.484] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20276} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A18200960011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 18200960011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:02:10.458', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Cleaning Tools(清洁工具)/Brushes(刷子)'
where id = 263
[2025-06-15 19:02:20.455] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20277} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:02:20.478] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20278} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A13749851%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 13749851, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:02:20.456', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Shower Curtains, Hooks & Liners(浴帘、挂钩和衬垫)/Shower Curtain Liners(浴帘衬垫)'
where id = 264
[2025-06-15 19:02:30.465] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20279} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:02:30.534] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10012, pstmt-20280} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3735021%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3735021, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:02:30.466', source_category_path = 'Home & Kitchen/Home Décor Products/Clocks/Specialty Clocks/Cuckoo Clocks'
where id = 265
[2025-06-15 19:02:40.518] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20281} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:02:40.546] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20282} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A17874230011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 17874230011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:02:40.519', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Kids'' Bedding(儿童床上用品)/Decorative Pillows & Covers(装饰枕头和覆盖物)/Throw Pillow Covers(扔枕头套)'
where id = 266
[2025-06-15 19:02:50.881] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20283} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:02:50.919] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20284} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A21579649011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 21579649011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:02:50.886', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Household Cleaners(家用清洁工)/Disinfectant Sprays & Liquids(消毒喷雾剂和液体)'
where id = 267
[2025-06-15 19:03:00.482] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20285} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:03:00.512] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20286} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3733251%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3733251, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:03:00.483', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Bedroom Furniture(卧室家具)/Nightstands(床头柜)'
where id = 268
[2025-06-15 19:03:10.456] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20287} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:03:10.510] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20288} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A18200961011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 18200961011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:03:10.457', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Cleaning Tools(清洁工具)/Buckets(水桶)'
where id = 269
[2025-06-15 19:03:20.482] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20289} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:03:20.582] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20290} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3743911%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3743911, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:03:20.482', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Shower Curtains, Hooks & Liners(浴帘、挂钩和衬垫)/Shower Curtain Rods(浴帘杆)'
where id = 270
[2025-06-15 19:03:30.581] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20291} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:03:30.612] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20292} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3734981%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3734981, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:03:30.581', source_category_path = 'Home & Kitchen/Home Décor Products/Clocks/Specialty Clocks/Projection Clocks'
where id = 271
[2025-06-15 19:03:40.454] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20293} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:03:40.498] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20294} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A17874231011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 17874231011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:03:40.454', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Kids'' Bedding(儿童床上用品)/Decorative Pillows & Covers(装饰枕头和覆盖物)/Throw Pillows(扔枕头)'
where id = 272
[2025-06-15 19:03:50.445] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20295} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:03:50.470] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20296} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A15524338011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 15524338011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:03:50.446', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Household Cleaners(家用清洁工)/Disinfectant Wipes(消毒湿巾)'
where id = 273
[2025-06-15 19:04:00.450] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20297} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:04:00.478] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20298} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A16353441%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 16353441, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:04:00.450', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Bedroom Furniture(卧室家具)/Quilt Stands(被子架)'
where id = 274
[2025-06-15 19:04:10.050] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20274} executed. SELECT COUNT( * ) AS total FROM zz_fruugo_product_sku
[2025-06-15 19:04:10.452] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20300} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:04:10.475] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20301} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A18200962011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 18200962011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:04:10.452', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Cleaning Tools(清洁工具)/Caddies(球童)'
where id = 275
[2025-06-15 19:04:20.496] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20302} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:04:20.520] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20303} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A13159327011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 13159327011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:04:20.497', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Shower Curtains, Hooks & Liners(浴帘、挂钩和衬垫)/Shower Curtain Sets(浴帘套装)'
where id = 276
[2025-06-15 19:04:30.442] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20304} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:04:30.464] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10013, pstmt-20305} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3734961%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3734961, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:04:30.444', source_category_path = 'Home & Kitchen/Home Décor Products/Clocks/Specialty Clocks/Shower Clocks'
where id = 277
[2025-06-15 19:04:40.501] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20306} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:04:40.527] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20307} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A17874232011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 17874232011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:04:40.501', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Kids'' Bedding(儿童床上用品)/Decorative Pillows & Covers(装饰枕头和覆盖物)/Travel Pillows(旅行枕头)'
where id = 278
[2025-06-15 19:04:50.448] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20308} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:04:50.474] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20309} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A15377711%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 15377711, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:04:50.449', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Household Cleaners(家用清洁工)/Drain Openers(排水口开启器)'
where id = 279
[2025-06-15 19:05:00.459] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20310} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:05:00.484] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20311} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3734261%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3734261, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:05:00.459', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Bedroom Furniture(卧室家具)/Room Dividers(房间隔断)'
where id = 280
[2025-06-15 19:05:10.445] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20312} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:05:10.488] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20313} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A13749881%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 13749881, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:05:10.445', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Shower Curtains, Hooks & Liners(浴帘、挂钩和衬垫)/Shower Curtains(浴帘)'
where id = 281
[2025-06-15 19:05:20.448] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20314} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:05:20.471] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20315} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3735001%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3735001, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:05:20.449', source_category_path = 'Home & Kitchen/Home Décor Products/Clocks/Specialty Clocks/Sundial Clocks'
where id = 282
[2025-06-15 19:05:30.457] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20316} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:05:30.478] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20317} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A15342861%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 15342861, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:05:30.457', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Household Cleaners(家用清洁工)/Floor Cleaners(地板清洁剂)'
where id = 283
[2025-06-15 19:05:40.452] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20318} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:05:40.482] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20319} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A18200964011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 18200964011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:05:40.454', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Cleaning Tools(清洁工具)/Cloths & Sponges(布和海绵)/Cleaning Cloths(清洁布)'
where id = 284
[2025-06-15 19:05:50.448] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20320} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:05:50.471] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20321} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A108372011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 108372011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:05:50.448', source_category_path = 'Home & Kitchen/Home Décor Products/Clocks/Specialty Clocks/Weather Monitoring Clocks'
where id = 285
[2025-06-15 19:06:00.440] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20322} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:06:00.464] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20323} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A10671056011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 10671056011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:06:00.440', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Kids'' Bedding(儿童床上用品)/Duvet Covers & Sets(羽绒被套和套装)/Duvet Cover Sets(羽绒被套)'
where id = 286
[2025-06-15 19:06:10.449] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20324} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:06:10.480] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20325} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A15356151%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 15356151, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:06:10.449', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Household Cleaners(家用清洁工)/Glass Cleaners(玻璃清洁剂)'
where id = 287
[2025-06-15 19:06:20.448] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20326} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:06:20.472] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20327} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A684877011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 684877011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:06:20.449', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Bedroom Furniture(卧室家具)/Storage Trunks & Chests(储物箱和箱子)/Storage Chests(储物箱)'
where id = 288
[2025-06-15 19:06:30.451] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20328} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:06:30.475] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10014, pstmt-20329} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A18200966011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 18200966011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:06:30.452', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Cleaning Tools(清洁工具)/Cloths & Sponges(布和海绵)/Paperless Towels(无纸毛巾)'
where id = 289
[2025-06-15 19:06:41.789] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20330} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:06:41.816] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20331} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A6810213011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 6810213011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:06:41.790', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Showerheads(淋浴喷头)/Fixed Showerheads(固定喷头)'
where id = 290
[2025-06-15 19:06:50.466] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20332} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:06:50.502] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20333} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3734871%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3734871, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:06:50.467', source_category_path = 'Home & Kitchen/Home Décor Products/Clocks/Wall Clocks'
where id = 291
[2025-06-15 19:07:00.445] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20334} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:07:00.470] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20335} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A362536011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 362536011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:07:00.446', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Kids'' Bedding(儿童床上用品)/Duvet Covers & Sets(羽绒被套和套装)/Duvet Covers(羽绒被盖)'
where id = 292
[2025-06-15 19:07:10.661] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20336} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:07:10.689] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20337} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A***********%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = ***********, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:07:10.662', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Cleaning Tools(清洁工具)/Cloths & Sponges(布和海绵)/Polishing Cloths(抛光布)'
where id = 293
[2025-06-15 19:07:20.437] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20338} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:07:20.460] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20339} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3734271%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3734271, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:07:20.438', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Bedroom Furniture(卧室家具)/Storage Trunks & Chests(储物箱和箱子)/Storage Trunks(储物行李箱)'
where id = 294
[2025-06-15 19:07:30.462] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20340} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:07:30.487] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20341} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A6810212011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 6810212011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:07:30.464', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Showerheads(淋浴喷头)/Handheld Showerheads(手持淋浴喷头)'
where id = 295
[2025-06-15 19:07:32.016] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20299} executed. select count(*) as total
from zz_fruugo_product_sku
where (f_created_at between TIMESTAMP '2025-06-15 00:00:00.000' and TIMESTAMP '2025-06-15 23:59:59.999')
[2025-06-15 19:07:32.054] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20342} executed. select id, stat_date, collect_offer_total, collect_offer_pending, collect_offer_processed
	, collect_offer_new, collect_sale_total, collect_sale_new, seller_total, seller_new
	, product_total, product_new, sku_total, sku_new, f_created_at as createdAt
	, f_last_updated_at as lastUpdatedAt
from zz_fruugo_monitor
where id = '703721994465583173'
[2025-06-15 19:07:32.108] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20343} executed. update zz_fruugo_monitor
set stat_date = TIMESTAMP '2025-06-15 00:00:00.000', collect_offer_total = 581232, collect_offer_pending = 120267, collect_offer_processed = 371647, collect_offer_new = 0, collect_sale_total = 8180610, collect_sale_new = 0, seller_total = 2960, seller_new = 0, product_total = 1032438, product_new = 0, sku_total = 2684538, sku_new = 0, f_created_at = TIMESTAMP '2025-06-15 00:00:00.000', f_last_updated_at = TIMESTAMP '2025-06-15 19:07:32.016'
where id = '703721994465583173'
[2025-06-15 19:07:32.231] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20344} executed. select id, stat_date, collect_offer_total, collect_offer_pending, collect_offer_processed
	, collect_offer_new, collect_sale_total, collect_sale_new, seller_total, seller_new
	, product_total, product_new, sku_total, sku_new, f_created_at as createdAt
	, f_last_updated_at as lastUpdatedAt
from zz_fruugo_monitor
where (stat_date = '2025-06-15')
[2025-06-15 19:07:38.084] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20345} executed. SELECT COUNT( * ) AS total FROM zz_fruugo_collect_offer
[2025-06-15 19:07:38.904] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20346} executed. select count(*) as total
from zz_fruugo_collect_offer
where (request_status = 0)
[2025-06-15 19:07:40.450] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20348} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:07:40.481] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20349} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A15524348011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 15524348011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:07:40.450', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Household Cleaners(家用清洁工)/Kitchen Cleaners(厨房清洁工)/All-Purpose Surface Cleaners(通用表面清洁剂)'
where id = 296
[2025-06-15 19:07:43.459] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20347} executed. select count(*) as total
from zz_fruugo_collect_offer
where (request_status = 2)
[2025-06-15 19:07:45.527] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20350} executed. select count(*) as total
from zz_fruugo_collect_offer
where (f_created_at between TIMESTAMP '2025-06-15 00:00:00.000' and TIMESTAMP '2025-06-15 23:59:59.999')
[2025-06-15 19:07:50.468] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20352} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:07:50.492] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20353} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A18200967011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 18200967011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:07:50.468', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Cleaning Tools(清洁工具)/Cloths & Sponges(布和海绵)/Sponges & Scouring Pads(海绵和擦洗垫)'
where id = 297
[2025-06-15 19:08:00.460] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20354} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:08:00.489] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20355} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A6871688011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 6871688011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:08:00.461', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Showerheads(淋浴喷头)/Shower Body Sprays(淋浴体喷雾)'
where id = 298
[2025-06-15 19:08:10.635] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20356} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:08:10.662] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20357} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A10671060011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 10671060011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:08:10.635', source_category_path = 'Home & Kitchen/Home Décor Products/Decorative Pillows/Floor Pillows & Cushions'
where id = 299
[2025-06-15 19:08:20.446] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20358} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:08:20.474] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20359} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A10671058011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 10671058011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:08:20.446', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Kids'' Bedding(儿童床上用品)/Quilts & Sets(被子和套装)/Quilt Sets(被子套装)'
where id = 300
[2025-06-15 19:08:30.449] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20360} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:08:30.476] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10015, pstmt-20361} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A15524343011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 15524343011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:08:30.450', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Household Cleaners(家用清洁工)/Kitchen Cleaners(厨房清洁工)/Cooktop Cleaners(炉灶面清洁剂)'
where id = 301
[2025-06-15 19:08:31.113] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20351} executed. SELECT COUNT( * ) AS total FROM zz_fruugo_collect_sale
[2025-06-15 19:08:31.174] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20362} executed. select count(*) as total
from zz_fruugo_collect_sale
where (f_created_at between TIMESTAMP '2025-06-15 00:00:00.000' and TIMESTAMP '2025-06-15 23:59:59.999')
[2025-06-15 19:08:31.314] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20363} executed. SELECT COUNT( * ) AS total FROM zz_fruugo_seller
[2025-06-15 19:08:31.330] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20364} executed. select count(*) as total
from zz_fruugo_seller
where (f_created_at between TIMESTAMP '2025-06-15 00:00:00.000' and TIMESTAMP '2025-06-15 23:59:59.999')
[2025-06-15 19:08:40.488] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20366} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:08:40.520] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20367} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A1272504011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 1272504011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:08:40.489', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Showerheads(淋浴喷头)/Showerhead Filters(淋浴喷头过滤器)'
where id = 302
[2025-06-15 19:08:50.467] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20368} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:08:50.491] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20369} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A18200968011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 18200968011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:08:50.468', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Cleaning Tools(清洁工具)/Dusting Tools(除尘工具)'
where id = 303
[2025-06-15 19:08:51.368] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20365} executed. SELECT COUNT( * ) AS total FROM zz_fruugo_product
[2025-06-15 19:09:00.461] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20371} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:09:00.499] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20372} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3294165011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3294165011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:09:00.462', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Bedroom Furniture(卧室家具)/Vanities & Vanity Benches(梳妆台和梳妆台)/Vanities(虚荣)'
where id = 304
[2025-06-15 19:09:10.502] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20373} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:09:10.546] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20374} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3732331%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3732331, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:09:10.502', source_category_path = 'Home & Kitchen/Home Décor Products/Decorative Pillows/Pillow Inserts'
where id = 305
[2025-06-15 19:09:20.458] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20375} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:09:20.483] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20376} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A362538011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 362538011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:09:20.459', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Kids'' Bedding(儿童床上用品)/Quilts & Sets(被子和套装)/Quilts(被子)'
where id = 306
[2025-06-15 19:09:30.447] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20370} executed. select count(*) as total
from zz_fruugo_product
where (f_created_at between TIMESTAMP '2025-06-15 00:00:00.000' and TIMESTAMP '2025-06-15 23:59:59.999')
[2025-06-15 19:09:30.471] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20377} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:09:30.493] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20379} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A15524344011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 15524344011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:09:30.471', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Household Cleaners(家用清洁工)/Kitchen Cleaners(厨房清洁工)/Degreasers(脱脂剂)'
where id = 307
[2025-06-15 19:09:40.470] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20380} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:09:40.501] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20381} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A18200969011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 18200969011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:09:40.471', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Cleaning Tools(清洁工具)/Gloves(手套)'
where id = 308
[2025-06-15 19:09:50.456] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20382} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:09:50.486] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20383} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A16183925011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 16183925011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:09:50.457', source_category_path = 'Home & Kitchen/Home Décor Products/Decorative Pillows/Poufs'
where id = 309
[2025-06-15 19:10:00.457] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20384} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:10:00.486] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20385} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3294166011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3294166011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:10:00.457', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Bedroom Furniture(卧室家具)/Vanities & Vanity Benches(梳妆台和梳妆台)/Vanity Benches(梳妆台)'
where id = 310
[2025-06-15 19:10:10.464] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20386} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:10:10.489] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20387} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A85975011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 85975011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:10:10.465', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Toilet Accessories(马桶配件)/Toilet Brushes & Holders(马桶刷和支架)'
where id = 311
[2025-06-15 19:10:20.463] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20388} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:10:20.492] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20389} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3732341%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3732341, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:10:20.464', source_category_path = 'Home & Kitchen/Home Décor Products/Decorative Pillows/Throw Pillow Covers'
where id = 312
[2025-06-15 19:10:30.450] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20390} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:10:30.472] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10016, pstmt-20391} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A13750181%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 13750181, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:10:30.450', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Kids'' Bedding(儿童床上用品)/Sheets & Pillowcases(床单和枕套)/Fitted Sheets(合身的床单)'
where id = 313
[2025-06-15 19:10:40.455] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20392} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:10:40.481] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20393} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A15524347011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 15524347011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:10:40.456', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Household Cleaners(家用清洁工)/Kitchen Cleaners(厨房清洁工)/Dishwasher & Garbage Disposal Cleaners(洗碗机和垃圾处理清洁剂)/Dishwasher Cleaners(洗碗机清洁剂)'
where id = 314
[2025-06-15 19:10:50.452] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20394} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:10:50.476] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20395} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3731791%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3731791, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:10:50.454', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Toilet Accessories(马桶配件)/Toilet Lid & Tank Covers(马桶盖和水箱盖)'
where id = 315
[2025-06-15 19:11:00.479] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20396} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:11:00.508] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20397} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A18200972011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 18200972011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:11:00.479', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Cleaning Tools(清洁工具)/Mopping(拖地)/Mop Heads(拖把头)'
where id = 316
[2025-06-15 19:11:10.482] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20398} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:11:10.507] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20399} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3732321%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3732321, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:11:10.482', source_category_path = 'Home & Kitchen/Home Décor Products/Decorative Pillows/Throw Pillows'
where id = 317
[2025-06-15 19:11:20.457] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20400} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:11:20.482] [DEBUG] [http-nio-32000-exec-9] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20401} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3733831%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3733831, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:11:20.458', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Dining Room Furniture(餐厅家具)/Buffets & Sideboards(自助餐和餐具柜)'
where id = 318
[2025-06-15 19:11:30.466] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20402} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:11:30.505] [DEBUG] [http-nio-32000-exec-8] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20403} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A13750191%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 13750191, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:11:30.467', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Kids'' Bedding(儿童床上用品)/Sheets & Pillowcases(床单和枕套)/Flat Sheets(平板)'
where id = 319
[2025-06-15 19:11:32.690] [DEBUG] [scheduling-1] [druid.sql.Statement] [?] [?] - {conn-10010, pstmt-20378} executed. SELECT COUNT( * ) AS total FROM zz_fruugo_product_sku
[2025-06-15 19:11:40.464] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20405} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:11:40.493] [DEBUG] [http-nio-32000-exec-1] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20406} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A15524346011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 15524346011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:11:40.465', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Household Cleaners(家用清洁工)/Kitchen Cleaners(厨房清洁工)/Dishwasher & Garbage Disposal Cleaners(洗碗机和垃圾处理清洁剂)/Garbage Disposal Cleaners(垃圾处理清洁剂)'
where id = 320
[2025-06-15 19:11:50.456] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20407} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:11:50.491] [DEBUG] [http-nio-32000-exec-6] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20408} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A6810481011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 6810481011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:11:50.456', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Toilet Accessories(马桶配件)/Toilet Lid Decals(马桶盖贴花)'
where id = 321
[2025-06-15 19:12:00.466] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20409} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:12:00.492] [DEBUG] [http-nio-32000-exec-2] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20410} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=handmade&rh=n%3A18200973011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 18200973011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:12:00.467', source_category_path = 'Handmade Products(手工产品)/Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Cleaning Tools(清洁工具)/Mopping(拖地)/Mop Pads(拖把垫)'
where id = 322
[2025-06-15 19:12:10.462] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20411} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:12:10.486] [DEBUG] [http-nio-32000-exec-5] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20412} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3291684011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3291684011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:12:10.462', source_category_path = 'Home & Kitchen/Home Décor Products/Draft Stoppers'
where id = 323
[2025-06-15 19:12:20.480] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20413} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:12:20.508] [DEBUG] [http-nio-32000-exec-10] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20414} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A3733821%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 3733821, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:12:20.481', source_category_path = 'Home & Kitchen(家庭和厨房)/Furniture(家具)/Dining Room Furniture(餐厅家具)/Chairs(椅子)'
where id = 324
[2025-06-15 19:12:30.455] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20415} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:12:30.477] [DEBUG] [http-nio-32000-exec-3] [druid.sql.Statement] [?] [?] - {conn-10017, pstmt-20416} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A362537011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 362537011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:12:30.455', source_category_path = 'Home & Kitchen(家庭和厨房)/Bedding(床上用品)/Kids'' Bedding(儿童床上用品)/Sheets & Pillowcases(床单和枕套)/Pillow Shams(枕头沙姆斯)'
where id = 325
[2025-06-15 19:12:40.458] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20417} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:12:40.484] [DEBUG] [http-nio-32000-exec-7] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20418} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A15524351011%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 15524351011, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:12:40.458', source_category_path = 'Home & Kitchen(家庭和厨房)/Cleaning Supplies(清洁用品)/Household Cleaners(家用清洁工)/Kitchen Cleaners(厨房清洁工)/Oven & Grill Cleaners(烤箱和烤架清洁剂)'
where id = 326
[2025-06-15 19:12:50.445] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20419} executed. select id, url, source_category_id, status, max_pages_to_crawl
	, crawled_pages, error_message, created_at, updated_at, source_category_path
	, temu_category_id, temu_category_path
from zz_amazon_list_tasks
where (status = 0)
order by created_at desc
limit 1
[2025-06-15 19:12:50.473] [DEBUG] [http-nio-32000-exec-4] [druid.sql.Statement] [?] [?] - {conn-10018, pstmt-20420} executed. update zz_amazon_list_tasks
set url = 'https://www.amazon.com/s?i=kitchen&rh=n%3A16350361%2Cp_76%3A2661625011%2Cp_36%3A-1000', source_category_id = 16350361, status = 1, max_pages_to_crawl = 400, crawled_pages = 0, created_at = TIMESTAMP '2025-06-15 10:24:19.000', updated_at = TIMESTAMP '2025-06-15 19:12:50.446', source_category_path = 'Home & Kitchen(家庭和厨房)/Bath(浴缸)/Bathroom Accessories(浴室配件)/Toilet Accessories(马桶配件)/Toilet Paper Holders(卫生纸架)'
where id = 327
