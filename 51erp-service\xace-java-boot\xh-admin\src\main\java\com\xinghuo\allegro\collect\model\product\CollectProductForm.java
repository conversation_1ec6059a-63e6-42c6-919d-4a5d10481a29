package com.xinghuo.allegro.collect.model.product;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import jakarta.validation.constraints.NotBlank;

import java.util.Date;

@Data
public class CollectProductForm {

    @Schema(description = "产品ID", required = true)
    @NotBlank(message = "产品ID不能为空")
    private String productId;

    @Schema(description = "SKU ID")
    private Integer skuId;

    @Schema(description = "分类ID")
    private String categoryId;

    @Schema(description = "Offer名称")
    private String offerName;

    @Schema(description = "产品名称")
    private String productName;

    @Schema(description = "购买数量")
    private Integer buyerQuantity;

    @Schema(description = "销售日期")
    private String saleDate;

    @Schema(description = "产品Offer数量")
    private Integer productOffersCount;

    @Schema(description = "状态")
    private Integer status;
}
