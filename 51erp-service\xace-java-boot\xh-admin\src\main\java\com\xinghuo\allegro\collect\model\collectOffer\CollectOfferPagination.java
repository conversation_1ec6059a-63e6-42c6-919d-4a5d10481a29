package com.xinghuo.allegro.collect.model.collectOffer;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xinghuo.common.base.model.Pagination;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 采集箱查询
 *
 * <AUTHOR>
 * @date 2024-10-20
 */
@Data
public class CollectOfferPagination extends Pagination {
    @Schema(description = "查询key")
    private String[] selectKey;
    @Schema(description = "json")
    private String json;
    @Schema(description = "数据类型 0-当前页，1-全部数据")
    private String dataType;
    @Schema(description = "高级查询")
    private String superQueryJson;
    @Schema(description = "功能id")
    private String moduleId;
    @Schema(description = "菜单id")
    private String menuId;


    @Schema(description = "排序字段-6个排序 ，例如： 创建时间(create_time)，更新时间(push_time)  正序，倒序 示例：(create_time|desc)  ")
    private String listOrder;

    @Schema(description = "店铺ID，数组")
    private List<String> sellerIdList;


    @Schema(description = "时间类型： 创建时间(create_time)，更新时间(_time)")
    private String dateType;

    @Schema(description = "下单时间-开始")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @Schema(description = "下单时间-结束")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    @Schema(description = "搜索类型  （SKU:externalId|EQ, 分类ID:category_id|EQ,分类名称模糊：categoryName|LIKE，失败信息： error_msg|LIKE, 备注：note|LIKE）,  示例：offer_id|EQ   ,方式：EQ-精确，LIKERIGHT-右模糊 ，LIKE-全模糊")
    private String searchType;

    @Schema(description = "搜索关键字 ")
    private String searchKey;

    private String sellerId;



}
