package com.xinghuo.allegro.collect.dao;

import com.xinghuo.allegro.collect.entity.CollectOfferEntity;
import com.xinghuo.common.base.dao.XHBaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.ResultType;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 店铺token mapper
 *
 * <AUTHOR>
 * @date 2024-06-02
 */
public interface CollectOfferMapper extends XHBaseMapper<CollectOfferEntity> {

    /**
     * 未后台的数据，deal_status 设置为0
     *
     * @return
     */
    int updateDealStatusClear();

    List<CollectOfferEntity> collectListByOfferId(@Param("offerId") String offerId);

    List<CollectOfferEntity> blockedSellerOfferList();

    @Select("SELECT COUNT(0) FROM zz_collect_offer_remove WHERE F_LastModifyTime > DATE(NOW())")
    @ResultType(Long.class)
    Long todayDelete();

    @Select("SELECT COUNT(0)  FROM zz_collect_offer t   WHERE t.req_finish_time > CURRENT_DATE AND t.req_finish_time < CURRENT_DATE + INTERVAL 1 DAY   AND t.request_status = 2")
    @ResultType(Long.class)
    Long todayCollect();

    @Select("SELECT COUNT(0) AS value FROM zz_collect_offer t  WHERE t.req_finish_time > CURRENT_DATE - INTERVAL 1 DAY AND t.req_finish_time < CURRENT_DATE   AND t.request_status = 2")
    @ResultType(Long.class)
    Long yesterdayCollect();


    /**
     * 更新产品的buyers_quantity
     */
    @Update("UPDATE zz_erp_product t " +
            "SET t.buyers_quantity = (SELECT COALESCE(SUM(buyers_quantity), 0) " +
            "FROM zz_collect_offer o WHERE o.sku_id = #{skuId}) " +
            "WHERE t.sku_id = #{skuId}")
    int updateBuyersQuantity(@Param("skuId") Integer skuId);



}

