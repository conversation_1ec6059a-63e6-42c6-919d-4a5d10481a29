package com.xinghuo.allegro.collect.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xinghuo.allegro.collect.dao.CollectAccountMapper;
import com.xinghuo.allegro.collect.entity.CollectAccountEntity;
import com.xinghuo.allegro.collect.service.CollectAccountService;
import com.xinghuo.common.base.service.BaseServiceImpl;
import org.springframework.stereotype.Service;

@Service
public class CollectAccountServiceImpl extends BaseServiceImpl<CollectAccountMapper, CollectAccountEntity> implements CollectAccountService {
    @Override
    public CollectAccountEntity getByActiveCode(String activeCode) {
        QueryWrapper<CollectAccountEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(CollectAccountEntity::getActiveCode, activeCode);
        return this.getOne(queryWrapper);
    }
}
