package com.xinghuo.allegro.manage.controller;


import com.xinghuo.allegro.manage.entity.SysExchangeRateEntity;
import com.xinghuo.allegro.manage.model.currency.CurrencyModel;
import com.xinghuo.allegro.manage.model.currency.RateModel;
import com.xinghuo.allegro.manage.model.currency.RatePagination;
import com.xinghuo.allegro.manage.service.SysExchangeRateService;
import com.xinghuo.allegro.manage.util.ExchangeUtil;
import com.xinghuo.common.annotation.NoDataSourceBind;
import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.constant.MsgCode;
import com.xinghuo.common.exception.DataException;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.common.util.core.RandomUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * Allegro店铺管理
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@Tag(name = "汇率管理", description = "汇率管理")
@RequestMapping("/api/allegro/sys/exchangeRate")
public class SysExchangeRateController {

    @Autowired
    private SysExchangeRateService sysExchangeRateService;



   /**
 * 列表
 */
@Operation(summary = "汇率列表")
@PostMapping("/getList")
public ActionResult list(@RequestBody RatePagination pagination) {
    List<SysExchangeRateEntity> list = sysExchangeRateService.getList(pagination);
    List<RateModel> listVO = BeanCopierUtils.copyList(list, RateModel.class);
    PaginationVO page = BeanCopierUtils.copy(pagination, PaginationVO.class);
    return ActionResult.page(listVO, page);
}

/**
 * 获取详情(编辑页)--转换数据
 */
@Operation(summary = "汇率-详情")
@GetMapping("/{id}")
@Parameters({
    @Parameter(name = "id", description = "主键", required = true)
})
public ActionResult info(@PathVariable("id") String id) {
    SysExchangeRateEntity entity = sysExchangeRateService.getById(id);
    RateModel infoVo = BeanCopierUtils.copy(entity, RateModel.class);
    return ActionResult.success(infoVo);
}

/**
 * 编辑
 */
@PutMapping("/{id}")
@Operation(summary = "汇率-更新")
public ActionResult update(@PathVariable("id") String id, @RequestBody @Valid RateModel exchangeRateModel) throws DataException {
    SysExchangeRateEntity entity = sysExchangeRateService.getById(id);
    if (entity != null) {
        entity.setId(id);
        entity.setCurrency(exchangeRateModel.getCurrency());
        entity.setRate(exchangeRateModel.getRate());
        entity.setNote(exchangeRateModel.getNote());
        sysExchangeRateService.updateById(entity);
        return ActionResult.success(MsgCode.SU004.get());
    }
    return ActionResult.success(MsgCode.FA002.get());
}

    @Operation(summary = "汇率-新增")
    @PostMapping
    public ActionResult create(@RequestBody @Valid RateModel model) {
        SysExchangeRateEntity entity  = BeanCopierUtils.copy(model, SysExchangeRateEntity.class);
        entity.setId(RandomUtil.snowId());
        sysExchangeRateService.save(entity);
        return ActionResult.success("新建成功");
    }

@DeleteMapping("/{id}")
@Parameters({
    @Parameter(name = "id", description = "主键", required = true)
})
@Operation(summary = "汇率单个删除")
public ActionResult delete(@PathVariable("id") String id) throws DataException {
    SysExchangeRateEntity entity = sysExchangeRateService.getById(id);
    if (entity != null) {
        sysExchangeRateService.removeById(id);
        return ActionResult.success(MsgCode.SU003.get());
    }
    return ActionResult.success(MsgCode.FA002.get());
}





    /**
     * 获取详情(编辑页)--转换数据
     */
    @Operation(summary = "汇率转换")
    @GetMapping("/exchange")
    @Parameters({
            @Parameter(name = "s", description = "源币种", required = true),
            @Parameter(name = "d", description = "目标币种", required = true)
    })
    public ActionResult<BigDecimal> exchange(@RequestParam @NotBlank String s,@RequestParam @NotBlank String d) {
        SysExchangeRateEntity source = sysExchangeRateService.getInfoByCurrency(s,null);
        SysExchangeRateEntity target = sysExchangeRateService.getInfoByCurrency(d,null);
        if (source == null || target == null) {
            return ActionResult.fail("币种在系统中未设置，请联系管理员");
        }

        BigDecimal sourceRate = source.getRate();
        BigDecimal targetRate = target.getRate();
        if (sourceRate.compareTo(BigDecimal.ZERO) == 0 || targetRate.compareTo(BigDecimal.ZERO) == 0) {
            return ActionResult.fail("币种在系统中未设置，请联系管理员");
        }
        if (sourceRate.compareTo(targetRate) == 0) {
            return ActionResult.fail("源币种和目标币种相同，请重新选择");
        }
        BigDecimal rate = sourceRate.divide(targetRate, 4, BigDecimal.ROUND_HALF_UP);
        return ActionResult.success(rate);
    }


    @Operation(summary = "同步汇率")
    @GetMapping("/syncRate")
    @NoDataSourceBind
    public ActionResult<String> syncRate() {
        CurrencyModel currency = ExchangeUtil.getCurrency();
        if (currency != null) {
            sysExchangeRateService.syncRate(currency);
        }
       return ActionResult.success("成功同步汇率！");
    }








}
