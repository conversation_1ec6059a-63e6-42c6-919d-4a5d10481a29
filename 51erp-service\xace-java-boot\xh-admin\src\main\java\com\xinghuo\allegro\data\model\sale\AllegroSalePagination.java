package com.xinghuo.allegro.data.model.sale;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xinghuo.common.base.model.Pagination;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * Allegro销售数据查询分页模型
 *
 * <AUTHOR>
 * @date 2024-08-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AllegroSalePagination extends Pagination {
    @Schema(description = "查询key")
    private String[] selectKey;

    @Schema(description = "json")
    private String json;

    @Schema(description = "数据类型 0-当前页，1-全部数据")
    private String dataType;

    @Schema(description = "高级查询")
    private String superQueryJson;

    @Schema(description = "功能id")
    private String moduleId;

    @Schema(description = "菜单id")
    private String menuId;

    @Schema(description = "卖家ID")
    private Integer sellerId;

    @Schema(description = "产品ID")
    private Long productId;

    @Schema(description = "SKU ID")
    private Integer skuId;

    @Schema(description = "销售日期-开始")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startDate;

    @Schema(description = "销售日期-结束")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endDate;

    @Schema(description = "搜索类型")
    private String searchType;

    @Schema(description = "搜索关键字")
    private String searchKey;

    @Schema(description = "分类ID")
    private Long categoryId;

    @Schema(description = "分类路径")
    private String categoryPath;

    @Schema(description = "排序字段，例如：created_at|desc")
    private String listOrder;

    @Schema(description = "最小销售数量")
    private Integer minSalesNum;

    @Schema(description = "最大销售数量")
    private Integer maxSalesNum;
}
