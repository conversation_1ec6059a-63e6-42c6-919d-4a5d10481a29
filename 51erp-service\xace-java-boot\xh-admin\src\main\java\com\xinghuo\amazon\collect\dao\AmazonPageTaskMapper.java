package com.xinghuo.amazon.collect.dao;

import com.xinghuo.amazon.collect.entity.AmazonPageTaskEntity;
import com.xinghuo.common.base.dao.XHBaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * Amazon页面任务Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Mapper
public interface AmazonPageTaskMapper extends XHBaseMapper<AmazonPageTaskEntity> {

    /**
     * 更新任务状态为完成
     * @param taskId 任务ID
     * @return 更新结果
     */
    @Update("UPDATE zz_amazon_page_tasks SET status = 'completed', updated_at = NOW() WHERE id = #{taskId}")
    int updateTaskCompleted(@Param("taskId") String taskId);

    /**
     * 更新任务状态为失败
     * @param taskId 任务ID
     * @param errorMessage 错误信息
     * @return 更新结果
     */
    @Update("UPDATE zz_amazon_page_tasks SET status = 'failed', error_message = #{errorMessage}, updated_at = NOW() WHERE id = #{taskId}")
    int updateTaskFailed(@Param("taskId") String taskId, @Param("errorMessage") String errorMessage);

    /**
     * 更新重试次数
     * @param taskId 任务ID
     * @param retryCount 重试次数
     * @return 更新结果
     */
    @Update("UPDATE zz_amazon_page_tasks SET retry_count = #{retryCount}, updated_at = NOW() WHERE id = #{taskId}")
    int updateRetryCount(@Param("taskId") Integer taskId, @Param("retryCount") Integer retryCount);
}
