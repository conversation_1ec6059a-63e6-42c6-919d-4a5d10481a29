package com.xinghuo.admin.aop;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

// 创建切面类
@Aspect
@Slf4j
@Component
public class MethodCountAspect {
    private ThreadLocal<Map<String, Integer>> countMap = new ThreadLocal<>();
    private ThreadLocal<Map<String, Long>> timeMap = new ThreadLocal<>();

    @Pointcut("execution(* com.xinghuo.*.service.impl.*.*(..)) ||execution(* com.xinghuo.*.*.service.impl.*.*(..))")
    public void classLevelMethodCount() {
    }

    @Around("classLevelMethodCount()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        long start = System.currentTimeMillis();
        Object result = joinPoint.proceed();
        long end = System.currentTimeMillis();

        String methodNameShort = joinPoint.getSignature().toShortString();
        String methodSignature = joinPoint.getSignature().toString();

        // 提取方法名
        String methodName = methodSignature.substring(methodSignature.lastIndexOf('.') + 1, methodSignature.indexOf('('));

        // 提取参数列表
        String params = methodSignature.substring(methodSignature.indexOf('(') + 1, methodSignature.indexOf(')'));

        // 格式化参数列表，以便展示
        params = params.replaceAll("\\s*,\\s*", ", "); // 去除参数中的空格

        String methodDescription = methodName + "(" + params + ")";
        System.out.println("Method description: " + methodDescription);

        if (countMap.get() == null || timeMap.get() == null) {
            countMap.set(new ConcurrentHashMap<>());
            timeMap.set(new ConcurrentHashMap<>());
        }

        countMap.get().put(methodNameShort, countMap.get().getOrDefault(methodName, 0) + 1);
        timeMap.get().put(methodNameShort, timeMap.get().getOrDefault(methodName, 0L) + (end - start));

        return result;
    }

    @Before("execution(* com.xinghuo.*.controller.*.*(..)) ||execution(* com.xinghuo.*.*.controller.*.*(..))")
    public void beforeController(JoinPoint joinPoint) {
        countMap.set(new ConcurrentHashMap<>());
        timeMap.set(new ConcurrentHashMap<>());
    }

    @After("execution(* com.xinghuo.*.controller.*.*(..)) ||execution(* com.xinghuo.*.*.controller.*.*(..))")
    public void afterController(JoinPoint joinPoint) {
        StringBuffer sbuf = new StringBuffer("Method summary:\n");
        // Sorting countMap by method name
        Map<String, Integer> sortedCountMap = countMap.get()
                .entrySet()
                .stream()
                .sorted(Comparator.comparing(Map.Entry::getKey))
                .collect(LinkedHashMap::new, (map, entry) -> map.put(entry.getKey(), entry.getValue()), Map::putAll);

        sortedCountMap.forEach((methodName, count) -> {
            long totalTime = timeMap.get().getOrDefault(methodName, 0L);
            long averageTime = count > 0 ? totalTime / count : 0;
            sbuf.append(methodName + ":\t Count=" + count + ", TotalTime=" + totalTime + " ms, AverageTime=" + averageTime + " ms\n");
        });
        log.info(sbuf.toString());
    }
}
