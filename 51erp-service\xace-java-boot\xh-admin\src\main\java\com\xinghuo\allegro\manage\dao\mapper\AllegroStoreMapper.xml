<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinghuo.allegro.manage.dao.AllegroStoreMapper">
    <select id="syncStoreOfferAndOrder">
        {call syncStoreOfferAndOrder()}
    </select>

    <select id="checkVioOfferToBatch">
        {call checkVioOfferToBatch()}
    </select>

    <select id="genTodayStoreInfo">
        {call genTodayStoreInfo()}
    </select>

    <update id="updateOrderCny" parameterType="String">
        UPDATE zz_allegro_order o
        SET o.payment_amount_cny = (
            SELECT o.payment_amount * e.rate
            FROM zz_sys_exchange_rate e
            WHERE e.currency = o.payment_currency and e.f_tenantId=o.f_tenantId
        )
        WHERE o.seller_id= #{sellerId} and (o.payment_amount_cny IS NULL OR o.payment_amount_cny = 0)
    </update>
    <update id="updateOrderItemCny" parameterType="String">
        UPDATE zz_allegro_order_item o
        SET o.price_cny = (
            SELECT o.price * e.rate
            FROM zz_sys_exchange_rate e
            WHERE e.currency = o.currency
        )
        WHERE o.seller_id= #{sellerId} and (o.price_cny IS NULL OR o.price_cny = 0)
    </update>


    <update id="updateOrderItemCnyTenant" parameterType="String">
        UPDATE zz_allegro_order_item o
        SET o.price_cny = (
            SELECT o.price * e.rate
            FROM zz_sys_exchange_rate e
            WHERE e.currency = o.currency and e.f_tenantId=o.f_tenantId
        )
        WHERE o.seller_id= #{sellerId} and (o.price_cny IS NULL OR o.price_cny = 0)
    </update>

    <update id="updateStoreOrders" parameterType="String">
        UPDATE zz_allegro_store
        SET orders = (
            SELECT COUNT(*)
            FROM zz_allegro_order
            WHERE seller_id = #{sellerId}
              AND payment_finished_at IS NOT NULL
              AND fulfillment_status != 'CANCELLED'
        ) where seller_id= #{sellerId}
    </update>

    <update id="updateStoreTodayOrders" parameterType="String">



        UPDATE zz_allegro_store
        SET today_orders = (
            SELECT COUNT(*)
            FROM zz_allegro_order
            WHERE seller_id = #{sellerId}
              AND payment_finished_at > CURRENT_DATE
              AND payment_finished_at IS NOT NULL
              AND fulfillment_status != 'CANCELLED'
        ) where seller_id= #{sellerId}
    </update>
</mapper>