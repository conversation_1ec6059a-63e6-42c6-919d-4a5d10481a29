package com.xinghuo.allegro.collect.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.xinghuo.allegro.collect.dao.CollectEventMapper;
import com.xinghuo.allegro.collect.entity.CollectEventEntity;
import com.xinghuo.allegro.collect.entity.CollectOfferEntity;
import com.xinghuo.allegro.collect.model.OfferJsonUtil;
import com.xinghuo.allegro.collect.service.CollectEventService;
import com.xinghuo.allegro.collect.service.CollectOfferService;
import com.xinghuo.allegro.manage.entity.AllegroStoreEntity;
import com.xinghuo.allegro.manage.service.AllegroStoreService;
import com.xinghuo.allegro.order.service.ErpOrderService;
import com.xinghuo.allegro.push.entity.*;
import com.xinghuo.allegro.push.service.*;
import com.xinghuo.allegro.sale.entity.AllegroNewProductEntity;
import com.xinghuo.allegro.sale.entity.AllegroOfferEntity;
import com.xinghuo.allegro.sale.service.AllegroNewProductService;
import com.xinghuo.allegro.sale.service.AllegroOfferService;
import com.xinghuo.allegro.util.AllegroConstant;
import com.xinghuo.common.base.service.BaseServiceImpl;
import com.xinghuo.common.util.core.RandomUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.common.util.json.JsonXhUtil;
import com.xinghuo.niceapi.allegro.WebHookAPI;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.util.StopWatch;

import java.util.*;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 自动检索事务，继承自BaseServiceImpl，提供特定的EAN编码管理功能。
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class CollectEventServiceImpl extends BaseServiceImpl<CollectEventMapper, CollectEventEntity> implements CollectEventService {

    // 每页拉取的数据量
    private static final int PAGE_SIZE = 1000;

    @Autowired
    private AllegroNewProductService allegroNewProductService;

    @Autowired
    private AllegroStoreService allegroStoreService;

    @Autowired
    private CollectVioWordService collectVioWordService;


    @Autowired
    private AllegroCategoryService allegroCategoryService;

    @Autowired
    private AllegroBrandService allegroBrandService;
    @Autowired
    private AllegroOfferService allegroOfferService;

    @Autowired
    private CollectOfferService collectOfferService;

    @Autowired
    private ErpProductService erpProductService;
    @Autowired
    private ErpOrderService erpOrderService;

    @Autowired
    private ErpProductVioService erpProductVioService;

    @Autowired
    private PushBatchService pushBatchService;



    @Autowired
    private PlatformTransactionManager transactionManager;

    private static String getBrand(JsonNode parametersNode) {
        if (parametersNode.isArray()) {
            for (JsonNode parameter : parametersNode) {
                if (parameter.path("name").asText().equals("Marka")) {
                    return parameter.path("valuesLabels").get(0).asText();
                }
            }
        }
        return null;
    }

    private static Double getWeight(JsonNode parametersNode) {
        if (parametersNode.isArray()) {
            for (JsonNode parameter : parametersNode) {
                //获取包装的重量
                if (parameter.path("id").asText().equals("17448")) {
                    return parameter.path("values").get(0).asDouble();
                }
            }
        }
        return null;
    }

    @Override
    public int checkCollectOfferVio() {

        long start = System.currentTimeMillis();
        int size = collectOfferService.updateDealStatusClear();
        log.info("定时检查采集条目的侵权词，待处理条目：" + size);
        //每次取1000条数据，每次处理完之后，再取下一页数据，直到没有数据为止。
        List<CollectOfferEntity> list = collectOfferService.dealDataList();
        AtomicInteger vioNum = new AtomicInteger();
        Set<String> vioWords = collectVioWordService.getValidList(null);
        int page = 0;

        while (!list.isEmpty()) {

            log.info("定时检查采集条目:处理进度，1000条/页，当前页数：", page++);

            list.forEach(collectOfferEntity -> {
                //判断 name 是否包含侵权词，如果包含设置vio_status 为true
                String offerName = StrXhUtil.emptyToDefault(collectOfferEntity.getOfferName(), "");
                boolean containsVioWord = vioWords.stream()
                        .anyMatch(vioWord -> offerName.toLowerCase().contains(vioWord.toLowerCase()));
                if (containsVioWord) {
                    List<String> matchingVioWords = vioWords.stream()
                            .filter(vioWord -> offerName.toLowerCase().contains(vioWord.toLowerCase()))
                            .toList();
                    if (!matchingVioWords.isEmpty()) {
                        // 这里可以处理匹配到的侵权词
                        log.warn("定时检查collectOffer,匹配到的侵权词: " + matchingVioWords);
                        collectOfferEntity.setNote("" + matchingVioWords);
                        collectOfferEntity.setLastModifyTime(new Date());
                        vioNum.getAndIncrement();
                    }
                }

                collectOfferEntity.setDealStatus(1);
                collectOfferService.updateById(collectOfferEntity);

            });

            list = collectOfferService.dealDataList();
            if (list.isEmpty()) {
                break;
            }


        }

        long costTime = System.currentTimeMillis() - start;
        String message = "定时检查采集条目的侵权词，处理条目：" + size + ",新发现侵权条目：" + vioNum.get() + ",耗时：" + costTime + "ms";
        log.info(message);
        WebHookAPI.sendMessage(message);
        return vioNum.get();

    }

    /**
     * 检查侵权词和目录管理
     */
    @Override
    public int checkNewProductVio(String tenantId) {
        Set<String> vioWords = collectVioWordService.getValidList(null);
        Set<String> probitCategoryList = allegroCategoryService.getProbitList(tenantId);
        Set<String> brandList = allegroBrandService.getValidList(tenantId);


        boolean hasMoreData = true;
        Integer currentPage = 1;
        Date date = new Date();



        List<AllegroNewProductEntity> newVioProductList = new ArrayList<>();


        while (hasMoreData) {
            log.info("=======================================currentPage:{}============", currentPage);
            List<AllegroNewProductEntity> list = allegroNewProductService.noDealOfferList(1000);
            if (list.isEmpty()) {
                hasMoreData = false;
            } else {

                for (AllegroNewProductEntity entity : list) {
                    boolean isValid = true;
                    //第一次：更新offer
                    ObjectNode productNode = JsonXhUtil.parseObject(entity.getMainJson());
                    JsonNode parametersNode = productNode.get("parameters");
                    String brand = getBrand(parametersNode);
                    if (brand != null) {
                        log.debug("Brand: " + brand);
                        entity.setBrand(brand);
                        if (brandList.contains(brand.toLowerCase())) {
                            entity.setNote(StrXhUtil.emptyToDefault(entity.getNote(), "") + ",品牌属于限制类，不能发布，品牌名称: " + brand);
                            isValid = false;
                        }
//                        updateProduct(entity);
                    } else {
                        log.debug("Brand not found");
                    }

                    // TODO 租户33不用去

                    Double weight = getWeight(parametersNode);
                    if (weight != null) {
                        log.debug("Weight: " + weight);
                        entity.setWeight(weight);

                        if (  weight > 5) {
                            entity.setNote(StrXhUtil.emptyToDefault(entity.getNote(), "") + ",重量大于5KG，不能发布，重量: " + weight);
                            isValid = false;
                        }
                    }

                    //判断 name 是否包含侵权词，如果包含设置vio_status 为true
                    String productName = StrXhUtil.emptyToDefault(entity.getName(), "");
                    boolean containsVioWord = vioWords.stream()
                            .anyMatch(vioWord -> productName.toLowerCase().contains(vioWord.toLowerCase()));
                    if (containsVioWord) {
                        List<String> matchingVioWords = vioWords.stream()
                                .filter(vioWord -> productName.toLowerCase().contains(vioWord.toLowerCase()))
                                .toList();
                        if (!matchingVioWords.isEmpty()) {
                            // 这里可以处理匹配到的侵权词
                            log.warn("匹配到的侵权词: " + matchingVioWords);
                            entity.setNote(StrXhUtil.emptyToDefault(entity.getNote(), "") + ",productName匹配到的侵权词: " + matchingVioWords);
                            entity.setUpdateTime(new Date());
                            isValid = false;

                        }
                    }



                    //判断 是否是目录限制
                    if (probitCategoryList.contains(entity.getCategoryId())) {
                        entity.setNote(StrXhUtil.emptyToDefault(entity.getNote(), "") + ",目录禁止推送: ");
                        isValid = false;
                    }
                    //正常
                    entity.setStatus(Boolean.TRUE);
                    entity.setDealStatus(Boolean.TRUE);
                    if (!isValid) {
                        //异常
                        entity.setStatus(Boolean.FALSE);
                        newVioProductList.add(entity);
                    }
                    entity.setUpdateTime(new Date());
                    updateNewProduct(entity);
                }
                currentPage++;
            }

        }


        for (AllegroNewProductEntity product : newVioProductList) {
//            allegroProductService.updateById(product);
            CollectEventEntity eventEntity = new CollectEventEntity();
            eventEntity.setId(RandomUtil.snowId());
            eventEntity.setEventDesc("PRODUCT:" + product.getId() + ":" + product.getNote());
            eventEntity.setEventStatus(0);
            eventEntity.setEventType("PRODUCT");
            eventEntity.setCreateTime(new Date());
            super.save(eventEntity);
        }
        return newVioProductList.size();
    }

    @Override
    public int checkErpProductVio(String tenantId) {
        Set<String> vioWords = collectVioWordService.getVioList(tenantId);
        Set<String> lossWords = collectVioWordService.getLossList(tenantId);
        Set<String> notFoundWords = collectVioWordService.getNotFoundList(tenantId);
        Set<String> probitCategoryList = allegroCategoryService.getProbitList(tenantId);
        Set<String> brandList = allegroBrandService.getValidList(tenantId);
        List<CollectOfferEntity> blockedSellerOfferList = collectOfferService.blockedSellerOfferList(tenantId);

        long start = System.currentTimeMillis();
        int dealNum = erpProductService.updateDealStatusClear(tenantId);
        log.info("租户：{},Erp待检查数据有:{}", tenantId, dealNum);

        boolean hasMoreData = true;
        Integer currentPage = 0;


        List<ErpProductEntity> newVioProductList = new ArrayList<>();


        while (hasMoreData) {
            log.info("Erp待检查数据,当前页面数：" + (currentPage++));
            List<ErpProductEntity> list = erpProductService.dealDataList(tenantId);
            if (list.isEmpty()) {
                hasMoreData = false;
            } else {

                for (ErpProductEntity entity : list) {
                    int deleteVioNum = erpProductVioService.removeBySkuId(entity.getSkuId());
                    if (deleteVioNum>0) {
                        log.info("skuId:{},成功删除侵权数据结果数：{}",entity.getSkuId(),deleteVioNum);
                    }

                    List<ErpProductVioEntity> vioList = new ArrayList<>();


                    boolean isValid = true;
                    //标记已处理
                    entity.setDealStatus(Boolean.TRUE);
                    entity.setOfferName(OfferJsonUtil.trunTitle75(entity.getOfferName()));
                    //判断 name 是否包含侵权词，如果包含设置vio_status 为true
                    String productName = StrXhUtil.emptyToDefault(entity.getName(), "");
                    String offerName = StrXhUtil.emptyToDefault(entity.getOfferName(), "");
                    String product_offer_name = productName + offerName;



                    boolean containsVioWord = vioWords.stream()
                            .anyMatch(word -> product_offer_name.toLowerCase().contains(word.toLowerCase()));
                    if (containsVioWord) {
                        List<String> matchingVioWords = vioWords.stream()
                                .filter(word -> product_offer_name.toLowerCase().contains(word.toLowerCase()))
                                .toList();
                        if (!matchingVioWords.isEmpty()) {
                            for(String vioWord:matchingVioWords){
                                CollectVioWordEntity vioWordEntity = collectVioWordService.getByTitle(vioWord,tenantId);
                                if(vioWordEntity!=null){
                                    vioList.add(new ErpProductVioEntity(entity.getSkuId(), "VIOWORD", vioWordEntity.getId(), vioWord, "侵权："+vioWord,tenantId));
                                }else{
                                    log.warn("未找到对应的VioWord实体："+vioWord);
                                }
                            }

                        }
                    }

                    boolean containsLossWord = lossWords.stream()
                            .anyMatch(lossWord -> product_offer_name.toLowerCase().contains(lossWord.toLowerCase()));
                    if (containsLossWord) {
                        List<String> matchingLossWords = lossWords.stream()
                                .filter(lossWord -> product_offer_name.toLowerCase().contains(lossWord.toLowerCase()))
                                .toList();
                        if (!matchingLossWords.isEmpty()) {
                            for(String vioWord:matchingLossWords){
                                CollectVioWordEntity vioWordEntity = collectVioWordService.getByTitle(vioWord,tenantId);
                                if(vioWordEntity!=null){
                                    vioList.add(new ErpProductVioEntity(entity.getSkuId(), "LOSS", vioWordEntity.getId(), vioWord, "亏本："+vioWord,tenantId));
                                }else{
                                    log.warn("未找到对应的VioWord实体："+vioWord);
                                }
                            }

                        }
                    }

                    boolean containsNotFoundWord = notFoundWords.stream()
                            .anyMatch(word -> product_offer_name.toLowerCase().contains(word.toLowerCase()));
                    if (containsNotFoundWord) {
                        List<String> matchingNotFoundWords = notFoundWords.stream()
                                .filter(word -> product_offer_name.toLowerCase().contains(word.toLowerCase()))
                                .toList();
                        if (!matchingNotFoundWords.isEmpty()) {
                            for(String vioWord:matchingNotFoundWords){
                                CollectVioWordEntity vioWordEntity = collectVioWordService.getByTitle(vioWord,tenantId);
                                if(vioWordEntity!=null){
                                    vioList.add(new ErpProductVioEntity(entity.getSkuId(), "NOTFOUND", vioWordEntity.getId(), vioWord, "找不到："+vioWord,tenantId));
                                }else{
                                    log.warn("未找到对应的VioWord实体："+vioWord);
                                }
                            }

                        }
                    }



                    //判断 是否是目录限制
                    if (probitCategoryList.contains(entity.getCategoryId())) {
                        AllegroCategoryEntity category = allegroCategoryService.getSimpleInfoByCategoryId(entity.getCategoryId(), tenantId);
                        vioList.add(new ErpProductVioEntity(entity.getSkuId(), "CATEGORY", entity.getCategoryId(), category.getPath(), "目录禁推："+category.getPath(),tenantId));
//                        entity.setNote(StrXhUtil.emptyToDefault(entity.getNote(), "") + ",目录禁止推送: ");
//                        isValid = false;
                    }

                    //判断是否是黑名单卖家售卖的产品
                    blockedSellerOfferList.forEach(collectOfferEntity -> {
                        if (collectOfferEntity.getSkuId().equals(entity.getSkuId())) {
                            vioList.add(new ErpProductVioEntity(entity.getSkuId(), "SELLER", collectOfferEntity.getSellerId(), collectOfferEntity.getSellerId(), "卖家禁止推送："+collectOfferEntity.getSellerId(),tenantId));
                        }
                    });

                    AllegroNewProductEntity newProductEntity = allegroNewProductService.getById(entity.getProductId());
                    if (newProductEntity != null) {
                        //根据品牌判断，产品是否可用
                        if (StrXhUtil.isNotBlank(newProductEntity.getBrand())) {
                            if (brandList.contains(newProductEntity.getBrand().toLowerCase())) {
                                AllegroBrandEntity brandEntity = allegroBrandService.getByBrand(newProductEntity.getBrand(), tenantId);
                                if (brandEntity!=null){
                                    vioList.add(new ErpProductVioEntity(entity.getSkuId(), "BRAND", ""+brandEntity.getId(),  newProductEntity.getBrand() , "品牌限制："+newProductEntity.getBrand(),tenantId));
                                }
                                else{
                                    log.warn("品牌没有找到对应的实体：{}", newProductEntity.getBrand());
                                }
                            }
                        }
                        //重量
                        if(!( StrXhUtil.equalsIgnoreCase(entity.getTenantId(),"33"))) {
                            if (newProductEntity.getWeight() != null && newProductEntity.getWeight() > 5) {
                                vioList.add(new ErpProductVioEntity(entity.getSkuId(), "WEIGHT", "" + newProductEntity.getWeight(), "", "重量大于5KG，不能发布，重量：" + newProductEntity.getWeight(), tenantId));
                            }
                        }

                        if (StrXhUtil.isNotBlank(newProductEntity.getOfferRequirementId())
                                && !Objects.equals(newProductEntity.getOfferRequirementId(), "null")) {
                            vioList.add(new ErpProductVioEntity(entity.getSkuId(), "TEMP", ""+newProductEntity.getOfferRequirementId(), "", "关联指定报价的产品，不能使用，报价ID："+newProductEntity.getOfferRequirementId(),tenantId));
                        }
                    }
                    entity.setStatus(AllegroConstant.STATUS_GOOD_NORMAL);
                    if(!vioList.isEmpty() ){
                        erpProductVioService.saveBatch(vioList);
                    }

                    if(entity.getForcePush()==1){
                        entity.setStatus(AllegroConstant.STATUS_GOOD_NORMAL);
                    }else  if (entity.getForcePush()==-1){
                        entity.setStatus(AllegroConstant.STATUS_GOOD_BLOCKED);
                    }else{
                        if(vioList.size()>0){
                            entity.setStatus(AllegroConstant.STATUS_GOOD_BLOCKED);
                        }
                    }
                    entity.setUpdateTime(new Date());
                    updateErpProduct(entity);
                }
            }
        }
        String message = "租户："+ tenantId + "：定时全量检查erp产品，耗时：" + (System.currentTimeMillis() - start) + "ms" ;
        WebHookAPI.sendMessage(message);
        log.info(message);
        return newVioProductList.size();
    }

    /**
     * 获取到ACTIVE状态的offer 状态，进行对比
     */
    @Override
    public int checkOfferVio() {
        long start = System.currentTimeMillis();
        Set<String> vioWords = collectVioWordService.getValidList(null);
        boolean hasMoreData = true;
        Integer currentPage = 1;
        while (hasMoreData) {
            log.info("正在全量检查offer的侵权词，当前页面：" + (currentPage++));
            List<AllegroOfferEntity> list = allegroOfferService.dealDataList();
            if (list.isEmpty()) {
                hasMoreData = false;
            } else {

                for (AllegroOfferEntity entity : list) {
                    entity.setVioStatus(false);
                    entity.setDealStatus(1);
                    //判断 name 是否包含侵权词，如果包含设置vio_status 为true
                    String name = StrXhUtil.emptyToDefault(entity.getName(), "");
                    boolean containsVioWord = vioWords.stream()
                            .anyMatch(vioWord -> name.toLowerCase().contains(vioWord.toLowerCase()));
                    if (containsVioWord) {
                        List<String> matchingVioWords = vioWords.stream()
                                .filter(vioWord -> name.toLowerCase().contains(vioWord.toLowerCase()))
                                .toList();
                        if (!matchingVioWords.isEmpty()) {
                            log.warn("匹配到的侵权词: " + matchingVioWords);
                            entity.setNote(StrXhUtil.emptyToDefault(entity.getNote(), "") + ",productName匹配到的侵权词: " + matchingVioWords);
                            entity.setVioStatus(true);
                        }
                    }
                }
            }
            updateOffers(list);
        }

        log.info("全部offer 侵权词检查耗时：" + (System.currentTimeMillis() - start) + "ms");
        return 0;
    }




    public void updateOffers(List<AllegroOfferEntity> list) {
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        TransactionStatus status = transactionManager.getTransaction(def);
        try {
            allegroOfferService.saveOrUpdateBatch(list);
            transactionManager.commit(status);
        } catch (Exception e) {
            transactionManager.rollback(status);
            throw e;
        }
    }

    public void updateNewProduct(AllegroNewProductEntity entity) {
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        TransactionStatus status = transactionManager.getTransaction(def);
        try {
            allegroNewProductService.updateById(entity);
            transactionManager.commit(status);
        } catch (Exception e) {
            transactionManager.rollback(status);
            throw e;
        }
    }

    public void updateErpProduct(ErpProductEntity entity) {
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        TransactionStatus status = transactionManager.getTransaction(def);
        try {
            erpProductService.updateById(entity);
            transactionManager.commit(status);
        } catch (Exception e) {
            transactionManager.rollback(status);
            throw e;
        }
    }


    //事件1： 检索系统中 新增侵权词，检查线上的所有数据和本地的所有数据


    //事件2: category 是否发生更新，尽量全量检索，是否发生异常

    //检查是否存在已经是侵权词，allegro_product 中的状态还是不正常的。

    @Override
    @Async
    public void todaySummary(boolean isSummary) {
        List<AllegroStoreEntity> storeList = allegroStoreService.list();
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("同步订单和报价");
        log.info("每日总结，当前店铺数：" + storeList.size());
        AtomicInteger validNum = new AtomicInteger();
        // 创建一个具有指定并行度的 ForkJoinPool
        ForkJoinPool customPool = new ForkJoinPool(15);

        // 使用自定义的 ForkJoinPool 作为并行流的执行器   先转换为顺序流，以避免并行流的默认执行器
        storeList.parallelStream()
                .filter(store -> store.getStoreType() != 0)
                .sequential()
                .peek(store -> customPool.execute(() -> {
                    // 使用自定义的执行器
                    // 同步订单数
                    erpOrderService.syncOrder(store,null);
                    // 同步报价数
                    allegroOfferService.syncOffer(store.getSellerId(), true);
                    validNum.getAndIncrement();
                }))
                .collect(Collectors.toList());
        // 收集结果，确保所有任务完成

        stopWatch.stop();
        log.info("同步订单和报价耗时秒：" + stopWatch.lastTaskInfo().getTimeSeconds());
        stopWatch.start("同步写入店铺信息");

        //异步执行
        allegroStoreService.syncStoreOfferAndOrder();
        stopWatch.stop();

        if (isSummary) {
            //检查 categoryId ,已经放在 syncStoreOfferAndOrder。
            //检查 erp_product的检查词 的目录分类，侵权等，
            allegroStoreService.genTodayStoreInfo();

//            stopWatch.start("检查ERP_PRODUCT");
//
//            //根据当前活跃的店铺，进行数据的刷新
//            this.checkErpProductVio();
//            stopWatch.stop();
//            log.info("检查ERP_PRODUCT耗时秒：" + stopWatch.lastTaskInfo().getTimeSeconds());
        }

        //全量检查报价的违禁词
        stopWatch.start("检查在线报价侵权词");
        this.checkOfferVio();
        stopWatch.stop();
        log.info("检查在线报价侵权词耗时秒：" + stopWatch.lastTaskInfo().getTimeSeconds());
        stopWatch.start("检查并推送报价下架");
        allegroStoreService.checkVioOfferToBatch();
        pushBatchService.batchPushEnd();
        stopWatch.stop();
        log.info("检查并推送报价下架侵权词耗时秒：" + stopWatch.lastTaskInfo().getTimeSeconds());
        String message = stopWatch.prettyPrint();
        log.info("每日总结：" + message);
        WebHookAPI.sendMessage(message);
    }

}
