<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ProjectModuleManager">
    <modules>
      <module fileurl="file://$PROJECT_DIR$/.idea/51erp.iml" filepath="$PROJECT_DIR$/.idea/51erp.iml" />
      <module fileurl="file://$PROJECT_DIR$/51erp-service/xace-java-boot/xh-admin/xh-admin.iml" filepath="$PROJECT_DIR$/51erp-service/xace-java-boot/xh-admin/xh-admin.iml" />
      <module fileurl="file://$PROJECT_DIR$/51erp-service/xace-java-boot/xh-oauth/xh-oauth-api/xh-oauth-api.iml" filepath="$PROJECT_DIR$/51erp-service/xace-java-boot/xh-oauth/xh-oauth-api/xh-oauth-api.iml" />
      <module fileurl="file://$PROJECT_DIR$/51erp-service/xace-java-boot/xh-oauth/xh-oauth-biz/xh-oauth-biz.iml" filepath="$PROJECT_DIR$/51erp-service/xace-java-boot/xh-oauth/xh-oauth-biz/xh-oauth-biz.iml" />
      <module fileurl="file://$PROJECT_DIR$/51erp-service/xace-java-boot/xh-oauth/xh-oauth-controller/xh-oauth-controller.iml" filepath="$PROJECT_DIR$/51erp-service/xace-java-boot/xh-oauth/xh-oauth-controller/xh-oauth-controller.iml" />
      <module fileurl="file://$PROJECT_DIR$/51erp-service/xace-java-boot/xh-oauth/xh-oauth-entity/xh-oauth-entity.iml" filepath="$PROJECT_DIR$/51erp-service/xace-java-boot/xh-oauth/xh-oauth-entity/xh-oauth-entity.iml" />
      <module fileurl="file://$PROJECT_DIR$/51erp-service/xace-java-boot/xh-system/xh-system.iml" filepath="$PROJECT_DIR$/51erp-service/xace-java-boot/xh-system/xh-system.iml" />
    </modules>
  </component>
</project>