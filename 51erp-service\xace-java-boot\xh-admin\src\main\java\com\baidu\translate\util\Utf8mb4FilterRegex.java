package com.baidu.translate.util;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class Utf8mb4FilterRegex {

    // 预编译正则表达式以提高性能，特别是当频繁调用时
    // 匹配所有不在 BMP (U+0000 - U+FFFF) 内的字符
    // 方法 A: 使用范围排除
    private static final Pattern NON_BMP_PATTERN = Pattern.compile("[^\\u0000-\\uFFFF]");
    // 方法 B: 使用 Unicode 属性 \P{Block=Basic_Multilingual_Plane} 或 \P{BMP} (更推荐)
    // private static final Pattern NON_BMP_PATTERN = Pattern.compile("\\P{BMP}");


    /**
     * 使用正则表达式过滤掉大于 U+FFFF 的字符。
     *
     * @param text        输入字符串
     * @param replacement 用于替换 4 字节 UTF-8 字符的字符串。如果为 null 或空字符串，则直接移除。
     * @return 过滤后的字符串
     */
    public static String filterUtf8mb4ByRegex(String text, String replacement) {
        if (text == null) {
            return null;
        }
        // 如果 replacement 为 null，视为空字符串处理（移除）
        final String effectiveReplacement = (replacement == null) ? "" : replacement;

        Matcher matcher = NON_BMP_PATTERN.matcher(text);
        return matcher.replaceAll(effectiveReplacement);
        // 或者直接用 String.replaceAll (内部也会编译 Pattern)
        // return text.replaceAll("[^\\u0000-\\uFFFF]", effectiveReplacement);
        // return text.replaceAll("\\P{BMP}", effectiveReplacement);
    }

    /**
     * 过滤掉大于 U+FFFF 的字符（默认移除）。
     *
     * @param text 输入字符串
     * @return 过滤后的字符串
     */
    public static String filterUtf8mb4ByRegex(String text) {
        return filterUtf8mb4ByRegex(text, ""); // 默认移除
    }

    // 匹配 Unicode 中 Symbol (S) 类别的所有字符
    // \p{S} 是 \p{Symbol} 的缩写
    private static final Pattern SYMBOL_PATTERN = Pattern.compile("\\p{S}");

    public static String filterSymbols(String text, String replacement) {
        if (text == null) return null;
        String rep = (replacement == null) ? "" : replacement;
        return SYMBOL_PATTERN.matcher(text).replaceAll(rep);
    }

    public static String filterSymbols(String text) {
        return filterSymbols(text, "");
    }

    // --- 示例 ---
    public static void main(String[] args) {
        String textWithEmoji = "你好世界  🌍，这里有表情😊和一些文字。";
        String textWithoutEmoji = "这是一个普通字符串。";
        String nullInput = null;


        textWithEmoji=textWithEmoji+"❤◉★●😀◆🎉⌛ ✂⚠✳🚗🚙🚚🔅💎🤩🎮⚡️►•⚡🚘🎴🎶⏪💛⌚ 🚀🔍🕹==CAIJIANRONG";


        System.out.println("--- Regex Method ---");
        System.out.println("Original: " + textWithEmoji);
        System.out.println("Filtered (remove): " + filterUtf8mb4ByRegex(textWithEmoji));
        System.out.println("Filtered (\\p{S} removed): " + filterSymbols(textWithEmoji));

        System.out.println("Filtered (replace ?): " + filterUtf8mb4ByRegex(textWithEmoji, "?"));

        System.out.println("\nOriginal: " + textWithoutEmoji);
        System.out.println("Filtered (remove): " + filterUtf8mb4ByRegex(textWithoutEmoji));

        System.out.println("\nOriginal: " + nullInput);
        System.out.println("Filtered (remove): " + filterUtf8mb4ByRegex(nullInput));

    }
}