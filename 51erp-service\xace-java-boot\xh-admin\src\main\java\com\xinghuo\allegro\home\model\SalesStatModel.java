package com.xinghuo.allegro.home.model;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Builder
public class SalesStatModel {
    private BigDecimal totalAmount;

    private BigDecimal yesterdayAmount;
    private Double yesterdayRate;

    @Data
    @Builder
    public static class Metrics {
        private Long orderCount;
        private Long productCount;
        private Long shopCount;
    }

    private Metrics metrics;
}
