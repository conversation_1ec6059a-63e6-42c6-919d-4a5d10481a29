package com.baidu.translate;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class TranslateFileWithMapping {

    // 读取翻译映射文件
    public static Map<String, String> loadTranslationMap(String filePath) throws IOException {
        Map<String, String> translationMap = new HashMap<>();
        try (BufferedReader reader = new BufferedReader(new FileReader(filePath))) {
            String line;
            while ((line = reader.readLine()) != null) {
                // 解析翻译映射
                if (line.contains("\t")) {
                    String[] parts = line.split("\t");
                    if (parts.length == 2) {
                        translationMap.put(parts[0].trim(), parts[1].trim());
                    }
                }
            }
        }
        return translationMap;
    }

    // 翻译文本
    public static String translateText(String text, Map<String, String> translationMap) {
        if (translationMap.containsKey(text)) {
            return translationMap.get(text);
        }
        return text;
    }

    // 主程序
    public static void main(String[] args) {
        String translationFilePath = "d:/1.txt"; // 翻译映射文件路径
        String inputFilePath = "d:/2.txt"; // 输入文件路径
        String outputFilePath = "d:/2_translated.txt"; // 输出文件路径

        try {
            // 加载翻译映射
            Map<String, String> translationMap = loadTranslationMap(translationFilePath);

            // 读取文件并翻译内容
            try (BufferedReader reader = new BufferedReader(new FileReader(inputFilePath));
                 BufferedWriter writer = new BufferedWriter(new FileWriter(outputFilePath))) {

                String line;
                while ((line = reader.readLine()) != null) {
                    // 翻译每一行中的每个部分
                    String[] parts = line.split(">");
                    StringBuilder translatedLine = new StringBuilder();

                    for (String part : parts) {
                        String trimmedPart = part.trim();
                        String translatedPart = translateText(trimmedPart, translationMap);
                        if (translatedLine.length() > 0) {
                            translatedLine.append(" > ");
                        }
                        translatedLine.append(translatedPart);
                    }

                    writer.write(translatedLine.toString());
                    writer.newLine();
                }
            }

            System.out.println("Translation completed and written to " + outputFilePath);

        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}