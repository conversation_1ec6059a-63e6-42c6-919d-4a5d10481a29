package com.xinghuo.allegro.collect.model.product;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
public class CollectProductModel {

    @Schema(description = "ID")
    private Integer id;

    @Schema(description = "产品ID")
    private String productId;

    @Schema(description = "SKU ID")
    private Integer skuId;

    @Schema(description = "分类ID")
    private String categoryId;

    @Schema(description = "Offer名称")
    private String offerName;

    @Schema(description = "产品名称")
    private String productName;

    @Schema(description = "购买数量")
    private Integer buyerQuantity;

    @Schema(description = "销售日期")
    private String saleDate;

    @Schema(description = "产品Offer数量")
    private Integer productOffersCount;

    @Schema(description = "状态")
    private Integer status;

    @Schema(description = "创建时间")
    private Date createdAt;

    @Schema(description = "最后修改时间")
    private Date lastModifiedAt;

    // 扩展字段
    @Schema(description = "分类路径")
    private String categoryPath;

    @Schema(description = "状态文本")
    private String statusText;
}
