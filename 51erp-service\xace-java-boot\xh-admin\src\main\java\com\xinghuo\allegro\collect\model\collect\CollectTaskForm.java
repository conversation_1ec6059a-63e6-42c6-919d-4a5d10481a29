package com.xinghuo.allegro.collect.model.collect;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.util.Date;

@Data
public class CollectTaskForm {

    private String id;

    @Schema(description = "任务类型", required = true)
    @NotBlank(message = "任务类型不能为空")
    private String taskType;

    @Schema(description = "目录")
    private String categoryId;

    private Integer suppendStatus;

    private String sellType;

    @Schema(description = "采集优先级", required = true)
    @NotNull(message = "采集优先级不能为空")
    private Integer priority;

    @Schema(description = "链接名称", required = true)
    @NotBlank(message = "链接名称不能为空")
    private String link;

    @Schema(description = "采集客户端")
    private String clientId;

    @Schema(description = "Offer数量")
    private Integer totalNum;

    @Schema(description = "累计收集数")
    private Integer sumCount;

    @Schema(description = "有销售额数")
    private Integer totalSalesNum;

    @Schema(description = "请求时间")
    private Date requestTime;

    @Schema(description = "完成时间")
    private Date finishTime;

    @Schema(description = "采集状态")
    private Integer status;

    @Schema(description = "平台")
    private String platform;

    @Schema(description = "路径")
    private String path;

    @Schema(description = "最大页数")
    private Integer maxPages;
}
