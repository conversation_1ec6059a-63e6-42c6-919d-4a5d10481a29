package com.xinghuo.allegro.aiservice.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xinghuo.allegro.aiservice.dao.TitleVariantMapper;
import com.xinghuo.allegro.aiservice.entity.TitleVariantEntity;
import com.xinghuo.allegro.aiservice.service.TitleVariantService;
import com.xinghuo.common.base.service.BaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 *
 */
@Slf4j
@Service
public class TitleVariantServiceImpl extends BaseServiceImpl<TitleVariantMapper, TitleVariantEntity> implements TitleVariantService {







    @Override
        //判断唯一哈希值是否存在
        public boolean isExistDigest(String digest) {
            QueryWrapper<TitleVariantEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(TitleVariantEntity::getDigest, digest);
            return this.count(queryWrapper) > 0;
        }


}
