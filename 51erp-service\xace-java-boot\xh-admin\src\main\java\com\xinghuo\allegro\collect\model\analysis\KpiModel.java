package com.xinghuo.allegro.collect.model.analysis;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * KPI指标模型
 * <AUTHOR>
 */
@Data
public class KpiModel {

    @Schema(description = "指标名称")
    private String name;

    @Schema(description = "指标值")
    private Long value;

    @Schema(description = "指标单位")
    private String unit;

    @Schema(description = "对比值（如昨日、上月等）")
    private Long compareValue;

    @Schema(description = "增长率（百分比）")
    private Double growthRate;

    @Schema(description = "趋势（up/down/stable）")
    private String trend;

    public KpiModel() {}

    public KpiModel(String name, Long value, String unit) {
        this.name = name;
        this.value = value;
        this.unit = unit;
    }

    public KpiModel(String name, Long value, String unit, Long compareValue) {
        this.name = name;
        this.value = value;
        this.unit = unit;
        this.compareValue = compareValue;
        
        // 计算增长率和趋势
        if (compareValue != null && compareValue > 0) {
            this.growthRate = ((double) (value - compareValue) / compareValue) * 100;
            if (growthRate > 0) {
                this.trend = "up";
            } else if (growthRate < 0) {
                this.trend = "down";
            } else {
                this.trend = "stable";
            }
        }
    }
}
