package com.xinghuo.amazon.collect.service;

import com.xinghuo.amazon.collect.entity.AmazonPageTaskEntity;
import com.xinghuo.amazon.util.AmazonConstant;
import com.xinghuo.amazon.util.AmazonUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Amazon HTML解析服务
 * 解析Amazon页面HTML内容，提取商品信息
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Slf4j
@Service
public class AmazonHtmlParseService {

    /**
     * 解析Amazon列表页HTML内容，提取商品信息并创建页面任务
     *
     * @param htmlContent HTML内容
     * @param listTaskId 列表任务ID
     * @param pageUrl 页面URL
     * @return 解析出的页面任务列表
     */
    public List<AmazonPageTaskEntity> parseListPageHtml(String htmlContent, Integer listTaskId, String pageUrl) {
        List<AmazonPageTaskEntity> taskList = new ArrayList<>();
        
        if (StrXhUtil.isBlank(htmlContent)) {
            log.warn("Amazon列表页HTML内容为空");
            return taskList;
        }
        
        try {
            Document doc = Jsoup.parse(htmlContent);
            
            // 查找商品容器，Amazon通常使用这些选择器
            Elements productElements = doc.select("[data-component-type='s-search-result']");
            if (productElements.isEmpty()) {
                // 备用选择器
                productElements = doc.select(".s-result-item");
            }
            if (productElements.isEmpty()) {
                // 再次备用选择器
                productElements = doc.select("[data-asin]");
            }
            
            if (productElements.isEmpty()) {
                log.warn("未找到商品元素，可能页面结构已变化");
                return taskList;
            }
            
            Date now = new Date();
            
            for (Element productElement : productElements) {
                AmazonPageTaskEntity task = parseProductElement(productElement, listTaskId, now);
                if (task != null) {
                    taskList.add(task);
                }
            }
            
            log.info("成功解析Amazon列表页HTML，共解析出{}个商品", taskList.size());
            
        } catch (Exception e) {
            log.error("解析Amazon列表页HTML失败", e);
        }
        
        return taskList;
    }

    /**
     * 解析单个商品元素
     *
     * @param productElement 商品元素
     * @param listTaskId 列表任务ID
     * @param createTime 创建时间
     * @return 页面任务实体
     */
    private AmazonPageTaskEntity parseProductElement(Element productElement, Integer listTaskId, Date createTime) {
        try {
            AmazonPageTaskEntity task = new AmazonPageTaskEntity();
            
            // 基本信息
            task.setListTaskId(listTaskId);
            task.setCreatedAt(createTime);
            task.setUpdatedAt(createTime);
            task.setStatus(AmazonConstant.REQUEST_STATUS_INIT);
            task.setRetryCount(0);
            
            // 提取ASIN
            String asin = extractAsinFromElement(productElement);
            if (StrXhUtil.isBlank(asin)) {
                log.debug("无法提取ASIN，跳过该商品");
                return null;
            }
            task.setEntryAsin(asin);
            
            // 提取商品URL
            String productUrl = extractProductUrl(productElement);
            if (StrXhUtil.isNotBlank(productUrl)) {
                task.setUrl(AmazonUtil.buildFullUrl(productUrl));
            }
            
            // 提取商品标题
            String title = extractProductTitle(productElement);
            if (StrXhUtil.isNotBlank(title)) {
                task.setListPageTitle(AmazonUtil.cleanTitle(title));
            }
            
            // 提取价格信息
            extractPriceInfo(productElement, task);
            
            // 提取评分和评论数
            extractRatingInfo(productElement, task);
            
            // 提取图片URL
            String imageUrl = extractImageUrl(productElement);
            if (StrXhUtil.isNotBlank(imageUrl)) {
                task.setListPageMainImageUrl(imageUrl);
            }
            
            // 检查是否为赞助商品
            boolean isSponsored = checkIfSponsored(productElement);
            task.setIsSponsored(isSponsored);
            
            // 提取Prime信息
            String primeInfo = extractPrimeInfo(productElement);
            if (StrXhUtil.isNotBlank(primeInfo)) {
                task.setListPagePrimeInfo(primeInfo);
            }
            
            return task;
            
        } catch (Exception e) {
            log.error("解析单个商品元素失败", e);
            return null;
        }
    }

    /**
     * 从元素中提取ASIN
     */
    private String extractAsinFromElement(Element element) {
        // 首先尝试从data-asin属性获取
        String asin = element.attr("data-asin");
        if (StrXhUtil.isNotBlank(asin)) {
            return asin;
        }
        
        // 从URL中提取
        Elements linkElements = element.select("a[href]");
        for (Element link : linkElements) {
            String href = link.attr("href");
            String extractedAsin = AmazonUtil.extractAsinFromUrl(href);
            if (StrXhUtil.isNotBlank(extractedAsin)) {
                return extractedAsin;
            }
        }
        
        return null;
    }

    /**
     * 提取商品URL
     */
    private String extractProductUrl(Element element) {
        // 查找商品链接
        Elements titleLinks = element.select("h2 a, .s-link-style a, [data-cy='title-recipe-link']");
        if (!titleLinks.isEmpty()) {
            return titleLinks.first().attr("href");
        }
        
        // 备用方案
        Elements allLinks = element.select("a[href*='/dp/']");
        if (!allLinks.isEmpty()) {
            return allLinks.first().attr("href");
        }
        
        return null;
    }

    /**
     * 提取商品标题
     */
    private String extractProductTitle(Element element) {
        // 多种可能的标题选择器
        String[] titleSelectors = {
            "h2 a span",
            "h2 span",
            ".s-link-style span",
            "[data-cy='title-recipe-link'] span",
            ".a-text-normal"
        };
        
        for (String selector : titleSelectors) {
            Elements titleElements = element.select(selector);
            if (!titleElements.isEmpty()) {
                String title = titleElements.first().text().trim();
                if (StrXhUtil.isNotBlank(title)) {
                    return title;
                }
            }
        }
        
        return null;
    }

    /**
     * 提取价格信息
     */
    private void extractPriceInfo(Element element, AmazonPageTaskEntity task) {
        // 当前价格
        Elements priceElements = element.select(".a-price-whole, .a-price .a-offscreen, .a-price-range");
        if (!priceElements.isEmpty()) {
            String priceText = priceElements.first().text();
            BigDecimal price = AmazonUtil.extractPrice(priceText);
            if (price != null) {
                task.setListPagePrice(price);
            }
        }
        
        // 原价（划线价格）
        Elements originalPriceElements = element.select(".a-text-price .a-offscreen, .a-price.a-text-price .a-offscreen");
        if (!originalPriceElements.isEmpty()) {
            String originalPriceText = originalPriceElements.first().text();
            BigDecimal originalPrice = AmazonUtil.extractPrice(originalPriceText);
            if (originalPrice != null) {
                task.setListOrginPrice(originalPrice);
            }
        }
    }

    /**
     * 提取评分信息
     */
    private void extractRatingInfo(Element element, AmazonPageTaskEntity task) {
        // 评分
        Elements ratingElements = element.select(".a-icon-alt, [aria-label*='stars'], [aria-label*='星']");
        if (!ratingElements.isEmpty()) {
            String ratingText = ratingElements.first().attr("aria-label");
            if (StrXhUtil.isBlank(ratingText)) {
                ratingText = ratingElements.first().text();
            }
            BigDecimal rating = AmazonUtil.extractRating(ratingText);
            if (rating != null) {
                task.setListPageRating(rating);
            }
        }
        
        // 评论数
        Elements reviewElements = element.select("a[href*='#customerReviews'] span, .a-size-base");
        for (Element reviewElement : reviewElements) {
            String reviewText = reviewElement.text();
            if (reviewText.matches(".*\\d+.*")) {
                Integer reviewCount = AmazonUtil.extractInteger(reviewText);
                if (reviewCount != null && reviewCount > 0) {
                    task.setListPageReviewCount(reviewCount);
                    break;
                }
            }
        }
    }

    /**
     * 提取图片URL
     */
    private String extractImageUrl(Element element) {
        Elements imgElements = element.select("img");
        for (Element img : imgElements) {
            String src = img.attr("src");
            if (StrXhUtil.isNotBlank(src) && src.contains("images-amazon")) {
                return src;
            }
        }
        return null;
    }

    /**
     * 检查是否为赞助商品
     */
    private boolean checkIfSponsored(Element element) {
        String elementText = element.text().toLowerCase();
        return elementText.contains("sponsored") || 
               elementText.contains("广告") || 
               elementText.contains("推广") ||
               element.select("[data-component-type='sp-sponsored-result']").size() > 0;
    }

    /**
     * 提取Prime信息
     */
    private String extractPrimeInfo(Element element) {
        Elements primeElements = element.select("[aria-label*='Prime'], .a-icon-prime, .s-prime");
        if (!primeElements.isEmpty()) {
            return "Prime";
        }
        return null;
    }
}
