package com.xinghuo.allegro.data.dao;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Allegro卖家分析Mapper
 *
 * <AUTHOR>
 */
public interface AllegroSellerAnalysisMapper {

    /**
     * 获取卖家类型分布数据
     *
     * @return 卖家类型分布数据
     */
    @Select("SELECT sell_type as type, COUNT(*) as count FROM zz_allegro_seller GROUP BY sell_type")
    List<Map<String, Object>> getSellerTypeStats();

    /**
     * 获取卖家状态分布数据
     *
     * @return 卖家状态分布数据
     */
    @Select("SELECT status, COUNT(*) as count FROM zz_allegro_seller GROUP BY status")
    List<Map<String, Object>> getSellerStatusStats();

    /**
     * 获取新发现卖家趋势数据
     *
     * @param days 天数
     * @return 新发现卖家趋势数据
     */
    @Select("SELECT DATE_FORMAT(F_CreatorTime, '%Y-%m-%d') as date, COUNT(*) as count " +
            "FROM zz_allegro_seller " +
            "WHERE F_CreatorTime >= DATE_SUB(CURDATE(), INTERVAL #{days} DAY) " +
            "GROUP BY DATE_FORMAT(F_CreatorTime, '%Y-%m-%d') " +
            "ORDER BY date")
    List<Map<String, Object>> getNewSellersTrend(@Param("days") Integer days);

    /**
     * 获取黑名单卖家趋势数据
     *
     * @param days 天数
     * @return 黑名单卖家趋势数据
     */
    @Select("SELECT DATE_FORMAT(end_date, '%Y-%m-%d')    as date, COUNT(*) as count " +
            "FROM zz_allegro_seller " +
            "WHERE sell_type = 'BLOCKED' AND end_date >= DATE_SUB(CURDATE(), INTERVAL #{days} DAY) " +
            "GROUP BY   DATE_FORMAT(end_date, '%Y-%m-%d')  " +
            "ORDER BY date")
    List<Map<String, Object>> getBlockedSellersTrend(@Param("days") Integer days);

    /**
     * 获取月度卖家统计数据
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 月度卖家统计数据
     */
    @Select("<script>" +
            "SELECT DATE_FORMAT(date, '%Y年%m月') as month, date, end_count, new_count " +
            "FROM (" +
            "  SELECT " +
            "    m.end_date AS date, " +
            "    m.count AS end_count, " +
            "    IFNULL(n.new_count, 0) AS new_count " +
            "  FROM (" +
            "    SELECT " +
            "      end_date, " +
            "      COUNT(*) AS count " +
            "    FROM " +
            "      zz_allegro_seller " +
            "    WHERE " +
            "      sell_type = 'BLOCKED' " +
            "      <if test='startDate != null'> AND end_date &gt;= #{startDate} </if>" +
            "      <if test='endDate != null'> AND end_date &lt;= #{endDate} </if>" +
            "    GROUP BY " +
            "      end_date" +
            "  ) m " +
            "  LEFT JOIN (" +
            "    SELECT " +
            "      DATE(F_CreatorTime) AS created_date, " +
            "      COUNT(*) AS new_count " +
            "    FROM " +
            "      zz_allegro_seller " +
            "    <where>" +
            "      <if test='startDate != null'> AND F_CreatorTime &gt;= #{startDate} </if>" +
            "      <if test='endDate != null'> AND F_CreatorTime &lt;= #{endDate} </if>" +
            "    </where>" +
            "    GROUP BY " +
            "      DATE(F_CreatorTime) " +
            "  ) n ON m.end_date = n.created_date " +
            "  ORDER BY " +
            "    m.end_date DESC" +
            ") M" +
            "</script>")
    List<Map<String, Object>> getMonthlyStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 获取高级卖家统计数据
     *
     * @return 高级卖家统计数据
     */
    @Select("SELECT " +
            "  COUNT(*) as totalSellers, " +
            "  SUM(CASE WHEN total_num > 500 THEN 1 ELSE 0 END) as premiumSellers, " +
            "  SUM(CASE WHEN total_num <= 500 THEN 1 ELSE 0 END) as normalSellers " +
            "FROM zz_allegro_seller where company_address_country_code = 'CN' and status=1")
    Map<String, Object> getPremiumSellersStats();

    /**
     * 获取中国卖家统计数据
     *
     * @return 中国卖家统计数据
     */
    @Select("SELECT " +
            "  COUNT(*) as totalSellers, " +
            "  SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as activeSellers, " +
            "  SUM(CASE WHEN total_num > 500 THEN 1 ELSE 0 END) as premiumSellers, " +
            "  SUM(CASE WHEN DATE(F_CreatorTime) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as newSellers " +
            "FROM zz_allegro_seller " +
            "WHERE company_address_country_code = 'CN' and status=1 ")
    Map<String, Object> getChinaSellerStats();

    /**
     * 获取中国卖家趋势数据
     *
     * @param days 天数
     * @return 中国卖家趋势数据
     */
    @Select("SELECT DATE_FORMAT(F_CreatorTime, '%Y-%m-%d') as date, COUNT(*) as count " +
            "FROM zz_allegro_seller " +
            "WHERE company_address_country_code = 'CN' " +
            "  AND F_CreatorTime >= DATE_SUB(CURDATE(), INTERVAL #{days} DAY) " +
            "GROUP BY DATE_FORMAT(F_CreatorTime, '%Y-%m-%d') " +
            "ORDER BY date")
    List<Map<String, Object>> getChinaSellersTrend(@Param("days") Integer days);

    /**
     * 获取热门卖家数据
     *
     * @param days 天数
     * @param limit 限制数量
     * @return 热门卖家数据
     */
    @Select("SELECT " +
            "  s.id, " +
            "  s.seller_id as sellerId, " +
            "  s.seller_name as sellerName, " +
            "  s.company_name as companyName, " +
            "  s.total_num as totalNum, " +
            "  s.sell_type as sellType, " +
            "  s.status, " +
            "  COUNT(o.id) as offerCount " +
            "FROM zz_allegro_seller s " +
            "LEFT JOIN zz_collect_offer o ON s.seller_id = o.seller_id " +
            "WHERE o.F_CreatorTime >= DATE_SUB(CURDATE(), INTERVAL #{days} DAY) " +
            "GROUP BY s.id, s.seller_id, s.seller_name, s.company_name, s.total_num, s.sell_type, s.status " +
            "ORDER BY offerCount DESC " +
            "LIMIT #{limit}")
    List<Map<String, Object>> getHotSellers(@Param("days") Integer days, @Param("limit") Integer limit);
}
