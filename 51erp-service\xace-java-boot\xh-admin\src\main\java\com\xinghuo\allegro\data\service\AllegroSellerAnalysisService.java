package com.xinghuo.allegro.data.service;

import com.xinghuo.allegro.data.model.seller.AllegroSellerAnalysisVO;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Allegro卖家分析服务接口
 *
 * <AUTHOR>
 */
public interface AllegroSellerAnalysisService {

    /**
     * 获取卖家统计数据
     *
     * @return 卖家统计数据
     */
    AllegroSellerAnalysisVO getSellerStats();

    /**
     * 获取中国卖家统计数据
     *
     * @return 中国卖家统计数据
     */
    Map<String, Object> getChinaSellerStats();

    /**
     * 获取中国卖家趋势数据
     *
     * @param days 天数
     * @return 中国卖家趋势数据
     */
    List<Map<String, Object>> getChinaSellersTrend(Integer days);

    /**
     * 获取卖家类型分布数据
     *
     * @return 卖家类型分布数据
     */
    Map<String, Object> getSellerTypeStats();

    /**
     * 获取卖家状态分布数据
     *
     * @return 卖家状态分布数据
     */
    Map<String, Object> getSellerStatusStats();

    /**
     * 获取新发现卖家趋势数据
     *
     * @param days 天数
     * @return 新发现卖家趋势数据
     */
    List<Map<String, Object>> getNewSellersTrend(Integer days);

    /**
     * 获取黑名单卖家趋势数据
     *
     * @param days 天数
     * @return 黑名单卖家趋势数据
     */
    List<Map<String, Object>> getBlockedSellersTrend(Integer days);

    /**
     * 获取月度卖家统计数据
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 月度卖家统计数据
     */
    List<Map<String, Object>> getMonthlyStats(Date startDate, Date endDate);

    /**
     * 获取高级卖家统计数据
     *
     * @return 高级卖家统计数据
     */
    Map<String, Object> getPremiumSellersStats();

    /**
     * 获取热门卖家数据
     *
     * @param days 天数
     * @param limit 限制数量
     * @return 热门卖家数据
     */
    List<Map<String, Object>> getHotSellers(Integer days, Integer limit);
}
