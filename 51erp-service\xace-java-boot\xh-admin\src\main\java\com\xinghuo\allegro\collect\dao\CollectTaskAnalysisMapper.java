package com.xinghuo.allegro.collect.dao;

import com.xinghuo.allegro.collect.model.analysis.TaskStatusSummaryModel;
import com.xinghuo.allegro.collect.model.analysis.DailyCompletionModel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 采集任务分析专用Mapper
 * <AUTHOR>
 */
@Mapper
public interface CollectTaskAnalysisMapper {

    /**
     * 获取任务积压与优先级分布统计
     * @param taskType 任务类型筛选
     * @return 统计结果列表
     */
    List<TaskStatusSummaryModel> getTaskSummaryByStatusPriority(@Param("taskType") String taskType);

    /**
     * 获取任务类型状态统计
     * @param date 查询日期
     * @return 统计结果列表
     */
    List<TaskStatusSummaryModel> getTaskSummaryByTypeStatus(@Param("date") String date);



    /**
     * 获取当日完成任务详细分析 - 按小时分组
     * @param date 分析日期
     * @param filterTaskType 任务类型筛选
     * @param filterClientId 客户端ID筛选
     * @return 分析结果列表
     */
    List<DailyCompletionModel> getDailyCompletionByHour(@Param("date") String date,
                                                        @Param("filterTaskType") String filterTaskType,
                                                        @Param("filterClientId") String filterClientId);

    /**
     * 获取当日完成任务详细分析 - 按任务类型分组
     * @param date 分析日期
     * @param filterTaskType 任务类型筛选
     * @param filterClientId 客户端ID筛选
     * @return 分析结果列表
     */
    List<DailyCompletionModel> getDailyCompletionByTaskType(@Param("date") String date,
                                                            @Param("filterTaskType") String filterTaskType,
                                                            @Param("filterClientId") String filterClientId);

    /**
     * 获取当日完成任务详细分析 - 按客户端分组
     * @param date 分析日期
     * @param filterTaskType 任务类型筛选
     * @param filterClientId 客户端ID筛选
     * @return 分析结果列表
     */
    List<DailyCompletionModel> getDailyCompletionByClient(@Param("date") String date,
                                                          @Param("filterTaskType") String filterTaskType,
                                                          @Param("filterClientId") String filterClientId);

    /**
     * 获取当日完成任务详细分析 - 最细粒度（按小时、类型、客户端）
     * @param date 分析日期
     * @param filterTaskType 任务类型筛选
     * @param filterClientId 客户端ID筛选
     * @return 分析结果列表
     */
    List<DailyCompletionModel> getDailyCompletionDetails(@Param("date") String date,
                                                         @Param("filterTaskType") String filterTaskType,
                                                         @Param("filterClientId") String filterClientId);
}
