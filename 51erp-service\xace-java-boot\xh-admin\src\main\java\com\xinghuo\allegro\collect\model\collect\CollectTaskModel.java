package com.xinghuo.allegro.collect.model.collect;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class CollectTaskModel {

    @Schema(description = "id")
    private String id;

    @Schema(description = "任务类型")
    private String taskType;

    @Schema(description = "目录")
    private String categoryId;

    @Schema(description = "采集优先级")
    private Integer priority;

    @Schema(description = "链接名称")
    private String link;

    @Schema(description = "采集客户端")
    private String clientId;

    @Schema(description = "Offer数量")
    private Integer totalNum;

    @Schema(description = "累计收集数")
    private Integer sumCount;

    @Schema(description = "有销售额数")
    private Integer totalSalesNum;

    @Schema(description = "创建时间")
    private java.util.Date createdAt;

    @Schema(description = "请求时间")
    private java.util.Date requestTime;

    @Schema(description = "完成时间")
    private java.util.Date finishTime;

    @Schema(description = "采集状态")
    private Integer status;

    @Schema(description = "平台")
    private String platform;

    @Schema(description = "路径")
    private String path;

    @Schema(description = "最大页数")
    private Integer maxPages;

    @Schema(description = "创建人")
    private String createdBy;

    @Schema(description = "最后更新人")
    private String lastUpdatedBy;

    @Schema(description = "最后更新时间")
    private java.util.Date lastUpdatedAt;





}
