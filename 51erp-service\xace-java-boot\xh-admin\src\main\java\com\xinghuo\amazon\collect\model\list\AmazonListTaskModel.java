package com.xinghuo.amazon.collect.model.list;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * Amazon列表任务模型
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Data
public class AmazonListTaskModel {

    @Schema(description = "主键ID")
    private Integer id;

    @Schema(description = "URL")
    private String url;

    @Schema(description = "源分类ID")
    private Long sourceCategoryId;

    @Schema(description = "任务状态")
    private Integer status;

    @Schema(description = "最大爬取页数")
    private Integer maxPagesToCrawl;

    @Schema(description = "已爬取页数")
    private Integer crawledPages;

    @Schema(description = "错误信息")
    private String errorMessage;

    @Schema(description = "创建时间")
    private Date createdAt;

    @Schema(description = "更新时间")
    private Date updatedAt;

    @Schema(description = "源分类路径")
    private String sourceCategoryPath;

    @Schema(description = "Temu分类ID")
    private Integer temuCategoryId;

    @Schema(description = "Temu分类路径")
    private String temuCategoryPath;
}
