package com.xinghuo.allegro.manage.entity;


import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 */
@TableName("zz_allegro_store_user")
@Data
public class AllegroStoreUserEntity   {

    @TableId
    private String id;

    private String sellerId;

    private String userId;

    private String note;

    private Date createTime;

}
