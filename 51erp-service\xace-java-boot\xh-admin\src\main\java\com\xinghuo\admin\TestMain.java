package com.xinghuo.admin;

import cn.hutool.crypto.symmetric.SymmetricAlgorithm;
import cn.hutool.crypto.symmetric.SymmetricCrypto;
import com.xinghuo.common.util.json.JsonXhUtil;

import java.util.ArrayList;
import java.util.List;

public class TestMain {



    // 密钥，长度需要为16位、24位或32位
    private static final String SECRET_KEY = "2k9m4p7sB3v6y1u8";

    /**
     * 根据sellerId和skuid生成exterid
     * @param sellerId 卖家ID
     * @param skuid 商品ID
     * @return 加密后的exterid
     */
    public static String encrypt(String sellerId, String skuid) {
        // 拼接sellerId和skuid
        String data = sellerId +"_"+ skuid;
        // 创建对称加密对象
        SymmetricCrypto aes = new SymmetricCrypto(SymmetricAlgorithm.AES, SECRET_KEY.getBytes());
        // 加密
        return aes.encryptHex(data);
    }

    /**
     * 解密exterid得到skuid
     * @param exterid 加密后的exterid
     * @return 解密后的skuid
     */
    public static String decryptToSkuid(String exterid) {
        // 创建对称加密对象
        SymmetricCrypto aes = new SymmetricCrypto(SymmetricAlgorithm.AES, SECRET_KEY.getBytes());
        // 解密
        String data = aes.decryptStr(exterid);
        // 截取skuid，假设sellerId固定为10位
        return data ;
    }
    public static void main(String[] args) {

//        Calendar now = Calendar.getInstance();
//        int currentHour = now.get(Calendar.HOUR_OF_DAY);
//
//        System.out.println("当前时间为：" + currentHour);
//        String sellerId = "9783";
//        String skuid = "********";
//
//        // 加密
//        String exterid = encrypt(sellerId, skuid);
//        System.out.println("加密后的exterid: " + exterid);
//
//        // 解密
//        String decryptedSkuid = decryptToSkuid(exterid);
//        System.out.println("解密后的skuid: " + decryptedSkuid);


        List<String> idList = new ArrayList<>();
        idList.add("64859380778428493");
        idList.add("64859380778428494");
        String str = JsonXhUtil.toJSONString(idList);

        List<String> idList2 = JsonXhUtil.jsonToList(str, String.class);
        System.out.println(idList.size());

    }
}
