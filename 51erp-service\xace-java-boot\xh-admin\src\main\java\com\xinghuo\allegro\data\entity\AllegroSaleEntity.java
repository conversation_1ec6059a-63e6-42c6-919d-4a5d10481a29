package com.xinghuo.allegro.data.entity;


import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("zz_collect_sale")
public class AllegroSaleEntity {

    @TableId
    private String id;
    @TableField("seller_id")
    private String sellerId;
    @TableField("offer_id")
    private String offerId;
    @TableField("sku_id")
    private Integer skuId;
    @TableField("category_id")
    private String categoryId;
    @TableField("old_sales")
    private Integer oldSales;
    @TableField("old_sales_collect_time")
    private Date oldSalesCollectTime;
    @TableField("new_sales")
    private Integer newSales;
    @TableField("new_sales_collect_time")
    private Date newSalesCollectTime;
    @TableField("sale_date")
    private Date saleDate;
    @TableField("sales_num")
    private Integer salesNum;
    @TableField("create_time")
    private Date createTime;
    @TableField("offer_link")
    private String offerLink;
    @TableField("image_url")
    private String imageUrl;
    @TableField("delete_reason_type")
    private String deleteReasonType;
}
