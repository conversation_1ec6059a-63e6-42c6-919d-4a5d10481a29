package com.xinghuo.allegro.collect.service.impl;

import com.xinghuo.allegro.collect.dao.CollectZeroOfferMapper;
import com.xinghuo.allegro.collect.entity.CollectZeroOfferEntity;
import com.xinghuo.allegro.collect.service.CollectZeroOfferService;
import com.xinghuo.common.base.service.BaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 *
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class CollectZeroOfferServiceImpl extends BaseServiceImpl<CollectZeroOfferMapper, CollectZeroOfferEntity> implements CollectZeroOfferService {


   public void saveOfferId(String offerId){
       CollectZeroOfferEntity entity = this.getById(offerId);
       if(entity == null){
           entity = new CollectZeroOfferEntity();
           entity.setOfferId(offerId);
           entity.setCreatedAt(new Date());
           this.save(entity);
       }

   }


}
