package com.xinghuo.allegro.data.controller;

import com.xinghuo.allegro.data.service.AllegroProductService;
import com.xinghuo.allegro.push.entity.ErpProductEntity;
import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.fruugo.collect.service.FruugoPushOfferService;
import com.xinghuo.fruugo.data.model.AllegroProductClaimForm;
import com.xinghuo.fruugo.data.model.AllegroProductModel;
import com.xinghuo.fruugo.data.model.AllegroProductPagination;
import com.xinghuo.fruugo.data.model.AllegroProductUpdateModel;
import com.xinghuo.fruugo.manage.service.FruugoShelfTemplateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Allegro产品控制器
 *
 * <AUTHOR>
 * @date 2025-05-18
 */
@Slf4j
@RestController
@Tag(name = "Allegro产品管理")
@RequestMapping("/api/allegro/product")
public class AllegroProductController {

    @Resource
    private AllegroProductService allegroProductService;

    @Resource
    private FruugoPushOfferService fruugoPushOfferService;

    @Resource
    private FruugoShelfTemplateService fruugoShelfTemplateService;

    /**
     * 获取产品列表
     */
    @Operation(summary = "获取产品列表")
    @PostMapping("/getList")
    public ActionResult<PageListVO<AllegroProductModel>> getList(@RequestBody AllegroProductPagination pagination) {
        log.info("获取Allegro产品列表，参数: {}", pagination);

        List<ErpProductEntity> list = allegroProductService.getList(pagination);
        List<AllegroProductModel> modelList = BeanCopierUtils.copyList(list, AllegroProductModel.class);

        // 设置额外信息
        for (AllegroProductModel model : modelList) {
            // 设置销售数据
            model.setSaleList(null); // 暂时设为null，后续实现

            // 设置店铺数据
            model.setSellerList(null); // 暂时设为null，后续实现
        }

        PaginationVO paginationVO = BeanCopierUtils.copy(pagination, PaginationVO.class);
        return ActionResult.page(modelList, paginationVO);
    }

    /**
     * 获取产品详情
     */
    @Operation(summary = "获取产品详情")
    @GetMapping("/info/{skuId}")
    @Parameters({
            @Parameter(name = "skuId", description = "产品SKU ID", required = true)
    })
    public ActionResult<AllegroProductModel> info(@PathVariable("skuId") Integer skuId) {
        log.info("获取Allegro产品详情，ID: {}", skuId);

        ErpProductEntity entity = allegroProductService.getInfo(skuId);
        if (entity == null) {
            return ActionResult.fail("未找到产品数据");
        }

        AllegroProductModel model = BeanCopierUtils.copy(entity, AllegroProductModel.class);

        // 设置销售数据
        model.setSaleList(null); // 暂时设为null，后续实现

        // 设置店铺数据
        model.setSellerList(null); // 暂时设为null，后续实现

        return ActionResult.success(model);
    }

    /**
     * 更新产品信息
     */
    @Operation(summary = "更新产品信息")
    @PutMapping("/update/{skuId}")
    @Parameters({
            @Parameter(name = "skuId", description = "产品SKU ID", required = true)
    })
    public ActionResult<String> update(@PathVariable("skuId") Integer skuId, @RequestBody AllegroProductUpdateModel form) {
        log.info("更新Allegro产品信息，ID: {}, 表单: {}", skuId, form);

        form.setSkuId(skuId);
        boolean success = allegroProductService.updateProduct(form);
        return success ? ActionResult.success("更新成功") : ActionResult.fail("更新失败");
    }

    /**
     * 删除产品
     */
    @Operation(summary = "删除产品")
    @DeleteMapping("/delete/{skuId}")
    @Parameters({
            @Parameter(name = "skuId", description = "产品SKU ID", required = true)
    })
    public ActionResult<String> delete(@PathVariable("skuId") Integer skuId) {
        log.info("删除Allegro产品，ID: {}", skuId);

        int count = allegroProductService.batchDelete(List.of(skuId));
        return count > 0 ? ActionResult.success("删除成功") : ActionResult.fail("删除失败");
    }

    /**
     * 批量删除产品
     */
    @Operation(summary = "批量删除产品")
    @DeleteMapping("/batchDelete")
    public ActionResult<String> batchDelete(@RequestBody List<Integer> skuIds) {
        log.info("批量删除Allegro产品，ID列表: {}", skuIds);

        if (skuIds == null || skuIds.isEmpty()) {
            return ActionResult.fail("SKU ID列表不能为空");
        }

        int count = allegroProductService.batchDelete(skuIds);
        return count > 0 ? 
                ActionResult.success(String.format("成功删除 %d 条记录", count)) : 
                ActionResult.fail("删除失败");
    }

    /**
     * 同步产品英文数据
     */
    @Operation(summary = "同步产品英文数据")
    @PostMapping("/syncEnData/{skuId}")
    @Parameters({
            @Parameter(name = "skuId", description = "产品SKU ID", required = true)
    })
    public ActionResult<String> syncEnData(@PathVariable("skuId") Integer skuId) {
        log.info("同步Allegro产品英文数据，ID: {}", skuId);

        boolean success = allegroProductService.syncProductEnData(skuId);
        return success ? ActionResult.success("同步成功") : ActionResult.fail("同步失败");
    }

    /**
     * 从产品认领到Fruugo推送报价
     */
    @Operation(summary = "从产品认领到Fruugo推送报价")
    @PostMapping("/claimToFruugo")
    public ActionResult<String> claimToFruugo(@RequestBody @Valid AllegroProductClaimForm form) {
        log.info("从Allegro产品认领到Fruugo推送报价，参数: sellerId={}, templateId={}, skuIds数量={}",
                form.getSellerId(), form.getTemplateId(), form.getSkuIds().size());

        if (form.getSkuIds().isEmpty()) {
            return ActionResult.fail("SKU ID列表不能为空");
        }

        int count = allegroProductService.claimToFruugoPushOffer(form);
        return count > 0 ?
                ActionResult.success(String.format("成功认领 %d 条记录", count)) :
                ActionResult.fail("认领失败");
    }

    /**
     * 获取产品销售数据
     */
    @Operation(summary = "获取产品销售数据")
    @GetMapping("/getSaleData/{skuId}")
    @Parameters({
            @Parameter(name = "skuId", description = "产品SKU ID", required = true)
    })
    public ActionResult<?> getSaleData(@PathVariable("skuId") Integer skuId) {
        log.info("获取Allegro产品销售数据，ID: {}", skuId);

        List<?> saleData = allegroProductService.getProductSaleData(skuId);
        return ActionResult.success(saleData);
    }

    /**
     * 获取产品店铺数据
     */
    @Operation(summary = "获取产品店铺数据")
    @GetMapping("/getSellerData/{skuId}")
    @Parameters({
            @Parameter(name = "skuId", description = "产品SKU ID", required = true)
    })
    public ActionResult<?> getSellerData(@PathVariable("skuId") Integer skuId) {
        log.info("获取Allegro产品店铺数据，ID: {}", skuId);

        List<?> sellerData = allegroProductService.getProductSellerData(skuId);
        return ActionResult.success(sellerData);
    }
}
