package com.xinghuo.allegro.manage.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.BaseEntityV2;
import lombok.Data;

import java.util.Date;

@Data
@TableName("zz_export_task")
public class ExportTaskEntity  extends BaseEntityV2.CUBaseEntityV2 {

    @TableField("task_name")
    private String taskName;

    @TableField("execute_time")
    private Date executeTime;

    @TableField("complete_time")
    private Date completeTime;

    @TableField("status")
    private String status;

    @TableField("file_url")
    private String fileUrl;

    /**
     * 导出类型：pushOffer-推送报价, product-产品数据, etc.
     */
    @TableField("export_type")
    private String exportType;

    /**
     * 查询条件JSON
     */
    @TableField("query_params")
    private String queryParams;

    /**
     * 总记录数
     */
    @TableField("total_count")
    private Integer totalCount;

    /**
     * 已处理记录数
     */
    @TableField("processed_count")
    private Integer processedCount;

    /**
     * 文件名
     */
    @TableField("file_name")
    private String fileName;

    /**
     * 文件大小（字节）
     */
    @TableField("file_size")
    private Long fileSize;

    /**
     * 错误信息
     */
    @TableField("error_message")
    private String errorMessage;

    /**
     * 进度百分比
     */
    @TableField("progress")
    private Integer progress;

}
