package com.xinghuo.allegro.aiservice.model;

public class AiUtil {

    /**
     * 判断文本是否包含三个以上的数字
     *
     * @param text 文本
     * @return 是否包含三个以上的数字
     */
    public static boolean containsMoreThanThreeNumbers(String text) {
        int count = 0;
        for (char c : text.toCharArray()) {
            if (Character.isDigit(c)) {
                count++;
            }
        }
        return count >= 3;
    }

    //判断长度是否小于50,则不处理。
    public static boolean isLessThan50(String text) {
        return text.length() < 50;
    }

    //总体判断，长度小于50，不处理，包含三个以上数字，不处理
    public static boolean isNotProcess(String text) {
        return isLessThan50(text) || containsMoreThanThreeNumbers(text);
    }
}
