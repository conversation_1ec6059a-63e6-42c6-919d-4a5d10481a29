package com.xinghuo.admin.aop;

import com.xinghuo.admin.constant.PermissionConstant;
import com.xinghuo.admin.util.PermissionAspectUtil;
import com.xinghuo.common.util.UserProvider;
import com.xinghuo.permission.entity.OrganizeRelationEntity;
import com.xinghuo.permission.model.authorize.SaveBatchForm;
import com.xinghuo.permission.service.OrganizeRelationService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 切面类：权限管理切面
 * 实现了 PermissionAdminBase 接口
 *
 * <AUTHOR>
 * @date 2023-10-05
 */
@Slf4j
@Aspect
@Component
public class PermissionAdminAspect implements PermissionAdminBase {

    @Autowired
    private UserProvider userProvider;
    @Autowired
    private OrganizeRelationService organizeRelationService;

    /**
     * 分级管理切点  定义切点：标注有 @OrganizeAdminIsTrator 注解的方法
     */
    @Pointcut("@annotation(com.xinghuo.common.annotation.OrganizeAdminIsTrator)")
    public void pointcut() {
    }

    /**
     * 环绕通知：处理标注了 @OrganizeAdminIsTrator 注解的方法
     *
     * @param pjp 切点
     * @return 切点执行结果
     * @throws Throwable 异常
     */
    @Around("pointcut()")
    public Object around(ProceedingJoinPoint pjp) throws Throwable {
        return PermissionAdminBase.permissionCommon(pjp, userProvider, this);
    }

    /**
     * 实现 PermissionAdminBase 接口中的 detailPermission 方法
     * 该方法根据方法名判断是否有权限执行操作
     *
     * @param pjp            切点
     * @param operatorUserId 操作者用户ID
     * @param methodName     方法名
     * @return 是否有权限
     */
    @Override
    public Boolean detailPermission(ProceedingJoinPoint pjp, String operatorUserId, String methodName) {
        switch (methodName) {
            case PermissionConstant.METHOD_SAVE:
                // 如果是超级管理员，直接返回 true
                if (userProvider.get().getIsAdministrator()) {
                    return true;
                }
                // 获取角色ID
                String roleId = (String) pjp.getArgs()[0];
                // 获取与角色关联的组织ID列表
                List<String> orgIdList = organizeRelationService.getRelationListByRoleId(roleId).stream().map(OrganizeRelationEntity::getOrganizeId).collect(Collectors.toList());
                StringBuilder orgId = new StringBuilder();
                // 将组织ID拼接成字符串
                orgIdList.stream().forEach(t -> {
                    orgId.append(t + ",");
                });
                // 调用工具类判断是否有权限
                return PermissionAspectUtil.getPermitByOrgId(
                        // 操作目标对象组织ID集合
                        orgId.toString(),
                        operatorUserId,
                        PermissionConstant.METHOD_UPDATE);
            case PermissionConstant.METHOD_SAVE_BATCH:
                // 修改为只有超管才能操作
                if (userProvider.get().getIsAdministrator()) {
                    return true;
                }
                // 得到角色id
                SaveBatchForm saveBatchForm = (SaveBatchForm) pjp.getArgs()[0];
                List<String> list = Arrays.asList(saveBatchForm.getRoleIds());
                if (list.size() == 0) {
                    list = new ArrayList<>();
                    list.add("");
                }
                // 得到组织id
                List<String> orgIdLists = organizeRelationService.getRelationListByRoleIdList(list).stream().map(OrganizeRelationEntity::getOrganizeId).collect(Collectors.toList());
                StringBuilder orgIds = new StringBuilder();
                orgIdLists.stream().forEach(t -> {
                    orgIds.append(t + ",");
                });
                return PermissionAspectUtil.getPermitByOrgId(
                        // 操作目标对象组织ID集合
                        orgIds.toString(),
                        operatorUserId,
                        PermissionConstant.METHOD_UPDATE);
            case PermissionConstant.METHOD_UPDATE:
                //判断是否有当前组织的修改权限
                String organizeId = String.valueOf(pjp.getArgs()[0]);
                return PermissionAspectUtil.containPermission(organizeId, operatorUserId, methodName);
            default:
                return false;
        }
    }
}
