package com.xinghuo.allegro.manage.controller;

import com.xinghuo.allegro.manage.entity.ExportTaskEntity;
import com.xinghuo.allegro.manage.model.task.ExportTaskModel;
import com.xinghuo.allegro.manage.model.task.TaskPagination;
import com.xinghuo.allegro.manage.service.ExportTaskService;
import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.constant.MsgCode;
import com.xinghuo.common.exception.DataException;
import com.xinghuo.common.file.util.FileUploadUtils;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.fruugo.collect.util.FruugoImageUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.util.List;

@Slf4j
@RestController
@Tag(name = "导出任务管理", description = "导出任务管理")
@RequestMapping("/api/allegro/tool/export")
public class ExportTaskController {

    @Resource
    private ExportTaskService exportTaskService;

    @Operation(summary = "导出列表")
    @PostMapping("/getList")
    public ActionResult list(@RequestBody TaskPagination pagination) {
        List<ExportTaskEntity> list = exportTaskService.getList(pagination);
        List<ExportTaskModel> listVO = BeanCopierUtils.copyList(list, ExportTaskModel.class);
        PaginationVO page = BeanCopierUtils.copy(pagination, PaginationVO.class);
        return ActionResult.page(listVO, page);
    }

    @DeleteMapping("/{id}")
    @Parameters({
            @Parameter(name = "id", description = "任务ID", required = true)
    })
    @Operation(summary = "删除导出任务")
    public ActionResult delete(@PathVariable("id") String id) throws DataException {
        log.info("删除导出任务，任务ID: {}", id);

        ExportTaskEntity entity = exportTaskService.getById(id);
        if (entity == null) {
            return ActionResult.fail("任务不存在");
        }

        try {
            // 删除关联的文件
            if (entity.getFileName() != null && !entity.getFileName().isEmpty()) {
                try {
                    // 构建COS Key
                    String cosKey = FruugoImageUtil.BUCKET_PACKAGE + "export/" + entity.getFileName();

                    // 检查文件是否存在于COS中
                    if (FruugoImageUtil.doesImageExist(cosKey)) {
                        // 从COS删除文件
                        FruugoImageUtil.deleteImage(cosKey);
                        log.info("成功从COS删除导出文件: {}", cosKey);
                    } else {
                        log.info("COS中不存在导出文件: {}", cosKey);
                    }
                } catch (Exception e) {
                    log.error("从COS删除导出文件失败，文件名: {}", entity.getFileName(), e);
                    // 继续执行，不因为文件删除失败而阻止任务记录删除
                }
            }

            // 删除数据库记录
            exportTaskService.removeById(id);
            log.info("成功删除导出任务: {}", id);

            return ActionResult.success(MsgCode.SU003.get());

        } catch (Exception e) {
            log.error("删除导出任务失败，任务ID: {}", id, e);
            return ActionResult.fail("删除失败: " + e.getMessage());
        }
    }
}
