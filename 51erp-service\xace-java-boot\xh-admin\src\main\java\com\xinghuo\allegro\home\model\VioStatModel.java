package com.xinghuo.allegro.home.model;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class VioStatModel {

    private Integer totalCount;
    private Integer todayIncrease;
    private Integer monthIncrease;

    @Data
    @Builder
    public static class Metrics {
        private Integer violations;
        private Integer lossMaking;
        private Integer notFound;
    }

    private Metrics metrics;

    @Data
    @Builder
    public static class ProductStats {
        private Integer violationProducts;
        private Integer lossMakingProducts;
        private Integer others;
    }

    private ProductStats productStats;
}
