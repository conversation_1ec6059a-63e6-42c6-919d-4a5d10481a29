package com.xinghuo.allegro.data.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xinghuo.allegro.data.entity.AllegroSaleEntity;
import com.xinghuo.allegro.data.model.sale.AllegroSaleForm;
import com.xinghuo.allegro.data.model.sale.AllegroSalePagination;
import com.xinghuo.allegro.data.model.sale.SaleSkuModel;
import com.xinghuo.allegro.data.service.AllegroSaleService;
import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.common.util.core.DateXhUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * Allegro销售数据Controller
 */
@Slf4j
@RestController
@RequestMapping("/api/allegro/data/sale")
@Tag(name = "Allegro销售数据", description = "Allegro销售数据相关接口")
public class AllegroDataSaleController {

    @Resource
    private AllegroSaleService allegroSaleService;

    /**
     * 获取销售数据列表
     */
    @Operation(summary = "获取销售数据列表")
    @PostMapping("/getList")
    public ActionResult getList(@RequestBody AllegroSalePagination pagination) {
        log.info("获取Allegro销售数据列表，参数: {}", pagination);
        List<AllegroSaleEntity>  list = allegroSaleService.getList(pagination);
        List<AllegroSaleForm> listVO = BeanCopierUtils.copyList(list, AllegroSaleForm.class);
        PaginationVO page = BeanCopierUtils.copy(pagination, PaginationVO.class);
        return ActionResult.page(listVO, page);

    }

    /**
     * 获取销售数据详情
     */
    @Operation(summary = "获取销售数据详情")
    @GetMapping("/{id}")
    public ActionResult<AllegroSaleEntity> getDetail(@Parameter(description = "销售数据ID") @PathVariable String id) {
        log.info("获取Allegro销售数据详情，ID: {}", id);
        AllegroSaleEntity entity = allegroSaleService.getById(id);
        if (entity == null) {
            return ActionResult.fail("销售数据不存在");
        }
        return ActionResult.success(entity);
    }

    /**
     * 更新销售数据
     */
    @Operation(summary = "更新销售数据")
    @PutMapping("/{id}")
    public ActionResult<Boolean> update(@Parameter(description = "销售数据ID") @PathVariable String id, 
                                       @RequestBody AllegroSaleForm form) {
        log.info("更新Allegro销售数据，ID: {}, 参数: {}", id, form);
        
        AllegroSaleEntity entity = allegroSaleService.getById(id);
        if (entity == null) {
            return ActionResult.fail("销售数据不存在");
        }
        
        // 更新实体属性
        if (form.getSellerId() != null) {
            entity.setSellerId(form.getSellerId());
        }
        
        if (form.getProductId() != null) {
            entity.setOfferId(form.getProductId().toString());
        }
        
        if (form.getSkuId() != null) {
            entity.setSkuId(form.getSkuId());
        }
        
        if (StrXhUtil.isNotEmpty(form.getOfferLink())) {
            entity.setOfferLink(form.getOfferLink());
        }
        
        if (StrXhUtil.isNotEmpty(form.getImageUrl())) {
            entity.setImageUrl(form.getImageUrl());
        }
        
        if (form.getSaleDate() != null) {
            entity.setSaleDate(form.getSaleDate());
        }
        
        if (form.getOldStock() != null) {
            entity.setOldSales(form.getOldStock());
        }
        
        if (form.getNewStock() != null) {
            entity.setNewSales(form.getNewStock());
        }
        
        if (form.getSalesNum() != null) {
            entity.setSalesNum(form.getSalesNum());
        }
        
        if (form.getCategoryId() != null) {
            entity.setCategoryId(form.getCategoryId());
        }

        // 更新实体
        boolean result = allegroSaleService.updateById(entity);
        
        return ActionResult.success(result);
    }

    /**
     * 删除销售数据
     */
    @Operation(summary = "删除销售数据")
    @DeleteMapping("/{id}")
    public ActionResult<Boolean> delete(@Parameter(description = "销售数据ID") @PathVariable String id) {
        log.info("删除Allegro销售数据，ID: {}", id);
        boolean result = allegroSaleService.removeById(id);
        return ActionResult.success(result);
    }

    /**
     * 批量删除销售数据
     */
    @Operation(summary = "批量删除销售数据")
    @PostMapping("/batchDelete")
    public ActionResult<Boolean> batchDelete(@RequestBody List<String> ids) {
        log.info("批量删除Allegro销售数据，IDs: {}", ids);
        boolean result = allegroSaleService.removeByIds(ids);
        return ActionResult.success(result);
    }

    /**
     * 获取产品销售记录
     */
    @Operation(summary = "获取产品销售记录")
    @GetMapping("/product/{productId}")
    public ActionResult<List<AllegroSaleEntity>> getSalesByProductId(
            @Parameter(description = "产品ID") @PathVariable String productId,
            @Parameter(description = "开始日期") @RequestParam(required = false) String startDate,
            @Parameter(description = "结束日期") @RequestParam(required = false) String endDate) {
        log.info("获取Allegro产品销售记录，产品ID: {}, 开始日期: {}, 结束日期: {}", productId, startDate, endDate);
        
        LambdaQueryWrapper<AllegroSaleEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AllegroSaleEntity::getOfferId, productId);
        
        if (StrXhUtil.isNotEmpty(startDate)) {
            Date start = DateXhUtil.parseDate(startDate);
            queryWrapper.ge(AllegroSaleEntity::getSaleDate, start);
        }
        
        if (StrXhUtil.isNotEmpty(endDate)) {
            Date end = DateXhUtil.parseDate(endDate);
            queryWrapper.le(AllegroSaleEntity::getSaleDate, end);
        }
        
        queryWrapper.orderByDesc(AllegroSaleEntity::getSaleDate);
        
        List<AllegroSaleEntity> list = allegroSaleService.list(queryWrapper);
        
        return ActionResult.success(list);
    }

    /**
     * 获取SKU列表
     */
    @Operation(summary = "获取SKU列表")
    @GetMapping("/getSkuList")
    public ActionResult<List<SaleSkuModel>> getSkuList() {
        return ActionResult.success(allegroSaleService.getSkuList());
    }
}
