package com.xinghuo.amazon.collect.entity;


import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 亚马逊产品SKU（变体）信息实体类
 */
@Data
@TableName("zz_amazon_products_sku")
public class AmazonProductSkuEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "sku_id", type = IdType.AUTO)
    private Integer skuId;

    @TableField("asin")
    private String asin;

    @TableField("parent_asin")
    private String parentAsin;

    @TableField("price")
    private BigDecimal price;

    @TableField("currency")
    private String currency;

    @TableField("stock_status")
    private String stockStatus;

    @TableField("image_url")
    private String imageUrl;

    @TableField("variation_attributes")
    private String variationAttributes; // JSON格式字符串，可后续映射为对象或Map

    @TableField("is_deleted")
    private Boolean isDeleted;

    @TableField(value = "last_crawled_at", fill = FieldFill.INSERT_UPDATE)
    private Date lastCrawledAt;
}