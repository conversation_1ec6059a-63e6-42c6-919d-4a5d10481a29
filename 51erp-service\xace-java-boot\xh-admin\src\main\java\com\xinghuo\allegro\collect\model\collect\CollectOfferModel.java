package com.xinghuo.allegro.collect.model.collect;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 报价model
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class  CollectOfferModel {

    @Schema(description = "报价链接")
    private String offerLink;

    @Schema(description = "报价ID")
    private String offerId;

    @Schema(description = "报价名称")
    private String offerName;

    @Schema(description = "产品名称")
    private String productName;

    @Schema(description = "首图地址")
    private String imageUrl;

    @Schema(description = "产品价格")
    private BigDecimal price;

    @Schema(description = "产品运费")
    private BigDecimal shipFee;

    @Schema(description = "产品总价")
    private BigDecimal totalPrice;

    @Schema(description = "产品报价数")
    private Integer productOffersCount;


    @Schema(description = "产品销量")
    private Integer buyersQuantity;

    @Schema(description = "产品ID")
    private String productId;

    @Schema(description = "采集人")
    private String clientId;

    @Schema(description = "卖家ID")
    private String sellerId;

    @Schema(description = "目录ID")
    private String categoryId;

    @Schema(description = "任务ID")
    private String taskId;
    @Schema(description = "任务类型")
    private String taskType;

    private Integer reviewCount;
    private BigDecimal rating;

}
