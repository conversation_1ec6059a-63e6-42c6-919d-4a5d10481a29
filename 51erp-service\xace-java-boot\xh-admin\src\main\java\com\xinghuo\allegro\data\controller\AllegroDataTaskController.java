package com.xinghuo.allegro.data.controller;

import com.xinghuo.allegro.collect.model.collect.CollectTaskPagination;
import com.xinghuo.allegro.data.entity.AllegroDataTaskEntity;
import com.xinghuo.allegro.data.model.AllegroDataTaskForm;
import com.xinghuo.allegro.data.service.AllegroDataTaskService;
import com.xinghuo.allegro.util.AllegroConstant;
import com.xinghuo.common.base.ActionResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

import java.util.List;

/**
 * Allegro 英文采集任务控制器
 *
 * <AUTHOR>
 * @date 2024-11-05
 */
@Slf4j
@RestController
@Tag(name = "Allegro 英文采集任务", description = "Allegro 英文采集任务管理")
@RequestMapping("/api/allegro/data/task")
public class AllegroDataTaskController {

    @Resource
    private AllegroDataTaskService allegroDataTaskService;

    /**
     * 分页查询采集任务列表
     */
    @Operation(summary = "分页查询英文采集任务列表")
    @PostMapping("/list")
    public ActionResult<List<AllegroDataTaskEntity>> getList(@RequestBody CollectTaskPagination pagination) {
        List<AllegroDataTaskEntity> list = allegroDataTaskService.getList(pagination);
        return ActionResult.success(list);
    }

    /**
     * 获取待处理的任务
     */
    @Operation(summary = "获取待处理的英文采集任务")
    @GetMapping("/waitGets")
    public ActionResult<AllegroDataTaskEntity> waitGets(
            @RequestParam String clientId,
            @RequestParam(required = false) String taskType,
            @RequestParam(defaultValue = "allegro") String platform) {

        AllegroDataTaskEntity task = allegroDataTaskService.waitGets(clientId, taskType, platform);
        return ActionResult.success(task);
    }

    /**
     * 更新采集任务状态
     */
    @Operation(summary = "更新英文采集任务状态")
    @PutMapping("/updateStatus/{taskId}")
    public ActionResult<String> updateCollectTask(@PathVariable String taskId) {
        int result = allegroDataTaskService.updateCollectTask(taskId);
        if (result > 0) {
            return ActionResult.success("任务状态更新成功");
        } else {
            return ActionResult.fail("任务状态更新失败");
        }
    }

    /**
     * 检查是否存在指定产品的任务
     */
    @Operation(summary = "检查是否存在指定产品的英文采集任务")
    @GetMapping("/existTask/{productId}")
    public ActionResult<Boolean> existTask(@PathVariable String productId) {
        boolean exists = allegroDataTaskService.existTask(productId);
        return ActionResult.success(exists);
    }

    /**
     * 获取待处理任务数量
     */
    @Operation(summary = "获取待处理英文采集任务数量")
    @GetMapping("/pendingCount")
    public ActionResult<Long> getPendingTaskCount(@RequestParam(required = false) String taskType) {
        long count = allegroDataTaskService.getPendingTaskCount(taskType);
        return ActionResult.success(count);
    }

    /**
     * 接收并处理前端提交的任务完成信息
     * 该方法主要负责更新任务状态，设置完成时间和统计数据
     *
     * @param taskForm 包含任务完成信息的表单
     * @return 成功保存的提示信息
     */
    @Operation(summary = "前端提交英文采集任务完成的json数据")
    @PutMapping("/putTaskJson")
    public ActionResult<String> putTaskJson(@RequestBody AllegroDataTaskForm taskForm) {
        log.debug("前端提交英文采集任务完成的json数据 taskId:{}", taskForm.getId());

        // 获取任务实体
        AllegroDataTaskEntity taskEntity = allegroDataTaskService.getById(taskForm.getId());
        if (taskEntity != null) {
            // 更新任务状态为已完成
            taskEntity.setStatus(AllegroConstant.REQUEST_STATUS_FINISH);
            taskEntity.setTotalNum(taskForm.getTotalNum());
            taskEntity.setTotalSalesNum(taskForm.getTotalSalesNum());
            taskEntity.setFinishTime(new Date());
            taskEntity.setSumCount(taskForm.getSumCount());
            taskEntity.setLastUpdatedAt(new Date());

            allegroDataTaskService.updateById(taskEntity);

            log.info("英文采集任务完成，taskId: {}, totalNum: {}, sumCount: {}, totalSalesNum: {}",
                    taskForm.getId(), taskForm.getTotalNum(), taskForm.getSumCount(), taskForm.getTotalSalesNum());

            return ActionResult.success("成功保存英文采集任务完成信息！" + taskForm.getId());
        } else {
            log.warn("英文采集任务数据在系统中不存在，忽略。ID:" + taskForm.getId());
            return ActionResult.fail("任务数据不存在，忽略。ID:" + taskForm.getId());
        }
    }
}
