package com.xinghuo.allegro.aiservice.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

@Data
@TableName("zz_service_description_variant")
public class DescriptionVariantEntity   {

    /**
     * 主键，自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 描述ID，关联到 zz_service_description 表
     */
    @TableField("description_id")
    private Long descriptionId;

    /**
     * 变体描述
     */
    @TableField("variant_description")
    private String variantDescription;

    @TableField("digest")
    private String digest;

    /**
     * 描述长度
     */
    @TableField("length")
    private Integer length;

    /**
     * 所属平台（如 QWEN, XUNFEI）
     */
    @TableField("platform")
    private String platform;

    /**
     * 质量标记，0表示不合格，1表示合格
     */
    @TableField("quality_flag")
    private Boolean qualityFlag;

    /**
     * 创建时间，默认当前时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private Date createdAt;
}
