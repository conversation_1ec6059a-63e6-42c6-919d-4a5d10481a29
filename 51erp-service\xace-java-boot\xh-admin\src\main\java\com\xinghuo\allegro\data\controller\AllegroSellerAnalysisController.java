package com.xinghuo.allegro.data.controller;

import com.xinghuo.allegro.data.model.seller.AllegroSellerAnalysisVO;
import com.xinghuo.allegro.data.service.AllegroSellerAnalysisService;
import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.util.core.DateXhUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Allegro卖家分析控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@Tag(name = "Allegro卖家分析", description = "Allegro卖家分析")
@RequestMapping("/api/allegro/data/seller/analysis")
public class AllegroSellerAnalysisController {

    @Resource
    private AllegroSellerAnalysisService allegroSellerAnalysisService;

    /**
     * 获取卖家统计数据
     */
    @Operation(summary = "获取卖家统计数据")
    @GetMapping("/stats")
    public ActionResult<AllegroSellerAnalysisVO> getSellerStats() {
        log.info("获取Allegro卖家统计数据");
        AllegroSellerAnalysisVO stats = allegroSellerAnalysisService.getSellerStats();

        // 获取中国卖家统计数据并添加到返回结果中
        Map<String, Object> chinaStats = allegroSellerAnalysisService.getChinaSellerStats();

        // 设置中国卖家统计数据
        stats.setChinaTotalSellers(((Number) chinaStats.get("totalSellers")).longValue());
        stats.setChinaActiveSellers(((Number) chinaStats.get("activeSellers")).longValue());
        stats.setChinaPremiumSellers(((Number) chinaStats.get("premiumSellers")).longValue());
        stats.setChinaNewSellers(((Number) chinaStats.get("newSellers")).longValue());
        stats.setChinaActiveRate((Double) chinaStats.get("activeRate"));
        stats.setChinaPremiumRate((Double) chinaStats.get("premiumRate"));
        stats.setChinaGrowthRate((Double) chinaStats.get("growthRate"));

        return ActionResult.success(stats);
    }

    /**
     * 获取中国卖家统计数据
     */
    @Operation(summary = "获取中国卖家统计数据")
    @GetMapping("/chinaSellerStats")
    public ActionResult<Map<String, Object>> getChinaSellerStats() {
        log.info("获取Allegro中国卖家统计数据");
        Map<String, Object> stats = allegroSellerAnalysisService.getChinaSellerStats();
        return ActionResult.success(stats);
    }

    /**
     * 获取中国卖家趋势数据
     */
    @Operation(summary = "获取中国卖家趋势数据")
    @GetMapping("/chinaSellersTrend")
    public ActionResult<List<Map<String, Object>>> getChinaSellersTrend(@RequestParam(required = false, defaultValue = "30") Integer days) {
        log.info("获取Allegro中国卖家趋势数据, days={}", days);
        List<Map<String, Object>> trend = allegroSellerAnalysisService.getChinaSellersTrend(days);
        return ActionResult.success(trend);
    }

    /**
     * 获取卖家类型分布数据
     */
    @Operation(summary = "获取卖家类型分布数据")
    @GetMapping("/typeStats")
    public ActionResult<Map<String, Object>> getSellerTypeStats() {
        log.info("获取Allegro卖家类型分布数据");
        Map<String, Object> stats = allegroSellerAnalysisService.getSellerTypeStats();
        return ActionResult.success(stats);
    }

    /**
     * 获取卖家状态分布数据
     */
    @Operation(summary = "获取卖家状态分布数据")
    @GetMapping("/statusStats")
    public ActionResult<Map<String, Object>> getSellerStatusStats() {
        log.info("获取Allegro卖家状态分布数据");
        Map<String, Object> stats = allegroSellerAnalysisService.getSellerStatusStats();
        return ActionResult.success(stats);
    }

    /**
     * 获取新发现卖家趋势数据
     */
    @Operation(summary = "获取新发现卖家趋势数据")
    @GetMapping("/newSellersTrend")
    public ActionResult<List<Map<String, Object>>> getNewSellersTrend(
            @Parameter(description = "天数") @RequestParam(value = "days", defaultValue = "30") Integer days) {
        log.info("获取Allegro新发现卖家趋势数据，天数: {}", days);
        List<Map<String, Object>> trend = allegroSellerAnalysisService.getNewSellersTrend(days);
        return ActionResult.success(trend);
    }

    /**
     * 获取黑名单卖家趋势数据
     */
    @Operation(summary = "获取黑名单卖家趋势数据")
    @GetMapping("/blockedSellersTrend")
    public ActionResult<List<Map<String, Object>>> getBlockedSellersTrend(
            @Parameter(description = "天数") @RequestParam(value = "days", defaultValue = "30") Integer days) {
        log.info("获取Allegro黑名单卖家趋势数据，天数: {}", days);
        List<Map<String, Object>> trend = allegroSellerAnalysisService.getBlockedSellersTrend(days);
        return ActionResult.success(trend);
    }

    /**
     * 获取月度卖家统计数据
     */
    @Operation(summary = "获取月度卖家统计数据")
    @GetMapping("/monthlyStats")
    public ActionResult<List<Map<String, Object>>> getMonthlyStats(
            @Parameter(description = "开始日期") @RequestParam(value = "startDate", required = false) String startDate,
            @Parameter(description = "结束日期") @RequestParam(value = "endDate", required = false) String endDate) {
        log.info("获取Allegro月度卖家统计数据，开始日期: {}, 结束日期: {}", startDate, endDate);

        Date start = startDate != null ? DateXhUtil.parseDate(startDate) : null;
        Date end = endDate != null ? DateXhUtil.parseDate(endDate) : null;

        List<Map<String, Object>> stats = allegroSellerAnalysisService.getMonthlyStats(start, end);
        return ActionResult.success(stats);
    }

    /**
     * 获取高级卖家统计数据
     */
    @Operation(summary = "获取高级卖家统计数据")
    @GetMapping("/premiumSellersStats")
    public ActionResult<Map<String, Object>> getPremiumSellersStats() {
        log.info("获取Allegro高级卖家统计数据");
        Map<String, Object> stats = allegroSellerAnalysisService.getPremiumSellersStats();
        return ActionResult.success(stats);
    }

    /**
     * 获取热门卖家数据
     */
    @Operation(summary = "获取热门卖家数据")
    @GetMapping("/hotSellers")
    public ActionResult<List<Map<String, Object>>> getHotSellers(
            @Parameter(description = "天数") @RequestParam(value = "days", defaultValue = "7") Integer days,
            @Parameter(description = "限制数量") @RequestParam(value = "limit", defaultValue = "10") Integer limit) {
        log.info("获取Allegro热门卖家数据，天数: {}, 限制数量: {}", days, limit);
        List<Map<String, Object>> hotSellers = allegroSellerAnalysisService.getHotSellers(days, limit);
        return ActionResult.success(hotSellers);
    }
}
