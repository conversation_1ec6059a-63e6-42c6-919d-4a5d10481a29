package com.xinghuo.allegro.collect.model.collectOffer;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class CollectSettingModel {
    private RaiseMode raiseMode;
    private PriceMode priceMode;
    private CompanyMode companyMode;
    private GoodRegionMode goodRegionMode;
    private ZeroSaleMode zeroSaleMode;
    private DuplicateMode duplicateMode;
    private BlackSellerMode blackSellerMode;
    private NonDeliveryMode nonDeliveryMode;


    @Data
    public static class PriceMode {
        private boolean enabled;
        private BigDecimal min;
        private BigDecimal max;
    }

    @Data
    public static class RaiseMode {
        private boolean enabled;
        private BigDecimal lessPrice;
        private BigDecimal targetPrice;
    }

    @Data
    public static class CompanyMode {
        @Schema(description = "是否启用公司所属国家过滤")
        private boolean enabled;
        @Schema(description = "公司国家列表")
        private List<String> countries;

    }

    //商品的发货国家
    @Data
    public static class GoodRegionMode {
        @Schema(description = "发货地检测")
        private boolean enabled;
        @Schema(description = "发货地国家列表")
        private List<String> countries;

    }

    @Data
    public static class ZeroSaleMode {
        @Schema(description = "零销量,命中侵权词则不采集")
        private boolean vioEnabled;
        @Schema(description = "零销量,存在同样标题和首图的数据不采集")
        private boolean sameBlockedEnabled;


    }

    @Data
    public static class DuplicateMode {
        @Schema(description = "是否启用重复检查，检查的是offerId")
        private boolean enabled;
    }

    @Data
    public static class BlackSellerMode {
        @Schema(description = "是否启用黑名单卖家过滤")
        private boolean enabled;
        @Schema(description = "黑名单卖家列表")
        private List<String> blockBlackSellerIdlist;
    }

    @Data
    public static class NonDeliveryMode {
        @Schema(description = "是否启用不发货城市过滤")
        private boolean enabled;
        private List<NonDeliverySeller> nonDeliverySellers;

    }
    @Data
    public static class NonDeliverySeller {
        @Schema(description = "城市名称")
        private String city;
        @Schema(description = "运费")
        private BigDecimal shipee;
    }
}
