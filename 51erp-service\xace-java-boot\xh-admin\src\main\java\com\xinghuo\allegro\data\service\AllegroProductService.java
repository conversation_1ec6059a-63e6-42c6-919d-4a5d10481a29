package com.xinghuo.allegro.data.service;

import com.xinghuo.allegro.push.entity.ErpProductEntity;
import com.xinghuo.common.base.service.BaseService;
import com.xinghuo.fruugo.data.model.AllegroProductClaimForm;
import com.xinghuo.fruugo.data.model.AllegroProductPagination;
import com.xinghuo.fruugo.data.model.AllegroProductUpdateModel;

import java.util.List;

/**
 * Allegro产品Service接口
 *
 * <AUTHOR>
 * @date 2025-05-18
 */
public interface AllegroProductService extends BaseService<ErpProductEntity> {

    /**
     * 获取产品列表
     *
     * @param pagination 分页查询条件
     * @return 产品列表
     */
    List<ErpProductEntity> getList(AllegroProductPagination pagination);

    /**
     * 获取产品详情
     *
     * @param skuId 产品SKU ID
     * @return 产品详情
     */
    ErpProductEntity getInfo(Integer skuId);

    /**
     * 更新产品信息
     *
     * @param model 产品更新模型
     * @return 是否成功
     */
    boolean updateProduct(AllegroProductUpdateModel model);

    /**
     * 批量删除产品
     *
     * @param skuIds 产品SKU ID列表
     * @return 删除数量
     */
    int batchDelete(List<Integer> skuIds);

    /**
     * 从Allegro产品认领到Fruugo推送报价
     *
     * @param form 认领表单
     * @return 认领数量
     */
    int claimToFruugoPushOffer(AllegroProductClaimForm form);

    /**
     * 同步Allegro产品英文数据
     *
     * @param skuId 产品SKU ID
     * @return 是否成功
     */
    boolean syncProductEnData(Integer skuId);

    /**
     * 获取产品关联的销售数据
     *
     * @param skuId 产品SKU ID
     * @return 销售数据列表
     */
    List<?> getProductSaleData(Integer skuId);

    /**
     * 获取产品关联的店铺数据
     *
     * @param skuId 产品SKU ID
     * @return 店铺数据列表
     */
    List<?> getProductSellerData(Integer skuId);
}
