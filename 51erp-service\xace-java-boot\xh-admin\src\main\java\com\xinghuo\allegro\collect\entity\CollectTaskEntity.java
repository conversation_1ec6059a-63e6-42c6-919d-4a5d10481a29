package com.xinghuo.allegro.collect.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.BaseEntityV2;
import lombok.Data;

import java.util.Date;

@Data
@TableName("zz_collect_task")
public class CollectTaskEntity extends BaseEntityV2.CUBaseEntityV2 {

    @TableId
    private String id;

    /** 任务类型 */
    @TableField("task_type")
    private String taskType;

    /** 目录 */
    @TableField("category_id")
    private String categoryId;

    /** 采集优先级 */
    @TableField("priority")
    private Integer priority;

    /** 链接名称 */
    @TableField("link")
    private String link;

    /** 采集客户端 */
    @TableField("client_id")
    private String clientId;

    /** Offer数量 */
    @TableField("total_num")
    private Integer totalNum;

    /** 累计收集数 */
    @TableField("sum_count")
    private Integer sumCount;

    /** 有销售额数 */
    @TableField("total_sales_num")
    private Integer totalSalesNum;

    /** 请求时间 */
    @TableField("request_time")
    private Date requestTime;

    /** 完成时间 */
    @TableField("finish_time")
    private Date finishTime;

    /** 采集状态 */
    @TableField("status")
    private Integer status;

    /** 平台 */
    @TableField("platform")
    private String platform;

    /** 路径 */
    @TableField("path")
    private String path;

    /** 最大页数 */
    @TableField("max_pages")
    private Integer maxPages;
}
