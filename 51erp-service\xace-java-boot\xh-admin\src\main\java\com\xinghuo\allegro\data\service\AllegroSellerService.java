package com.xinghuo.allegro.data.service;

import com.alicp.jetcache.anno.CacheInvalidate;
import com.alicp.jetcache.anno.Cached;
import com.fasterxml.jackson.databind.JsonNode;
import com.xinghuo.allegro.collect.model.collect.CollectTaskForm;
import com.xinghuo.allegro.data.entity.AllegroSellerEntity;
import com.xinghuo.allegro.data.model.seller.AllegroSellerPagination;
import com.xinghuo.common.base.service.BaseService;

import java.util.List;

public interface AllegroSellerService extends BaseService<AllegroSellerEntity> {

    String CACHE_NAME =  "COLLECT_SELLER";

    /**
     * 获取卖家列表（新接口）
     *
     * @param pagination 分页参数
     * @return 卖家列表
     */
    List<AllegroSellerEntity> getList(AllegroSellerPagination pagination);

    AllegroSellerEntity getInfo(String id);

    @Cached(name = CACHE_NAME, key = "#sellerId", expire = 360)
    AllegroSellerEntity getSimpleInfoBySellerId(String sellerId);


    AllegroSellerEntity getInfoBySellerId(String sellerId);

    @CacheInvalidate(name = CACHE_NAME, key = "#entity.sellerId")
    boolean save(AllegroSellerEntity entity);


    @CacheInvalidate(name = CACHE_NAME, key = "#entity.sellerId")
    boolean update(String id, AllegroSellerEntity entity);

    int updateSellerOfferCount(CollectTaskForm taskForm);

    AllegroSellerEntity syncSellerData(JsonNode offerJsonNode);
    void checkBlockSeller(String tenantId);

    void checkBlockSeller(String tenantId,String sellerId,boolean newBlock);

    long validCount();


}
