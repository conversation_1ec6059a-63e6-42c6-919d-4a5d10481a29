[2025-06-15 17:24:07.294] [INFO ] [main] [com.xinghuo.admin.XhAdminApplication] [?] [?] - Starting XhAdminApplication using Java 20.0.1 with PID 35996 (G:\v2\51erp\51erp-service\xace-java-boot\xh-admin\target\classes started by Administrator in G:\v2\51erp)
[2025-06-15 17:24:07.295] [INFO ] [main] [com.xinghuo.admin.XhAdminApplication] [?] [?] - The following 1 profile is active: "vpn"
[2025-06-15 17:24:09.888] [INFO ] [main] [o.s.d.r.config.RepositoryConfigurationDelegate] [?] [?] - Multiple Spring Data modules found, entering strict repository configuration mode
[2025-06-15 17:24:09.891] [INFO ] [main] [o.s.d.r.config.RepositoryConfigurationDelegate] [?] [?] - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[2025-06-15 17:24:09.916] [INFO ] [main] [o.s.d.r.config.RepositoryConfigurationDelegate] [?] [?] - Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces.
[2025-06-15 17:24:11.521] [INFO ] [main] [o.s.boot.web.embedded.tomcat.TomcatWebServer] [?] [?] - Tomcat initialized with port 32000 (http)
[2025-06-15 17:24:11.532] [INFO ] [main] [org.apache.catalina.core.StandardService] [?] [?] - Starting service [Tomcat]
[2025-06-15 17:24:11.533] [INFO ] [main] [org.apache.catalina.core.StandardEngine] [?] [?] - Starting Servlet engine: [Apache Tomcat/10.1.19]
[2025-06-15 17:24:11.616] [INFO ] [main] [o.a.c.core.ContainerBase.[Tomcat].[localhost].[/]] [?] [?] - Initializing Spring embedded WebApplicationContext
[2025-06-15 17:24:11.616] [INFO ] [main] [o.s.b.w.s.c.ServletWebServerApplicationContext] [?] [?] - Root WebApplicationContext: initialization completed in 4082 ms
[2025-06-15 17:24:12.060] [INFO ] [main] [org.redisson.Version] [?] [?] - Redisson 3.27.2
[2025-06-15 17:24:12.448] [INFO ] [redisson-netty-1-4] [org.redisson.connection.ConnectionsHolder] [?] [?] - 1 connections initialized for 127.0.0.1/127.0.0.1:8001
[2025-06-15 17:24:12.525] [INFO ] [redisson-netty-1-19] [org.redisson.connection.ConnectionsHolder] [?] [?] - 24 connections initialized for 127.0.0.1/127.0.0.1:8001
[2025-06-15 17:24:13.501] [INFO ] [main] [com.alibaba.druid.pool.DruidDataSource] [?] [?] - {dataSource-1,master} inited
[2025-06-15 17:24:13.502] [INFO ] [main] [c.b.dynamic.datasource.DynamicRoutingDataSource] [?] [?] - dynamic-datasource - add a datasource named [master] success
[2025-06-15 17:24:13.503] [INFO ] [main] [c.b.dynamic.datasource.DynamicRoutingDataSource] [?] [?] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
[2025-06-15 17:24:25.152] [INFO ] [main] [c.b.dynamic.datasource.DynamicRoutingDataSource] [?] [?] - dynamic-datasource start closing ....
[2025-06-15 17:24:25.155] [INFO ] [main] [com.alibaba.druid.pool.DruidDataSource] [?] [?] - {dataSource-1} closing ...
[2025-06-15 17:24:25.166] [INFO ] [main] [com.alibaba.druid.pool.DruidDataSource] [?] [?] - {dataSource-1} closed
[2025-06-15 17:24:25.167] [INFO ] [main] [c.b.d.d.destroyer.DefaultDataSourceDestroyer] [?] [?] - dynamic-datasource close the datasource named [master] success,
[2025-06-15 17:24:25.167] [INFO ] [main] [c.b.dynamic.datasource.DynamicRoutingDataSource] [?] [?] - dynamic-datasource all closed success,bye
[2025-06-15 17:24:25.195] [INFO ] [main] [org.apache.catalina.core.StandardService] [?] [?] - Stopping service [Tomcat]
[2025-06-15 17:24:25.221] [INFO ] [main] [o.s.b.a.logging.ConditionEvaluationReportLogger] [?] [?] - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
[2025-06-15 17:48:19.915] [INFO ] [main] [com.xinghuo.admin.XhAdminApplication] [?] [?] - Starting XhAdminApplication using Java 20.0.1 with PID 31484 (G:\v2\51erp\51erp-service\xace-java-boot\xh-admin\target\classes started by Administrator in G:\v2\51erp)
[2025-06-15 17:48:19.916] [INFO ] [main] [com.xinghuo.admin.XhAdminApplication] [?] [?] - The following 1 profile is active: "vpn"
[2025-06-15 17:48:22.417] [INFO ] [main] [o.s.d.r.config.RepositoryConfigurationDelegate] [?] [?] - Multiple Spring Data modules found, entering strict repository configuration mode
[2025-06-15 17:48:22.419] [INFO ] [main] [o.s.d.r.config.RepositoryConfigurationDelegate] [?] [?] - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[2025-06-15 17:48:22.446] [INFO ] [main] [o.s.d.r.config.RepositoryConfigurationDelegate] [?] [?] - Finished Spring Data repository scanning in 13 ms. Found 0 Redis repository interfaces.
[2025-06-15 17:48:24.145] [INFO ] [main] [o.s.boot.web.embedded.tomcat.TomcatWebServer] [?] [?] - Tomcat initialized with port 32000 (http)
[2025-06-15 17:48:24.158] [INFO ] [main] [org.apache.catalina.core.StandardService] [?] [?] - Starting service [Tomcat]
[2025-06-15 17:48:24.158] [INFO ] [main] [org.apache.catalina.core.StandardEngine] [?] [?] - Starting Servlet engine: [Apache Tomcat/10.1.19]
[2025-06-15 17:48:24.238] [INFO ] [main] [o.a.c.core.ContainerBase.[Tomcat].[localhost].[/]] [?] [?] - Initializing Spring embedded WebApplicationContext
[2025-06-15 17:48:24.238] [INFO ] [main] [o.s.b.w.s.c.ServletWebServerApplicationContext] [?] [?] - Root WebApplicationContext: initialization completed in 4114 ms
[2025-06-15 17:48:24.697] [INFO ] [main] [org.redisson.Version] [?] [?] - Redisson 3.27.2
[2025-06-15 17:48:25.104] [INFO ] [redisson-netty-1-4] [org.redisson.connection.ConnectionsHolder] [?] [?] - 1 connections initialized for 127.0.0.1/127.0.0.1:8001
[2025-06-15 17:48:25.169] [INFO ] [redisson-netty-1-19] [org.redisson.connection.ConnectionsHolder] [?] [?] - 24 connections initialized for 127.0.0.1/127.0.0.1:8001
[2025-06-15 17:48:26.106] [INFO ] [main] [com.alibaba.druid.pool.DruidDataSource] [?] [?] - {dataSource-1,master} inited
[2025-06-15 17:48:26.107] [INFO ] [main] [c.b.dynamic.datasource.DynamicRoutingDataSource] [?] [?] - dynamic-datasource - add a datasource named [master] success
[2025-06-15 17:48:26.107] [INFO ] [main] [c.b.dynamic.datasource.DynamicRoutingDataSource] [?] [?] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
[2025-06-15 17:48:54.891] [INFO ] [main] [com.xinghuo.fruugo.collect.util.FruugoSkuInfoUtil] [?] [?] - 注入FruugoImageService成功
[2025-06-15 17:48:54.891] [INFO ] [main] [com.xinghuo.fruugo.collect.util.FruugoSkuInfoUtil] [?] [?] - 设置allgeroUrl: http://127.0.0.1:3100/staticImage/
[2025-06-15 17:49:01.928] [INFO ] [main] [c.a.jetcache.autoconfigure.AbstractCacheAutoInit] [?] [?] - init cache area default , type= linkedhashmap
[2025-06-15 17:49:01.934] [INFO ] [main] [c.a.jetcache.autoconfigure.AbstractCacheAutoInit] [?] [?] - init cache area default , type= redis.springdata
[2025-06-15 17:49:01.960] [INFO ] [main] [com.alicp.jetcache.support.DefaultMetricsManager] [?] [?] - cache stat period at 15 MINUTES
[2025-06-15 17:49:01.987] [INFO ] [main] [c.a.j.redis.springdata.SpringDataBroadcastManager] [?] [?] - create RedisMessageListenerContainer instance
[2025-06-15 17:49:02.011] [INFO ] [main] [c.a.j.redis.springdata.SpringDataBroadcastManager] [?] [?] - subscribe jetcache invalidate notification. channel=projectA
[2025-06-15 17:49:10.771] [INFO ] [main] [c.xinghuo.common.database.config.IdGeneratorConfig] [?] [?] - 当前ID生成器编号: 730
[2025-06-15 17:49:11.580] [INFO ] [main] [com.xinghuo.scheduletask.config.XxlJobConfig] [?] [?] - >>>>>>>>>>> xxl-job config init.
[2025-06-15 17:49:11.631] [INFO ] [main] [o.d.x.file.storage.core.FileStorageServiceBuilder] [?] [?] - 加载本地升级版存储平台：local-plus-1
[2025-06-15 17:49:12.959] [INFO ] [main] [org.quartz.impl.StdSchedulerFactory] [?] [?] - Using default implementation for ThreadExecutor
[2025-06-15 17:49:12.970] [INFO ] [main] [org.quartz.core.SchedulerSignalerImpl] [?] [?] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
[2025-06-15 17:49:12.971] [INFO ] [main] [org.quartz.core.QuartzScheduler] [?] [?] - Quartz Scheduler v.2.3.2 created.
[2025-06-15 17:49:12.971] [INFO ] [main] [org.quartz.simpl.RAMJobStore] [?] [?] - RAMJobStore initialized.
[2025-06-15 17:49:12.971] [INFO ] [main] [org.quartz.core.QuartzScheduler] [?] [?] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

[2025-06-15 17:49:12.971] [INFO ] [main] [org.quartz.impl.StdSchedulerFactory] [?] [?] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
[2025-06-15 17:49:12.971] [INFO ] [main] [org.quartz.impl.StdSchedulerFactory] [?] [?] - Quartz scheduler version: 2.3.2
[2025-06-15 17:49:12.971] [INFO ] [main] [org.quartz.core.QuartzScheduler] [?] [?] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@43e086f2
[2025-06-15 17:49:13.781] [INFO ] [main] [o.s.b.actuate.endpoint.web.EndpointLinksResolver] [?] [?] - Exposing 14 endpoint(s) beneath base path '/actuator'
[2025-06-15 17:49:15.177] [INFO ] [main] [com.xxl.job.core.executor.XxlJobExecutor] [?] [?] - >>>>>>>>>>> xxl-job register jobhandler success, name:defaultHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@65fc21f4[class com.xinghuo.scheduletask.task.ScheduleTaskHandler#defaultHandler]
[2025-06-15 17:49:15.648] [INFO ] [main] [com.xxl.job.core.executor.XxlJobExecutor] [?] [?] - >>>>>>>>>>> xxl-job registry-remove success, registryParam:{defaultHandler=com.xxl.job.core.handler.impl.MethodJobHandler@65fc21f4[class com.xinghuo.scheduletask.task.ScheduleTaskHandler#defaultHandler]}, registryResult:ReturnT [code=200, msg=null, content=null]
[2025-06-15 17:49:15.701] [INFO ] [main] [o.s.boot.web.embedded.tomcat.TomcatWebServer] [?] [?] - Tomcat started on port 32000 (http) with context path ''
[2025-06-15 17:49:15.702] [INFO ] [main] [o.s.scheduling.quartz.SchedulerFactoryBean] [?] [?] - Starting Quartz Scheduler now
[2025-06-15 17:49:15.702] [INFO ] [main] [org.quartz.core.QuartzScheduler] [?] [?] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
[2025-06-15 17:49:15.722] [INFO ] [main] [com.xinghuo.admin.XhAdminApplication] [?] [?] - Started XhAdminApplication in 56.352 seconds (process running for 57.182)
[2025-06-15 17:49:15.805] [INFO ] [Thread-12] [com.xxl.job.core.server.EmbedServer] [?] [?] - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
[2025-06-15 17:49:16.323] [INFO ] [RMI TCP Connection(5)-**************] [o.a.c.core.ContainerBase.[Tomcat].[localhost].[/]] [?] [?] - Initializing Spring DispatcherServlet 'dispatcherServlet'
[2025-06-15 17:49:16.323] [INFO ] [RMI TCP Connection(5)-**************] [org.springframework.web.servlet.DispatcherServlet] [?] [?] - Initializing Servlet 'dispatcherServlet'
[2025-06-15 17:49:16.326] [INFO ] [RMI TCP Connection(5)-**************] [org.springframework.web.servlet.DispatcherServlet] [?] [?] - Completed initialization in 3 ms
[2025-06-15 17:50:00.035] [INFO ] [scheduling-1] [com.xinghuo.fruugo.analysis.task.FruugoAlertTask] [?] [?] - 开始执行Fruugo数据监控检查任务
[2025-06-15 17:50:00.036] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 开始检查数据处理状态
[2025-06-15 17:50:00.801] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 发送企业微信告警通知 [JustBought数据缺失] [JustBought数据]: 失败
[2025-06-15 17:50:00.986] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 发送企业微信告警通知 [产品数据堆积] [产品数据]: 失败
[2025-06-15 17:50:01.078] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 数据处理状态检查完成，是否有告警: true
[2025-06-15 17:50:01.078] [INFO ] [scheduling-1] [com.xinghuo.fruugo.analysis.task.FruugoAlertTask] [?] [?] - Fruugo数据监控检查任务执行完成，是否有告警: true
[2025-06-15 17:55:00.009] [INFO ] [scheduling-1] [com.xinghuo.fruugo.analysis.task.FruugoAlertTask] [?] [?] - 开始执行Fruugo数据监控检查任务
[2025-06-15 17:55:00.009] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 开始检查数据处理状态
[2025-06-15 17:55:00.134] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 告警 [JustBought数据缺失] [JustBought数据] 在告警间隔期内，跳过本次告警
[2025-06-15 17:55:18.893] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 告警 [产品数据堆积] [产品数据] 在告警间隔期内，跳过本次告警
[2025-06-15 17:55:18.944] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 数据处理状态检查完成，是否有告警: true
[2025-06-15 17:55:18.944] [INFO ] [scheduling-1] [com.xinghuo.fruugo.analysis.task.FruugoAlertTask] [?] [?] - Fruugo数据监控检查任务执行完成，是否有告警: true
[2025-06-15 18:00:00.011] [INFO ] [scheduling-1] [c.x.fruugo.analysis.task.FruugoMonitorRefreshTask] [?] [?] - 开始执行Fruugo监控数据定时刷新任务
[2025-06-15 18:00:00.012] [INFO ] [scheduling-1] [c.x.f.a.service.impl.FruugoMonitorServiceImpl] [?] [?] - 开始生成 2025-06-15 的监控数据
[2025-06-15 18:00:00.027] [INFO ] [JetCacheDefaultExecutor] [com.alicp.jetcache.support.StatInfoLogger] [?] [?] - jetcache stat from 2025-06-15 17:49:01,959 to 2025-06-15 18:00:00,013
cache           |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
----------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
xaceCache       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
xaceCache_local |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
xaceCache_remote|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
----------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

[2025-06-15 18:04:15.733] [INFO ] [scheduling-1] [c.x.f.a.service.impl.FruugoMonitorServiceImpl] [?] [?] - 生成 2025-06-15 的监控数据 成功
[2025-06-15 18:04:15.733] [INFO ] [scheduling-1] [c.x.fruugo.analysis.task.FruugoMonitorRefreshTask] [?] [?] - Fruugo基础监控数据刷新 成功
[2025-06-15 18:04:15.733] [INFO ] [scheduling-1] [c.x.f.a.service.impl.FruugoDataMonitorServiceImpl] [?] [?] - 生成今日的Fruugo数据监控信息
[2025-06-15 18:04:15.733] [INFO ] [scheduling-1] [c.x.f.a.service.impl.FruugoDataMonitorServiceImpl] [?] [?] - 生成指定日期的Fruugo数据监控信息，日期: 2025-06-15
[2025-06-15 18:04:15.734] [INFO ] [scheduling-1] [c.x.f.a.service.impl.FruugoMonitorServiceImpl] [?] [?] - 开始生成 2025-06-15 的监控数据
[2025-06-15 18:07:13.125] [INFO ] [scheduling-1] [c.x.f.a.service.impl.FruugoMonitorServiceImpl] [?] [?] - 生成 2025-06-15 的监控数据 成功
[2025-06-15 18:07:13.125] [INFO ] [scheduling-1] [c.x.f.a.service.impl.FruugoDataMonitorServiceImpl] [?] [?] - 生成指定日期的Fruugo数据监控信息成功，日期: 2025-06-15
[2025-06-15 18:07:13.125] [INFO ] [scheduling-1] [c.x.fruugo.analysis.task.FruugoMonitorRefreshTask] [?] [?] - Fruugo数据监控信息刷新 成功
[2025-06-15 18:07:13.125] [INFO ] [scheduling-1] [c.x.fruugo.analysis.task.FruugoMonitorRefreshTask] [?] [?] - Fruugo监控数据定时刷新任务执行完成
[2025-06-15 18:07:13.126] [INFO ] [scheduling-1] [com.xinghuo.fruugo.analysis.task.FruugoAlertTask] [?] [?] - 开始执行Fruugo数据监控检查任务
[2025-06-15 18:07:13.126] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 开始检查数据处理状态
[2025-06-15 18:07:13.213] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 告警 [JustBought数据缺失] [JustBought数据] 在告警间隔期内，跳过本次告警
[2025-06-15 18:07:20.476] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 告警 [产品数据堆积] [产品数据] 在告警间隔期内，跳过本次告警
[2025-06-15 18:07:20.489] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 数据处理状态检查完成，是否有告警: true
[2025-06-15 18:07:20.489] [INFO ] [scheduling-1] [com.xinghuo.fruugo.analysis.task.FruugoAlertTask] [?] [?] - Fruugo数据监控检查任务执行完成，是否有告警: true
[2025-06-15 18:07:36.541] [INFO ] [SpringApplicationShutdownHook] [org.quartz.core.QuartzScheduler] [?] [?] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
[2025-06-15 18:07:36.582] [INFO ] [SpringApplicationShutdownHook] [o.s.scheduling.quartz.SchedulerFactoryBean] [?] [?] - Shutting down Quartz Scheduler
[2025-06-15 18:07:36.582] [INFO ] [SpringApplicationShutdownHook] [org.quartz.core.QuartzScheduler] [?] [?] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
[2025-06-15 18:07:36.582] [INFO ] [SpringApplicationShutdownHook] [org.quartz.core.QuartzScheduler] [?] [?] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
[2025-06-15 18:07:36.582] [INFO ] [SpringApplicationShutdownHook] [org.quartz.core.QuartzScheduler] [?] [?] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
[2025-06-15 18:07:36.584] [INFO ] [SpringApplicationShutdownHook] [o.dromara.x.file.storage.core.FileStorageService] [?] [?] - 销毁存储平台 local-plus-1 成功
[2025-06-15 18:07:36.585] [INFO ] [Thread-12] [com.xxl.job.core.server.EmbedServer] [?] [?] - >>>>>>>>>>> xxl-job remoting server stop.
[2025-06-15 18:07:39.600] [INFO ] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.thread.ExecutorRegistryThread] [?] [?] - >>>>>>>>>>> xxl-job registry-remove fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='xxl-job-executor-sample1', registryValue='http://**************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(Connect timed out), for url : http://101.33.227.194:30020/xxl-job-admin/api/registryRemove, content=null]
[2025-06-15 18:07:39.600] [INFO ] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.thread.ExecutorRegistryThread] [?] [?] - >>>>>>>>>>> xxl-job, executor registry thread destroy.
[2025-06-15 18:07:39.600] [INFO ] [SpringApplicationShutdownHook] [com.xxl.job.core.server.EmbedServer] [?] [?] - >>>>>>>>>>> xxl-job remoting server destroy success.
[2025-06-15 18:07:39.600] [INFO ] [xxl-job, executor JobLogFileCleanThread] [com.xxl.job.core.thread.JobLogFileCleanThread] [?] [?] - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destroy.
[2025-06-15 18:07:39.601] [INFO ] [xxl-job, executor TriggerCallbackThread] [com.xxl.job.core.thread.TriggerCallbackThread] [?] [?] - >>>>>>>>>>> xxl-job, executor callback thread destroy.
[2025-06-15 18:07:39.601] [INFO ] [Thread-11] [com.xxl.job.core.thread.TriggerCallbackThread] [?] [?] - >>>>>>>>>>> xxl-job, executor retry callback thread destroy.
[2025-06-15 18:07:41.613] [INFO ] [SpringApplicationShutdownHook] [com.alicp.jetcache.support.DefaultMetricsManager] [?] [?] - cache stat canceled
[2025-06-15 18:07:41.649] [INFO ] [SpringApplicationShutdownHook] [c.b.dynamic.datasource.DynamicRoutingDataSource] [?] [?] - dynamic-datasource start closing ....
[2025-06-15 18:07:41.651] [INFO ] [SpringApplicationShutdownHook] [com.alibaba.druid.pool.DruidDataSource] [?] [?] - {dataSource-1} closing ...
[2025-06-15 18:07:41.654] [INFO ] [SpringApplicationShutdownHook] [com.alibaba.druid.pool.DruidDataSource] [?] [?] - {dataSource-1} closed
[2025-06-15 18:07:41.654] [INFO ] [SpringApplicationShutdownHook] [c.b.d.d.destroyer.DefaultDataSourceDestroyer] [?] [?] - dynamic-datasource close the datasource named [master] success,
[2025-06-15 18:07:41.654] [INFO ] [SpringApplicationShutdownHook] [c.b.dynamic.datasource.DynamicRoutingDataSource] [?] [?] - dynamic-datasource all closed success,bye
[2025-06-15 18:07:43.992] [INFO ] [main] [com.xinghuo.admin.XhAdminApplication] [?] [?] - Starting XhAdminApplication using Java 20.0.1 with PID 11068 (G:\v2\51erp\51erp-service\xace-java-boot\xh-admin\target\classes started by Administrator in G:\v2\51erp)
[2025-06-15 18:07:43.992] [INFO ] [main] [com.xinghuo.admin.XhAdminApplication] [?] [?] - The following 1 profile is active: "vpn"
[2025-06-15 18:07:46.113] [INFO ] [main] [o.s.d.r.config.RepositoryConfigurationDelegate] [?] [?] - Multiple Spring Data modules found, entering strict repository configuration mode
[2025-06-15 18:07:46.115] [INFO ] [main] [o.s.d.r.config.RepositoryConfigurationDelegate] [?] [?] - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[2025-06-15 18:07:46.138] [INFO ] [main] [o.s.d.r.config.RepositoryConfigurationDelegate] [?] [?] - Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces.
[2025-06-15 18:07:47.514] [INFO ] [main] [o.s.boot.web.embedded.tomcat.TomcatWebServer] [?] [?] - Tomcat initialized with port 32000 (http)
[2025-06-15 18:07:47.527] [INFO ] [main] [org.apache.catalina.core.StandardService] [?] [?] - Starting service [Tomcat]
[2025-06-15 18:07:47.527] [INFO ] [main] [org.apache.catalina.core.StandardEngine] [?] [?] - Starting Servlet engine: [Apache Tomcat/10.1.19]
[2025-06-15 18:07:47.604] [INFO ] [main] [o.a.c.core.ContainerBase.[Tomcat].[localhost].[/]] [?] [?] - Initializing Spring embedded WebApplicationContext
[2025-06-15 18:07:47.605] [INFO ] [main] [o.s.b.w.s.c.ServletWebServerApplicationContext] [?] [?] - Root WebApplicationContext: initialization completed in 3422 ms
[2025-06-15 18:07:48.032] [INFO ] [main] [org.redisson.Version] [?] [?] - Redisson 3.27.2
[2025-06-15 18:07:48.377] [INFO ] [redisson-netty-1-4] [org.redisson.connection.ConnectionsHolder] [?] [?] - 1 connections initialized for 127.0.0.1/127.0.0.1:8001
[2025-06-15 18:07:48.443] [INFO ] [redisson-netty-1-19] [org.redisson.connection.ConnectionsHolder] [?] [?] - 24 connections initialized for 127.0.0.1/127.0.0.1:8001
[2025-06-15 18:07:49.334] [INFO ] [main] [com.alibaba.druid.pool.DruidDataSource] [?] [?] - {dataSource-1,master} inited
[2025-06-15 18:07:49.334] [INFO ] [main] [c.b.dynamic.datasource.DynamicRoutingDataSource] [?] [?] - dynamic-datasource - add a datasource named [master] success
[2025-06-15 18:07:49.334] [INFO ] [main] [c.b.dynamic.datasource.DynamicRoutingDataSource] [?] [?] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
[2025-06-15 18:08:18.922] [INFO ] [main] [com.xinghuo.fruugo.collect.util.FruugoSkuInfoUtil] [?] [?] - 注入FruugoImageService成功
[2025-06-15 18:08:18.922] [INFO ] [main] [com.xinghuo.fruugo.collect.util.FruugoSkuInfoUtil] [?] [?] - 设置allgeroUrl: http://127.0.0.1:3100/staticImage/
[2025-06-15 18:08:26.013] [INFO ] [main] [c.a.jetcache.autoconfigure.AbstractCacheAutoInit] [?] [?] - init cache area default , type= linkedhashmap
[2025-06-15 18:08:26.019] [INFO ] [main] [c.a.jetcache.autoconfigure.AbstractCacheAutoInit] [?] [?] - init cache area default , type= redis.springdata
[2025-06-15 18:08:26.042] [INFO ] [main] [com.alicp.jetcache.support.DefaultMetricsManager] [?] [?] - cache stat period at 15 MINUTES
[2025-06-15 18:08:26.071] [INFO ] [main] [c.a.j.redis.springdata.SpringDataBroadcastManager] [?] [?] - create RedisMessageListenerContainer instance
[2025-06-15 18:08:26.095] [INFO ] [main] [c.a.j.redis.springdata.SpringDataBroadcastManager] [?] [?] - subscribe jetcache invalidate notification. channel=projectA
[2025-06-15 18:08:34.867] [INFO ] [main] [c.xinghuo.common.database.config.IdGeneratorConfig] [?] [?] - 当前ID生成器编号: 731
[2025-06-15 18:08:35.638] [INFO ] [main] [com.xinghuo.scheduletask.config.XxlJobConfig] [?] [?] - >>>>>>>>>>> xxl-job config init.
[2025-06-15 18:08:35.688] [INFO ] [main] [o.d.x.file.storage.core.FileStorageServiceBuilder] [?] [?] - 加载本地升级版存储平台：local-plus-1
[2025-06-15 18:08:37.066] [INFO ] [main] [org.quartz.impl.StdSchedulerFactory] [?] [?] - Using default implementation for ThreadExecutor
[2025-06-15 18:08:37.076] [INFO ] [main] [org.quartz.core.SchedulerSignalerImpl] [?] [?] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
[2025-06-15 18:08:37.076] [INFO ] [main] [org.quartz.core.QuartzScheduler] [?] [?] - Quartz Scheduler v.2.3.2 created.
[2025-06-15 18:08:37.077] [INFO ] [main] [org.quartz.simpl.RAMJobStore] [?] [?] - RAMJobStore initialized.
[2025-06-15 18:08:37.077] [INFO ] [main] [org.quartz.core.QuartzScheduler] [?] [?] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

[2025-06-15 18:08:37.077] [INFO ] [main] [org.quartz.impl.StdSchedulerFactory] [?] [?] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
[2025-06-15 18:08:37.078] [INFO ] [main] [org.quartz.impl.StdSchedulerFactory] [?] [?] - Quartz scheduler version: 2.3.2
[2025-06-15 18:08:37.078] [INFO ] [main] [org.quartz.core.QuartzScheduler] [?] [?] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@46109086
[2025-06-15 18:08:37.972] [INFO ] [main] [o.s.b.actuate.endpoint.web.EndpointLinksResolver] [?] [?] - Exposing 14 endpoint(s) beneath base path '/actuator'
[2025-06-15 18:08:39.332] [INFO ] [main] [com.xxl.job.core.executor.XxlJobExecutor] [?] [?] - >>>>>>>>>>> xxl-job register jobhandler success, name:defaultHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@36959928[class com.xinghuo.scheduletask.task.ScheduleTaskHandler#defaultHandler]
[2025-06-15 18:08:39.822] [INFO ] [main] [com.xxl.job.core.executor.XxlJobExecutor] [?] [?] - >>>>>>>>>>> xxl-job registry-remove success, registryParam:{defaultHandler=com.xxl.job.core.handler.impl.MethodJobHandler@36959928[class com.xinghuo.scheduletask.task.ScheduleTaskHandler#defaultHandler]}, registryResult:ReturnT [code=200, msg=null, content=null]
[2025-06-15 18:08:39.869] [INFO ] [main] [o.s.boot.web.embedded.tomcat.TomcatWebServer] [?] [?] - Tomcat started on port 32000 (http) with context path ''
[2025-06-15 18:08:39.870] [INFO ] [main] [o.s.scheduling.quartz.SchedulerFactoryBean] [?] [?] - Starting Quartz Scheduler now
[2025-06-15 18:08:39.870] [INFO ] [main] [org.quartz.core.QuartzScheduler] [?] [?] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
[2025-06-15 18:08:39.889] [INFO ] [main] [com.xinghuo.admin.XhAdminApplication] [?] [?] - Started XhAdminApplication in 56.487 seconds (process running for 57.243)
[2025-06-15 18:08:39.981] [INFO ] [Thread-12] [com.xxl.job.core.server.EmbedServer] [?] [?] - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
[2025-06-15 18:08:40.140] [INFO ] [RMI TCP Connection(2)-**************] [o.a.c.core.ContainerBase.[Tomcat].[localhost].[/]] [?] [?] - Initializing Spring DispatcherServlet 'dispatcherServlet'
[2025-06-15 18:08:40.140] [INFO ] [RMI TCP Connection(2)-**************] [org.springframework.web.servlet.DispatcherServlet] [?] [?] - Initializing Servlet 'dispatcherServlet'
[2025-06-15 18:08:40.143] [INFO ] [RMI TCP Connection(2)-**************] [org.springframework.web.servlet.DispatcherServlet] [?] [?] - Completed initialization in 3 ms
[2025-06-15 18:09:52.815] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 没有找到待处理的Amazon列表任务
[2025-06-15 18:09:52.828] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=624 ms, AverageTime=624 ms

[2025-06-15 18:10:00.018] [INFO ] [scheduling-1] [com.xinghuo.fruugo.analysis.task.FruugoAlertTask] [?] [?] - 开始执行Fruugo数据监控检查任务
[2025-06-15 18:10:00.019] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 开始检查数据处理状态
[2025-06-15 18:10:00.457] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 发送企业微信告警通知 [JustBought数据缺失] [JustBought数据]: 失败
[2025-06-15 18:10:00.585] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 发送企业微信告警通知 [产品数据堆积] [产品数据]: 失败
[2025-06-15 18:10:00.671] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 数据处理状态检查完成，是否有告警: true
[2025-06-15 18:10:00.671] [INFO ] [scheduling-1] [com.xinghuo.fruugo.analysis.task.FruugoAlertTask] [?] [?] - Fruugo数据监控检查任务执行完成，是否有告警: true
[2025-06-15 18:10:01.917] [INFO ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 没有找到待处理的Amazon列表任务
[2025-06-15 18:10:01.917] [INFO ] [http-nio-32000-exec-2] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=21 ms, AverageTime=21 ms

[2025-06-15 18:10:11.896] [INFO ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 没有找到待处理的Amazon列表任务
[2025-06-15 18:10:11.896] [INFO ] [http-nio-32000-exec-3] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=23 ms, AverageTime=23 ms

[2025-06-15 18:10:21.912] [INFO ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 没有找到待处理的Amazon列表任务
[2025-06-15 18:10:21.912] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=22 ms, AverageTime=22 ms

[2025-06-15 18:10:31.895] [INFO ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 没有找到待处理的Amazon列表任务
[2025-06-15 18:10:31.896] [INFO ] [http-nio-32000-exec-6] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=18 ms, AverageTime=18 ms

[2025-06-15 18:10:41.907] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 没有找到待处理的Amazon列表任务
[2025-06-15 18:10:41.908] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=19 ms, AverageTime=19 ms

[2025-06-15 18:10:51.901] [INFO ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 没有找到待处理的Amazon列表任务
[2025-06-15 18:10:51.901] [INFO ] [http-nio-32000-exec-7] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=20 ms, AverageTime=20 ms

[2025-06-15 18:11:01.908] [INFO ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 没有找到待处理的Amazon列表任务
[2025-06-15 18:11:01.909] [INFO ] [http-nio-32000-exec-8] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=21 ms, AverageTime=21 ms

[2025-06-15 18:11:11.908] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 没有找到待处理的Amazon列表任务
[2025-06-15 18:11:11.908] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=17 ms, AverageTime=17 ms

[2025-06-15 18:11:21.899] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 没有找到待处理的Amazon列表任务
[2025-06-15 18:11:21.900] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=19 ms, AverageTime=19 ms

[2025-06-15 18:11:31.906] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 没有找到待处理的Amazon列表任务
[2025-06-15 18:11:31.906] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=18 ms, AverageTime=18 ms

[2025-06-15 18:11:41.902] [INFO ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 没有找到待处理的Amazon列表任务
[2025-06-15 18:11:41.902] [INFO ] [http-nio-32000-exec-2] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=17 ms, AverageTime=17 ms

[2025-06-15 18:11:52.047] [INFO ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 没有找到待处理的Amazon列表任务
[2025-06-15 18:11:52.048] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=169 ms, AverageTime=169 ms

[2025-06-15 18:12:01.895] [INFO ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 没有找到待处理的Amazon列表任务
[2025-06-15 18:12:01.895] [INFO ] [http-nio-32000-exec-6] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=21 ms, AverageTime=21 ms

[2025-06-15 18:12:10.095] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 没有找到待处理的Amazon列表任务
[2025-06-15 18:12:10.095] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=22 ms, AverageTime=22 ms

[2025-06-15 18:12:20.094] [INFO ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 没有找到待处理的Amazon列表任务
[2025-06-15 18:12:20.094] [INFO ] [http-nio-32000-exec-7] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=19 ms, AverageTime=19 ms

[2025-06-15 18:12:24.561] [INFO ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 没有找到待处理的Amazon列表任务
[2025-06-15 18:12:24.561] [INFO ] [http-nio-32000-exec-8] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=26 ms, AverageTime=26 ms

[2025-06-15 18:12:30.109] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 没有找到待处理的Amazon列表任务
[2025-06-15 18:12:30.109] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=23 ms, AverageTime=23 ms

[2025-06-15 18:12:40.117] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 没有找到待处理的Amazon列表任务
[2025-06-15 18:12:40.117] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=21 ms, AverageTime=21 ms

[2025-06-15 18:12:50.122] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 没有找到待处理的Amazon列表任务
[2025-06-15 18:12:50.122] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=35 ms, AverageTime=35 ms

[2025-06-15 18:12:59.390] [INFO ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 没有找到待处理的Amazon列表任务
[2025-06-15 18:12:59.391] [INFO ] [http-nio-32000-exec-2] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=19 ms, AverageTime=19 ms

[2025-06-15 18:13:00.093] [INFO ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 没有找到待处理的Amazon列表任务
[2025-06-15 18:13:00.095] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=25 ms, AverageTime=25 ms

[2025-06-15 18:13:10.130] [INFO ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 没有找到待处理的Amazon列表任务
[2025-06-15 18:13:10.130] [INFO ] [http-nio-32000-exec-6] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=21 ms, AverageTime=21 ms

[2025-06-15 18:13:20.103] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 没有找到待处理的Amazon列表任务
[2025-06-15 18:13:20.105] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=17 ms, AverageTime=17 ms

[2025-06-15 18:13:30.135] [INFO ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 4112
[2025-06-15 18:13:30.148] [INFO ] [http-nio-32000-exec-8] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=53 ms, AverageTime=53 ms

[2025-06-15 18:13:40.127] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 1
[2025-06-15 18:13:40.127] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=42 ms, AverageTime=42 ms

[2025-06-15 18:13:50.130] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 2
[2025-06-15 18:13:50.130] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=48 ms, AverageTime=48 ms

[2025-06-15 18:13:59.403] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 3
[2025-06-15 18:13:59.403] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=40 ms, AverageTime=40 ms

[2025-06-15 18:14:00.122] [INFO ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 4
[2025-06-15 18:14:00.123] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=46 ms, AverageTime=46 ms

[2025-06-15 18:14:10.132] [INFO ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 5
[2025-06-15 18:14:10.132] [INFO ] [http-nio-32000-exec-6] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=49 ms, AverageTime=49 ms

[2025-06-15 18:14:20.119] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 6
[2025-06-15 18:14:20.119] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=42 ms, AverageTime=42 ms

[2025-06-15 18:14:28.270] [INFO ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 7
[2025-06-15 18:14:28.270] [INFO ] [http-nio-32000-exec-7] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=46 ms, AverageTime=46 ms

[2025-06-15 18:14:30.175] [INFO ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 8
[2025-06-15 18:14:30.175] [INFO ] [http-nio-32000-exec-8] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=57 ms, AverageTime=57 ms

[2025-06-15 18:14:40.128] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 9
[2025-06-15 18:14:40.128] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=45 ms, AverageTime=45 ms

[2025-06-15 18:14:50.120] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 10
[2025-06-15 18:14:50.121] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=46 ms, AverageTime=46 ms

[2025-06-15 18:15:00.006] [INFO ] [scheduling-1] [com.xinghuo.fruugo.analysis.task.FruugoAlertTask] [?] [?] - 开始执行Fruugo数据监控检查任务
[2025-06-15 18:15:00.006] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 开始检查数据处理状态
[2025-06-15 18:15:00.014] [INFO ] [JetCacheDefaultExecutor] [com.alicp.jetcache.support.StatInfoLogger] [?] [?] - jetcache stat from 2025-06-15 18:08:26,042 to 2025-06-15 18:15:00,006
cache           |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
----------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
xaceCache       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
xaceCache_local |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
xaceCache_remote|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
----------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

[2025-06-15 18:15:00.087] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 告警 [JustBought数据缺失] [JustBought数据] 在告警间隔期内，跳过本次告警
[2025-06-15 18:15:00.159] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 告警 [产品数据堆积] [产品数据] 在告警间隔期内，跳过本次告警
[2025-06-15 18:15:00.172] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 数据处理状态检查完成，是否有告警: true
[2025-06-15 18:15:00.172] [INFO ] [scheduling-1] [com.xinghuo.fruugo.analysis.task.FruugoAlertTask] [?] [?] - Fruugo数据监控检查任务执行完成，是否有告警: true
[2025-06-15 18:15:00.191] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 11
[2025-06-15 18:15:00.192] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=52 ms, AverageTime=52 ms

[2025-06-15 18:15:10.142] [INFO ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 12
[2025-06-15 18:15:10.142] [INFO ] [http-nio-32000-exec-3] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=52 ms, AverageTime=52 ms

[2025-06-15 18:15:20.126] [INFO ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 13
[2025-06-15 18:15:20.126] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=40 ms, AverageTime=40 ms

[2025-06-15 18:15:30.152] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 14
[2025-06-15 18:15:30.154] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=51 ms, AverageTime=51 ms

[2025-06-15 18:15:40.114] [INFO ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 15
[2025-06-15 18:15:40.114] [INFO ] [http-nio-32000-exec-7] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=42 ms, AverageTime=42 ms

[2025-06-15 18:15:50.136] [INFO ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 16
[2025-06-15 18:15:50.136] [INFO ] [http-nio-32000-exec-8] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=51 ms, AverageTime=51 ms

[2025-06-15 18:16:00.152] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 17
[2025-06-15 18:16:00.152] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=70 ms, AverageTime=70 ms

[2025-06-15 18:16:10.125] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 18
[2025-06-15 18:16:10.125] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=50 ms, AverageTime=50 ms

[2025-06-15 18:16:20.127] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 19
[2025-06-15 18:16:20.127] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=47 ms, AverageTime=47 ms

[2025-06-15 18:16:30.122] [INFO ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 20
[2025-06-15 18:16:30.122] [INFO ] [http-nio-32000-exec-2] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=51 ms, AverageTime=51 ms

[2025-06-15 18:16:40.142] [INFO ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 21
[2025-06-15 18:16:40.142] [INFO ] [http-nio-32000-exec-3] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=48 ms, AverageTime=48 ms

[2025-06-15 18:16:50.138] [INFO ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 22
[2025-06-15 18:16:50.138] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=52 ms, AverageTime=52 ms

[2025-06-15 18:17:00.144] [INFO ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 23
[2025-06-15 18:17:00.144] [INFO ] [http-nio-32000-exec-6] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=64 ms, AverageTime=64 ms

[2025-06-15 18:17:10.129] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 24
[2025-06-15 18:17:10.129] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=50 ms, AverageTime=50 ms

[2025-06-15 18:17:20.120] [INFO ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 25
[2025-06-15 18:17:20.120] [INFO ] [http-nio-32000-exec-7] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=45 ms, AverageTime=45 ms

[2025-06-15 18:17:30.164] [INFO ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 26
[2025-06-15 18:17:30.165] [INFO ] [http-nio-32000-exec-8] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=75 ms, AverageTime=75 ms

[2025-06-15 18:17:40.135] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 27
[2025-06-15 18:17:40.136] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=56 ms, AverageTime=56 ms

[2025-06-15 18:17:50.122] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 28
[2025-06-15 18:17:50.122] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=43 ms, AverageTime=43 ms

[2025-06-15 18:18:00.135] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 29
[2025-06-15 18:18:00.136] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=58 ms, AverageTime=58 ms

[2025-06-15 18:18:10.137] [INFO ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 30
[2025-06-15 18:18:10.137] [INFO ] [http-nio-32000-exec-2] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=55 ms, AverageTime=55 ms

[2025-06-15 18:18:20.128] [INFO ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 31
[2025-06-15 18:18:20.128] [INFO ] [http-nio-32000-exec-3] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=45 ms, AverageTime=45 ms

[2025-06-15 18:18:30.120] [INFO ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 32
[2025-06-15 18:18:30.121] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=42 ms, AverageTime=42 ms

[2025-06-15 18:18:40.150] [INFO ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 33
[2025-06-15 18:18:40.150] [INFO ] [http-nio-32000-exec-6] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=42 ms, AverageTime=42 ms

[2025-06-15 18:18:50.128] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 34
[2025-06-15 18:18:50.128] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=43 ms, AverageTime=43 ms

[2025-06-15 18:19:00.136] [INFO ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 35
[2025-06-15 18:19:00.137] [INFO ] [http-nio-32000-exec-7] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=53 ms, AverageTime=53 ms

[2025-06-15 18:19:10.122] [INFO ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 36
[2025-06-15 18:19:10.122] [INFO ] [http-nio-32000-exec-8] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=49 ms, AverageTime=49 ms

[2025-06-15 18:19:20.117] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 37
[2025-06-15 18:19:20.118] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=42 ms, AverageTime=42 ms

[2025-06-15 18:19:30.143] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 38
[2025-06-15 18:19:30.143] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=54 ms, AverageTime=54 ms

[2025-06-15 18:19:40.133] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 39
[2025-06-15 18:19:40.134] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=46 ms, AverageTime=46 ms

[2025-06-15 18:19:50.149] [INFO ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 40
[2025-06-15 18:19:50.149] [INFO ] [http-nio-32000-exec-2] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=46 ms, AverageTime=46 ms

[2025-06-15 18:20:00.002] [INFO ] [scheduling-1] [com.xinghuo.fruugo.analysis.task.FruugoAlertTask] [?] [?] - 开始执行Fruugo数据监控检查任务
[2025-06-15 18:20:00.002] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 开始检查数据处理状态
[2025-06-15 18:20:00.084] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 告警 [JustBought数据缺失] [JustBought数据] 在告警间隔期内，跳过本次告警
[2025-06-15 18:20:00.144] [INFO ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 41
[2025-06-15 18:20:00.146] [INFO ] [http-nio-32000-exec-3] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=69 ms, AverageTime=69 ms

[2025-06-15 18:20:00.232] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 告警 [产品数据堆积] [产品数据] 在告警间隔期内，跳过本次告警
[2025-06-15 18:20:00.265] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 数据处理状态检查完成，是否有告警: true
[2025-06-15 18:20:00.265] [INFO ] [scheduling-1] [com.xinghuo.fruugo.analysis.task.FruugoAlertTask] [?] [?] - Fruugo数据监控检查任务执行完成，是否有告警: true
[2025-06-15 18:20:10.124] [INFO ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 42
[2025-06-15 18:20:10.125] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=45 ms, AverageTime=45 ms

[2025-06-15 18:20:20.141] [INFO ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 43
[2025-06-15 18:20:20.141] [INFO ] [http-nio-32000-exec-6] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=63 ms, AverageTime=63 ms

[2025-06-15 18:20:30.154] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 44
[2025-06-15 18:20:30.154] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=68 ms, AverageTime=68 ms

[2025-06-15 18:20:40.151] [INFO ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 45
[2025-06-15 18:20:40.152] [INFO ] [http-nio-32000-exec-7] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=73 ms, AverageTime=73 ms

[2025-06-15 18:20:50.152] [INFO ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 46
[2025-06-15 18:20:50.153] [INFO ] [http-nio-32000-exec-8] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=63 ms, AverageTime=63 ms

[2025-06-15 18:21:00.148] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 47
[2025-06-15 18:21:00.149] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=65 ms, AverageTime=65 ms

[2025-06-15 18:21:10.145] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 48
[2025-06-15 18:21:10.145] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=47 ms, AverageTime=47 ms

[2025-06-15 18:21:20.124] [INFO ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 49
[2025-06-15 18:21:20.125] [INFO ] [http-nio-32000-exec-2] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=47 ms, AverageTime=47 ms

[2025-06-15 18:21:30.128] [INFO ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 50
[2025-06-15 18:21:30.129] [INFO ] [http-nio-32000-exec-3] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=49 ms, AverageTime=49 ms

[2025-06-15 18:21:40.341] [INFO ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 51
[2025-06-15 18:21:40.341] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=49 ms, AverageTime=49 ms

[2025-06-15 18:21:50.125] [INFO ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 52
[2025-06-15 18:21:50.125] [INFO ] [http-nio-32000-exec-6] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=48 ms, AverageTime=48 ms

[2025-06-15 18:22:00.190] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 53
[2025-06-15 18:22:00.190] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=73 ms, AverageTime=73 ms

[2025-06-15 18:22:10.131] [INFO ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 54
[2025-06-15 18:22:10.132] [INFO ] [http-nio-32000-exec-7] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=57 ms, AverageTime=57 ms

[2025-06-15 18:22:20.127] [INFO ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 55
[2025-06-15 18:22:20.127] [INFO ] [http-nio-32000-exec-8] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=50 ms, AverageTime=50 ms

[2025-06-15 18:22:30.176] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 56
[2025-06-15 18:22:30.177] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=49 ms, AverageTime=49 ms

[2025-06-15 18:22:40.156] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 57
[2025-06-15 18:22:40.157] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=55 ms, AverageTime=55 ms

[2025-06-15 18:22:50.143] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 58
[2025-06-15 18:22:50.143] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=56 ms, AverageTime=56 ms

[2025-06-15 18:23:00.128] [INFO ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 59
[2025-06-15 18:23:00.129] [INFO ] [http-nio-32000-exec-2] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=54 ms, AverageTime=54 ms

[2025-06-15 18:23:10.136] [INFO ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 60
[2025-06-15 18:23:10.136] [INFO ] [http-nio-32000-exec-3] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=54 ms, AverageTime=54 ms

[2025-06-15 18:23:20.122] [INFO ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 61
[2025-06-15 18:23:20.122] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=46 ms, AverageTime=46 ms

[2025-06-15 18:23:30.126] [INFO ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 62
[2025-06-15 18:23:30.127] [INFO ] [http-nio-32000-exec-6] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=48 ms, AverageTime=48 ms

[2025-06-15 18:23:40.131] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 63
[2025-06-15 18:23:40.132] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=50 ms, AverageTime=50 ms

[2025-06-15 18:23:50.122] [INFO ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 64
[2025-06-15 18:23:50.123] [INFO ] [http-nio-32000-exec-7] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=44 ms, AverageTime=44 ms

[2025-06-15 18:24:00.148] [INFO ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 65
[2025-06-15 18:24:00.148] [INFO ] [http-nio-32000-exec-8] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=63 ms, AverageTime=63 ms

[2025-06-15 18:24:10.145] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 66
[2025-06-15 18:24:10.146] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=62 ms, AverageTime=62 ms

[2025-06-15 18:24:20.131] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 67
[2025-06-15 18:24:20.131] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=55 ms, AverageTime=55 ms

[2025-06-15 18:24:24.934] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 68
[2025-06-15 18:24:24.934] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=43 ms, AverageTime=43 ms

[2025-06-15 18:24:34.940] [INFO ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 69
[2025-06-15 18:24:34.941] [INFO ] [http-nio-32000-exec-2] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=48 ms, AverageTime=48 ms

[2025-06-15 18:24:44.954] [INFO ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 70
[2025-06-15 18:24:44.954] [INFO ] [http-nio-32000-exec-3] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=54 ms, AverageTime=54 ms

[2025-06-15 18:24:54.939] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 71
[2025-06-15 18:24:54.939] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=45 ms, AverageTime=45 ms

[2025-06-15 18:25:00.003] [INFO ] [scheduling-1] [com.xinghuo.fruugo.analysis.task.FruugoAlertTask] [?] [?] - 开始执行Fruugo数据监控检查任务
[2025-06-15 18:25:00.003] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 开始检查数据处理状态
[2025-06-15 18:25:00.178] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 告警 [JustBought数据缺失] [JustBought数据] 在告警间隔期内，跳过本次告警
[2025-06-15 18:25:00.317] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 告警 [产品数据堆积] [产品数据] 在告警间隔期内，跳过本次告警
[2025-06-15 18:25:00.329] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 数据处理状态检查完成，是否有告警: true
[2025-06-15 18:25:00.329] [INFO ] [scheduling-1] [com.xinghuo.fruugo.analysis.task.FruugoAlertTask] [?] [?] - Fruugo数据监控检查任务执行完成，是否有告警: true
[2025-06-15 18:25:04.936] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 72
[2025-06-15 18:25:04.936] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=49 ms, AverageTime=49 ms

[2025-06-15 18:25:14.960] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 73
[2025-06-15 18:25:14.960] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=56 ms, AverageTime=56 ms

[2025-06-15 18:25:24.960] [INFO ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 74
[2025-06-15 18:25:24.961] [INFO ] [http-nio-32000-exec-3] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=61 ms, AverageTime=61 ms

[2025-06-15 18:25:34.934] [INFO ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 75
[2025-06-15 18:25:34.934] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=42 ms, AverageTime=42 ms

[2025-06-15 18:25:44.957] [INFO ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 76
[2025-06-15 18:25:44.957] [INFO ] [http-nio-32000-exec-7] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=65 ms, AverageTime=65 ms

[2025-06-15 18:25:54.951] [INFO ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 77
[2025-06-15 18:25:54.952] [INFO ] [http-nio-32000-exec-8] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=49 ms, AverageTime=49 ms

[2025-06-15 18:26:04.959] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 78
[2025-06-15 18:26:04.960] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=47 ms, AverageTime=47 ms

[2025-06-15 18:26:14.942] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 79
[2025-06-15 18:26:14.942] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=42 ms, AverageTime=42 ms

[2025-06-15 18:26:24.952] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 80
[2025-06-15 18:26:24.952] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=42 ms, AverageTime=42 ms

[2025-06-15 18:26:34.952] [INFO ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 81
[2025-06-15 18:26:34.952] [INFO ] [http-nio-32000-exec-2] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=42 ms, AverageTime=42 ms

[2025-06-15 18:26:44.938] [INFO ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 82
[2025-06-15 18:26:44.938] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=46 ms, AverageTime=46 ms

[2025-06-15 18:26:54.935] [INFO ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 83
[2025-06-15 18:26:54.935] [INFO ] [http-nio-32000-exec-6] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=45 ms, AverageTime=45 ms

[2025-06-15 18:27:04.942] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 84
[2025-06-15 18:27:04.942] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=51 ms, AverageTime=51 ms

[2025-06-15 18:27:14.937] [INFO ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 85
[2025-06-15 18:27:14.937] [INFO ] [http-nio-32000-exec-7] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=46 ms, AverageTime=46 ms

[2025-06-15 18:27:24.951] [INFO ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 86
[2025-06-15 18:27:24.952] [INFO ] [http-nio-32000-exec-8] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=48 ms, AverageTime=48 ms

[2025-06-15 18:27:34.947] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 87
[2025-06-15 18:27:34.947] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=56 ms, AverageTime=56 ms

[2025-06-15 18:27:44.942] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 88
[2025-06-15 18:27:44.942] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=56 ms, AverageTime=56 ms

[2025-06-15 18:27:54.951] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 89
[2025-06-15 18:27:54.951] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=50 ms, AverageTime=50 ms

[2025-06-15 18:28:04.946] [INFO ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 90
[2025-06-15 18:28:04.946] [INFO ] [http-nio-32000-exec-2] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=42 ms, AverageTime=42 ms

[2025-06-15 18:28:14.937] [INFO ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 91
[2025-06-15 18:28:14.937] [INFO ] [http-nio-32000-exec-3] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=45 ms, AverageTime=45 ms

[2025-06-15 18:28:24.931] [INFO ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 92
[2025-06-15 18:28:24.931] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=43 ms, AverageTime=43 ms

[2025-06-15 18:28:34.961] [INFO ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 93
[2025-06-15 18:28:34.961] [INFO ] [http-nio-32000-exec-6] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=40 ms, AverageTime=40 ms

[2025-06-15 18:28:44.934] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 94
[2025-06-15 18:28:44.934] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=45 ms, AverageTime=45 ms

[2025-06-15 18:28:54.961] [INFO ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 95
[2025-06-15 18:28:54.961] [INFO ] [http-nio-32000-exec-7] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=53 ms, AverageTime=53 ms

[2025-06-15 18:29:04.946] [INFO ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 96
[2025-06-15 18:29:04.946] [INFO ] [http-nio-32000-exec-8] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=46 ms, AverageTime=46 ms

[2025-06-15 18:29:14.927] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 97
[2025-06-15 18:29:14.927] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=39 ms, AverageTime=39 ms

[2025-06-15 18:29:24.944] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 98
[2025-06-15 18:29:24.945] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=42 ms, AverageTime=42 ms

[2025-06-15 18:29:34.938] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 99
[2025-06-15 18:29:34.938] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=41 ms, AverageTime=41 ms

[2025-06-15 18:29:44.942] [INFO ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 100
[2025-06-15 18:29:44.942] [INFO ] [http-nio-32000-exec-2] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=48 ms, AverageTime=48 ms

[2025-06-15 18:29:55.007] [INFO ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 101
[2025-06-15 18:29:55.008] [INFO ] [http-nio-32000-exec-3] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=107 ms, AverageTime=107 ms

[2025-06-15 18:30:00.002] [INFO ] [JetCacheDefaultExecutor] [com.alicp.jetcache.support.StatInfoLogger] [?] [?] - jetcache stat from 2025-06-15 18:15:00,006 to 2025-06-15 18:30:00,001
cache           |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
----------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
xaceCache       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
xaceCache_local |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
xaceCache_remote|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
----------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

[2025-06-15 18:30:00.004] [INFO ] [scheduling-1] [com.xinghuo.fruugo.analysis.task.FruugoAlertTask] [?] [?] - 开始执行Fruugo数据监控检查任务
[2025-06-15 18:30:00.004] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 开始检查数据处理状态
[2025-06-15 18:30:00.137] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 告警 [JustBought数据缺失] [JustBought数据] 在告警间隔期内，跳过本次告警
[2025-06-15 18:30:03.209] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 告警 [产品数据堆积] [产品数据] 在告警间隔期内，跳过本次告警
[2025-06-15 18:30:03.266] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 数据处理状态检查完成，是否有告警: true
[2025-06-15 18:30:03.266] [INFO ] [scheduling-1] [com.xinghuo.fruugo.analysis.task.FruugoAlertTask] [?] [?] - Fruugo数据监控检查任务执行完成，是否有告警: true
[2025-06-15 18:30:03.267] [INFO ] [scheduling-1] [c.x.fruugo.analysis.task.FruugoMonitorRefreshTask] [?] [?] - 开始执行Fruugo监控数据定时刷新任务
[2025-06-15 18:30:03.267] [INFO ] [scheduling-1] [c.x.f.a.service.impl.FruugoMonitorServiceImpl] [?] [?] - 开始生成 2025-06-15 的监控数据
[2025-06-15 18:30:04.954] [INFO ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 102
[2025-06-15 18:30:04.954] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=54 ms, AverageTime=54 ms

[2025-06-15 18:30:15.035] [INFO ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 103
[2025-06-15 18:30:15.036] [INFO ] [http-nio-32000-exec-6] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=146 ms, AverageTime=146 ms

[2025-06-15 18:30:24.941] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 104
[2025-06-15 18:30:24.941] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=51 ms, AverageTime=51 ms

[2025-06-15 18:30:34.951] [INFO ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 105
[2025-06-15 18:30:34.951] [INFO ] [http-nio-32000-exec-7] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=56 ms, AverageTime=56 ms

[2025-06-15 18:30:44.946] [INFO ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 106
[2025-06-15 18:30:44.946] [INFO ] [http-nio-32000-exec-8] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=47 ms, AverageTime=47 ms

[2025-06-15 18:30:54.936] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 107
[2025-06-15 18:30:54.936] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=37 ms, AverageTime=37 ms

[2025-06-15 18:31:04.936] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 108
[2025-06-15 18:31:04.936] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=46 ms, AverageTime=46 ms

[2025-06-15 18:31:14.932] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 109
[2025-06-15 18:31:14.932] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=37 ms, AverageTime=37 ms

[2025-06-15 18:31:25.019] [INFO ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 110
[2025-06-15 18:31:25.020] [INFO ] [http-nio-32000-exec-2] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=116 ms, AverageTime=116 ms

[2025-06-15 18:31:34.941] [INFO ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 111
[2025-06-15 18:31:34.941] [INFO ] [http-nio-32000-exec-3] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=44 ms, AverageTime=44 ms

[2025-06-15 18:31:44.930] [INFO ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 112
[2025-06-15 18:31:44.930] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=43 ms, AverageTime=43 ms

[2025-06-15 18:31:54.974] [INFO ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 113
[2025-06-15 18:31:54.974] [INFO ] [http-nio-32000-exec-6] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=86 ms, AverageTime=86 ms

[2025-06-15 18:32:04.942] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 114
[2025-06-15 18:32:04.942] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=47 ms, AverageTime=47 ms

[2025-06-15 18:32:14.951] [INFO ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 115
[2025-06-15 18:32:14.951] [INFO ] [http-nio-32000-exec-7] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=45 ms, AverageTime=45 ms

[2025-06-15 18:32:24.955] [INFO ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 116
[2025-06-15 18:32:24.955] [INFO ] [http-nio-32000-exec-8] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=52 ms, AverageTime=52 ms

[2025-06-15 18:32:34.945] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 117
[2025-06-15 18:32:34.945] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=38 ms, AverageTime=38 ms

[2025-06-15 18:32:44.938] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 118
[2025-06-15 18:32:44.938] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=52 ms, AverageTime=52 ms

[2025-06-15 18:32:54.942] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 119
[2025-06-15 18:32:54.942] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=45 ms, AverageTime=45 ms

[2025-06-15 18:33:04.047] [INFO ] [scheduling-1] [c.x.f.a.service.impl.FruugoMonitorServiceImpl] [?] [?] - 生成 2025-06-15 的监控数据 成功
[2025-06-15 18:33:04.047] [INFO ] [scheduling-1] [c.x.fruugo.analysis.task.FruugoMonitorRefreshTask] [?] [?] - Fruugo基础监控数据刷新 成功
[2025-06-15 18:33:04.047] [INFO ] [scheduling-1] [c.x.f.a.service.impl.FruugoDataMonitorServiceImpl] [?] [?] - 生成今日的Fruugo数据监控信息
[2025-06-15 18:33:04.047] [INFO ] [scheduling-1] [c.x.f.a.service.impl.FruugoDataMonitorServiceImpl] [?] [?] - 生成指定日期的Fruugo数据监控信息，日期: 2025-06-15
[2025-06-15 18:33:04.047] [INFO ] [scheduling-1] [c.x.f.a.service.impl.FruugoMonitorServiceImpl] [?] [?] - 开始生成 2025-06-15 的监控数据
[2025-06-15 18:33:04.971] [INFO ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 120
[2025-06-15 18:33:04.971] [INFO ] [http-nio-32000-exec-2] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=76 ms, AverageTime=76 ms

[2025-06-15 18:33:14.982] [INFO ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 121
[2025-06-15 18:33:14.982] [INFO ] [http-nio-32000-exec-3] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=94 ms, AverageTime=94 ms

[2025-06-15 18:33:24.949] [INFO ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 122
[2025-06-15 18:33:24.949] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=48 ms, AverageTime=48 ms

[2025-06-15 18:33:34.935] [INFO ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 123
[2025-06-15 18:33:34.935] [INFO ] [http-nio-32000-exec-6] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=43 ms, AverageTime=43 ms

[2025-06-15 18:33:44.985] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 124
[2025-06-15 18:33:44.985] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=91 ms, AverageTime=91 ms

[2025-06-15 18:33:54.967] [INFO ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 125
[2025-06-15 18:33:54.967] [INFO ] [http-nio-32000-exec-7] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=72 ms, AverageTime=72 ms

[2025-06-15 18:34:04.951] [INFO ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 126
[2025-06-15 18:34:04.951] [INFO ] [http-nio-32000-exec-8] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=50 ms, AverageTime=50 ms

[2025-06-15 18:34:14.964] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 127
[2025-06-15 18:34:14.965] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=64 ms, AverageTime=64 ms

[2025-06-15 18:34:24.939] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 128
[2025-06-15 18:34:24.939] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=48 ms, AverageTime=48 ms

[2025-06-15 18:34:34.946] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 129
[2025-06-15 18:34:34.946] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=55 ms, AverageTime=55 ms

[2025-06-15 18:34:44.952] [INFO ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 130
[2025-06-15 18:34:44.952] [INFO ] [http-nio-32000-exec-2] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=55 ms, AverageTime=55 ms

[2025-06-15 18:34:54.942] [INFO ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 131
[2025-06-15 18:34:54.944] [INFO ] [http-nio-32000-exec-3] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=44 ms, AverageTime=44 ms

[2025-06-15 18:35:04.978] [INFO ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 132
[2025-06-15 18:35:04.978] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=80 ms, AverageTime=80 ms

[2025-06-15 18:35:14.996] [INFO ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 133
[2025-06-15 18:35:14.996] [INFO ] [http-nio-32000-exec-6] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=93 ms, AverageTime=93 ms

[2025-06-15 18:35:24.948] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 134
[2025-06-15 18:35:24.948] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=43 ms, AverageTime=43 ms

[2025-06-15 18:35:35.122] [INFO ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 135
[2025-06-15 18:35:35.122] [INFO ] [http-nio-32000-exec-7] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=233 ms, AverageTime=233 ms

[2025-06-15 18:35:44.939] [INFO ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 136
[2025-06-15 18:35:44.939] [INFO ] [http-nio-32000-exec-8] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=40 ms, AverageTime=40 ms

[2025-06-15 18:35:54.992] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 137
[2025-06-15 18:35:54.992] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=101 ms, AverageTime=101 ms

[2025-06-15 18:36:04.940] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 138
[2025-06-15 18:36:04.940] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=44 ms, AverageTime=44 ms

[2025-06-15 18:36:14.958] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 139
[2025-06-15 18:36:14.958] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=47 ms, AverageTime=47 ms

[2025-06-15 18:36:24.983] [INFO ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 140
[2025-06-15 18:36:24.984] [INFO ] [http-nio-32000-exec-2] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=86 ms, AverageTime=86 ms

[2025-06-15 18:36:34.965] [INFO ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 141
[2025-06-15 18:36:34.965] [INFO ] [http-nio-32000-exec-3] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=75 ms, AverageTime=75 ms

[2025-06-15 18:36:44.948] [INFO ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 142
[2025-06-15 18:36:44.949] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=57 ms, AverageTime=57 ms

[2025-06-15 18:36:54.946] [INFO ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 143
[2025-06-15 18:36:54.946] [INFO ] [http-nio-32000-exec-6] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=55 ms, AverageTime=55 ms

[2025-06-15 18:37:04.941] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 144
[2025-06-15 18:37:04.941] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=51 ms, AverageTime=51 ms

[2025-06-15 18:37:14.605] [INFO ] [scheduling-1] [c.x.f.a.service.impl.FruugoMonitorServiceImpl] [?] [?] - 生成 2025-06-15 的监控数据 成功
[2025-06-15 18:37:14.605] [INFO ] [scheduling-1] [c.x.f.a.service.impl.FruugoDataMonitorServiceImpl] [?] [?] - 生成指定日期的Fruugo数据监控信息成功，日期: 2025-06-15
[2025-06-15 18:37:14.605] [INFO ] [scheduling-1] [c.x.fruugo.analysis.task.FruugoMonitorRefreshTask] [?] [?] - Fruugo数据监控信息刷新 成功
[2025-06-15 18:37:14.605] [INFO ] [scheduling-1] [c.x.fruugo.analysis.task.FruugoMonitorRefreshTask] [?] [?] - Fruugo监控数据定时刷新任务执行完成
[2025-06-15 18:37:14.605] [INFO ] [scheduling-1] [com.xinghuo.fruugo.analysis.task.FruugoAlertTask] [?] [?] - 开始执行Fruugo数据监控检查任务
[2025-06-15 18:37:14.605] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 开始检查数据处理状态
[2025-06-15 18:37:14.943] [INFO ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 145
[2025-06-15 18:37:14.944] [INFO ] [http-nio-32000-exec-7] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=51 ms, AverageTime=51 ms

[2025-06-15 18:37:15.036] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 告警 [JustBought数据缺失] [JustBought数据] 在告警间隔期内，跳过本次告警
[2025-06-15 18:37:17.619] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 告警 [产品数据堆积] [产品数据] 在告警间隔期内，跳过本次告警
[2025-06-15 18:37:17.631] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 数据处理状态检查完成，是否有告警: true
[2025-06-15 18:37:17.631] [INFO ] [scheduling-1] [com.xinghuo.fruugo.analysis.task.FruugoAlertTask] [?] [?] - Fruugo数据监控检查任务执行完成，是否有告警: true
[2025-06-15 18:37:24.965] [INFO ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 146
[2025-06-15 18:37:24.965] [INFO ] [http-nio-32000-exec-8] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=56 ms, AverageTime=56 ms

[2025-06-15 18:37:34.954] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 147
[2025-06-15 18:37:34.955] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=52 ms, AverageTime=52 ms

[2025-06-15 18:37:44.945] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 148
[2025-06-15 18:37:44.945] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=44 ms, AverageTime=44 ms

[2025-06-15 18:37:54.957] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 149
[2025-06-15 18:37:54.958] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=55 ms, AverageTime=55 ms

[2025-06-15 18:38:04.946] [INFO ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 150
[2025-06-15 18:38:04.946] [INFO ] [http-nio-32000-exec-2] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=53 ms, AverageTime=53 ms

[2025-06-15 18:38:14.986] [INFO ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 151
[2025-06-15 18:38:14.987] [INFO ] [http-nio-32000-exec-3] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=99 ms, AverageTime=99 ms

[2025-06-15 18:38:24.970] [INFO ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 152
[2025-06-15 18:38:24.971] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=64 ms, AverageTime=64 ms

[2025-06-15 18:38:34.955] [INFO ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 153
[2025-06-15 18:38:34.955] [INFO ] [http-nio-32000-exec-6] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=56 ms, AverageTime=56 ms

[2025-06-15 18:38:45.113] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 154
[2025-06-15 18:38:45.114] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=51 ms, AverageTime=51 ms

[2025-06-15 18:38:54.945] [INFO ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 155
[2025-06-15 18:38:54.946] [INFO ] [http-nio-32000-exec-7] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=51 ms, AverageTime=51 ms

[2025-06-15 18:39:04.935] [INFO ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 156
[2025-06-15 18:39:04.935] [INFO ] [http-nio-32000-exec-8] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=42 ms, AverageTime=42 ms

[2025-06-15 18:39:14.956] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 157
[2025-06-15 18:39:14.957] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=61 ms, AverageTime=61 ms

[2025-06-15 18:39:20.076] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 158
[2025-06-15 18:39:20.077] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=52 ms, AverageTime=52 ms

[2025-06-15 18:39:30.063] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 159
[2025-06-15 18:39:30.063] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=47 ms, AverageTime=47 ms

[2025-06-15 18:39:33.016] [INFO ] [http-nio-32000-exec-3] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:

[2025-06-15 18:39:33.276] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:

[2025-06-15 18:39:40.068] [INFO ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 160
[2025-06-15 18:39:40.068] [INFO ] [http-nio-32000-exec-6] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=44 ms, AverageTime=44 ms

[2025-06-15 18:39:48.053] [INFO ] [http-nio-32000-exec-7] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:

[2025-06-15 18:39:50.075] [INFO ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 161
[2025-06-15 18:39:50.075] [INFO ] [http-nio-32000-exec-8] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=50 ms, AverageTime=50 ms

[2025-06-15 18:40:00.003] [INFO ] [scheduling-1] [com.xinghuo.fruugo.analysis.task.FruugoAlertTask] [?] [?] - 开始执行Fruugo数据监控检查任务
[2025-06-15 18:40:00.004] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 开始检查数据处理状态
[2025-06-15 18:40:00.139] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 162
[2025-06-15 18:40:00.140] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=105 ms, AverageTime=105 ms

[2025-06-15 18:40:00.168] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 告警 [JustBought数据缺失] [JustBought数据] 在告警间隔期内，跳过本次告警
[2025-06-15 18:40:01.301] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 发送企业微信告警通知 [产品数据堆积] [产品数据]: 失败
[2025-06-15 18:40:01.424] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 数据处理状态检查完成，是否有告警: true
[2025-06-15 18:40:01.424] [INFO ] [scheduling-1] [com.xinghuo.fruugo.analysis.task.FruugoAlertTask] [?] [?] - Fruugo数据监控检查任务执行完成，是否有告警: true
[2025-06-15 18:40:10.092] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 163
[2025-06-15 18:40:10.092] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=63 ms, AverageTime=63 ms

[2025-06-15 18:40:20.085] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 164
[2025-06-15 18:40:20.086] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=55 ms, AverageTime=55 ms

[2025-06-15 18:40:30.092] [INFO ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 165
[2025-06-15 18:40:30.094] [INFO ] [http-nio-32000-exec-2] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=58 ms, AverageTime=58 ms

[2025-06-15 18:40:40.070] [INFO ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 166
[2025-06-15 18:40:40.071] [INFO ] [http-nio-32000-exec-3] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=53 ms, AverageTime=53 ms

[2025-06-15 18:40:50.079] [INFO ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 167
[2025-06-15 18:40:50.079] [INFO ] [http-nio-32000-exec-6] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=47 ms, AverageTime=47 ms

[2025-06-15 18:40:54.240] [INFO ] [SpringApplicationShutdownHook] [org.quartz.core.QuartzScheduler] [?] [?] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
[2025-06-15 18:40:54.323] [INFO ] [SpringApplicationShutdownHook] [o.s.scheduling.quartz.SchedulerFactoryBean] [?] [?] - Shutting down Quartz Scheduler
[2025-06-15 18:40:54.323] [INFO ] [SpringApplicationShutdownHook] [org.quartz.core.QuartzScheduler] [?] [?] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
[2025-06-15 18:40:54.323] [INFO ] [SpringApplicationShutdownHook] [org.quartz.core.QuartzScheduler] [?] [?] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
[2025-06-15 18:40:54.323] [INFO ] [SpringApplicationShutdownHook] [org.quartz.core.QuartzScheduler] [?] [?] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
[2025-06-15 18:40:54.327] [INFO ] [SpringApplicationShutdownHook] [o.dromara.x.file.storage.core.FileStorageService] [?] [?] - 销毁存储平台 local-plus-1 成功
[2025-06-15 18:40:54.327] [INFO ] [Thread-12] [com.xxl.job.core.server.EmbedServer] [?] [?] - >>>>>>>>>>> xxl-job remoting server stop.
[2025-06-15 18:40:57.333] [INFO ] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.thread.ExecutorRegistryThread] [?] [?] - >>>>>>>>>>> xxl-job registry-remove fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='xxl-job-executor-sample1', registryValue='http://**************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(Connect timed out), for url : http://101.33.227.194:30020/xxl-job-admin/api/registryRemove, content=null]
[2025-06-15 18:40:57.333] [INFO ] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.thread.ExecutorRegistryThread] [?] [?] - >>>>>>>>>>> xxl-job, executor registry thread destroy.
[2025-06-15 18:40:57.333] [INFO ] [SpringApplicationShutdownHook] [com.xxl.job.core.server.EmbedServer] [?] [?] - >>>>>>>>>>> xxl-job remoting server destroy success.
[2025-06-15 18:40:57.334] [INFO ] [xxl-job, executor JobLogFileCleanThread] [com.xxl.job.core.thread.JobLogFileCleanThread] [?] [?] - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destroy.
[2025-06-15 18:40:57.334] [INFO ] [xxl-job, executor TriggerCallbackThread] [com.xxl.job.core.thread.TriggerCallbackThread] [?] [?] - >>>>>>>>>>> xxl-job, executor callback thread destroy.
[2025-06-15 18:40:57.334] [INFO ] [Thread-11] [com.xxl.job.core.thread.TriggerCallbackThread] [?] [?] - >>>>>>>>>>> xxl-job, executor retry callback thread destroy.
[2025-06-15 18:40:59.351] [INFO ] [SpringApplicationShutdownHook] [com.alicp.jetcache.support.DefaultMetricsManager] [?] [?] - cache stat canceled
[2025-06-15 18:40:59.383] [INFO ] [SpringApplicationShutdownHook] [c.b.dynamic.datasource.DynamicRoutingDataSource] [?] [?] - dynamic-datasource start closing ....
[2025-06-15 18:40:59.385] [INFO ] [SpringApplicationShutdownHook] [com.alibaba.druid.pool.DruidDataSource] [?] [?] - {dataSource-1} closing ...
[2025-06-15 18:40:59.387] [INFO ] [SpringApplicationShutdownHook] [com.alibaba.druid.pool.DruidDataSource] [?] [?] - {dataSource-1} closed
[2025-06-15 18:40:59.387] [INFO ] [SpringApplicationShutdownHook] [c.b.d.d.destroyer.DefaultDataSourceDestroyer] [?] [?] - dynamic-datasource close the datasource named [master] success,
[2025-06-15 18:40:59.387] [INFO ] [SpringApplicationShutdownHook] [c.b.dynamic.datasource.DynamicRoutingDataSource] [?] [?] - dynamic-datasource all closed success,bye
[2025-06-15 18:41:01.552] [INFO ] [main] [com.xinghuo.admin.XhAdminApplication] [?] [?] - Starting XhAdminApplication using Java 20.0.1 with PID 18004 (G:\v2\51erp\51erp-service\xace-java-boot\xh-admin\target\classes started by Administrator in G:\v2\51erp)
[2025-06-15 18:41:01.552] [INFO ] [main] [com.xinghuo.admin.XhAdminApplication] [?] [?] - The following 1 profile is active: "vpn"
[2025-06-15 18:41:03.913] [INFO ] [main] [o.s.d.r.config.RepositoryConfigurationDelegate] [?] [?] - Multiple Spring Data modules found, entering strict repository configuration mode
[2025-06-15 18:41:03.914] [INFO ] [main] [o.s.d.r.config.RepositoryConfigurationDelegate] [?] [?] - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[2025-06-15 18:41:03.936] [INFO ] [main] [o.s.d.r.config.RepositoryConfigurationDelegate] [?] [?] - Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces.
[2025-06-15 18:41:05.411] [INFO ] [main] [o.s.boot.web.embedded.tomcat.TomcatWebServer] [?] [?] - Tomcat initialized with port 32000 (http)
[2025-06-15 18:41:05.422] [INFO ] [main] [org.apache.catalina.core.StandardService] [?] [?] - Starting service [Tomcat]
[2025-06-15 18:41:05.422] [INFO ] [main] [org.apache.catalina.core.StandardEngine] [?] [?] - Starting Servlet engine: [Apache Tomcat/10.1.19]
[2025-06-15 18:41:05.499] [INFO ] [main] [o.a.c.core.ContainerBase.[Tomcat].[localhost].[/]] [?] [?] - Initializing Spring embedded WebApplicationContext
[2025-06-15 18:41:05.500] [INFO ] [main] [o.s.b.w.s.c.ServletWebServerApplicationContext] [?] [?] - Root WebApplicationContext: initialization completed in 3756 ms
[2025-06-15 18:41:05.967] [INFO ] [main] [org.redisson.Version] [?] [?] - Redisson 3.27.2
[2025-06-15 18:41:06.314] [INFO ] [redisson-netty-1-4] [org.redisson.connection.ConnectionsHolder] [?] [?] - 1 connections initialized for 127.0.0.1/127.0.0.1:8001
[2025-06-15 18:41:06.382] [INFO ] [redisson-netty-1-19] [org.redisson.connection.ConnectionsHolder] [?] [?] - 24 connections initialized for 127.0.0.1/127.0.0.1:8001
[2025-06-15 18:41:07.328] [INFO ] [main] [com.alibaba.druid.pool.DruidDataSource] [?] [?] - {dataSource-1,master} inited
[2025-06-15 18:41:07.329] [INFO ] [main] [c.b.dynamic.datasource.DynamicRoutingDataSource] [?] [?] - dynamic-datasource - add a datasource named [master] success
[2025-06-15 18:41:07.329] [INFO ] [main] [c.b.dynamic.datasource.DynamicRoutingDataSource] [?] [?] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
[2025-06-15 18:41:36.991] [INFO ] [main] [com.xinghuo.fruugo.collect.util.FruugoSkuInfoUtil] [?] [?] - 注入FruugoImageService成功
[2025-06-15 18:41:36.992] [INFO ] [main] [com.xinghuo.fruugo.collect.util.FruugoSkuInfoUtil] [?] [?] - 设置allgeroUrl: http://127.0.0.1:3100/staticImage/
[2025-06-15 18:41:44.070] [INFO ] [main] [c.a.jetcache.autoconfigure.AbstractCacheAutoInit] [?] [?] - init cache area default , type= linkedhashmap
[2025-06-15 18:41:44.077] [INFO ] [main] [c.a.jetcache.autoconfigure.AbstractCacheAutoInit] [?] [?] - init cache area default , type= redis.springdata
[2025-06-15 18:41:44.099] [INFO ] [main] [com.alicp.jetcache.support.DefaultMetricsManager] [?] [?] - cache stat period at 15 MINUTES
[2025-06-15 18:41:44.126] [INFO ] [main] [c.a.j.redis.springdata.SpringDataBroadcastManager] [?] [?] - create RedisMessageListenerContainer instance
[2025-06-15 18:41:44.151] [INFO ] [main] [c.a.j.redis.springdata.SpringDataBroadcastManager] [?] [?] - subscribe jetcache invalidate notification. channel=projectA
[2025-06-15 18:41:52.758] [INFO ] [main] [c.xinghuo.common.database.config.IdGeneratorConfig] [?] [?] - 当前ID生成器编号: 732
[2025-06-15 18:41:53.503] [INFO ] [main] [com.xinghuo.scheduletask.config.XxlJobConfig] [?] [?] - >>>>>>>>>>> xxl-job config init.
[2025-06-15 18:41:53.555] [INFO ] [main] [o.d.x.file.storage.core.FileStorageServiceBuilder] [?] [?] - 加载本地升级版存储平台：local-plus-1
[2025-06-15 18:41:54.922] [INFO ] [main] [org.quartz.impl.StdSchedulerFactory] [?] [?] - Using default implementation for ThreadExecutor
[2025-06-15 18:41:54.932] [INFO ] [main] [org.quartz.core.SchedulerSignalerImpl] [?] [?] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
[2025-06-15 18:41:54.932] [INFO ] [main] [org.quartz.core.QuartzScheduler] [?] [?] - Quartz Scheduler v.2.3.2 created.
[2025-06-15 18:41:54.933] [INFO ] [main] [org.quartz.simpl.RAMJobStore] [?] [?] - RAMJobStore initialized.
[2025-06-15 18:41:54.934] [INFO ] [main] [org.quartz.core.QuartzScheduler] [?] [?] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

[2025-06-15 18:41:54.934] [INFO ] [main] [org.quartz.impl.StdSchedulerFactory] [?] [?] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
[2025-06-15 18:41:54.934] [INFO ] [main] [org.quartz.impl.StdSchedulerFactory] [?] [?] - Quartz scheduler version: 2.3.2
[2025-06-15 18:41:54.934] [INFO ] [main] [org.quartz.core.QuartzScheduler] [?] [?] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@6bda77da
[2025-06-15 18:41:55.814] [INFO ] [main] [o.s.b.actuate.endpoint.web.EndpointLinksResolver] [?] [?] - Exposing 14 endpoint(s) beneath base path '/actuator'
[2025-06-15 18:41:57.114] [INFO ] [main] [com.xxl.job.core.executor.XxlJobExecutor] [?] [?] - >>>>>>>>>>> xxl-job register jobhandler success, name:defaultHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6f3b5684[class com.xinghuo.scheduletask.task.ScheduleTaskHandler#defaultHandler]
[2025-06-15 18:41:57.582] [INFO ] [main] [com.xxl.job.core.executor.XxlJobExecutor] [?] [?] - >>>>>>>>>>> xxl-job registry-remove success, registryParam:{defaultHandler=com.xxl.job.core.handler.impl.MethodJobHandler@6f3b5684[class com.xinghuo.scheduletask.task.ScheduleTaskHandler#defaultHandler]}, registryResult:ReturnT [code=200, msg=null, content=null]
[2025-06-15 18:41:57.629] [INFO ] [main] [o.s.boot.web.embedded.tomcat.TomcatWebServer] [?] [?] - Tomcat started on port 32000 (http) with context path ''
[2025-06-15 18:41:57.630] [INFO ] [main] [o.s.scheduling.quartz.SchedulerFactoryBean] [?] [?] - Starting Quartz Scheduler now
[2025-06-15 18:41:57.631] [INFO ] [main] [org.quartz.core.QuartzScheduler] [?] [?] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
[2025-06-15 18:41:57.650] [INFO ] [main] [com.xinghuo.admin.XhAdminApplication] [?] [?] - Started XhAdminApplication in 56.616 seconds (process running for 57.316)
[2025-06-15 18:41:57.737] [INFO ] [Thread-12] [com.xxl.job.core.server.EmbedServer] [?] [?] - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
[2025-06-15 18:41:58.116] [INFO ] [RMI TCP Connection(3)-**************] [o.a.c.core.ContainerBase.[Tomcat].[localhost].[/]] [?] [?] - Initializing Spring DispatcherServlet 'dispatcherServlet'
[2025-06-15 18:41:58.116] [INFO ] [RMI TCP Connection(3)-**************] [org.springframework.web.servlet.DispatcherServlet] [?] [?] - Initializing Servlet 'dispatcherServlet'
[2025-06-15 18:41:58.120] [INFO ] [RMI TCP Connection(3)-**************] [org.springframework.web.servlet.DispatcherServlet] [?] [?] - Completed initialization in 4 ms
[2025-06-15 18:42:00.511] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 168
[2025-06-15 18:42:00.528] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=352 ms, AverageTime=352 ms

[2025-06-15 18:42:10.082] [INFO ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 169
[2025-06-15 18:42:10.082] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=50 ms, AverageTime=50 ms

[2025-06-15 18:42:20.097] [INFO ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 170
[2025-06-15 18:42:20.097] [INFO ] [http-nio-32000-exec-3] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=59 ms, AverageTime=59 ms

[2025-06-15 18:42:30.116] [INFO ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 171
[2025-06-15 18:42:30.116] [INFO ] [http-nio-32000-exec-2] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=57 ms, AverageTime=57 ms

[2025-06-15 18:42:30.483] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 172
[2025-06-15 18:42:30.483] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=57 ms, AverageTime=57 ms

[2025-06-15 18:42:40.476] [INFO ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 173
[2025-06-15 18:42:40.476] [INFO ] [http-nio-32000-exec-6] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=49 ms, AverageTime=49 ms

[2025-06-15 18:45:00.578] [INFO ] [scheduling-1] [com.xinghuo.fruugo.analysis.task.FruugoAlertTask] [?] [?] - 开始执行Fruugo数据监控检查任务
[2025-06-15 18:45:00.578] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 开始检查数据处理状态
[2025-06-15 18:45:00.613] [INFO ] [JetCacheDefaultExecutor] [com.alicp.jetcache.support.StatInfoLogger] [?] [?] - jetcache stat from 2025-06-15 18:41:44,098 to 2025-06-15 18:45:00,556
cache           |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
----------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
xaceCache       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
xaceCache_local |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
xaceCache_remote|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
----------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

[2025-06-15 18:45:04.011] [INFO ] [http-nio-32000-exec-8] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:

[2025-06-15 18:45:04.030] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 174
[2025-06-15 18:45:04.030] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=3469 ms, AverageTime=3469 ms

[2025-06-15 18:45:04.032] [INFO ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 174
[2025-06-15 18:45:04.032] [INFO ] [http-nio-32000-exec-2] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=3471 ms, AverageTime=3471 ms

[2025-06-15 18:45:04.034] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 174
[2025-06-15 18:45:04.035] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=3473 ms, AverageTime=3473 ms

[2025-06-15 18:45:04.039] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 174
[2025-06-15 18:45:04.039] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=3474 ms, AverageTime=3474 ms

[2025-06-15 18:45:04.063] [INFO ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 174
[2025-06-15 18:45:04.063] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 174
[2025-06-15 18:45:04.064] [INFO ] [http-nio-32000-exec-3] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=3499 ms, AverageTime=3499 ms

[2025-06-15 18:45:04.064] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=3502 ms, AverageTime=3502 ms

[2025-06-15 18:45:04.085] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 发送企业微信告警通知 [JustBought数据缺失] [JustBought数据]: 失败
[2025-06-15 18:45:04.094] [INFO ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 175
[2025-06-15 18:45:04.094] [INFO ] [http-nio-32000-exec-7] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=57 ms, AverageTime=57 ms

[2025-06-15 18:45:04.124] [INFO ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 175
[2025-06-15 18:45:04.124] [INFO ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 175
[2025-06-15 18:45:04.124] [INFO ] [http-nio-32000-exec-8] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=81 ms, AverageTime=81 ms

[2025-06-15 18:45:04.124] [INFO ] [http-nio-32000-exec-6] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=90 ms, AverageTime=90 ms

[2025-06-15 18:45:04.134] [INFO ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 175
[2025-06-15 18:45:04.134] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=96 ms, AverageTime=96 ms

[2025-06-15 18:45:04.142] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 176
[2025-06-15 18:45:04.142] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=74 ms, AverageTime=74 ms

[2025-06-15 18:45:04.145] [INFO ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 176
[2025-06-15 18:45:04.145] [INFO ] [http-nio-32000-exec-2] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=77 ms, AverageTime=77 ms

[2025-06-15 18:45:04.207] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 发送企业微信告警通知 [产品数据堆积] [产品数据]: 失败
[2025-06-15 18:45:04.209] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 177
[2025-06-15 18:45:04.210] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=63 ms, AverageTime=63 ms

[2025-06-15 18:45:04.257] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 178
[2025-06-15 18:45:04.257] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=43 ms, AverageTime=43 ms

[2025-06-15 18:45:04.268] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 数据处理状态检查完成，是否有告警: true
[2025-06-15 18:45:04.268] [INFO ] [scheduling-1] [com.xinghuo.fruugo.analysis.task.FruugoAlertTask] [?] [?] - Fruugo数据监控检查任务执行完成，是否有告警: true
[2025-06-15 18:45:10.483] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 179
[2025-06-15 18:45:10.483] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=45 ms, AverageTime=45 ms

[2025-06-15 18:45:20.488] [INFO ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 180
[2025-06-15 18:45:20.489] [INFO ] [http-nio-32000-exec-3] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=60 ms, AverageTime=60 ms

[2025-06-15 18:49:32.489] [INFO ] [http-nio-32000-exec-8] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:

[2025-06-15 18:49:32.534] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 181
[2025-06-15 18:49:32.534] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=70 ms, AverageTime=70 ms

[2025-06-15 18:49:32.537] [INFO ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 181
[2025-06-15 18:49:32.537] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 181
[2025-06-15 18:49:32.537] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 181
[2025-06-15 18:49:32.537] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=73 ms, AverageTime=73 ms

[2025-06-15 18:49:32.537] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=73 ms, AverageTime=73 ms

[2025-06-15 18:49:32.537] [INFO ] [http-nio-32000-exec-3] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=73 ms, AverageTime=73 ms

[2025-06-15 18:49:32.548] [INFO ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 181
[2025-06-15 18:49:32.548] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 181
[2025-06-15 18:49:32.548] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=81 ms, AverageTime=81 ms

[2025-06-15 18:49:32.548] [INFO ] [http-nio-32000-exec-7] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=84 ms, AverageTime=84 ms

[2025-06-15 18:49:32.638] [INFO ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 182
[2025-06-15 18:49:32.639] [INFO ] [http-nio-32000-exec-8] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=89 ms, AverageTime=89 ms

[2025-06-15 18:49:32.676] [INFO ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 182
[2025-06-15 18:49:32.676] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 182
[2025-06-15 18:49:32.677] [INFO ] [http-nio-32000-exec-6] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=127 ms, AverageTime=127 ms

[2025-06-15 18:49:32.677] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=122 ms, AverageTime=122 ms

[2025-06-15 18:49:32.677] [INFO ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 182
[2025-06-15 18:49:32.677] [INFO ] [http-nio-32000-exec-2] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=126 ms, AverageTime=126 ms

[2025-06-15 18:49:32.677] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 182
[2025-06-15 18:49:32.678] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=124 ms, AverageTime=124 ms

[2025-06-15 18:49:32.706] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 183
[2025-06-15 18:49:32.706] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=64 ms, AverageTime=64 ms

[2025-06-15 18:49:32.729] [INFO ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 183
[2025-06-15 18:49:32.729] [INFO ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 183
[2025-06-15 18:49:32.729] [INFO ] [http-nio-32000-exec-8] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=43 ms, AverageTime=43 ms

[2025-06-15 18:49:32.729] [INFO ] [http-nio-32000-exec-7] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=44 ms, AverageTime=44 ms

[2025-06-15 18:49:32.742] [INFO ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 184
[2025-06-15 18:49:32.742] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 184
[2025-06-15 18:49:32.742] [INFO ] [http-nio-32000-exec-3] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=56 ms, AverageTime=56 ms

[2025-06-15 18:49:32.742] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=54 ms, AverageTime=54 ms

[2025-06-15 18:49:32.751] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 184
[2025-06-15 18:49:32.752] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=42 ms, AverageTime=42 ms

[2025-06-15 18:49:32.780] [INFO ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 185
[2025-06-15 18:49:32.780] [INFO ] [http-nio-32000-exec-6] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=46 ms, AverageTime=46 ms

[2025-06-15 18:49:32.781] [INFO ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 185
[2025-06-15 18:49:32.781] [INFO ] [http-nio-32000-exec-2] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=47 ms, AverageTime=47 ms

[2025-06-15 18:49:32.790] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 185
[2025-06-15 18:49:32.791] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=44 ms, AverageTime=44 ms

[2025-06-15 18:49:32.795] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 185
[2025-06-15 18:49:32.795] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=48 ms, AverageTime=48 ms

[2025-06-15 18:49:32.802] [INFO ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 185
[2025-06-15 18:49:32.802] [INFO ] [http-nio-32000-exec-8] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=47 ms, AverageTime=47 ms

[2025-06-15 18:49:32.826] [INFO ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 186
[2025-06-15 18:49:32.826] [INFO ] [http-nio-32000-exec-7] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=40 ms, AverageTime=40 ms

[2025-06-15 18:49:32.867] [INFO ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 187
[2025-06-15 18:49:32.868] [INFO ] [http-nio-32000-exec-3] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=38 ms, AverageTime=38 ms

[2025-06-15 18:49:32.876] [INFO ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 182
[2025-06-15 18:49:32.876] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=328 ms, AverageTime=328 ms

[2025-06-15 18:49:40.471] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 188
[2025-06-15 18:49:40.472] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=49 ms, AverageTime=49 ms

[2025-06-15 18:49:50.486] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 189
[2025-06-15 18:49:50.487] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=50 ms, AverageTime=50 ms

[2025-06-15 18:50:00.011] [INFO ] [scheduling-1] [com.xinghuo.fruugo.analysis.task.FruugoAlertTask] [?] [?] - 开始执行Fruugo数据监控检查任务
[2025-06-15 18:50:00.012] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 开始检查数据处理状态
[2025-06-15 18:50:00.198] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 告警 [JustBought数据缺失] [JustBought数据] 在告警间隔期内，跳过本次告警
[2025-06-15 18:50:00.653] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 告警 [产品数据堆积] [产品数据] 在告警间隔期内，跳过本次告警
[2025-06-15 18:50:00.674] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 数据处理状态检查完成，是否有告警: true
[2025-06-15 18:50:00.674] [INFO ] [scheduling-1] [com.xinghuo.fruugo.analysis.task.FruugoAlertTask] [?] [?] - Fruugo数据监控检查任务执行完成，是否有告警: true
[2025-06-15 18:50:00.755] [INFO ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 190
[2025-06-15 18:50:00.756] [INFO ] [http-nio-32000-exec-6] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=67 ms, AverageTime=67 ms

[2025-06-15 18:50:10.483] [INFO ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 191
[2025-06-15 18:50:10.483] [INFO ] [http-nio-32000-exec-2] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=56 ms, AverageTime=56 ms

[2025-06-15 18:50:20.483] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 192
[2025-06-15 18:50:20.483] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=55 ms, AverageTime=55 ms

[2025-06-15 18:50:30.485] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 193
[2025-06-15 18:50:30.485] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=64 ms, AverageTime=64 ms

[2025-06-15 18:50:40.488] [INFO ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 194
[2025-06-15 18:50:40.489] [INFO ] [http-nio-32000-exec-6] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=61 ms, AverageTime=61 ms

[2025-06-15 18:50:50.495] [INFO ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 195
[2025-06-15 18:50:50.495] [INFO ] [http-nio-32000-exec-2] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=65 ms, AverageTime=65 ms

[2025-06-15 18:51:00.488] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 196
[2025-06-15 18:51:00.488] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=62 ms, AverageTime=62 ms

[2025-06-15 18:51:10.480] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 197
[2025-06-15 18:51:10.481] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=52 ms, AverageTime=52 ms

[2025-06-15 18:51:20.494] [INFO ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 198
[2025-06-15 18:51:20.494] [INFO ] [http-nio-32000-exec-7] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=65 ms, AverageTime=65 ms

[2025-06-15 18:51:30.488] [INFO ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 199
[2025-06-15 18:51:30.489] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=60 ms, AverageTime=60 ms

[2025-06-15 18:51:40.901] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 200
[2025-06-15 18:51:40.901] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=58 ms, AverageTime=58 ms

[2025-06-15 18:51:50.473] [INFO ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 201
[2025-06-15 18:51:50.473] [INFO ] [http-nio-32000-exec-8] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=48 ms, AverageTime=48 ms

[2025-06-15 18:52:00.503] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 202
[2025-06-15 18:52:00.503] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=83 ms, AverageTime=83 ms

[2025-06-15 18:52:10.481] [INFO ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 203
[2025-06-15 18:52:10.481] [INFO ] [http-nio-32000-exec-6] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=52 ms, AverageTime=52 ms

[2025-06-15 18:52:20.497] [INFO ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 204
[2025-06-15 18:52:20.497] [INFO ] [http-nio-32000-exec-2] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=66 ms, AverageTime=66 ms

[2025-06-15 18:52:30.482] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 205
[2025-06-15 18:52:30.484] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=50 ms, AverageTime=50 ms

[2025-06-15 18:52:40.749] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 206
[2025-06-15 18:52:40.749] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=71 ms, AverageTime=71 ms

[2025-06-15 18:52:50.487] [INFO ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 207
[2025-06-15 18:52:50.487] [INFO ] [http-nio-32000-exec-3] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=57 ms, AverageTime=57 ms

[2025-06-15 18:53:00.491] [INFO ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 208
[2025-06-15 18:53:00.492] [INFO ] [http-nio-32000-exec-7] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=70 ms, AverageTime=70 ms

[2025-06-15 18:53:10.510] [INFO ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 209
[2025-06-15 18:53:10.511] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=59 ms, AverageTime=59 ms

[2025-06-15 18:53:20.496] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 210
[2025-06-15 18:53:20.496] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=60 ms, AverageTime=60 ms

[2025-06-15 18:53:30.475] [INFO ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 211
[2025-06-15 18:53:30.475] [INFO ] [http-nio-32000-exec-8] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=52 ms, AverageTime=52 ms

[2025-06-15 18:53:40.482] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 212
[2025-06-15 18:53:40.482] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=57 ms, AverageTime=57 ms

[2025-06-15 18:53:50.499] [INFO ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 213
[2025-06-15 18:53:50.499] [INFO ] [http-nio-32000-exec-6] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=70 ms, AverageTime=70 ms

[2025-06-15 18:54:00.507] [INFO ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 214
[2025-06-15 18:54:00.507] [INFO ] [http-nio-32000-exec-2] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=70 ms, AverageTime=70 ms

[2025-06-15 18:54:11.678] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 215
[2025-06-15 18:54:11.678] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=63 ms, AverageTime=63 ms

[2025-06-15 18:54:20.491] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 216
[2025-06-15 18:54:20.491] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=63 ms, AverageTime=63 ms

[2025-06-15 18:54:30.482] [INFO ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 217
[2025-06-15 18:54:30.482] [INFO ] [http-nio-32000-exec-3] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=58 ms, AverageTime=58 ms

[2025-06-15 18:54:40.489] [INFO ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 218
[2025-06-15 18:54:40.489] [INFO ] [http-nio-32000-exec-7] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=66 ms, AverageTime=66 ms

[2025-06-15 18:54:50.485] [INFO ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 219
[2025-06-15 18:54:50.485] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=49 ms, AverageTime=49 ms

[2025-06-15 18:55:00.008] [INFO ] [scheduling-1] [com.xinghuo.fruugo.analysis.task.FruugoAlertTask] [?] [?] - 开始执行Fruugo数据监控检查任务
[2025-06-15 18:55:00.008] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 开始检查数据处理状态
[2025-06-15 18:55:00.247] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 告警 [JustBought数据缺失] [JustBought数据] 在告警间隔期内，跳过本次告警
[2025-06-15 18:55:00.532] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 220
[2025-06-15 18:55:00.532] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=93 ms, AverageTime=93 ms

[2025-06-15 18:55:11.014] [INFO ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 221
[2025-06-15 18:55:11.014] [INFO ] [http-nio-32000-exec-8] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=57 ms, AverageTime=57 ms

[2025-06-15 18:55:19.878] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 告警 [产品数据堆积] [产品数据] 在告警间隔期内，跳过本次告警
[2025-06-15 18:55:19.914] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 数据处理状态检查完成，是否有告警: true
[2025-06-15 18:55:19.914] [INFO ] [scheduling-1] [com.xinghuo.fruugo.analysis.task.FruugoAlertTask] [?] [?] - Fruugo数据监控检查任务执行完成，是否有告警: true
[2025-06-15 18:55:20.520] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 222
[2025-06-15 18:55:20.520] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=55 ms, AverageTime=55 ms

[2025-06-15 18:55:30.476] [INFO ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 223
[2025-06-15 18:55:30.476] [INFO ] [http-nio-32000-exec-6] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=51 ms, AverageTime=51 ms

[2025-06-15 18:55:40.501] [INFO ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 224
[2025-06-15 18:55:40.501] [INFO ] [http-nio-32000-exec-2] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=55 ms, AverageTime=55 ms

[2025-06-15 18:55:50.503] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 225
[2025-06-15 18:55:50.503] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=66 ms, AverageTime=66 ms

[2025-06-15 18:56:00.502] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 226
[2025-06-15 18:56:00.502] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=71 ms, AverageTime=71 ms

[2025-06-15 18:56:10.509] [INFO ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 227
[2025-06-15 18:56:10.510] [INFO ] [http-nio-32000-exec-3] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=71 ms, AverageTime=71 ms

[2025-06-15 18:56:20.495] [INFO ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 228
[2025-06-15 18:56:20.495] [INFO ] [http-nio-32000-exec-7] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=67 ms, AverageTime=67 ms

[2025-06-15 18:56:30.481] [INFO ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 229
[2025-06-15 18:56:30.482] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=53 ms, AverageTime=53 ms

[2025-06-15 18:56:40.501] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 230
[2025-06-15 18:56:40.502] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=70 ms, AverageTime=70 ms

[2025-06-15 18:56:50.489] [INFO ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 231
[2025-06-15 18:56:50.489] [INFO ] [http-nio-32000-exec-8] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=56 ms, AverageTime=56 ms

[2025-06-15 18:57:00.494] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 232
[2025-06-15 18:57:00.495] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=58 ms, AverageTime=58 ms

[2025-06-15 18:57:10.496] [INFO ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 233
[2025-06-15 18:57:10.496] [INFO ] [http-nio-32000-exec-6] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=69 ms, AverageTime=69 ms

[2025-06-15 18:57:20.480] [INFO ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 234
[2025-06-15 18:57:20.481] [INFO ] [http-nio-32000-exec-2] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=58 ms, AverageTime=58 ms

[2025-06-15 18:57:30.513] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 235
[2025-06-15 18:57:30.513] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=65 ms, AverageTime=65 ms

[2025-06-15 18:57:40.483] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 236
[2025-06-15 18:57:40.483] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=49 ms, AverageTime=49 ms

[2025-06-15 18:57:50.484] [INFO ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 237
[2025-06-15 18:57:50.484] [INFO ] [http-nio-32000-exec-3] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=53 ms, AverageTime=53 ms

[2025-06-15 18:58:00.504] [INFO ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 238
[2025-06-15 18:58:00.504] [INFO ] [http-nio-32000-exec-7] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=71 ms, AverageTime=71 ms

[2025-06-15 18:58:10.490] [INFO ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 239
[2025-06-15 18:58:10.490] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=56 ms, AverageTime=56 ms

[2025-06-15 18:58:20.505] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 240
[2025-06-15 18:58:20.506] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=66 ms, AverageTime=66 ms

[2025-06-15 18:58:30.504] [INFO ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 241
[2025-06-15 18:58:30.504] [INFO ] [http-nio-32000-exec-8] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=74 ms, AverageTime=74 ms

[2025-06-15 18:58:40.499] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 242
[2025-06-15 18:58:40.499] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=66 ms, AverageTime=66 ms

[2025-06-15 18:58:50.491] [INFO ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 243
[2025-06-15 18:58:50.492] [INFO ] [http-nio-32000-exec-6] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=65 ms, AverageTime=65 ms

[2025-06-15 18:59:00.484] [INFO ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 244
[2025-06-15 18:59:00.485] [INFO ] [http-nio-32000-exec-2] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=55 ms, AverageTime=55 ms

[2025-06-15 18:59:10.479] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 245
[2025-06-15 18:59:10.479] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=59 ms, AverageTime=59 ms

[2025-06-15 18:59:20.511] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 246
[2025-06-15 18:59:20.512] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=76 ms, AverageTime=76 ms

[2025-06-15 18:59:30.491] [INFO ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 247
[2025-06-15 18:59:30.491] [INFO ] [http-nio-32000-exec-3] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=65 ms, AverageTime=65 ms

[2025-06-15 18:59:40.482] [INFO ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 248
[2025-06-15 18:59:40.483] [INFO ] [http-nio-32000-exec-7] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=46 ms, AverageTime=46 ms

[2025-06-15 18:59:50.530] [INFO ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 249
[2025-06-15 18:59:50.531] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=94 ms, AverageTime=94 ms

[2025-06-15 19:00:00.000] [INFO ] [scheduling-1] [com.xinghuo.fruugo.analysis.task.FruugoAlertTask] [?] [?] - 开始执行Fruugo数据监控检查任务
[2025-06-15 19:00:00.000] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 开始检查数据处理状态
[2025-06-15 19:00:00.015] [INFO ] [JetCacheDefaultExecutor] [com.alicp.jetcache.support.StatInfoLogger] [?] [?] - jetcache stat from 2025-06-15 18:45:00,556 to 2025-06-15 19:00:00,014
cache           |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
----------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
xaceCache       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
xaceCache_local |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
xaceCache_remote|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
----------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

[2025-06-15 19:00:00.422] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 告警 [JustBought数据缺失] [JustBought数据] 在告警间隔期内，跳过本次告警
[2025-06-15 19:00:00.477] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 250
[2025-06-15 19:00:00.477] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=53 ms, AverageTime=53 ms

[2025-06-15 19:00:10.542] [INFO ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 251
[2025-06-15 19:00:10.542] [INFO ] [http-nio-32000-exec-8] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=106 ms, AverageTime=106 ms

[2025-06-15 19:00:20.515] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 252
[2025-06-15 19:00:20.515] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=93 ms, AverageTime=93 ms

[2025-06-15 19:00:30.501] [INFO ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 253
[2025-06-15 19:00:30.501] [INFO ] [http-nio-32000-exec-6] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=74 ms, AverageTime=74 ms

[2025-06-15 19:00:31.864] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 告警 [产品数据堆积] [产品数据] 在告警间隔期内，跳过本次告警
[2025-06-15 19:00:31.886] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 数据处理状态检查完成，是否有告警: true
[2025-06-15 19:00:31.886] [INFO ] [scheduling-1] [com.xinghuo.fruugo.analysis.task.FruugoAlertTask] [?] [?] - Fruugo数据监控检查任务执行完成，是否有告警: true
[2025-06-15 19:00:31.887] [INFO ] [scheduling-1] [c.x.fruugo.analysis.task.FruugoMonitorRefreshTask] [?] [?] - 开始执行Fruugo监控数据定时刷新任务
[2025-06-15 19:00:31.887] [INFO ] [scheduling-1] [c.x.f.a.service.impl.FruugoMonitorServiceImpl] [?] [?] - 开始生成 2025-06-15 的监控数据
[2025-06-15 19:00:40.489] [INFO ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 254
[2025-06-15 19:00:40.490] [INFO ] [http-nio-32000-exec-2] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=67 ms, AverageTime=67 ms

[2025-06-15 19:00:50.481] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 255
[2025-06-15 19:00:50.481] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=49 ms, AverageTime=49 ms

[2025-06-15 19:01:00.484] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 256
[2025-06-15 19:01:00.484] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=50 ms, AverageTime=50 ms

[2025-06-15 19:01:10.512] [INFO ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 257
[2025-06-15 19:01:10.512] [INFO ] [http-nio-32000-exec-3] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=80 ms, AverageTime=80 ms

[2025-06-15 19:01:20.470] [INFO ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 258
[2025-06-15 19:01:20.470] [INFO ] [http-nio-32000-exec-7] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=48 ms, AverageTime=48 ms

[2025-06-15 19:01:30.482] [INFO ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 259
[2025-06-15 19:01:30.483] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=55 ms, AverageTime=55 ms

[2025-06-15 19:01:40.512] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 260
[2025-06-15 19:01:40.512] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=84 ms, AverageTime=84 ms

[2025-06-15 19:01:50.475] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 261
[2025-06-15 19:01:50.475] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=49 ms, AverageTime=49 ms

[2025-06-15 19:02:00.499] [INFO ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 262
[2025-06-15 19:02:00.499] [INFO ] [http-nio-32000-exec-2] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=64 ms, AverageTime=64 ms

[2025-06-15 19:02:10.484] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 263
[2025-06-15 19:02:10.485] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=46 ms, AverageTime=46 ms

[2025-06-15 19:02:20.478] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 264
[2025-06-15 19:02:20.478] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=55 ms, AverageTime=55 ms

[2025-06-15 19:02:30.534] [INFO ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 265
[2025-06-15 19:02:30.534] [INFO ] [http-nio-32000-exec-3] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=107 ms, AverageTime=107 ms

[2025-06-15 19:02:40.546] [INFO ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 266
[2025-06-15 19:02:40.546] [INFO ] [http-nio-32000-exec-7] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=118 ms, AverageTime=118 ms

[2025-06-15 19:02:50.919] [INFO ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 267
[2025-06-15 19:02:50.919] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=111 ms, AverageTime=111 ms

[2025-06-15 19:03:00.512] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 268
[2025-06-15 19:03:00.512] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=80 ms, AverageTime=80 ms

[2025-06-15 19:03:10.510] [INFO ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 269
[2025-06-15 19:03:10.510] [INFO ] [http-nio-32000-exec-8] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=78 ms, AverageTime=78 ms

[2025-06-15 19:03:20.583] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 270
[2025-06-15 19:03:20.583] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=124 ms, AverageTime=124 ms

[2025-06-15 19:03:30.612] [INFO ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 271
[2025-06-15 19:03:30.612] [INFO ] [http-nio-32000-exec-6] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=50 ms, AverageTime=50 ms

[2025-06-15 19:03:40.498] [INFO ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 272
[2025-06-15 19:03:40.498] [INFO ] [http-nio-32000-exec-2] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=79 ms, AverageTime=79 ms

[2025-06-15 19:03:50.470] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 273
[2025-06-15 19:03:50.470] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=44 ms, AverageTime=44 ms

[2025-06-15 19:04:00.478] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 274
[2025-06-15 19:04:00.478] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=53 ms, AverageTime=53 ms

[2025-06-15 19:04:10.475] [INFO ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 275
[2025-06-15 19:04:10.476] [INFO ] [http-nio-32000-exec-3] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=43 ms, AverageTime=43 ms

[2025-06-15 19:04:20.520] [INFO ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 276
[2025-06-15 19:04:20.520] [INFO ] [http-nio-32000-exec-7] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=50 ms, AverageTime=50 ms

[2025-06-15 19:04:30.464] [INFO ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 277
[2025-06-15 19:04:30.464] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=39 ms, AverageTime=39 ms

[2025-06-15 19:04:40.527] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 278
[2025-06-15 19:04:40.527] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=101 ms, AverageTime=101 ms

[2025-06-15 19:04:50.474] [INFO ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 279
[2025-06-15 19:04:50.474] [INFO ] [http-nio-32000-exec-8] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=47 ms, AverageTime=47 ms

[2025-06-15 19:05:00.484] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 280
[2025-06-15 19:05:00.485] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=46 ms, AverageTime=46 ms

[2025-06-15 19:05:10.488] [INFO ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 281
[2025-06-15 19:05:10.488] [INFO ] [http-nio-32000-exec-6] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=64 ms, AverageTime=64 ms

[2025-06-15 19:05:20.471] [INFO ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 282
[2025-06-15 19:05:20.471] [INFO ] [http-nio-32000-exec-2] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=46 ms, AverageTime=46 ms

[2025-06-15 19:05:30.478] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 283
[2025-06-15 19:05:30.478] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=43 ms, AverageTime=43 ms

[2025-06-15 19:05:40.482] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 284
[2025-06-15 19:05:40.482] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=53 ms, AverageTime=53 ms

[2025-06-15 19:05:50.471] [INFO ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 285
[2025-06-15 19:05:50.471] [INFO ] [http-nio-32000-exec-3] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=44 ms, AverageTime=44 ms

[2025-06-15 19:06:00.464] [INFO ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 286
[2025-06-15 19:06:00.464] [INFO ] [http-nio-32000-exec-7] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=44 ms, AverageTime=44 ms

[2025-06-15 19:06:10.480] [INFO ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 287
[2025-06-15 19:06:10.480] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=53 ms, AverageTime=53 ms

[2025-06-15 19:06:20.473] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 288
[2025-06-15 19:06:20.473] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=45 ms, AverageTime=45 ms

[2025-06-15 19:06:30.475] [INFO ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 289
[2025-06-15 19:06:30.476] [INFO ] [http-nio-32000-exec-8] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=44 ms, AverageTime=44 ms

[2025-06-15 19:06:41.816] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 290
[2025-06-15 19:06:41.816] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=50 ms, AverageTime=50 ms

[2025-06-15 19:06:50.502] [INFO ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 291
[2025-06-15 19:06:50.502] [INFO ] [http-nio-32000-exec-6] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=66 ms, AverageTime=66 ms

[2025-06-15 19:07:00.470] [INFO ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 292
[2025-06-15 19:07:00.470] [INFO ] [http-nio-32000-exec-2] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=46 ms, AverageTime=46 ms

[2025-06-15 19:07:10.689] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 293
[2025-06-15 19:07:10.690] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=57 ms, AverageTime=57 ms

[2025-06-15 19:07:20.460] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 294
[2025-06-15 19:07:20.460] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=41 ms, AverageTime=41 ms

[2025-06-15 19:07:30.487] [INFO ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 295
[2025-06-15 19:07:30.488] [INFO ] [http-nio-32000-exec-3] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=52 ms, AverageTime=52 ms

[2025-06-15 19:07:32.108] [INFO ] [scheduling-1] [c.x.f.a.service.impl.FruugoMonitorServiceImpl] [?] [?] - 生成 2025-06-15 的监控数据 成功
[2025-06-15 19:07:32.108] [INFO ] [scheduling-1] [c.x.fruugo.analysis.task.FruugoMonitorRefreshTask] [?] [?] - Fruugo基础监控数据刷新 成功
[2025-06-15 19:07:32.109] [INFO ] [scheduling-1] [c.x.f.a.service.impl.FruugoDataMonitorServiceImpl] [?] [?] - 生成今日的Fruugo数据监控信息
[2025-06-15 19:07:32.109] [INFO ] [scheduling-1] [c.x.f.a.service.impl.FruugoDataMonitorServiceImpl] [?] [?] - 生成指定日期的Fruugo数据监控信息，日期: 2025-06-15
[2025-06-15 19:07:32.109] [INFO ] [scheduling-1] [c.x.f.a.service.impl.FruugoMonitorServiceImpl] [?] [?] - 开始生成 2025-06-15 的监控数据
[2025-06-15 19:07:40.481] [INFO ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 296
[2025-06-15 19:07:40.481] [INFO ] [http-nio-32000-exec-7] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=57 ms, AverageTime=57 ms

[2025-06-15 19:07:50.492] [INFO ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 297
[2025-06-15 19:07:50.492] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=48 ms, AverageTime=48 ms

[2025-06-15 19:08:00.489] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 298
[2025-06-15 19:08:00.489] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=54 ms, AverageTime=54 ms

[2025-06-15 19:08:10.663] [INFO ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 299
[2025-06-15 19:08:10.663] [INFO ] [http-nio-32000-exec-8] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=57 ms, AverageTime=57 ms

[2025-06-15 19:08:20.474] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 300
[2025-06-15 19:08:20.474] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=50 ms, AverageTime=50 ms

[2025-06-15 19:08:30.476] [INFO ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 301
[2025-06-15 19:08:30.477] [INFO ] [http-nio-32000-exec-6] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=47 ms, AverageTime=47 ms

[2025-06-15 19:08:40.520] [INFO ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 302
[2025-06-15 19:08:40.520] [INFO ] [http-nio-32000-exec-2] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=81 ms, AverageTime=81 ms

[2025-06-15 19:08:50.491] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 303
[2025-06-15 19:08:50.491] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=42 ms, AverageTime=42 ms

[2025-06-15 19:09:00.499] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 304
[2025-06-15 19:09:00.499] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=61 ms, AverageTime=61 ms

[2025-06-15 19:09:10.546] [INFO ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 305
[2025-06-15 19:09:10.546] [INFO ] [http-nio-32000-exec-3] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=67 ms, AverageTime=67 ms

[2025-06-15 19:09:20.483] [INFO ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 306
[2025-06-15 19:09:20.483] [INFO ] [http-nio-32000-exec-7] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=48 ms, AverageTime=48 ms

[2025-06-15 19:09:30.493] [INFO ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 307
[2025-06-15 19:09:30.493] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=48 ms, AverageTime=48 ms

[2025-06-15 19:09:40.501] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 308
[2025-06-15 19:09:40.502] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=60 ms, AverageTime=60 ms

[2025-06-15 19:09:50.486] [INFO ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 309
[2025-06-15 19:09:50.487] [INFO ] [http-nio-32000-exec-8] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=56 ms, AverageTime=56 ms

[2025-06-15 19:10:00.486] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 310
[2025-06-15 19:10:00.486] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=50 ms, AverageTime=50 ms

[2025-06-15 19:10:10.489] [INFO ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 311
[2025-06-15 19:10:10.490] [INFO ] [http-nio-32000-exec-6] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=56 ms, AverageTime=56 ms

[2025-06-15 19:10:20.492] [INFO ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 312
[2025-06-15 19:10:20.492] [INFO ] [http-nio-32000-exec-2] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=54 ms, AverageTime=54 ms

[2025-06-15 19:10:30.472] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 313
[2025-06-15 19:10:30.473] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=48 ms, AverageTime=48 ms

[2025-06-15 19:10:40.481] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 314
[2025-06-15 19:10:40.482] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=47 ms, AverageTime=47 ms

[2025-06-15 19:10:50.476] [INFO ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 315
[2025-06-15 19:10:50.476] [INFO ] [http-nio-32000-exec-3] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=45 ms, AverageTime=45 ms

[2025-06-15 19:11:00.508] [INFO ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 316
[2025-06-15 19:11:00.508] [INFO ] [http-nio-32000-exec-7] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=56 ms, AverageTime=56 ms

[2025-06-15 19:11:10.507] [INFO ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 317
[2025-06-15 19:11:10.508] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=79 ms, AverageTime=79 ms

[2025-06-15 19:11:20.482] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 318
[2025-06-15 19:11:20.482] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=50 ms, AverageTime=50 ms

[2025-06-15 19:11:30.506] [INFO ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 319
[2025-06-15 19:11:30.506] [INFO ] [http-nio-32000-exec-8] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=68 ms, AverageTime=68 ms

[2025-06-15 19:11:40.494] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 320
[2025-06-15 19:11:40.494] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=56 ms, AverageTime=56 ms

[2025-06-15 19:11:50.491] [INFO ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 321
[2025-06-15 19:11:50.492] [INFO ] [http-nio-32000-exec-6] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=58 ms, AverageTime=58 ms

[2025-06-15 19:12:00.492] [INFO ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 322
[2025-06-15 19:12:00.492] [INFO ] [http-nio-32000-exec-2] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=61 ms, AverageTime=61 ms

[2025-06-15 19:12:10.486] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 323
[2025-06-15 19:12:10.486] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=52 ms, AverageTime=52 ms

[2025-06-15 19:12:20.508] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 324
[2025-06-15 19:12:20.509] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=50 ms, AverageTime=50 ms

[2025-06-15 19:12:30.477] [INFO ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 325
[2025-06-15 19:12:30.478] [INFO ] [http-nio-32000-exec-3] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=50 ms, AverageTime=50 ms

[2025-06-15 19:12:40.484] [INFO ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 326
[2025-06-15 19:12:40.484] [INFO ] [http-nio-32000-exec-7] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=47 ms, AverageTime=47 ms

[2025-06-15 19:12:50.473] [INFO ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 327
[2025-06-15 19:12:50.473] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=49 ms, AverageTime=49 ms

[2025-06-15 19:13:00.466] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 328
[2025-06-15 19:13:00.466] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=46 ms, AverageTime=46 ms

[2025-06-15 19:13:10.503] [INFO ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 329
[2025-06-15 19:13:10.503] [INFO ] [http-nio-32000-exec-8] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=61 ms, AverageTime=61 ms

[2025-06-15 19:13:20.477] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 330
[2025-06-15 19:13:20.478] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=48 ms, AverageTime=48 ms

[2025-06-15 19:13:30.514] [INFO ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 331
[2025-06-15 19:13:30.515] [INFO ] [http-nio-32000-exec-6] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=86 ms, AverageTime=86 ms

[2025-06-15 19:13:40.499] [INFO ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 332
[2025-06-15 19:13:40.499] [INFO ] [http-nio-32000-exec-2] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=57 ms, AverageTime=57 ms

[2025-06-15 19:13:42.856] [INFO ] [scheduling-1] [c.x.f.a.service.impl.FruugoMonitorServiceImpl] [?] [?] - 生成 2025-06-15 的监控数据 成功
[2025-06-15 19:13:42.856] [INFO ] [scheduling-1] [c.x.f.a.service.impl.FruugoDataMonitorServiceImpl] [?] [?] - 生成指定日期的Fruugo数据监控信息成功，日期: 2025-06-15
[2025-06-15 19:13:42.856] [INFO ] [scheduling-1] [c.x.fruugo.analysis.task.FruugoMonitorRefreshTask] [?] [?] - Fruugo数据监控信息刷新 成功
[2025-06-15 19:13:42.856] [INFO ] [scheduling-1] [c.x.fruugo.analysis.task.FruugoMonitorRefreshTask] [?] [?] - Fruugo监控数据定时刷新任务执行完成
[2025-06-15 19:13:42.861] [INFO ] [scheduling-1] [com.xinghuo.fruugo.analysis.task.FruugoAlertTask] [?] [?] - 开始执行Fruugo数据监控检查任务
[2025-06-15 19:13:42.862] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 开始检查数据处理状态
[2025-06-15 19:13:43.036] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 告警 [JustBought数据缺失] [JustBought数据] 在告警间隔期内，跳过本次告警
[2025-06-15 19:13:49.849] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 告警 [产品数据堆积] [产品数据] 在告警间隔期内，跳过本次告警
[2025-06-15 19:13:49.867] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 数据处理状态检查完成，是否有告警: true
[2025-06-15 19:13:49.867] [INFO ] [scheduling-1] [com.xinghuo.fruugo.analysis.task.FruugoAlertTask] [?] [?] - Fruugo数据监控检查任务执行完成，是否有告警: true
[2025-06-15 19:13:50.481] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 333
[2025-06-15 19:13:50.482] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=50 ms, AverageTime=50 ms

[2025-06-15 19:14:00.498] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 334
[2025-06-15 19:14:00.498] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=63 ms, AverageTime=63 ms

[2025-06-15 19:14:10.478] [INFO ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 335
[2025-06-15 19:14:10.478] [INFO ] [http-nio-32000-exec-3] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=43 ms, AverageTime=43 ms

[2025-06-15 19:14:20.479] [INFO ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 336
[2025-06-15 19:14:20.479] [INFO ] [http-nio-32000-exec-7] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=44 ms, AverageTime=44 ms

[2025-06-15 19:14:30.489] [INFO ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 337
[2025-06-15 19:14:30.489] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=60 ms, AverageTime=60 ms

[2025-06-15 19:14:40.481] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 338
[2025-06-15 19:14:40.482] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=46 ms, AverageTime=46 ms

[2025-06-15 19:14:50.479] [INFO ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 339
[2025-06-15 19:14:50.479] [INFO ] [http-nio-32000-exec-8] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=44 ms, AverageTime=44 ms

[2025-06-15 19:15:00.009] [INFO ] [scheduling-1] [com.xinghuo.fruugo.analysis.task.FruugoAlertTask] [?] [?] - 开始执行Fruugo数据监控检查任务
[2025-06-15 19:15:00.009] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 开始检查数据处理状态
[2025-06-15 19:15:00.011] [INFO ] [JetCacheDefaultExecutor] [com.alicp.jetcache.support.StatInfoLogger] [?] [?] - jetcache stat from 2025-06-15 19:00:00,014 to 2025-06-15 19:15:00,009
cache           |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
----------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
xaceCache       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
xaceCache_local |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
xaceCache_remote|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
----------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

[2025-06-15 19:15:00.111] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 告警 [JustBought数据缺失] [JustBought数据] 在告警间隔期内，跳过本次告警
[2025-06-15 19:15:00.496] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 340
[2025-06-15 19:15:00.496] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=54 ms, AverageTime=54 ms

[2025-06-15 19:15:09.616] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 发送企业微信告警通知 [产品数据堆积] [产品数据]: 失败
[2025-06-15 19:15:09.714] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 数据处理状态检查完成，是否有告警: true
[2025-06-15 19:15:09.714] [INFO ] [scheduling-1] [com.xinghuo.fruugo.analysis.task.FruugoAlertTask] [?] [?] - Fruugo数据监控检查任务执行完成，是否有告警: true
[2025-06-15 19:15:10.518] [INFO ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 341
[2025-06-15 19:15:10.519] [INFO ] [http-nio-32000-exec-6] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=66 ms, AverageTime=66 ms

[2025-06-15 19:15:20.465] [INFO ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 342
[2025-06-15 19:15:20.466] [INFO ] [http-nio-32000-exec-2] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=45 ms, AverageTime=45 ms

[2025-06-15 19:15:30.467] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 343
[2025-06-15 19:15:30.468] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=43 ms, AverageTime=43 ms

[2025-06-15 19:15:40.473] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 344
[2025-06-15 19:15:40.473] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=48 ms, AverageTime=48 ms

[2025-06-15 19:15:50.485] [INFO ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 345
[2025-06-15 19:15:50.486] [INFO ] [http-nio-32000-exec-3] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=59 ms, AverageTime=59 ms

[2025-06-15 19:16:00.497] [INFO ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 346
[2025-06-15 19:16:00.498] [INFO ] [http-nio-32000-exec-7] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=51 ms, AverageTime=51 ms

[2025-06-15 19:16:10.481] [INFO ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 347
[2025-06-15 19:16:10.481] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=47 ms, AverageTime=47 ms

[2025-06-15 19:16:20.549] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 348
[2025-06-15 19:16:20.549] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=120 ms, AverageTime=120 ms

[2025-06-15 19:16:30.464] [INFO ] [http-nio-32000-exec-8] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 349
[2025-06-15 19:16:30.464] [INFO ] [http-nio-32000-exec-8] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=43 ms, AverageTime=43 ms

[2025-06-15 19:16:40.484] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 350
[2025-06-15 19:16:40.484] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=50 ms, AverageTime=50 ms

[2025-06-15 19:16:50.491] [INFO ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 351
[2025-06-15 19:16:50.491] [INFO ] [http-nio-32000-exec-6] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=65 ms, AverageTime=65 ms

[2025-06-15 19:17:00.499] [INFO ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 352
[2025-06-15 19:17:00.500] [INFO ] [http-nio-32000-exec-2] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=66 ms, AverageTime=66 ms

[2025-06-15 19:17:10.490] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 353
[2025-06-15 19:17:10.490] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=56 ms, AverageTime=56 ms

[2025-06-15 19:17:20.480] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 354
[2025-06-15 19:17:20.480] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=43 ms, AverageTime=43 ms

[2025-06-15 19:17:30.497] [INFO ] [http-nio-32000-exec-3] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 355
[2025-06-15 19:17:30.498] [INFO ] [http-nio-32000-exec-3] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=43 ms, AverageTime=43 ms

[2025-06-15 19:17:40.502] [INFO ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 356
[2025-06-15 19:17:40.502] [INFO ] [http-nio-32000-exec-7] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=52 ms, AverageTime=52 ms

[2025-06-15 19:17:50.466] [INFO ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 357
[2025-06-15 19:17:50.467] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=41 ms, AverageTime=41 ms

[2025-06-15 19:17:53.784] [INFO ] [SpringApplicationShutdownHook] [org.quartz.core.QuartzScheduler] [?] [?] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
[2025-06-15 19:17:53.934] [INFO ] [SpringApplicationShutdownHook] [o.s.scheduling.quartz.SchedulerFactoryBean] [?] [?] - Shutting down Quartz Scheduler
[2025-06-15 19:17:53.935] [INFO ] [SpringApplicationShutdownHook] [org.quartz.core.QuartzScheduler] [?] [?] - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
[2025-06-15 19:17:53.935] [INFO ] [SpringApplicationShutdownHook] [org.quartz.core.QuartzScheduler] [?] [?] - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
[2025-06-15 19:17:53.935] [INFO ] [SpringApplicationShutdownHook] [org.quartz.core.QuartzScheduler] [?] [?] - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
[2025-06-15 19:17:53.942] [INFO ] [SpringApplicationShutdownHook] [o.dromara.x.file.storage.core.FileStorageService] [?] [?] - 销毁存储平台 local-plus-1 成功
[2025-06-15 19:17:53.942] [INFO ] [Thread-12] [com.xxl.job.core.server.EmbedServer] [?] [?] - >>>>>>>>>>> xxl-job remoting server stop.
[2025-06-15 19:17:56.945] [INFO ] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.thread.ExecutorRegistryThread] [?] [?] - >>>>>>>>>>> xxl-job registry-remove fail, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='xxl-job-executor-sample1', registryValue='http://**************:9999/'}, registryResult:ReturnT [code=500, msg=xxl-job remoting error(Connect timed out), for url : http://101.33.227.194:30020/xxl-job-admin/api/registryRemove, content=null]
[2025-06-15 19:17:56.945] [INFO ] [xxl-job, executor ExecutorRegistryThread] [com.xxl.job.core.thread.ExecutorRegistryThread] [?] [?] - >>>>>>>>>>> xxl-job, executor registry thread destroy.
[2025-06-15 19:17:56.945] [INFO ] [SpringApplicationShutdownHook] [com.xxl.job.core.server.EmbedServer] [?] [?] - >>>>>>>>>>> xxl-job remoting server destroy success.
[2025-06-15 19:17:56.945] [INFO ] [xxl-job, executor JobLogFileCleanThread] [com.xxl.job.core.thread.JobLogFileCleanThread] [?] [?] - >>>>>>>>>>> xxl-job, executor JobLogFileCleanThread thread destroy.
[2025-06-15 19:17:56.946] [INFO ] [xxl-job, executor TriggerCallbackThread] [com.xxl.job.core.thread.TriggerCallbackThread] [?] [?] - >>>>>>>>>>> xxl-job, executor callback thread destroy.
[2025-06-15 19:17:56.947] [INFO ] [Thread-11] [com.xxl.job.core.thread.TriggerCallbackThread] [?] [?] - >>>>>>>>>>> xxl-job, executor retry callback thread destroy.
[2025-06-15 19:17:58.991] [INFO ] [SpringApplicationShutdownHook] [com.alicp.jetcache.support.DefaultMetricsManager] [?] [?] - cache stat canceled
[2025-06-15 19:17:59.063] [INFO ] [SpringApplicationShutdownHook] [c.b.dynamic.datasource.DynamicRoutingDataSource] [?] [?] - dynamic-datasource start closing ....
[2025-06-15 19:17:59.070] [INFO ] [SpringApplicationShutdownHook] [com.alibaba.druid.pool.DruidDataSource] [?] [?] - {dataSource-1} closing ...
[2025-06-15 19:17:59.076] [INFO ] [SpringApplicationShutdownHook] [com.alibaba.druid.pool.DruidDataSource] [?] [?] - {dataSource-1} closed
[2025-06-15 19:17:59.076] [INFO ] [SpringApplicationShutdownHook] [c.b.d.d.destroyer.DefaultDataSourceDestroyer] [?] [?] - dynamic-datasource close the datasource named [master] success,
[2025-06-15 19:17:59.077] [INFO ] [SpringApplicationShutdownHook] [c.b.dynamic.datasource.DynamicRoutingDataSource] [?] [?] - dynamic-datasource all closed success,bye
[2025-06-15 19:18:01.312] [INFO ] [main] [com.xinghuo.admin.XhAdminApplication] [?] [?] - Starting XhAdminApplication using Java 20.0.1 with PID 45956 (G:\v2\51erp\51erp-service\xace-java-boot\xh-admin\target\classes started by Administrator in G:\v2\51erp)
[2025-06-15 19:18:01.314] [INFO ] [main] [com.xinghuo.admin.XhAdminApplication] [?] [?] - The following 1 profile is active: "vpn"
[2025-06-15 19:18:03.780] [INFO ] [main] [o.s.d.r.config.RepositoryConfigurationDelegate] [?] [?] - Multiple Spring Data modules found, entering strict repository configuration mode
[2025-06-15 19:18:03.782] [INFO ] [main] [o.s.d.r.config.RepositoryConfigurationDelegate] [?] [?] - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[2025-06-15 19:18:03.807] [INFO ] [main] [o.s.d.r.config.RepositoryConfigurationDelegate] [?] [?] - Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces.
[2025-06-15 19:18:05.335] [INFO ] [main] [o.s.boot.web.embedded.tomcat.TomcatWebServer] [?] [?] - Tomcat initialized with port 32000 (http)
[2025-06-15 19:18:05.346] [INFO ] [main] [org.apache.catalina.core.StandardService] [?] [?] - Starting service [Tomcat]
[2025-06-15 19:18:05.346] [INFO ] [main] [org.apache.catalina.core.StandardEngine] [?] [?] - Starting Servlet engine: [Apache Tomcat/10.1.19]
[2025-06-15 19:18:05.421] [INFO ] [main] [o.a.c.core.ContainerBase.[Tomcat].[localhost].[/]] [?] [?] - Initializing Spring embedded WebApplicationContext
[2025-06-15 19:18:05.422] [INFO ] [main] [o.s.b.w.s.c.ServletWebServerApplicationContext] [?] [?] - Root WebApplicationContext: initialization completed in 3886 ms
[2025-06-15 19:18:05.855] [INFO ] [main] [org.redisson.Version] [?] [?] - Redisson 3.27.2
[2025-06-15 19:18:06.204] [INFO ] [redisson-netty-1-4] [org.redisson.connection.ConnectionsHolder] [?] [?] - 1 connections initialized for 127.0.0.1/127.0.0.1:8001
[2025-06-15 19:18:06.269] [INFO ] [redisson-netty-1-19] [org.redisson.connection.ConnectionsHolder] [?] [?] - 24 connections initialized for 127.0.0.1/127.0.0.1:8001
[2025-06-15 19:18:07.176] [INFO ] [main] [com.alibaba.druid.pool.DruidDataSource] [?] [?] - {dataSource-1,master} inited
[2025-06-15 19:18:07.177] [INFO ] [main] [c.b.dynamic.datasource.DynamicRoutingDataSource] [?] [?] - dynamic-datasource - add a datasource named [master] success
[2025-06-15 19:18:07.178] [INFO ] [main] [c.b.dynamic.datasource.DynamicRoutingDataSource] [?] [?] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
[2025-06-15 19:18:36.879] [INFO ] [main] [com.xinghuo.fruugo.collect.util.FruugoSkuInfoUtil] [?] [?] - 注入FruugoImageService成功
[2025-06-15 19:18:36.879] [INFO ] [main] [com.xinghuo.fruugo.collect.util.FruugoSkuInfoUtil] [?] [?] - 设置allgeroUrl: http://127.0.0.1:3100/staticImage/
[2025-06-15 19:18:43.973] [INFO ] [main] [c.a.jetcache.autoconfigure.AbstractCacheAutoInit] [?] [?] - init cache area default , type= linkedhashmap
[2025-06-15 19:18:43.979] [INFO ] [main] [c.a.jetcache.autoconfigure.AbstractCacheAutoInit] [?] [?] - init cache area default , type= redis.springdata
[2025-06-15 19:18:44.001] [INFO ] [main] [com.alicp.jetcache.support.DefaultMetricsManager] [?] [?] - cache stat period at 15 MINUTES
[2025-06-15 19:18:44.026] [INFO ] [main] [c.a.j.redis.springdata.SpringDataBroadcastManager] [?] [?] - create RedisMessageListenerContainer instance
[2025-06-15 19:18:44.051] [INFO ] [main] [c.a.j.redis.springdata.SpringDataBroadcastManager] [?] [?] - subscribe jetcache invalidate notification. channel=projectA
[2025-06-15 19:18:52.606] [INFO ] [main] [c.xinghuo.common.database.config.IdGeneratorConfig] [?] [?] - 当前ID生成器编号: 733
[2025-06-15 19:18:53.365] [INFO ] [main] [com.xinghuo.scheduletask.config.XxlJobConfig] [?] [?] - >>>>>>>>>>> xxl-job config init.
[2025-06-15 19:18:53.416] [INFO ] [main] [o.d.x.file.storage.core.FileStorageServiceBuilder] [?] [?] - 加载本地升级版存储平台：local-plus-1
[2025-06-15 19:18:54.772] [INFO ] [main] [org.quartz.impl.StdSchedulerFactory] [?] [?] - Using default implementation for ThreadExecutor
[2025-06-15 19:18:54.782] [INFO ] [main] [org.quartz.core.SchedulerSignalerImpl] [?] [?] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
[2025-06-15 19:18:54.782] [INFO ] [main] [org.quartz.core.QuartzScheduler] [?] [?] - Quartz Scheduler v.2.3.2 created.
[2025-06-15 19:18:54.782] [INFO ] [main] [org.quartz.simpl.RAMJobStore] [?] [?] - RAMJobStore initialized.
[2025-06-15 19:18:54.784] [INFO ] [main] [org.quartz.core.QuartzScheduler] [?] [?] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

[2025-06-15 19:18:54.784] [INFO ] [main] [org.quartz.impl.StdSchedulerFactory] [?] [?] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
[2025-06-15 19:18:54.784] [INFO ] [main] [org.quartz.impl.StdSchedulerFactory] [?] [?] - Quartz scheduler version: 2.3.2
[2025-06-15 19:18:54.785] [INFO ] [main] [org.quartz.core.QuartzScheduler] [?] [?] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@4683f3a8
[2025-06-15 19:18:55.611] [INFO ] [main] [o.s.b.actuate.endpoint.web.EndpointLinksResolver] [?] [?] - Exposing 14 endpoint(s) beneath base path '/actuator'
[2025-06-15 19:18:56.928] [INFO ] [main] [com.xxl.job.core.executor.XxlJobExecutor] [?] [?] - >>>>>>>>>>> xxl-job register jobhandler success, name:defaultHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@54465788[class com.xinghuo.scheduletask.task.ScheduleTaskHandler#defaultHandler]
[2025-06-15 19:18:57.399] [INFO ] [main] [com.xxl.job.core.executor.XxlJobExecutor] [?] [?] - >>>>>>>>>>> xxl-job registry-remove success, registryParam:{defaultHandler=com.xxl.job.core.handler.impl.MethodJobHandler@54465788[class com.xinghuo.scheduletask.task.ScheduleTaskHandler#defaultHandler]}, registryResult:ReturnT [code=200, msg=null, content=null]
[2025-06-15 19:18:57.440] [INFO ] [main] [o.s.boot.web.embedded.tomcat.TomcatWebServer] [?] [?] - Tomcat started on port 32000 (http) with context path ''
[2025-06-15 19:18:57.440] [INFO ] [main] [o.s.scheduling.quartz.SchedulerFactoryBean] [?] [?] - Starting Quartz Scheduler now
[2025-06-15 19:18:57.441] [INFO ] [main] [org.quartz.core.QuartzScheduler] [?] [?] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
[2025-06-15 19:18:57.457] [INFO ] [main] [com.xinghuo.admin.XhAdminApplication] [?] [?] - Started XhAdminApplication in 56.671 seconds (process running for 57.409)
[2025-06-15 19:18:57.513] [INFO ] [Thread-12] [com.xxl.job.core.server.EmbedServer] [?] [?] - >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 9999
[2025-06-15 19:18:57.804] [INFO ] [RMI TCP Connection(3)-**************] [o.a.c.core.ContainerBase.[Tomcat].[localhost].[/]] [?] [?] - Initializing Spring DispatcherServlet 'dispatcherServlet'
[2025-06-15 19:18:57.804] [INFO ] [RMI TCP Connection(3)-**************] [org.springframework.web.servlet.DispatcherServlet] [?] [?] - Initializing Servlet 'dispatcherServlet'
[2025-06-15 19:18:57.808] [INFO ] [RMI TCP Connection(3)-**************] [org.springframework.web.servlet.DispatcherServlet] [?] [?] - Completed initialization in 4 ms
[2025-06-15 19:19:00.988] [INFO ] [http-nio-32000-exec-1] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 358
[2025-06-15 19:19:01.004] [INFO ] [http-nio-32000-exec-1] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=402 ms, AverageTime=402 ms

[2025-06-15 19:19:10.483] [INFO ] [http-nio-32000-exec-2] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 359
[2025-06-15 19:19:10.483] [INFO ] [http-nio-32000-exec-2] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=53 ms, AverageTime=53 ms

[2025-06-15 19:19:20.486] [INFO ] [http-nio-32000-exec-9] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 360
[2025-06-15 19:19:20.486] [INFO ] [http-nio-32000-exec-9] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=46 ms, AverageTime=46 ms

[2025-06-15 19:19:30.469] [INFO ] [http-nio-32000-exec-4] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 361
[2025-06-15 19:19:30.470] [INFO ] [http-nio-32000-exec-4] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=43 ms, AverageTime=43 ms

[2025-06-15 19:19:37.610] [INFO ] [http-nio-32000-exec-5] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 362
[2025-06-15 19:19:37.612] [INFO ] [http-nio-32000-exec-5] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=52 ms, AverageTime=52 ms

[2025-06-15 19:19:47.617] [INFO ] [http-nio-32000-exec-6] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 363
[2025-06-15 19:19:47.618] [INFO ] [http-nio-32000-exec-6] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=54 ms, AverageTime=54 ms

[2025-06-15 19:19:57.610] [INFO ] [http-nio-32000-exec-7] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 364
[2025-06-15 19:19:57.610] [INFO ] [http-nio-32000-exec-7] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=55 ms, AverageTime=55 ms

[2025-06-15 19:20:06.190] [INFO ] [scheduling-1] [com.xinghuo.fruugo.analysis.task.FruugoAlertTask] [?] [?] - 开始执行Fruugo数据监控检查任务
[2025-06-15 19:20:06.190] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 开始检查数据处理状态
[2025-06-15 19:20:08.533] [INFO ] [http-nio-32000-exec-10] [c.x.a.c.service.impl.AmazonListTaskServiceImpl] [?] [?] - 获取到待处理的Amazon列表任务: 365
[2025-06-15 19:20:08.533] [INFO ] [http-nio-32000-exec-10] [com.xinghuo.admin.aop.MethodCountAspect] [?] [?] - Method summary:
AmazonListTaskServiceImpl.waitGets(..):	 Count=1, TotalTime=49 ms, AverageTime=49 ms

[2025-06-15 19:20:08.541] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 发送企业微信告警通知 [JustBought数据缺失] [JustBought数据]: 失败
[2025-06-15 19:20:08.679] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 发送企业微信告警通知 [产品数据堆积] [产品数据]: 失败
[2025-06-15 19:20:08.737] [INFO ] [scheduling-1] [c.x.f.analysis.service.impl.FruugoAlertServiceImpl] [?] [?] - 数据处理状态检查完成，是否有告警: true
[2025-06-15 19:20:08.737] [INFO ] [scheduling-1] [com.xinghuo.fruugo.analysis.task.FruugoAlertTask] [?] [?] - Fruugo数据监控检查任务执行完成，是否有告警: true
[2025-06-15 19:20:15.997] [INFO ] [http-nio-32000-exec-3] [c.x.amazon.collect.service.AmazonHtmlParseService] [?] [?] - 成功解析Amazon列表页HTML，共解析出33个商品
