package com.xinghuo.allegro.data.model.seller;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * Allegro卖家分析VO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "Allegro卖家分析VO")
public class AllegroSellerAnalysisVO {

    @Schema(description = "总卖家数")
    private Long totalSellers;

    @Schema(description = "活跃卖家数")
    private Long activeSellers;

    @Schema(description = "高级卖家数")
    private Long premiumSellers;

    @Schema(description = "普通卖家数")
    private Long normalSellers;

    @Schema(description = "黑名单卖家数")
    private Long blockedSellers;

    @Schema(description = "今日新增卖家数")
    private Long todayNewSellers;

    @Schema(description = "昨日新增卖家数")
    private Long yesterdayNewSellers;

    @Schema(description = "本周新增卖家数")
    private Long weekNewSellers;

    @Schema(description = "本月新增卖家数")
    private Long monthNewSellers;

    @Schema(description = "今日黑名单卖家数")
    private Long todayBlockedSellers;

    @Schema(description = "昨日黑名单卖家数")
    private Long yesterdayBlockedSellers;

    @Schema(description = "本周黑名单卖家数")
    private Long weekBlockedSellers;

    @Schema(description = "本月黑名单卖家数")
    private Long monthBlockedSellers;

    @Schema(description = "卖家增长率")
    private Double sellerGrowthRate;

    @Schema(description = "高级卖家占比")
    private Double premiumSellerRate;

    @Schema(description = "黑名单卖家占比")
    private Double blockedSellerRate;

    // 中国卖家统计数据
    @Schema(description = "中国卖家总数")
    private Long chinaTotalSellers;

    @Schema(description = "中国活跃卖家数")
    private Long chinaActiveSellers;

    @Schema(description = "中国高级卖家数")
    private Long chinaPremiumSellers;

    @Schema(description = "中国新增卖家数")
    private Long chinaNewSellers;

    @Schema(description = "中国活跃卖家占比")
    private Double chinaActiveRate;

    @Schema(description = "中国高级卖家占比")
    private Double chinaPremiumRate;

    @Schema(description = "中国卖家增长率")
    private Double chinaGrowthRate;
}
