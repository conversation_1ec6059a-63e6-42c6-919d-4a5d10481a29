package com.xinghuo.allegro.data.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xinghuo.allegro.collect.entity.CollectOfferEntity;
import com.xinghuo.allegro.collect.service.CollectOfferService;
import com.xinghuo.allegro.data.entity.AllegroSaleEntity;
import com.xinghuo.allegro.data.entity.AllegroSellerEntity;
import com.xinghuo.allegro.data.model.seller.AllegroSellerForm;
import com.xinghuo.allegro.data.model.seller.AllegroSellerModel;
import com.xinghuo.allegro.data.model.seller.AllegroSellerPagination;
import com.xinghuo.allegro.data.model.seller.AllegroSellerStatsVO;
import com.xinghuo.allegro.data.service.AllegroSaleService;
import com.xinghuo.allegro.data.service.AllegroSellerAnalysisService;
import com.xinghuo.allegro.data.service.AllegroSellerService;
import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.vo.PageListVO;
import com.xinghuo.common.base.vo.PaginationVO;
import com.xinghuo.common.util.core.BeanCopierUtils;
import com.xinghuo.common.util.core.DateXhUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * Allegro卖家控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@Tag(name = "Allegro卖家管理", description = "Allegro卖家管理")
@RequestMapping("/api/allegro/data/seller")
public class AllegroDataSellerController {

    @Resource
    private AllegroSellerService allegroSellerService;

    @Resource
    private CollectOfferService collectOfferService;

    @Resource
    private AllegroSaleService allegroSaleService;

    @Resource
    private AllegroSellerAnalysisService allegroSellerAnalysisService;

    /**
     * 卖家列表查询
     */
    @Operation(summary = "卖家列表查询")
    @PostMapping("/getList")
    public ActionResult<PageListVO<AllegroSellerModel>> list(@RequestBody AllegroSellerPagination pagination) {
        log.info("Allegro卖家列表查询，参数: {}", pagination);

        // 获取卖家列表
        List<AllegroSellerEntity> list = allegroSellerService.getList(pagination);
        List<AllegroSellerModel> listVO = BeanCopierUtils.copyList(list, AllegroSellerModel.class);

        // 设置分页信息
        PaginationVO page = BeanCopierUtils.copy(pagination, PaginationVO.class);
        return ActionResult.page(listVO, page);
    }

    /**
     * 获取卖家详情
     */
    @Operation(summary = "卖家详情")
    @GetMapping("/{id}")
    @Parameters({
            @Parameter(name = "id", description = "卖家ID", required = true)
    })
    public ActionResult<AllegroSellerModel> info(@PathVariable("id") String id) {
        AllegroSellerEntity entity = allegroSellerService.getInfo(id);
        if (entity == null) {
            return ActionResult.fail("未找到卖家数据");
        }

        AllegroSellerModel model = BeanCopierUtils.copy(entity, AllegroSellerModel.class);
        return ActionResult.success(model);
    }



    /**
     * 更新卖家信息
     */
    @Operation(summary = "更新卖家信息")
    @PutMapping("/{id}")
    @Parameters({
            @Parameter(name = "id", description = "卖家ID", required = true)
    })
    public ActionResult<Boolean> update(@PathVariable("id") String id, @RequestBody AllegroSellerForm form) {
        log.info("更新卖家信息，ID: {}, 参数: {}", id, form);

        AllegroSellerEntity entity = BeanCopierUtils.copy(form, AllegroSellerEntity.class);
        entity.setId(id);

        boolean result = allegroSellerService.update(id, entity);
        return ActionResult.success(result);
    }



    /**
     * 删除卖家
     */
    @Operation(summary = "删除卖家")
    @DeleteMapping("/{id}")
    @Parameters({
            @Parameter(name = "id", description = "卖家ID", required = true)
    })
    public ActionResult<Boolean> delete(@PathVariable("id") String id) {
        log.info("删除卖家，ID: {}", id);

        boolean result = allegroSellerService.removeById(id);
        if (result) {
            return ActionResult.success("删除成功");
        } else {
            return ActionResult.fail("删除失败，卖家可能不存在");
        }
    }

    /**
     * 批量删除卖家
     */
    @Operation(summary = "批量删除卖家")
    @PostMapping("/batchDelete")
    public ActionResult<Boolean> batchDelete(@RequestBody List<String> ids) {
        log.info("批量删除卖家，ID列表: {}", ids);

        if (ids == null || ids.isEmpty()) {
            return ActionResult.fail("卖家ID列表不能为空");
        }

        boolean result = allegroSellerService.removeByIds(ids);
        if (result) {
            return ActionResult.success("批量删除成功");
        } else {
            return ActionResult.fail("批量删除失败");
        }
    }

    /**
     * 获取卖家统计信息和产品列表
     */
    @Operation(summary = "获取卖家统计信息和产品列表")
    @GetMapping("/{id}/stats")
    @Parameters({
            @Parameter(name = "id", description = "卖家ID", required = true),
            @Parameter(name = "page", description = "页码", required = false),
            @Parameter(name = "limit", description = "每页条数", required = false)
    })
    public ActionResult<AllegroSellerStatsVO> getSellerStats(
            @PathVariable("id") String id,
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "limit", defaultValue = "10") Integer limit) {
        log.info("获取卖家统计信息和产品列表，ID: {}, 页码: {}, 每页条数: {}", id, page, limit);

        // 获取卖家信息
        AllegroSellerEntity seller = allegroSellerService.getInfo(id);
        if (seller == null) {
            return ActionResult.fail("未找到卖家数据");
        }

        // 创建返回对象
        AllegroSellerStatsVO statsVO = new AllegroSellerStatsVO();
        statsVO.setSeller(BeanCopierUtils.copy(seller, AllegroSellerModel.class));

        // 获取卖家产品总数
        long productCount = collectOfferService.countBySellerId(seller.getSellerId());
        statsVO.setProductCount(productCount);


        // 获取卖家产品列表（分页）
        List<CollectOfferEntity> products = collectOfferService.getProductsBySellerId(seller.getSellerId(), page, limit);
        statsVO.setProducts(products);

        // 设置分页信息
        statsVO.setPage(page);
        statsVO.setLimit(limit);
        statsVO.setTotal(productCount);

        return ActionResult.success(statsVO);
    }

    /**
     * 获取卖家产品列表
     */
    @Operation(summary = "获取卖家产品列表")
    @GetMapping("/{id}/products")
    @Parameters({
            @Parameter(name = "id", description = "卖家ID", required = true),
            @Parameter(name = "page", description = "页码", required = false),
            @Parameter(name = "limit", description = "每页条数", required = false)
    })
    public ActionResult<List<CollectOfferEntity>> getSellerProducts(
            @PathVariable("id") String id,
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "limit", defaultValue = "10") Integer limit) {
        log.info("获取卖家产品列表，ID: {}, 页码: {}, 每页条数: {}", id, page, limit);

        // 获取卖家信息
        AllegroSellerEntity seller = allegroSellerService.getInfo(id);
        if (seller == null) {
            return ActionResult.fail("未找到卖家数据");
        }

        // 获取卖家产品列表（分页）
        List<CollectOfferEntity> products = collectOfferService.getProductsBySellerId(seller.getSellerId(), page, limit);

        return ActionResult.success(products);
    }

    /**
     * 手动触发处理同地址卖家数据
     */
    @Operation(summary = "手动触发处理同地址卖家数据")
    @PostMapping("/process-same-address-sellers")
    public ActionResult<Map<String, Object>> processSameAddressSellers() {
        log.info("手动触发处理同地址卖家数据");
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("message", "处理同地址卖家数据成功");
        return ActionResult.success(result);
    }


    /**
     * 获取卖家产品列表（适配前端API）
     */
    @Operation(summary = "获取卖家产品列表")
    @PostMapping("/getProducts")
    public ActionResult<PageListVO<CollectOfferEntity>> getSellerProductsList(@RequestBody Map<String, Object> params) {
        log.info("获取卖家产品列表，参数: {}", params);

        // 从参数中获取卖家ID和分页信息
        String sellerId = params.get("sellerId") != null ? params.get("sellerId").toString() : null;
        Integer pageSize = params.get("pageSize") != null ? Integer.parseInt(params.get("pageSize").toString()) : 10;
        Integer currentPage = params.get("currentPage") != null ? Integer.parseInt(params.get("currentPage").toString()) : 1;

        if (StrXhUtil.isEmpty(sellerId)) {
            return ActionResult.fail("卖家ID不能为空");
        }

        // 获取卖家信息
        AllegroSellerEntity seller = allegroSellerService.getInfoBySellerId(sellerId);
        if (seller == null) {
            return ActionResult.fail("未找到卖家数据");
        }

        // 获取卖家产品列表（分页）
        List<CollectOfferEntity> products = collectOfferService.getProductsBySellerId(sellerId, currentPage, pageSize);

        // 获取产品总数
        long total = collectOfferService.countBySellerId(sellerId);

        // 设置分页信息
        PaginationVO page = new PaginationVO();
        page.setCurrentPage(currentPage);
        page.setPageSize(pageSize);
        page.setTotal(total);

        return ActionResult.page(products, page);
    }

    /**
     * 获取卖家销售数据（适配前端API）
     */
    @Operation(summary = "获取卖家销售数据")
    @PostMapping("/getSales")
    public ActionResult<PageListVO<AllegroSaleEntity>> getSellerSales(@RequestBody Map<String, Object> params) {
        log.info("获取卖家销售数据，参数: {}", params);

        // 从参数中获取卖家ID和分页信息
        String sellerId = params.get("sellerId") != null ? params.get("sellerId").toString() : null;
        Integer pageSize = params.get("pageSize") != null ? Integer.parseInt(params.get("pageSize").toString()) : 10;
        Integer currentPage = params.get("currentPage") != null ? Integer.parseInt(params.get("currentPage").toString()) : 1;
        String startDate = params.get("startDate") != null ? params.get("startDate").toString() : null;
        String endDate = params.get("endDate") != null ? params.get("endDate").toString() : null;

        if (StrXhUtil.isEmpty(sellerId)) {
            return ActionResult.fail("卖家ID不能为空");
        }

        // 获取卖家信息
        AllegroSellerEntity seller = allegroSellerService.getInfoBySellerId(sellerId);
        if (seller == null) {
            return ActionResult.fail("未找到卖家数据");
        }

        // 构建查询条件
        LambdaQueryWrapper<AllegroSaleEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AllegroSaleEntity::getSellerId, sellerId);

        // 添加日期过滤条件
        if (StrXhUtil.isNotEmpty(startDate)) {
            Date start = DateXhUtil.parseDate(startDate);
            queryWrapper.ge(AllegroSaleEntity::getSaleDate, start);
        }

        if (StrXhUtil.isNotEmpty(endDate)) {
            Date end = DateXhUtil.parseDate(endDate);
            queryWrapper.le(AllegroSaleEntity::getSaleDate, end);
        }

        // 按销售日期降序排序
        queryWrapper.orderByDesc(AllegroSaleEntity::getSaleDate);

        // 计算分页参数
        int offset = (currentPage - 1) * pageSize;
        queryWrapper.last("LIMIT " + offset + ", " + pageSize);

        // 查询销售数据
        List<AllegroSaleEntity> sales = allegroSaleService.list(queryWrapper);

        // 获取总记录数
        LambdaQueryWrapper<AllegroSaleEntity> countWrapper = new LambdaQueryWrapper<>();
        countWrapper.eq(AllegroSaleEntity::getSellerId, sellerId);
        if (StrXhUtil.isNotEmpty(startDate)) {
            Date start = DateXhUtil.parseDate(startDate);
            countWrapper.ge(AllegroSaleEntity::getSaleDate, start);
        }
        if (StrXhUtil.isNotEmpty(endDate)) {
            Date end = DateXhUtil.parseDate(endDate);
            countWrapper.le(AllegroSaleEntity::getSaleDate, end);
        }
        long total = allegroSaleService.count(countWrapper);

        // 设置分页信息
        PaginationVO page = new PaginationVO();
        page.setCurrentPage(currentPage);
        page.setPageSize(pageSize);
        page.setTotal(total);

        return ActionResult.page(sales, page);
    }



    /**
     * 获取卖家统计数据（适配前端API）
     */
    @Operation(summary = "获取卖家统计数据")
    @GetMapping("/getStatistics/{sellerId}")
    public ActionResult<Map<String, Object>> getSellerStatistics(@PathVariable("sellerId") String sellerId) {
        log.info("获取卖家统计数据，卖家ID: {}", sellerId);

        if (StrXhUtil.isEmpty(sellerId)) {
            return ActionResult.fail("卖家ID不能为空");
        }

        // 获取卖家信息
        AllegroSellerEntity seller = allegroSellerService.getInfoBySellerId(sellerId);
        if (seller == null) {
            return ActionResult.fail("未找到卖家数据");
        }

        // 获取卖家产品总数
        long productCount = collectOfferService.countBySellerId(sellerId);

        // 获取卖家销售数据
        LambdaQueryWrapper<AllegroSaleEntity> salesQuery = new LambdaQueryWrapper<>();
        salesQuery.eq(AllegroSaleEntity::getSellerId, sellerId);
        long salesCount = allegroSaleService.count(salesQuery);

        // 获取卖家分类统计数据
        List<Map<String, Object>> categoryStats = new ArrayList<>();
        // 这里应该从数据库中获取实际的分类统计数据，暂时使用模拟数据
        Map<String, Object> electronicsCategory = new HashMap<>();
        electronicsCategory.put("name", "电子产品");
        electronicsCategory.put("value", 120);
        categoryStats.add(electronicsCategory);

        Map<String, Object> homeCategory = new HashMap<>();
        homeCategory.put("name", "家居用品");
        homeCategory.put("value", 85);
        categoryStats.add(homeCategory);

        Map<String, Object> clothingCategory = new HashMap<>();
        clothingCategory.put("name", "服装");
        clothingCategory.put("value", 64);
        categoryStats.add(clothingCategory);

        Map<String, Object> toysCategory = new HashMap<>();
        toysCategory.put("name", "玩具");
        toysCategory.put("value", 42);
        categoryStats.add(toysCategory);

        Map<String, Object> otherCategory = new HashMap<>();
        otherCategory.put("name", "其他");
        otherCategory.put("value", 38);
        categoryStats.add(otherCategory);

        // 构建返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("seller", BeanCopierUtils.copy(seller, AllegroSellerModel.class));
        result.put("productCount", productCount);
        result.put("salesCount", salesCount);
        result.put("categoryStats", categoryStats);

        return ActionResult.success(result);
    }
}
