package com.xinghuo.allegro.data.service;

import com.xinghuo.allegro.data.entity.AllegroDataOfferEntity;
import com.xinghuo.allegro.collect.model.collect.CollectLinkModel;
import com.xinghuo.common.base.service.BaseService;

import java.util.List;

/**
 * Allegro 英文采集商品服务接口
 * 
 * <AUTHOR>
 * @date 2024-11-05
 */
public interface AllegroDataOfferService extends BaseService<AllegroDataOfferEntity> {

    /**
     * 批量保存商品数据
     * @param taskType 任务类型
     * @param entityList 商品实体列表
     */
    void saveBatch(String taskType, List<AllegroDataOfferEntity> entityList);

    /**
     * 批量保存商品数据（重载方法）
     * @param entityList 商品实体列表
     */
    void saveBatch(List<AllegroDataOfferEntity> entityList);

    /**
     * 获取待处理的商品列表
     * @return 待处理的商品链接列表
     */
    List<AllegroDataOfferEntity> waitGets();

    /**
     * 检查新卖家ID是否存在
     * @param sellerId 卖家ID
     * @return 是否存在
     */
    boolean checkNewSellerIdExists(String sellerId);

    /**
     * 根据商品名称和图片URL查询商品（排除指定ID）
     * @param offerName 商品名称
     * @param imageUrl 图片URL
     * @param excludeId 排除的ID
     * @return 商品实体
     */
    AllegroDataOfferEntity getByOfferNameAndImageUrl(String offerName, String imageUrl, String excludeId);

    /**
     * 处理商品数据
     * @param offerEntity 商品实体
     */
    void dealData(AllegroDataOfferEntity offerEntity);
}
