package com.xinghuo.allegro.manage.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.BaseEntityV2;
import lombok.Data;

import java.util.Date;

@Data
@TableName("zz_allegro_log")
public class AllegroLogEntity  extends BaseEntityV2.CUBaseEntityV2 {


    @TableField("seller_id")
    private String sellerId;

    @TableField("store_name")
    private String storeName;

    @TableField("log_type")
    private String logType;

    @TableField("relate_id")
    private String relateId;

    @TableField("note")
    private String note;

    @TableField("priority")
    private Integer priority;


    /** 执行时间 */
    @TableField("execution_time")
    private Date executionTime;

    /** 状态 */
    @TableField("status")
    private String status;

    /** 错误信息 */
    @TableField("error_msg")
    private String errorMsg;

    @TableField("complete_time")
    private Date completeTime;

}
