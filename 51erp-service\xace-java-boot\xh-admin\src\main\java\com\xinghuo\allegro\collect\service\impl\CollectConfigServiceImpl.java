package com.xinghuo.allegro.collect.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.databind.JsonNode;
import com.xinghuo.allegro.collect.dao.CollectConfigMapper;
import com.xinghuo.allegro.collect.entity.CollectConfigEntity;
import com.xinghuo.allegro.collect.model.config.ExpressConfigModel;
import com.xinghuo.allegro.collect.service.CollectConfigService;
import com.xinghuo.allegro.order.model.express.ExpressFeeSettingModel;
import com.xinghuo.common.base.service.BaseServiceImpl;
import com.xinghuo.common.util.core.RandomUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.common.util.json.JsonXhUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * 收藏销售服务的实现类，继承自基本服务实现类，处理收藏销售相关的业务逻辑。
 *
 * TODO 批量更新 历史删除的数据，到新的saleId中。
 * <AUTHOR>
 */

@Slf4j
@Service
public class CollectConfigServiceImpl extends BaseServiceImpl<CollectConfigMapper, CollectConfigEntity> implements CollectConfigService {

    @Override
    public CollectConfigEntity getInfo(String configType,  String tenantId){
        QueryWrapper<CollectConfigEntity> queryWrapper = new QueryWrapper<>();
        if(StrXhUtil.isNotBlank(tenantId)){
            queryWrapper.lambda().eq(CollectConfigEntity::getTenantId, tenantId);
        }
        queryWrapper.lambda().eq(CollectConfigEntity::getConfigType, configType);
        List<CollectConfigEntity> list = this.list(queryWrapper);
        if(!list.isEmpty()){
            return list.get(0);
        }
        else {
            CollectConfigEntity entity = new CollectConfigEntity();
            entity.setId(RandomUtil.snowId());
            entity.setConfigType(configType);
            entity.setTenantId(tenantId);
            return entity;
        }

    }


    @Override
    public boolean save(CollectConfigEntity entity){
        boolean flag = super.save(entity);
        return flag;
    }

    @Override
    public boolean update(CollectConfigEntity entity){
        boolean flag = super.updateById(entity);
        return flag;
    }


    /**
     * 物流费率设置
     */

    public ExpressFeeSettingModel getExpressFeeSetting(String tenantId){
        QueryWrapper<CollectConfigEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(CollectConfigEntity::getConfigType, "EXPRESS").last("limit 1");
        if(StrXhUtil.isNotBlank(tenantId)){
            queryWrapper.lambda().eq(CollectConfigEntity::getTenantId, tenantId);
        }
        CollectConfigEntity entity = this.getOne(queryWrapper);
        if(entity != null){
            ExpressFeeSettingModel model = JsonXhUtil.toBean(entity.getConfig(), ExpressFeeSettingModel.class);
            if(model==null){
                model = ExpressFeeSettingModel.builder().packageCostCny(BigDecimal.ONE).withdrawalExpenseRate(BigDecimal.valueOf(0.03)).build();
            }
            return model;
        }
        return null;
    }

    @Override
    public  String getIoss(){
        QueryWrapper<CollectConfigEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(CollectConfigEntity::getConfigType, "EXPRESS").last("limit 1");
        CollectConfigEntity entity = this.getOne(queryWrapper);
        if(entity != null){
            JsonNode jsonNode = JsonXhUtil.parseObject(entity.getConfig());
            if(jsonNode != null && jsonNode.has("ioss") ){
                return jsonNode.get("ioss").asText();
            }
        }
        return null;
    }


    @Override
    public ExpressConfigModel getExpressConfig(){
        QueryWrapper<CollectConfigEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(CollectConfigEntity::getConfigType, "EXPRESS").last("limit 1");
        CollectConfigEntity entity = this.getOne(queryWrapper);
        if(entity != null){
            return JsonXhUtil.toBean(entity.getConfig(),ExpressConfigModel.class);
        }
        return null;
    }
}