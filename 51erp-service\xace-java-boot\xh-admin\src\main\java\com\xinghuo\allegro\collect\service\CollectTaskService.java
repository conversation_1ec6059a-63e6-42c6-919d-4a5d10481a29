package com.xinghuo.allegro.collect.service;

import com.xinghuo.allegro.collect.entity.CollectTaskEntity;
import com.xinghuo.allegro.collect.model.collect.CollectTaskPagination;
import com.xinghuo.common.base.service.BaseService;

import java.util.List;

public interface CollectTaskService extends BaseService<CollectTaskEntity> {

    /**
     * 分页查询采集任务列表
     * @param pagination 分页查询参数
     * @return 采集任务列表
     */
    List<CollectTaskEntity> getList(CollectTaskPagination pagination);

    CollectTaskEntity waitGets(String clientId,String taskType,String platform);

    int  updateCollectTask(String taskId);

    boolean existTask(String productId);

    long getPendingTaskCount(String taskType);

}
